import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON>v, Tab, Modal } from "react-bootstrap";
import MaterialTable from "material-table";
import { RISKASSESSMENT_LIST, RISK_DELETE_WITH_ID_URL, RISK_WITH_ID_URL } from "../constants";
import { ThemeProvider, createTheme } from "@mui/material";
import { useHistory, useLocation } from "react-router-dom/cjs/react-router-dom";
import { useSelector } from "react-redux";
import API from "../services/API";
import moment from "moment";
import Swal from "sweetalert2";
import { FilterMatchMode, FilterOperator } from 'primereact/api';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { InputText } from 'primereact/inputtext';
import { MultiSelect } from 'primereact/multiselect';
import { Calendar } from 'primereact/calendar';
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";
import { Dropdown, Form } from 'react-bootstrap';
import { convertToLocalTime } from "../services/ConvertLocalTime";
import Routine from "./HazardBased";
import EditRoutine from "./EditRoutine";

const HignRiskHazard = ({ data, onFilterUpdate }) => {
    const user = useSelector((state) => state.login.user)
    console.log(user)
    const history = useHistory();
    const location = useLocation()

    const [risk, setRisk] = useState([])
    const [users, setUsers] = useState([])
    const [depart, setDepart] = useState([])
    const [overdue, setOverdue] = useState([])
    const [additional, setAdditional] = useState([])
    const [aOverdue, setAOverdue] = useState([])
    const [access, setAccess] = useState(false)
    const [showModal, setShowModal] = useState(false);
    const [viewModal, setViewModal] = useState(false);
    const [modalType, setModalType] = useState(''); // To differentiate routine/non-routine
    const [riskData, setRiskData] = useState(null); // S
    const [domain, setDomain] = useState([])

    const [filters, setFilters] = useState({
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        meetid: { value: null, matchMode: FilterMatchMode.IN },
        activity: { value: null, matchMode: FilterMatchMode.IN },
        date: { value: null, matchMode: FilterMatchMode.IN },
        nextdate: { value: null, matchMode: FilterMatchMode.IN },
        'type.label': { value: null, matchMode: FilterMatchMode.IN },
        status: { value: null, matchMode: FilterMatchMode.IN },
        'teamLeader.firstName': { value: null, matchMode: FilterMatchMode.IN },
        department: { value: null, matchMode: FilterMatchMode.IN },
    });
    // const [hazard,setHazard] =useState([])
    const renderHeader = () => {
        const value = filters['global'] ? filters['global'].value : '';

        return (
            <div className='d-flex justify-content-end'>
                {access &&
                    <div className="nav-item nav-profile">

                        <Button className="btn btn-primary  mb-3 " onClick={() => openAssessment('hazard')}>
                            Add New
                        </Button>


                    </div>
                }
            </div>
        );
    };

    const header = renderHeader();

    useEffect(() => {
        getPermit();

    }, [data])
    const customSwal = Swal.mixin({
        customClass: {
            confirmButton: 'btn btn-danger',
            cancelButton: 'btn btn-light'
        },
        buttonsStyling: false
    })
    const customSwal2 = Swal.mixin({
        customClass: {
            confirmButton: 'btn btn-primary',

        },
        buttonsStyling: false
    })

    const getPermit = async () => {


        setRisk(data)

        const teamLeaderNames = data
            .map(item => ({
                name: item.teamLeader.firstName,
                value: item.teamLeader.firstName // or use item.teamLeader.id if you prefer the ID
            }))
            .filter((obj, index, self) =>
                index === self.findIndex((t) => t.name === obj.name)
            );


        setUsers(teamLeaderNames)


        if (user.length !== 0) {
            setAccess(user.roles.some(item => item.maskId === 'ra_leader'))
        }

    }

    const openAssessment = (type) => {

        // history.push('/hazardnew', { type, domain: 'new', })

        setModalType(type); // 'routine' or 'nonroutine'
        setRiskData(null);  // Clear any previous data
        setShowModal(true); // Open the modal
        setDomain('new')

    }


    const viewRisk = (data) => {

        // history.push('/routineedit', { data, type: 'hazard', domain: 'view' })

        setModalType('hazard'); // 'routine' or 'nonroutine'
        setRiskData(data);  // Clear any previous data
        setViewModal(true); // Open the modal
        setDomain('view')

    }
    const editRisk = (data) => {
        // let id = data.id
        // if (data.type === 'High-Risk Hazard') {
        //     if (data.status === 'Draft') {
        //         history.push('/risk-assessment/hazarddraft', { id })
        //     } else {
        //         history.push('/hazardnew', { data, type: 'hazard', domain: 'edit' })
        //     }

        // } else {
        //     if (data.status === 'Draft') {
        //         history.push('/routineedit', { data, type: 'draft' })
        //     } else {
        //         if (data.type === 'Routine') {
        //             history.push('/routinenew', { type: 'routine', domain: 'edit', data })
        //         } else {
        //             history.push('/routinenew', { type: 'nonroutine', domain: 'edit', data })
        //         }
        //     }

        // }

        setModalType('hazard'); // 'routine' or 'nonroutine'
        setRiskData(data);  // Clear any previous data
        setShowModal(true); // Open the modal
        setDomain('edit')
    }

    const onDelete = async (id) => {

        customSwal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            reverseButtons: true,

            confirmButtonText: 'Delete'
        }).then(async (result) => {
            if (result.isConfirmed) {
                const response = await API.delete(RISK_DELETE_WITH_ID_URL(id));
                if (response.status === 204) {

                    customSwal2.fire(
                        'Deleted!',
                        '',
                        'success'
                    )


                }
                getPermit();
            }
        })

    }



    const actionBodyTemplate = (row) => {
        return (
            <div className="table-action d-flex">
                {/* <i className="pi pi-eye" onClick={() => viewRisk(row)}></i> */}
                {access && <>
                    <i className="pi pi-pencil" onClick={() => editRisk(row)}></i>
                    <i className="pi pi-trash text-danger" onClick={() => onDelete(row.id)}></i>
                </>}
            </div>
        );
    }

    const typeFilterTemplate = (options) => {

        return (
            <React.Fragment>
                <div className="mb-3 font-bold">Type</div>
                <MultiSelect value={options.value} options={[{ name: 'Routine Work', value: 'Routine Work' }, { name: 'Non-Routine Work', value: 'Non-Routine Work' }]} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }

    const leaderFilterTemplate = (options) => {

        return (
            <React.Fragment>
                <div className="mb-3 font-bold">Leader</div>
                <MultiSelect value={options.value} options={users} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }
    const statusFilterTemplate = (options) => {

        return (
            <React.Fragment>
                <div className="mb-3 font-bold">Status</div>
                <MultiSelect value={options.value} options={[{ name: 'Pending', value: 'Pending' }, { name: 'Published', value: 'Published' }, { name: 'Draft', value: 'Draft' }]} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }
    const representativesItemTemplate = (option) => {
        return (
            <div className="flex align-items-center gap-2">

                <span>{option.value}</span>
            </div>
        );
    };

    const reviewBodyTemplate = (data) => {
        if (data.nextReviewDate) {
            return convertToLocalTime(data.nextReviewDate)
        }

    }
    const createdBodyTemplate = (data) => {
        return convertToLocalTime(data.created)
    }


    const maskBodyTemplate = (row) => {
        return <div className='maskid' onClick={() => viewRisk(row)}>{row.maskId}</div>
    }

    const handleCloseModal = () => {

        Swal.fire({
            title: 'Are you sure?',
            text: 'You have made edits to this form. Are you sure you want to exit without saving the changes?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, close it!',
            cancelButtonText: 'No, keep it open',
        }).then((result) => {
            if (result.isConfirmed) {
                setShowModal(false);
                setRiskData(null); // Close the modal if the user confirms
                Swal.fire('Closed!', '', 'success');
            } else {
                Swal.fire('Cancelled', '', 'info');
            }
        });
        // setShowModal(false);
        // setRiskData(null);
    };
    const publishBodyTemplate = (data) => {
        if (data.publishedDate) {
            return convertToLocalTime(data.publishedDate)
        }

    }
    const handleFilterChange = (filteredData) => {


        // Update the count of filtered data in the parent component
        if (onFilterUpdate) {
            onFilterUpdate(filteredData.length);
        }
    };
    return (
        <>
            <div>
                <div className="row">
                    <div className="col-12">



                        <div className="card">
                            <div className="card-body p-0">
                                <div className="row">
                                    <div className="col-12">
                                        <div>
                                            <div className="p-3">
                                                <h4 className="fw-bold">Critical High Risk Activity Control Identification</h4>
                                                <p>A Critical High Risk Activity is any task with significant inherent dangers, where failing to implement proper controls could result in severe outcomes such as injury, death, major property damage, or environmental harm. These activities require strict adherence to safety protocols during the Permit to Work (PTW) process and must be emphasized during toolbox talks to ensure all workers understand the associated risks and necessary safety controls.</p>
                                                <p><strong>As a Risk Management Team</strong>, your task is to identify all Critical High Risk Activities at . For each activity, outline the potential consequences and agree on the necessary controls that must be in place. This information will guide the control requirements when issuing permits for these high-risk activities and will also be used to communicate the risks and controls during toolbox talks.</p>
                                            </div>
                                            <>

                                                <DataTable value={risk} paginator rows={10} onValueChange={handleFilterChange} header={header} filters={filters} onFilter={(e) => setFilters(e.filters)} paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                                                    rowsPerPageOptions={[10, 25, 50]}
                                                    emptyMessage="No Data found." tableStyle={{ minWidth: '50rem' }}>

                                                    <Column field="maskId" header="ID" sortable body={maskBodyTemplate}></Column>

                                                    <Column field="hazardName" header="Critical High Risk Activity" sortable ></Column>

                                                    {/* <Column field="type" header="Type" filterElement={typeFilterTemplate} showFilterMatchModes={false} filter filterPlaceholder="Search" ></Column> */}
                                                    <Column field="created" body={createdBodyTemplate} header="Initiated Date"  ></Column>
                                                    <Column field="publishedDate" body={publishBodyTemplate} header="Published / Amended Date"  ></Column>

                                                    <Column field="nextReviewDate" body={reviewBodyTemplate} header="Next Review Date" filterPlaceholder="Search" ></Column>

                                                    <Column field="status" header="Status" filterElement={statusFilterTemplate} showFilterMatchModes={false} filter filterPlaceholder="Search" ></Column>

                                                    <Column field="teamLeader.firstName" header="Initiator" filterElement={leaderFilterTemplate} showFilterMatchModes={false} filter filterPlaceholder="Search" ></Column>
                                                    {access &&
                                                        <Column header="Action" body={actionBodyTemplate} ></Column>
                                                    }

                                                </DataTable>
                                            </>

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>

            {showModal &&
                <Modal size="lg" show={showModal} onHide={handleCloseModal}>
                    <Modal.Header closeButton>
                        <Modal.Title>
                            Critical High Risk Activity Control Identification
                        </Modal.Title>

                    </Modal.Header>
                    <Modal.Body>
                        <Routine data={riskData} type={modalType} domain={domain} />
                    </Modal.Body>

                </Modal>
            }

            {viewModal &&

                <Modal size="lg" show={viewModal} onHide={() => { setRiskData(null); setViewModal(false) }}>
                    <Modal.Header closeButton>
                        <Modal.Title>

                            {`${modalType === 'routine' ? "Routine Work" : modalType === 'nonroutine' ? "Non Routine" : "Hazard Based"} RiskAssessment`}

                        </Modal.Title>

                    </Modal.Header>
                    <Modal.Body>
                        <EditRoutine data={riskData} type={modalType} domain={domain} />
                    </Modal.Body>

                </Modal>
            }
        </>
    );
};

export default HignRiskHazard;
