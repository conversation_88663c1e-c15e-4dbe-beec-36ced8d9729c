import React, { useState } from 'react';
import { DataTable } from 'primereact/datatable';
import { Modal } from 'react-bootstrap';
import { Column } from 'primereact/column';
import { Button } from 'primereact/button';
import 'primereact/resources/themes/saga-blue/theme.css';
import 'primereact/resources/primereact.min.css';
import 'primeicons/primeicons.css';
import ImageComponent from '../../services/FileDownlodS3';



const UpdateTable = ({ data }) => {
    const [showModal, setShowModal] = useState(false);
    const [viewAttachment, setViewAttachment] = useState([]);

    const viewRisk = (rowData) => {
        setShowModal(true);
        setViewAttachment(rowData.attachment);
        // Additional operations if needed
    };

    const actionBodyTemplate = (rowData) => {
        return (
            <Button icon="pi pi-eye" className="p-button-rounded p-button-text" onClick={() => viewRisk(rowData)} />
        );
    };

    return (
        <div className="card">
            <DataTable value={data} header="Reviews, Changes and Updates">
                <Column field="reasonForReview" header="Reason for Review" />
                <Column field="changes" header="Changes" />
                <Column field="reasonForChanges" header="Reason for Changes" />
                <Column field="initiatedBy" header="Initiated By" />
                <Column field="approvedBy" header="Approved By" />
                <Column field="reference" header="Reference" />
                <Column body={actionBodyTemplate} header="Actions" />
            </DataTable>
            <Modal show={showModal} onHide={() => setShowModal(false)}>
                <Modal.Header closeButton>
                    <Modal.Title>Attachments</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <div className='row'>
                        {viewAttachment.map((attachment, index) => (
                            <div key={index} className='col-4 mb-3'>
                                <ImageComponent fileName={attachment} size={'200'} name={true}/>
                            </div>
                        ))}
                    </div>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={() => setShowModal(false)}>
                        Close
                    </Button>
                </Modal.Footer>
            </Modal>

        </div>
    );
};

export default UpdateTable;
