import React from 'react';
import Select from 'react-select';
import { InputTextarea } from 'primereact/inputtextarea';
import { Button } from 'primereact/button';

function StageSix({ formData, setFormData, impacton, fetchRelatedQuestions, handleDeleteConsequenceItem, handleAddConsequenceItem, handleChange, errors, disable }) {
    return (
        <div>
            <p>Consequences refer to the impact of a workplace incident on people, property, the environment, or operations, describing who or what was affected and how.</p>
            {formData.consequences.consequenceItems.map((item, index) => (
                <div className="row mt-4 mb-4 align-items-end" key={index}>
                    <div className="col-3">
                        <p>Impact on</p>
                        <Select
                            options={impacton}
                            name={`consequences.${index}.impactOn`}
                            value={impacton.find(option => option.value === item.impactOn)}
                            onChange={async (selectedOption) => {
                                const field = 'consequences';
                                const subField = 'impactOn';

                                if (!disable) {
                                    // Update formData with the selected impactOn value
                                    setFormData((prevFormData) => ({
                                        ...prevFormData,
                                        [field]: {
                                            ...prevFormData[field],
                                            consequenceItems: prevFormData[field].consequenceItems.map((itm, i) =>
                                                i === index ? { ...itm, [subField]: selectedOption.value } : itm
                                            ),
                                        },
                                    }));

                                    // Fetch related questions based on the selected impactOn value
                                    const questions = await fetchRelatedQuestions(selectedOption.value);

                                    // Update formData with the fetched questions, attaching them to consequenceDescription
                                    setFormData((prevFormData) => ({
                                        ...prevFormData,
                                        consequences: {
                                            ...prevFormData.consequences,
                                            consequenceItems: prevFormData.consequences.consequenceItems.map((itm, i) =>
                                                i === index
                                                    ? {
                                                        ...itm,
                                                        consequenceDescription: questions,  // Use the fetched questions here
                                                    }
                                                    : itm
                                            ),
                                        },
                                    }));
                                }
                            }}
                            isDisabled={disable} // Disable when disable is true
                            styles={{ container: (provided) => ({ ...provided, width: '100%' }) }}
                        />
                    </div>

                    <div className="col-8">
                        {/* Additional space for consequence description */}
                    </div>
                    <div className="col-1 text-center">
                        {!disable && (
                            <i className="pi pi-trash mb-3" onClick={() => handleDeleteConsequenceItem(index)}></i>
                        )}
                    </div>

                    {/* Render fetched questions */}
                    {item.consequenceDescription && item.consequenceDescription.length > 0 && (
                        <div className="row mt-4">
                            {item.consequenceDescription.map((qItem, qIndex) => (
                                <div key={qIndex} className="col-6 mb-3">
                                    <p>{qIndex + 1}.{qItem.question}</p>
                                    <InputTextarea
                                        style={{ width: '100%' }}
                                        rows={2}
                                        name={`consequences.${index}.consequenceDescription.${qIndex}.description`}
                                        value={qItem.description}
                                        autoResize
                                        onChange={(e) => {
                                            const value = e.target.value;
                                            if (!disable) {
                                                setFormData((prevFormData) => ({
                                                    ...prevFormData,
                                                    consequences: {
                                                        ...prevFormData.consequences,
                                                        consequenceItems: prevFormData.consequences.consequenceItems.map((itm, i) =>
                                                            i === index
                                                                ? {
                                                                    ...itm,
                                                                    consequenceDescription: itm.consequenceDescription.map((descItem, dIndex) =>
                                                                        dIndex === qIndex
                                                                            ? { ...descItem, description: value }
                                                                            : descItem
                                                                    ),
                                                                }
                                                                : itm
                                                        ),
                                                    },
                                                }));
                                            }
                                        }}
                                        disabled={disable} // Disable input when disable is true
                                    />
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            ))}

            {errors.consequences && <small className="p-error">{errors.consequences}</small>}

            {!disable && (
                <Button outlined label="Add Consequence" onClick={handleAddConsequenceItem} />
            )}

            <div className="d-flex flex-column col-12 mt-4">
                <p htmlFor="comments" className="mb-2">Additional Comments on the Consequences</p>
                <InputTextarea
                    className="d-flex"
                    rows={2}
                    autoResize
                    name="consequences.comments"
                    value={formData.consequences.comments}
                    onChange={(e) => {
                        const value = e.target.value;
                        if (!disable) {
                            setFormData((prevFormData) => ({
                                ...prevFormData,
                                consequences: {
                                    ...prevFormData.consequences,
                                    comments: value,
                                },
                            }));
                        }
                    }}
                    disabled={disable} // Disable input when disable is true
                />
            </div>
        </div>
    );
}

export default StageSix;
