import React, { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON>, But<PERSON>, Form } from 'react-bootstrap';
import Box from '@mui/material/Box';
import Stepper from '@mui/material/Stepper';
import Step from '@mui/material/Step';
import StepLabel from '@mui/material/StepLabel';
import StepContent from '@mui/material/StepContent';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import Switch from "react-switch";
import API from "../services/API";
import { AIR_DRIVERS_WITH_ID_URL, AIR_SURVEYORS_URL, AIR_WITH_ID_URL, AIR_REVIEW_WITH_ID_URL, AIR_COST_ESTIMATOR_URL, AIR_REVIEW_RETURN_WITH_ID_URL, AIR_MEDICAL_OFFICER_URL, USERS_URL, AIR_ENGINEER_URL, API_URL, GENERAL_GROUP_URL, STATIC_URL } from "../constants";
import IncidentStory from "./IncidentStory";
import cogoToast from "cogo-toast";
import { DropzoneArea } from 'material-ui-dropzone';
import Select from "react-select";
import axios from "axios";
import ThirdPartyForm from "./ThirdPartyForm";
import ImageTooltip from "./ImageTooltip";
import HtmlTooltip from "./HtmlTooltip";
import IncidentEdit from "./IncidentEdit";
import { useHistory } from 'react-router-dom';
import { PictureAsPdf, Delete } from "@mui/icons-material";
import moment from "moment";
const AirReviewerViewCard = ({ showModal, setShowModal, data }) => {

    const [selectedFiles, setSelectedFiles] = useState([])

    const getFileExtension = (fileName) => {
        return fileName.slice(((fileName.lastIndexOf(".") - 1) >>> 0) + 2);
    };

    const isPDF = (fileName) => {
        return getFileExtension(fileName).toLowerCase() === 'pdf';
    };

    const filesElements = selectedFiles.map(fileName => (
        <div key={fileName} className="col-md-4 mb-3">
            <div className="position-relative">
                {isPDF(fileName) ? (
                    <PictureAsPdf style={{ width: '100%', fontSize: '100px' }} />
                ) : (
                    // For images, you need a server or a predefined path to serve these images
                    <img src={`${STATIC_URL}/${fileName}`} alt="Preview" className="mx-auto d-block" style={{ width: '100%', height: '150px' }} />
                )}

            </div>
            <p className="text-center mt-2">{fileName}</p>
        </div>
    ));

    const history = useHistory();
    const [yValue, setYValue] = useState("No any injury");
    const [xValue, setXValue] = useState("0");
    const [incidentMatrix, setIncidentMatrix] = useState(data.incidentMatrix);
    const [returnStatus, setReturnStatus] = useState('')
    const [disabled, setDisabled] = useState(false);
    const [selectedEvidence, setSelectedEvidence] = useState([]);

    const updateValuesBasedOnInput = (input) => {
        const matrix = {
            "No any injury": {
                0: { item: "C0H0", color: "green" },
                1: { item: "C1H0", color: "yellow" },
                2: { item: "C2H0", color: "orange" },
                3: { item: "C3H0", color: "red" },
            },
            "Human 1st Aid /Potential Medical Treatment/MT": {
                0: { item: "C0H1", color: "yellow" },
                1: { item: "C1H1", color: "yellow" },
                2: { item: "C2H1", color: "orange" },
                3: { item: "C3H1", color: "red" },
            },
            "Potential LTI/LTI": {
                0: { item: "C0H2", color: "orange" },
                1: { item: "C1H2", color: "orange" },
                2: { item: "C2H2", color: "orange" },
                3: { item: "C3H2", color: "red" },
            },
            "Permanent Disability/ Potential Fatal/ Fatal": {
                0: { item: "C0H3", color: "red" },
                1: { item: "C1H3", color: "red" },
                2: { item: "C2H3", color: "red" },
                3: { item: "C3H3", color: "red" },
            },
        };
        let foundXValue = "0"; // Default value
        let foundYValue = "No any injury"; // Default value

        // Iterate over each yValue (keys of the matrix)
        for (const yValue in matrix) {
            // Check each xValue within this yValue category
            for (const xValue in matrix[yValue]) {
                const currentEntry = matrix[yValue][xValue];

                // Compare currentEntry with input
                if (currentEntry.item === input.item && currentEntry.color === input.color) {
                    foundXValue = xValue;
                    foundYValue = yValue;
                    break; // Break out of the inner loop
                }
            }

            // If found, break out of the outer loop
            if (foundXValue !== "0" && foundYValue !== "No any injury") {
                break;
            }
        }

        // Update the state values
        setXValue(foundXValue);
        setYValue(foundYValue);
    };

    const handleImageToggle = (url) => {
        if (selectedEvidence.includes(url)) {
            setSelectedEvidence(prevImages => prevImages.filter(img => img !== url));
        } else {
            setSelectedEvidence(prevImages => [...prevImages, url]);
        }
    }
    useEffect(() => {
        const matrix = {
            "No any injury": {
                0: { item: "C0H0", color: "green" },
                1: { item: "C1H0", color: "yellow" },
                2: { item: "C2H0", color: "orange" },
                3: { item: "C3H0", color: "red" },
            },
            "Human 1st Aid /Potential Medical Treatment/MT": {
                0: { item: "C0H1", color: "yellow" },
                1: { item: "C1H1", color: "yellow" },
                2: { item: "C2H1", color: "orange" },
                3: { item: "C3H1", color: "red" },
            },
            "Potential LTI/LTI": {
                0: { item: "C0H2", color: "orange" },
                1: { item: "C1H2", color: "orange" },
                2: { item: "C2H2", color: "orange" },
                3: { item: "C3H2", color: "red" },
            },
            "Permanent Disability/ Potential Fatal/ Fatal": {
                0: { item: "C0H3", color: "red" },
                1: { item: "C1H3", color: "red" },
                2: { item: "C2H3", color: "red" },
                3: { item: "C3H3", color: "red" },
            },
        };
        setIncidentMatrix(matrix[yValue][xValue])
    }, [xValue, yValue])


    const [showNext, setShowNext] = useState(false);
    const [shortDescription, setShortDescription] = useState('')

    const rejectButtonRef = useRef(null);
    const submitButtonRef = useRef(null);




    const [surveyor, setSurveyor] = useState([])
    useEffect(() => {
        getSurveyor()
    }, [])

    const getSurveyor = async () => {
        const response = await API.post(AIR_SURVEYORS_URL, { locationOneId: '', locationTwoId: '', locationThreeId: '', locationFourId: '' });
        if (response.status === 200) {
            setSurveyor(response.data)
        }
    }

    const [estimator, setEstimator] = useState([])
    useEffect(() => {
        getEstimator()
    }, [])

    const getEstimator = async () => {
        const response = await API.post(AIR_COST_ESTIMATOR_URL, { locationOneId: '', locationTwoId: '', locationThreeId: '', locationFourId: '' });
        if (response.status === 200) {
            setEstimator(response.data)
        }
    }

    const [medicalOfficer, setMedicalOfficer] = useState([])
    useEffect(() => {
        getMedicalOfficer()
    }, [])

    const getMedicalOfficer = async () => {
        const response = await API.post(AIR_MEDICAL_OFFICER_URL, { locationOneId: '', locationTwoId: '', locationThreeId: '', locationFourId: '' });
        if (response.status === 200) {
            setMedicalOfficer(response.data)
        }
    }

    const [engineer, setEngineer] = useState([])
    useEffect(() => {
        getEngineer()
    }, [])

    const getEngineer = async () => {
        const response = await API.post(AIR_ENGINEER_URL, { locationOneId: '', locationTwoId: '', locationThreeId: '', locationFourId: '' });
        if (response.status === 200) {
            setEngineer(response.data)
        }
    }

    const [selectedSurveyor, setSelectedSurveyor] = useState('');
    const [selectedEstimator, setSelectedEstimator] = useState('');
    const [selectedMedicalOfficer, setSelectedMedicalOfficer] = useState('');
    const [selectedEngineer, setSelectedEngineer] = useState('');

    const [immediateAction, setImmediateAction] = useState(false);
    const [majorAccident, setMajorAccident] = useState(false);
    const [medicalPersonal, setMedicalPersonal] = useState(false);
    const [inspectionRequired, setInspectionRequired] = useState(false);
    const [cll, setCll] = useState(false);
    const [files, setFiles] = useState([]);
    const [investigationDate, setInvestigationDate] = useState('');

    const [users, setUsers] = useState([])
    useEffect(() => {
        getUsers()
    }, [])

    const getUsers = async () => {
        const response = await API.get(USERS_URL);
        if (response.status === 200) {
            setUsers(response.data)
        }
    }

    const handleUserSelectChange = (selectedOptions) => {
        setSelectedUsers(selectedOptions);
    };

    const [selectedUsers, setSelectedUsers] = useState([]);

    const handleFileChange = (file) => {
        setFiles(file)

    }

    const [drivers, setDrivers] = useState({
        driverDetails: [
            {
                name: "",
                vehicleNo: "",
                licenseNo: "",
                employeeId: "",
                ban: false
            }
        ]
    })

    const addDriver = () => {
        setDrivers(prevState => ({
            ...prevState,
            driverDetails: [...prevState.driverDetails, {
                name: "",
                vehicleNo: "",
                licenseNo: "",
                employeeId: "",
                ban: false
            }],
        }));
    };

    const handleDriver = (index, field, value) => {
        setDrivers(prevState => {
            const updatedActions = [...prevState.driverDetails];
            updatedActions[index][field] = value;
            return { ...prevState, driverDetails: updatedActions };
        });
    };

    const handleDeleteDriver = (index) => {
        const newDrivers = [...drivers.driverDetails];
        newDrivers.splice(index, 1);
        setDrivers(prevState => ({ ...prevState, driverDetails: newDrivers }));
    };

    const [generalGroups, setGeneralGroups] = useState([]);
    const [thirdParty, setThirdParty] = useState(null);

    const [insuranceNotify, setInsuranceNotify] = useState('')


    const updateForms = (forms) => {
        setThirdParty(forms)
    }
    const [actionTaken, setActionTaken] = useState('');

    const [isEdit, setIsEdit] = useState(false);
    const handleEdit = () => {

        setIsEdit(true);

    }

    function CustomThumbnail(props) {
        // This would render your custom thumbnail, which displays the file name
        // You might have to use the actual file to create a thumbnail (e.g., for images) or use a placeholder for other file types like PDF
        const { file } = props;

        return (
            <div style={{ position: 'relative' }}>
                {/* This is a placeholder; you'd use the actual file to render a thumbnail */}
                <img src={URL.createObjectURL(file)} alt={file.name} style={{ width: '100px', height: '100px' }} />
                <div style={{ position: 'absolute', bottom: 0, background: 'rgba(0, 0, 0, 0.6)', color: 'white', width: '100%', textAlign: 'center' }}>
                    {file.name}
                </div>
            </div>
        );
    }

    const handleDelete = (fileName) => {
        // Filter out the file you want to delete from the current files array
        const updatedFiles = files.filter(file => file.name !== fileName);

        // Update your state with the new list (Assuming you're using useState to manage files)
        setFiles(updatedFiles);
    };


    const [selectedTruck, setSelectedTruck] = useState([]);

    const handleCheckboxChange = (value) => {
        if (selectedTruck.includes(value)) {
            setSelectedTruck((prev) => prev.filter(item => item !== value));
        } else {
            setSelectedTruck((prev) => [...prev, value]);
        }
    };



    const [returnComments, setReturnComments] = useState('')

    const handleStatusChange = (event) => {
        setReturnStatus(event.target.value);
    }

    useEffect(() => {
        if (data) {

            updateValuesBasedOnInput(data.incidentRating)
            setIncidentMatrix(data.incidentRating)
            setShortDescription(data.shortDescription)
            setSelectedEvidence(data.evidence.map(img => { return img.src }))
            setSelectedFiles(data.documents)

            setThirdParty(data.thirdPartyForm)
            setCll(data.cllInvolved)
            setImmediateAction(data.immediateActionRequired)
            setActionTaken(data.actionsTaken)
            setSelectedTruck(data.bannedTruckDetails.split(','))
            setInvestigationDate(data.investigationDate)
            setSelectedUsers(data.investigation?.investigationTeam ?? [])
            setInsuranceNotify(data.insuranceTeamSelected)
        }
    }, [data])
    return (
        <>

            {data && <>
                <div className="row align-items-center my-3">
                    <div className="col-md-12 p-1">
                        <label>Header Information</label>
                        <p> {shortDescription} </p>
                    </div>
                </div>
                <div className="row align-items-center my-3">
                    <label htmlFor="chRankingY" className="col-form-label col-md-12 p-1">C/H Ranking
                        <ImageTooltip imageUrl={require("../../assets/images/im.png")}>
                            <i className="mdi mdi-information cursor-pointer"></i>
                        </ImageTooltip>
                    </label>

                   



                    <div className="col-md-4 p-1">
                        {incidentMatrix?.color && incidentMatrix?.item && (
                            <>
                                <label className="">Incident Rating</label>
                                <div
                                    style={{ backgroundColor: incidentMatrix.color, height: '37.6px' }}
                                    className={`d-flex align-items-center justify-content-center ${incidentMatrix.color === 'yellow' ? 'text-black' : 'text-white'}`}
                                >
                                    {incidentMatrix.item}
                                </div>
                            </>

                        )}
                    </div>
                </div>

                <div className="row">

                    <label htmlFor="" className='m-0 me-3'>Choose upto 4 images</label>
                    {data.allEvidence.map(url => (
                        <div key={url.src} className="col-md-4 mb-3">
                            <div className="card">
                                <div className="form-check">
                                    <input
                                        className="form-check-input img-checkbox"
                                        type="checkbox"
                                        checked={selectedEvidence.includes(url.src)}
                                        onChange={() => handleImageToggle(url.src)}
                                    />

                                </div>
                                <img src={url.src} alt="Preview" className="card-img-top" />

                            </div>
                        </div>
                    ))}

                </div>
                <div className='row'>
                    <div className='col'>
                        <label htmlFor="" className='m-0 me-3'>Attached classified statements / documents</label>
                        <div className='form-group'>


                            <div className="container mt-3">
                                <div className="row">
                                    {filesElements}
                                    {files.map(file => (
                                        <div key={file.name} className="col-md-4 mb-3">
                                            <div className="position-relative">
                                                {file.type === 'application/pdf' ? (
                                                    <PictureAsPdf style={{ width: '100%', fontSize: '100px' }} />
                                                ) : (
                                                    <img src={URL.createObjectURL(file)} alt="Preview" className="mx-auto d-block" style={{ width: '100%', height: '150px' }} />
                                                )}

                                            </div>
                                            <p className="text-center mt-2">{file.name}</p>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>




                <div className='row'>
                    <div className='col'>
                        <div className='form-group d-flex align-items-center'>
                            <label htmlFor="" className='m-0 me-3'>Any immediate actions required?</label>

                            <Switch onChange={(value) => setImmediateAction(value)} checked={immediateAction} />
                        </div>
                    </div>
                </div>
                {
                    immediateAction && (
                        <>

                            <div className='row'>
                                <div className='col'>
                                    <div className='form-group'>
                                        <label htmlFor="">Actions Taken</label>
                                        <input type='text' value={actionTaken} onChange={(e) => setActionTaken(e.target.value)} className='form-control' />
                                    </div>
                                </div>
                            </div>

                            <div className='row'>
                                <div className='col'>
                                    <div className='form-group d-flex align-items-center'>
                                        <label htmlFor="" className='m-0 me-3'>Does these truck to be held under terminal custody?</label>

                                        <Switch onChange={(value) => setCll(value)} checked={cll} />
                                    </div>
                                </div>
                            </div>

                            {data.truckDetails && <div className=''>
                                <div className=''>
                                    <div className='form-group'>
                                        <label htmlFor="" className='m-0 me-3'>Please select the truck(s) to be held: </label>
                                        <div className="list-group">
                                            {data.truckDetails.split(',').map((item, index) => (
                                                <div key={index} className="col">
                                                    <div className="form-check">
                                                        <input
                                                            className="form-check-input"
                                                            type="checkbox"
                                                            value={item}
                                                            id={`checkbox-${index}`}
                                                            checked={selectedTruck.includes(item)}
                                                            onChange={() => handleCheckboxChange(item)}
                                                        />
                                                        <label className="form-check-label" htmlFor={`checkbox-${index}`}>
                                                            {item}
                                                        </label>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>

                                    </div>
                                </div>
                            </div>
                            }
                        </>
                    )
                }
                <div className='row'>
                    <label htmlFor="" className='m-0 me-3'>Notify Insurance / Surveyor
                        <HtmlTooltip content={`
    <h4>TT - Club</h4>
    <p>Deductibles:</p>
    <ul>
    <li>Prime Movers  USD 10,000
    <li>Cargo                   USD 15,000
    <li>Ship Impact        USD 50,000
    <li>Other losses       USD 100,000
    </ul>

    <h4>Fair First</h4>
    <p>
    Damages to PMs exceeding LKR 250,000/- and less than LKR 1,500,000/-
    </p>
  `}>
                            <i className="mdi mdi-information cursor-pointer"></i>
                        </HtmlTooltip>
                    </label>

                    <select onChange={(e) => setInsuranceNotify(e.target.value)} value={insuranceNotify} className="form-select mb-5">
                        <option value="">Choose</option>
                        <option value="TT Club">TT Club</option>
                        <option value="Fair First">Fair First</option>
                    </select>




                </div>







                <div className='row'>
                    <div className='col'>
                        <div className="form-group">
                            <label>Investigation Start Date</label>
                            <input type="date" value={investigationDate} onChange={(e) => setInvestigationDate(e.target.value)} className="form-control" />
                        </div>
                        <div className='form-group'>
                            <label>Select Investigation Team Members:</label>

                            <Select
                                id="user_description"
                                isMulti={true} // Allow multiple selections
                                onChange={handleUserSelectChange} // Handle selection changes
                                options={users.map(user => ({ value: user.id, label: user.firstName }))} // Map users to options
                                value={selectedUsers} // Set selected options
                                placeholder="Type..."
                            />
                        </div>
                    </div>
                </div>


            </>}
        </>
    )
}

export default AirReviewerViewCard;