import React, { useEffect, useState, useCallback } from 'react'
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";
// import 'primereact/resources/themes/lara-light-indigo/theme.css';
import 'primeicons/primeicons.css';
import { Button } from 'primereact/button';
import { GMS1_URL, INCIDENT, GET_USER_ROLE_BY_MODE, INCIDENT_NEARTERM_WITH_ID, HAZARDS_CATEGOTY, GET_ALL_USER, DROPDOWNS, WORK_ACTIVITIES_URL, FILE_URL, INCIDENT_WITH_ID, INVERSTIGATION_TRIGGER, CHILD_DROPDOWNS, ADMINDROPDOWNS } from '../../constants';
import API from '../../services/API';
import 'react-datepicker/dist/react-datepicker.css';
import Swal from 'sweetalert2';
import { Modal, Nav,InputGroup,FormControl } from 'react-bootstrap';
import StageOne from './Stages/StageOne';
import StageTwo from './Stages/StageTwo';
import StageThree from './Stages/StageThree';
import StageFour from './Stages/StageFour';
import StageFive from './Stages/StageFive';
import StageSix from './Stages/StageSix';
import StageSeven from './Stages/StageSeven';
import StageEight from './Stages/StageEight';
import Overview from './OverView';
import HeadStepper from './HeadStepper';
import moment from 'moment';

const customSwal2 = Swal.mixin({
    customClass: {
        confirmButton: 'btn btn-primary',

    },
    buttonsStyling: false
})

const ReviewIncident = ({ show, data, handleClose, submitReport }) => {
    console.log(data);

    const [activeTabIndex, setActiveTabIndex] = useState(1);
    const [formDataList, setFormDataList] = useState([]);
    const [hazards, setHazards] = useState([]);
    const [formData, setFormData] = useState(data);
    const [errors, setErrors] = useState({});
    const [activeStage, setActiveStage] = useState(1); // Default stage for title and date
    const stages = ['Precursors', 'Hazardous Conditions', 'Preventative Controls', 'Incident Details', 'Mitigative Controls', 'Consequences', 'Immediate Action', 'Near-term Control Measures'];
    const [incidentType, setIncidentType] = useState([]);
    const [category, setCategory] = useState([]);
    const [circumstance, setCircumstance] = useState([]);
    const [surfaceCondition, setSurfaceCondition] = useState([]);
    const [surfaceType, setSurfaceType] = useState([]);
    const [lighting, setLighting] = useState([]);
    const [weatherCondition, setWeatherCondition] = useState([]);
    const [pathways, setPathways] = useState([]);
    const [operation, setOperation] = useState([]);
    const [environment, setEnviromnent] = useState([]);
    const [personnel, setPersonnel] = useState([]);
    const [propertyEquipment, setPropertyEquipment] = useState([]);
    const [workActivity, setWorkActivity] = useState([]);
    const [impacton, setImpacton] = useState([]);
    const [questions, setQuestions] = useState([]);
    const [isEditingTitle, setIsEditingTitle] = useState(false);
    const [editedTitle, setEditedTitle] = useState(data.title || '');

    const fetchDropdownData = useCallback(async (maskId, setState, additionalCondition) => {
        try {
            // Prepare the base URI
            let uriString = {
                where: { maskId },
                include: [{ relation: "dropdownItems" }]
            };

            // Check for the 'General' condition
            if (additionalCondition?.type === 'General') {
                uriString.where = { ...uriString.where, ...additionalCondition };
            }

            // Determine URL based on condition type
            const url = `${additionalCondition?.type === 'General' ? DROPDOWNS : ADMINDROPDOWNS}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
            const response = await API.get(url);

            if (response.status === 200) {
                // Map the response to dropdown items
                const data = response.data[0].dropdownItems.map((item) => ({
                    label: item.name,
                    value: item.id,
                }));
                setState(data);

                // Special handling for 'incident_type'
                if (maskId === 'incident_type' && formData?.incidentCategory !== '') {
                    const incident = data.find(option => option.label === formData.incidentType);
                    if (incident) {
                        console.log(incident);
                        await fetchChildDropdownData(incident.value, setCategory);
                    }
                }
            }
        } catch (error) {
            console.error(`Error fetching ${maskId} list:`, error);
            // Optionally, add user feedback (e.g., notification)
        }
    }, []);

    useEffect(() => {
        if (formData?.incidentCircumstance !== '' && category.length > 0) {
            const cate = category.find(option => option.label === formData.incidentCategory);
            if (cate) {
                fetchChildDropdownData(cate.value, setCircumstance);
            }
        }
    }, [category, formData]);
    const fetchChildDropdownData = useCallback(async (id, setState) => {
        try {

            const url = CHILD_DROPDOWNS(id);
            const response = await API.get(url);

            if (response.status === 200) {
                const data = response.data.map((item) => ({
                    label: item.name,
                    value: item.id,
                }));
                setState(data);


            }
        } catch (error) {
            console.error(`Error fetching list:`, error);
        }
    }, []);



    // Function to fetch related questions from API
    const fetchRelatedQuestions = async (id) => {
        console.log(id)
        try {
            const url = CHILD_DROPDOWNS(id);
            const response = await API.get(url);
            if (response.status === 200) {
                return response.data.map(question => ({
                    question: question.name, // Use 'name' as the question text
                    description: ''  // Initialize an empty description field
                }));
            }
        } catch (error) {
            console.error("Error fetching questions:", error);
            return [];
        }
    };

    useEffect(() => {
        const fetchData = async () => {
            await getHazardList();
            await getWorkActivity();

            await fetchDropdownData('incident_type', setIncidentType, { type: 'General' });
            await fetchDropdownData('incidentcategory', setCategory, { type: 'General' });
            await fetchDropdownData('surface_condition', setSurfaceCondition, { type: 'General' });
            await fetchDropdownData('surface_type', setSurfaceType, { type: 'General' });
            await fetchDropdownData('lighting', setLighting, { type: 'General' });
            await fetchDropdownData('weather_condition', setWeatherCondition, { type: 'General' });
            await fetchDropdownData('pathways', setPathways, { type: 'General' });
            await fetchDropdownData('operations', setOperation, { type: 'Application' });
            await fetchDropdownData('property_equipment', setPropertyEquipment, { type: 'Application' });
            await fetchDropdownData('environment', setEnviromnent, { type: 'Application' });
            await fetchDropdownData('personnel', setPersonnel, { type: 'Application' });
            await fetchDropdownData('consequences', setImpacton, { type: 'General' });



        };

        fetchData();
    }, [fetchDropdownData]);

    const getHazardList = useCallback(async () => {
        const uriString = { include: ["hazards"] };
        const url = `${HAZARDS_CATEGOTY}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
        const response = await API.get(url);
        if (response.status === 200) {
            const data = response.data.filter((item) => item.name !== 'Hazard-Based');
            setHazards(data);
        }
    }, []);

    const getWorkActivity = useCallback(async () => {
        const response = await API.get(WORK_ACTIVITIES_URL);
        if (response.status === 200) {
            const data = response.data.map((item) => ({
                label: item.name,
                value: item.id,
            }));
            setWorkActivity(data);
        }
    }, []);

    const handleFilter = useCallback((locationOneId, locationTwoId, locationThreeId, locationFourId, locationFiveId, locationSixId) => {
        setFormData((prev) => {
            if (
                prev.locationOneId !== locationOneId ||
                prev.locationTwoId !== locationTwoId ||
                prev.locationThreeId !== locationThreeId ||
                prev.locationFourId !== locationFourId ||
                prev.locationFiveId !== locationFiveId ||
                prev.locationSixId !== locationSixId
            ) {
                return {
                    ...prev,
                    locationOneId,
                    locationTwoId,
                    locationThreeId,
                    locationFourId,
                    locationFiveId,
                    locationSixId,
                };
            }
            return prev;
        });
    }, []);

    const handleSelectChange = useCallback(async (selectedOption, field) => {
        setFormData((prev) => ({
            ...prev,
            [field]: selectedOption ? selectedOption.label : '',
        }));

        if (field === 'incidentType') {
            await fetchChildDropdownData(selectedOption.value, setCategory)
        } else if (field === 'incidentCategory') {
            await fetchChildDropdownData(selectedOption.value, setCircumstance)
        }
    }, []);

    const handleChange = useCallback((e) => {
        const { name, value } = e.target;
        const nameParts = name.split('.');

        if (nameParts.length === 4) {
            const [parentField, nestedField, index, subfield] = nameParts;
            const updatedItems = formData[parentField][nestedField].map((item, i) =>
                i === parseInt(index) ? { ...item, [subfield]: value } : item
            );
            setFormData({
                ...formData,
                [parentField]: {
                    ...formData[parentField],
                    [nestedField]: updatedItems,
                },
            });
        } else if (nameParts.length === 3) {
            const [parentField, index, subfield] = nameParts;
            const updatedItems = formData[parentField].consequenceItems.map((item, i) =>
                i === parseInt(index) ? { ...item, [subfield]: value } : item
            );
            setFormData({
                ...formData,
                [parentField]: {
                    ...formData[parentField],
                    consequenceItems: updatedItems,
                },
            });
        } else if (nameParts.length === 2) {
            const [parentField, subfield] = nameParts;
            setFormData({
                ...formData,
                [parentField]: {
                    ...formData[parentField],
                    [subfield]: value,
                },
            });
        } else {
            setFormData({
                ...formData,
                [name]: value,
            });
        }
    }, [formData]);

    const handleAddConsequenceItem = useCallback(() => {
        setFormData((prev) => ({
            ...prev,
            consequences: {
                ...prev.consequences,
                consequenceItems: [
                    ...prev.consequences.consequenceItems,
                    { impactOn: '', description: '' },
                ],
            },
        }));
    }, [formData]);

    const handleAddHazard = useCallback((hazard) => {
        const isAlreadySelected = formData.hazards.some((existingHazard) => existingHazard.id === hazard.id);

        if (isAlreadySelected) {
            const updatedHazards = formData.hazards.filter((existingHazard) => existingHazard.id !== hazard.id);
            setFormData({
                ...formData,
                hazards: updatedHazards,
            });
        } else {
            setFormData({
                ...formData,
                hazards: [...formData.hazards, hazard],
            });
        }
    }, [formData]);

    const handleDeleteHazard = useCallback((index) => {
        const updatedHazards = [...formData.hazards];
        updatedHazards.splice(index, 1);
        setFormData({
            ...formData,
            hazards: updatedHazards,
        });
    }, [formData]);

    const handleDeleteConsequenceItem = useCallback((index) => {
        const updatedConsequenceItems = formData.consequences.consequenceItems.filter((_, i) => i !== index);
        setFormData({
            ...formData,
            consequences: {
                ...formData.consequences,
                consequenceItems: updatedConsequenceItems,
            },
        });
    }, [formData]);

    const handleToggleEffectiveness = useCallback((parentType, type, index, value) => {
        const updatedControls = formData[parentType][type].map((control, i) =>
            i === index ? { ...control, isEffective: value } : control
        );

        setFormData({
            ...formData,
            [parentType]: {
                ...formData[parentType],
                [type]: updatedControls,
            },
        });
    }, [formData]);

    const handleDeleteUnidentifiedPreventiveControl = useCallback((index) => {
        const updatedControls = formData.preventiveControls.unIdentifiedPreventiveControls.filter((_, i) => i !== index);
        setFormData({
            ...formData,
            preventiveControls: {
                ...formData.preventiveControls,
                unIdentifiedPreventiveControls: updatedControls,
            },
        });
    }, [formData]);

    const handleAddUnidentifiedPreventiveControl = useCallback(() => {
        const newOrder = formData.preventiveControls.unIdentifiedPreventiveControls.length + 1;
        setFormData((prev) => ({
            ...prev,
            preventiveControls: {
                ...prev.preventiveControls,
                unIdentifiedPreventiveControls: [
                    ...prev.preventiveControls.unIdentifiedPreventiveControls,
                    { order: newOrder, controlStatement: '', isControlImplemented: false, isEffective: true },
                ],
            },
        }));
    }, [formData]);

    const handleDeleteIdentifiedPreventiveControl = useCallback((index) => {
        const updatedControls = formData.preventiveControls.identifiedPreventiveControls.filter((_, i) => i !== index);
        setFormData({
            ...formData,
            preventiveControls: {
                ...formData.preventiveControls,
                identifiedPreventiveControls: updatedControls,
            },
        });
    }, [formData]);

    const handleAddIdentifiedPreventiveControl = useCallback(() => {
        const newOrder = formData.preventiveControls.identifiedPreventiveControls.length + 1;
        setFormData((prev) => ({
            ...prev,
            preventiveControls: {
                ...prev.preventiveControls,
                identifiedPreventiveControls: [
                    ...prev.preventiveControls.identifiedPreventiveControls,
                    { order: newOrder, controlStatement: '', isControlImplemented: false, isEffective: false },
                ],
            },
        }));
    }, [formData]);

    const handleAddIdentifiedMitigativeControl = useCallback(() => {
        const newOrder = formData.mitigativeControls.identifiedMitigativeControls.length + 1;
        setFormData((prev) => ({
            ...prev,
            mitigativeControls: {
                ...prev.mitigativeControls,
                identifiedMitigativeControls: [
                    ...prev.mitigativeControls.identifiedMitigativeControls,
                    { order: newOrder, controlStatement: '', isControlImplemented: false, isEffective: false },
                ],
            },
        }));
    }, [formData]);

    const handleDeleteIdentifiedMitigativeControl = useCallback((index) => {
        const updatedControls = formData.mitigativeControls.identifiedMitigativeControls.filter((_, i) => i !== index);
        setFormData({
            ...formData,
            mitigativeControls: {
                ...formData.mitigativeControls,
                identifiedMitigativeControls: updatedControls,
            },
        });
    }, [formData]);

    const handleAddUnidentifiedMitigativeControl = useCallback(() => {
        const newOrder = formData.mitigativeControls.unIdentifiedMitigativeControls.length + 1;
        setFormData((prev) => ({
            ...prev,
            mitigativeControls: {
                ...prev.mitigativeControls,
                unIdentifiedMitigativeControls: [
                    ...prev.mitigativeControls.unIdentifiedMitigativeControls,
                    { order: newOrder, controlStatement: '', isControlImplemented: false, isEffective:false },
                ],
            },
        }));
    }, [formData]);

    const handleDeleteUnidentifiedMitigativeControl = useCallback((index) => {
        const updatedControls = formData.mitigativeControls.unIdentifiedMitigativeControls.filter((_, i) => i !== index);
        setFormData({
            ...formData,
            mitigativeControls: {
                ...formData.mitigativeControls,
                unIdentifiedMitigativeControls: updatedControls,
            },
        });
    }, [formData]);

    const handleMainImageUpload = useCallback(async (files) => {
        if (files.length > 0) {
            const latestFile = files[files.length - 1];
            const formData1 = new FormData();
            formData1.append('file', latestFile);
            try {
                const response = await API.post(FILE_URL, formData1, {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                    },
                });

                if (response && response.status === 200) {
                    setFormData((prev) => ({
                        ...prev,
                        images: [...(prev.images || []), response.data.files[0].originalname],
                    }));
                }
            } catch (error) {
                console.error("File upload error: ", error);
            }
        }
    }, []);

    const handleRemoveMainImage = useCallback((index) => {
        const updatedImages = formData.images.filter((_, i) => i !== index);
        setFormData({
            ...formData,
            images: updatedImages,
        });
    }, [formData]);

    const handleDateChange = useCallback((date) => {
        setFormData((prev) => ({
            ...prev,
            incidentDate: date,
        }));
    }, []);

    const handleStageClick = useCallback((index) => {
        setActiveStage(index);
    }, []);

    const handleSaveProgress = useCallback(async () => {
        let dataToSend = {
            incidentType: formData.incidentType,
            incidentCategory: formData.incidentCategory,
            incidentCircumstance: formData.incidentCircumstance,
        };
        let stageKey = '';

        switch (activeStage) {
            case 1: // Precursors
                dataToSend = {
                    ...dataToSend,
                    surfaceType: formData.surfaceType,
                    surfaceCondition: formData.surfaceCondition,
                    lighting: formData.lighting,
                    weatherCondition: formData.weatherCondition,
                    pathways: formData.pathways,
                    precursors: {
                        ...formData.precursors,
                        status: 'incompleted',
                    },
                };
                stageKey = 'precursors';
                break;
            case 2: // Hazardous Conditions
                dataToSend = {
                    ...dataToSend,
                    hazards: formData.hazards,
                    hazardStatus: 'incompleted',
                    hazardousSituationOfTheIncident: formData.hazardousSituationOfTheIncident
                };
                stageKey = 'hazardousConditions';
                break;
            case 3: // Preventive Controls
                dataToSend = {
                    ...dataToSend,
                    preventiveControls: {
                        ...formData.preventiveControls,
                        status: 'incompleted',
                    },
                };
                stageKey = 'preventiveControls';
                break;
            case 4: // Incident Details
                dataToSend = {
                    ...dataToSend,
                    incidentStatus: 'incompleted',
                    images: formData.images,
                    description: formData.description,
                    personnelId: formData.personnelId,
                    environmentId: formData.environmentId,
                    propertyId: formData.propertyId,
                    operationId: formData.operationId,
                    personnelInvolved: formData.personnelInvolved,
                    immediateActionsTaken: formData.immediateActionsTaken
                };
                stageKey = 'incidentDetails';
                break;
            case 5: // Mitigative Controls
                dataToSend = {
                    ...dataToSend,
                    mitigativeControls: {
                        ...formData.mitigativeControls,
                        status: 'incompleted',
                    },
                };
                stageKey = 'mitigativeControls';
                break;
            case 6: // Consequences
                dataToSend = {
                    ...dataToSend,
                    consequences: {
                        ...formData.consequences,
                        status: 'incompleted',
                    },
                };
                stageKey = 'consequences';
                break;
            case 7: // Immmediate Action
                dataToSend = {
                    ...dataToSend,
                    immediateCause: {
                        ...formData.immediateCause,
                        status: 'incompleted',
                    },
                };
                stageKey = 'immediateCause';
                break;
            case 8: // Immmediate Action
                dataToSend = {
                    ...dataToSend,
                    nearTermControlMeasureStatus: 'incompleted'

                };
                stageKey = 'nearTerm';
                break;
            default:
                return;
        }

        try {
            await API.patch(INCIDENT_WITH_ID(formData.id), dataToSend);
            setFormData((prevFormData) => ({
                ...prevFormData,
                ...dataToSend,
            }));
            setStageStatus((prev) => ({ ...prev, [stageKey]: 'incomplete' }));

            Swal.fire({
                toast: true,
                icon: 'success',
                title: 'Saved as Draft',
                text: `${stageKey.replace(/([A-Z])/g, ' $1').trim()} saved as draft.`,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
            });
        } catch (error) {
            console.error('Error saving progress:', error);
            Swal.fire({
                toast: true,
                icon: 'error',
                title: 'Error',
                text: 'Failed to save as draft. Please try again.',
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
            });
        }
    }, [activeStage, formData]);


    const handleSaveFinalize = useCallback(async () => {
        const { isValid, errors } = validateForm();
        if (!isValid) {
            setErrors(errors);
            Swal.fire({
                icon: 'error',
                title: 'Validation Error',
                text: 'Please fill out all required fields before finalizing.',
            });
            return;
        }

        let dataToSend = {
            incidentType: formData.incidentType,
            incidentCategory: formData.incidentCategory,
            incidentCircumstance: formData.incidentCircumstance

        };
        let stageKey = '';

        switch (activeStage) {
            case 1: // Precursors
                dataToSend = {
                    ...dataToSend,
                    surfaceType: formData.surfaceType,
                    surfaceCondition: formData.surfaceCondition,
                    lighting: formData.lighting,
                    weatherCondition: formData.weatherCondition,
                    pathways: formData.pathways,
                    precursors: {
                        ...formData.precursors,
                        status: 'completed',
                    },
                };
                stageKey = 'precursors';
                break;
            case 2: // Hazardous Conditions
                dataToSend = {
                    ...dataToSend,
                    hazards: formData.hazards,
                    hazardStatus: 'completed',
                    hazardousSituationOfTheIncident: formData.hazardousSituationOfTheIncident
                };
                stageKey = 'hazardousConditions';
                break;
            case 3: // Preventive Controls
                dataToSend = {
                    ...dataToSend,
                    preventiveControls: {
                        ...formData.preventiveControls,
                        status: 'completed',
                    },
                };
                stageKey = 'preventiveControls';
                break;
            case 4: // Incident Details
                dataToSend = {
                    ...dataToSend,
                    incidentStatus: 'completed',
                    images: formData.images,
                    description: formData.description,
                    personnelId: formData.personnelId,
                    environmentId: formData.environmentId,
                    propertyId: formData.propertyId,
                    operationId: formData.operationId,
                    personnelInvolved: formData.personnelInvolved,
                    immediateActionsTaken: formData.immediateActionsTaken
                };
                stageKey = 'incidentDetails';
                break;
            case 5: // Mitigative Controls
                dataToSend = {
                    ...dataToSend,
                    mitigativeControls: {
                        ...formData.mitigativeControls,
                        status: 'completed',
                    },
                };
                stageKey = 'mitigativeControls';
                break;
            case 6: // Consequences
                dataToSend = {
                    ...dataToSend,
                    consequences: {
                        ...formData.consequences,
                        status: 'completed',
                    },
                };
                stageKey = 'consequences';
                break;
            case 7: // Immediate Action
                dataToSend = {
                    ...dataToSend,
                    immediateCause: {
                        ...formData.immediateCause,
                        status: 'completed',
                    },
                };
                stageKey = 'immediateCause';
                break;
            case 8: // Immediate Action
                dataToSend = {
                    ...dataToSend,
                    nearTermControlMeasureStatus: 'completed'
                };
                stageKey = 'nearTerm';
                break;
            default:
                return;
        }

        try {
            await API.patch(INCIDENT_WITH_ID(formData.id), dataToSend);
            setFormData((prevFormData) => ({
                ...prevFormData,
                ...dataToSend,
            }));
            setStageStatus((prev) => ({ ...prev, [stageKey]: 'completed' }));

            Swal.fire({
                toast: true,
                icon: 'success',
                title: 'Finalized',
                text: `${stageKey.replace(/([A-Z])/g, ' $1').trim()} finalized successfully.`,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
            });
        } catch (error) {
            console.error('Error finalizing:', error);
        }
    }, [activeStage, formData]);


    useEffect(() => {
        setStageStatus({
            precursors: formData.precursors.status || '',
            hazardousConditions: formData.hazardStatus || '',
            preventiveControls: formData.preventiveControls.status || '',
            incidentDetails: formData.incidentStatus || '',
            mitigativeControls: formData.mitigativeControls.status || '',
            consequences: formData.consequences.status || '',
            immediateCause: formData.immediateCause.status || '',
            nearTerm: formData.nearTermControlMeasureStatus || ''
        });
    }, [formData]);

    const [stageStatus, setStageStatus] = useState({
        precursors: '',
        hazardousConditions: '',
        preventiveControls: '',
        incidentDetails: '',
        mitigativeControls: '',
        consequences: '',
        immediateCause: '',
        nearTerm: ''
    });

    const validateForm = useCallback(() => {
        let errors = {};
        let isValid = true;

        // if (activeStage === 6) {
        //     formData.consequences.consequenceItems.forEach((item, index) => {
        //         if (!item.impactOn) {
        //             isValid = false;
        //             errors[`consequences.${index}.impactOn`] = "Impact On is required";
        //         }
        //         if (!item.description) {
        //             isValid = false;
        //             errors[`consequences.${index}.description`] = "Description is required";
        //         }
        //     });
        // }

        return { isValid, errors };
    }, [activeStage, formData]);

    const getStatusClass = useCallback((status) => {
        if (status === 'completed') return 'completed-stage';
        if (status === 'incompleted') return 'incomplete-stage';
        return 'not-started-stage';
    }, []);

    const stageKeyMap = {
        Precursors: 'precursors',
        'Hazardous Conditions': 'hazardousConditions',
        'Preventative Controls': 'preventiveControls',
        'Incident Details': 'incidentDetails',
        'Mitigative Controls': 'mitigativeControls',
        Consequences: 'consequences',
        'Immediate Action': 'immediateCause',
        'Near-term Control Measures': 'nearTerm'
    };
    const handleCheckboxChange = (parentField, field, index, isChecked) => {
        setFormData((prevFormData) => {
            const updatedControls = [...prevFormData[parentField][field]];
            updatedControls[index].isControlImplemented = isChecked;
            return {
                ...prevFormData,
                [parentField]: {
                    ...prevFormData[parentField],
                    [field]: updatedControls,
                },
            };
        });
    };

    const handleToggleImplementation = (parentField, field, index, isImplemented) => {
        setFormData((prevFormData) => {
            const updatedControls = [...prevFormData[parentField][field]];
            updatedControls[index].isControlImplemented = isImplemented;  // Update based on true/false from radio button
            return {
                ...prevFormData,
                [parentField]: {
                    ...prevFormData[parentField],
                    [field]: updatedControls,
                },
            };
        });
    };


    const handleSubmitFinalize = () => {

        submitReport(formData)

    }
    const handleInputChange = (index, field, value) => {
        const updatedFormDataList = [...formDataList];
        updatedFormDataList[index][field] = value;
        setFormDataList(updatedFormDataList);
    };

    const handleDueDateChange = (index, date) => {
        handleInputChange(index, "dueDate", date);
    };

    const handleUserSelect = (index, selectedOption) => {
        handleInputChange(index, "responsibleId", selectedOption);
    };

    const addMoreFields = () => {
        setFormDataList([
            ...formDataList,
            { controlMeasure: "", dueDate: new Date(), responsibleId: null }
        ]);
    };

    const handleRemoveField = (index) => {
        const updatedFormDataList = formDataList.filter((_, i) => i !== index);
        setFormDataList(updatedFormDataList);
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        const formattedData = formDataList.map((item) => ({
            ...item,
            responsibleId: item.responsibleId ? item.responsibleId.value : null
        }));
        console.log(formattedData);
        // Submit this data to the backend or perform further logic
    };

    const handleToggle = (section, field, value) => {
        setFormData(prevState => ({
            ...prevState,
            [section]: {
                ...prevState[section],
                [field]: value
            }
        }));
    };

    useEffect(() => {
        setEditedTitle(formData.title || ''); // Update editedTitle when applicationDetails changes
    }, [formData.title]);

    // Function to handle editing state
    const handleTitleEdit = () => {
        setIsEditingTitle(true);
    };

    // Save the edited title
    const handleTitleSave = async () => {
        try {
            const response = await API.patch(INCIDENT_WITH_ID(formData.id), { title: editedTitle });
            if (response.status === 204) {
                setIsEditingTitle(false);
                // Optionally, refresh data or notify parent
            }
        } catch (error) {
            console.error('Error updating title:', error);
        }
    };

    // Cancel editing
    const handleTitleCancel = () => {
        setEditedTitle(formData.title || '');
        setIsEditingTitle(false);
    };
    return (
        <>
            <Modal show={show} onHide={handleClose} size="lg">
                <Modal.Header closeButton>
                    <Modal.Title>
                        <h4>Incident Narrative and Impact Detailing</h4>
                        <p>A factual account of the incident and its known consequences, capturing all available information to the extent possible, without drawing conclusions or conducting an investigation.</p>

                    </Modal.Title>

                </Modal.Header>
                <Modal.Body>
                    <div class=" mb-3 fw-bold" style={{ marginTop: '-12px', color: 'grey' }}>As an incident reviewer, edit this overview and complete each of the 8 sections based on your knowledge of the process and the incident. You do not need to finalize all sections if information is unavailable—save your progress and return later to update as needed. If this incident requires further investigation, the investigation team may add additional details during their phase. Once finished, click the 'Complete and Submit Review' button; no further changes will be allowed afterward</div>

                    <div className="obs-section mb-2 p-4 pb-2">
                        {/* <h4 className='fw-bold'>Overview</h4> */}

                        <div className="row mb-3">
                            <div className="col-md-6">
                                <p className="obs-title">Incident ID</p>
                                <p className="obs-content mb-3">{formData.maskId || ''}</p>
                            </div>
                            <div className="col-md-6">
                                <p className="obs-title">Incident Title</p>
                                {!isEditingTitle ? (
                                    <div className="d-flex align-items-center">
                                        <p className="obs-content mb-0">{editedTitle}</p>
                                        <i
                                            className="pi pi-pencil ms-2"
                                            style={{ cursor: 'pointer' }}
                                            onClick={handleTitleEdit}
                                            title="Edit Title"
                                        ></i>
                                    </div>
                                ) : (
                                    <InputGroup className="mb-3">
                                        <FormControl
                                            value={editedTitle}
                                            style={{ height: 47 }}
                                            onChange={(e) => setEditedTitle(e.target.value)}
                                            placeholder="Enter title"
                                        />
                                        <Button variant="success" onClick={handleTitleSave}>
                                            <i className="pi pi-check"></i>
                                        </Button>
                                        <Button variant="secondary" onClick={handleTitleCancel}>
                                            <i className="pi pi-times"></i>
                                        </Button>
                                    </InputGroup>
                                )}
                            </div>
                            <div className="col-md-6">
                                <p className="obs-title">Incident Date & Time</p>
                                <p className="obs-content">
                                    {moment(formData.incidentDate || '').format("DD-MM-YYYY - HH:MM")}
                                </p>
                            </div>

                            <div className="col-md-6">
                                <p className="obs-title">Location</p>
                                {formData.isCustomLocation ?
                                    <p className="obs-content">
                                        {formData.customLocation}
                                    </p>
                                    :
                                    <p className="obs-content">
                                        {formData.locationOne?.name && (
                                            <>
                                                {formData.locationOne.name}
                                                {formData.locationTwo?.name && ' > '}
                                            </>
                                        )}
                                        {formData.locationTwo?.name && (
                                            <>
                                                {formData.locationTwo.name}
                                                {formData.locationThree?.name && ' > '}
                                            </>
                                        )}
                                        {formData.locationThree?.name && (
                                            <>
                                                {formData.locationThree.name}
                                                {formData.locationFour?.name && ' > '}
                                            </>
                                        )}
                                        {formData.locationFour?.name && formData.locationFour.name}
                                    </p>
                                }
                            </div>
                        </div>
                    </div>

                    {activeStage > 0 && (
                        <div>



                            <Overview
                                formData={formData}
                                incidentType={incidentType}
                                category={category}
                                circumstance={circumstance}
                                handleSelectChange={handleSelectChange}
                                disable={false}
                            />


                            <HeadStepper
                                stages={stages}
                                stageKeyMap={stageKeyMap}
                                stageStatus={stageStatus}
                                activeStage={activeStage}
                                handleStageClick={handleStageClick}
                                getStatusClass={getStatusClass}
                            />

                            <hr />

                            <div className="mt-4">
                                <h4 className="fw-bold mt-3 mb-1">{stages[activeStage - 1]}</h4>

                                {activeStage === 1 && (
                                    <StageOne
                                        formData={formData}
                                        handleSelectChange={handleSelectChange}
                                        surfaceType={surfaceType}
                                        surfaceCondition={surfaceCondition}
                                        lighting={lighting}
                                        weatherCondition={weatherCondition}
                                        pathways={pathways}
                                        errors={errors}
                                        handleChange={handleChange}
                                        disable={false}
                                    />
                                )}

                                {activeStage === 2 && (
                                    <StageTwo formData={formData} handleAddHazard={handleAddHazard} hazards={hazards} handleDeleteHazard={handleDeleteHazard} handleChange={handleChange} activeTabIndex={activeTabIndex} setActiveTabIndex={setActiveTabIndex} errors={errors}
                                        disable={false} />
                                )}

                                {activeStage === 3 && (
                                    <StageThree
                                        formData={formData}
                                        handleAddIdentifiedPreventiveControl={handleAddIdentifiedPreventiveControl}
                                        handleDeleteIdentifiedPreventiveControl={handleDeleteIdentifiedPreventiveControl}
                                        handleToggle={handleToggle}
                                        errors={errors}
                                        handleChange={handleChange}
                                        handleToggleImplementation={handleToggleImplementation}
                                        handleDeleteUnidentifiedPreventiveControl={handleDeleteUnidentifiedPreventiveControl}
                                        handleToggleEffectiveness={handleToggleEffectiveness}
                                        handleAddUnidentifiedPreventiveControl={handleAddUnidentifiedPreventiveControl}
                                        disable={false}
                                    />
                                )}

                                {activeStage === 4 && (
                                    <StageFour
                                        formData={formData}
                                        handleSelectChange={handleSelectChange}
                                        personnel={personnel}
                                        environment={environment}
                                        propertyEquipment={propertyEquipment}
                                        operation={operation}
                                        handleChange={handleChange}
                                        handleMainImageUpload={handleMainImageUpload}
                                        handleRemoveMainImage={handleRemoveMainImage}
                                        errors={errors}
                                        disable={false}
                                    />
                                )}

                                {activeStage === 5 && (
                                    <StageFive
                                        formData={formData}
                                        handleAddIdentifiedMitigativeControl={handleAddIdentifiedMitigativeControl}
                                        handleDeleteIdentifiedMitigativeControl={handleDeleteIdentifiedMitigativeControl}
                                        handleToggle={handleToggle}
                                        errors={errors}
                                        handleChange={handleChange}
                                        handleToggleImplementation={handleToggleImplementation}
                                        handleToggleEffectiveness={handleToggleEffectiveness}
                                        handleDeleteUnidentifiedMitigativeControl={handleDeleteUnidentifiedMitigativeControl}
                                        handleAddUnidentifiedMitigativeControl={handleAddUnidentifiedMitigativeControl}
                                        disable={false}
                                    />
                                )}

                                {activeStage === 6 && (
                                    <StageSix
                                        formData={formData}
                                        setFormData={setFormData}
                                        impacton={impacton}
                                        fetchRelatedQuestions={fetchRelatedQuestions}
                                        handleDeleteConsequenceItem={handleDeleteConsequenceItem}
                                        handleAddConsequenceItem={handleAddConsequenceItem}
                                        handleChange={handleChange}
                                        errors={errors}
                                        disable={false}
                                    />
                                )}

                                {activeStage === 7 && (
                                    <StageSeven formData={formData} handleChange={handleChange} errors={errors} disable={false} />
                                )}

                                {activeStage === 8 && (
                                    <StageEight formData={formData} disable={false} />
                                )}



                            </div>
                        </div>
                    )}
                </Modal.Body>

                <Modal.Footer className='d-flex justify-content-between'>
                    <div className=''>
                        <Button
                            variant="secondary"
                            onClick={handleSaveProgress}
                            style={{ backgroundColor: '#ffa500', borderColor: '#ffa500', color: '#fff', marginRight: 10 }}
                        >
                            Save Section Progress
                        </Button>
                        <Button
                            severity="secondary"
                            onClick={handleSaveFinalize}
                            style={{ backgroundColor: '#28a745', borderColor: '#28a745', color: '#fff' }}
                        >
                            Mark Section as Completed
                        </Button>
                    </div>
                    <div className="">


                        <Button
                            severity="primary"
                            onClick={handleSubmitFinalize}

                        >
                            Finalize & Submit Incident
                        </Button>
                    </div>
                </Modal.Footer>
            </Modal >
        </>
    );
};

export default ReviewIncident;
