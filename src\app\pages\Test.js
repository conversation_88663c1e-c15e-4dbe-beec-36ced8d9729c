import React, { useState } from 'react';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
// PrimeReact
import { Tab<PERSON>iew, Tab<PERSON>anel } from 'primereact/tabview';
import { <PERSON><PERSON> } from 'primereact/button';
// (Optional) For Font Awesome icons (if you're using them)
import 'font-awesome/css/font-awesome.min.css';
import 'primereact/resources/themes/lara-light-indigo/theme.css';
import 'primereact/resources/primereact.min.css';
import 'primeicons/primeicons.css';
import 'font-awesome/css/font-awesome.min.css';
// Import TextWidget that uses JoditEditor (converted to function component)
import TextWidget from '../knowledge/component/TextWidget';

// ---------------------------------------------------------------------
// Example Data Arrays
// ---------------------------------------------------------------------
const communicationWidgets = [
  { id: 'comm-0', title: 'Text', icon: 'fa fa-file-text-o' },
  { id: 'comm-1', title: 'Image', icon: 'fa fa-picture-o' },
  { id: 'comm-2', title: 'Youtube', icon: 'fa fa-youtube-play' },
  { id: 'comm-3', title: 'Videos', icon: 'fa fa-film' },
  { id: 'comm-4', title: 'Web Link', icon: 'fa fa-link' },
  { id: 'comm-5', title: 'Audio', icon: 'fa fa-volume-up' },
  { id: 'comm-6', title: 'PDF', icon: 'fa fa-file-pdf-o' },
  { id: 'comm-7', title: 'Embed Code', icon: 'fa fa-code' }
];

const feedbackWidgets = [
  { id: 'feed-0', title: 'IMCQ', icon: 'fa fa-bar-chart' },
  { id: 'feed-1', title: 'Textbox', icon: 'fa fa-file-text' },
  { id: 'feed-2', title: 'Image', icon: 'fa fa-camera-retro' },
  { id: 'feed-3', title: 'Videos', icon: 'fa fa-video-camera' },
  { id: 'feed-4', title: 'Audio', icon: 'fa fa-microphone' },
  { id: 'feed-5', title: 'Option', icon: 'fa fa-list-alt' },
  { id: 'feed-6', title: 'Sign', icon: 'fa fa-pencil-square-o' },
  { id: 'feed-7', title: 'Check Point', icon: 'fa fa-check-square-o' },
  { id: 'feed-8', title: 'Multimedia', icon: 'fa fa-upload' },
  { id: 'feed-9', title: 'Ques Bank', icon: 'fa fa-question-circle-o' }
];

// ---------------------------------------------------------------------
// Helper functions for react-beautiful-dnd
// ---------------------------------------------------------------------
const reorder = (list, startIndex, endIndex) => {
  const result = Array.from(list);
  const [removed] = result.splice(startIndex, 1);
  result.splice(endIndex, 0, removed);
  return result;
};

const copy = (source, destination, droppableSource, droppableDestination) => {
  const destClone = Array.from(destination);
  const item = source[droppableSource.index];

  destClone.splice(droppableDestination.index, 0, {
    ...item,
    // Give each copy a unique ID and an empty data object
    id: `${item.id}-${Date.now()}`,
    data: {}
  });

  return destClone;
};

// ---------------------------------------------------------------------
// A small custom header component for TabPanel to place icon on top
// ---------------------------------------------------------------------
function CustomTabHeader({ icon, label }) {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }} className="text-black">
      <i className={icon} style={{ marginBottom: 5 }} />
      <span>{label}</span>
    </div>
  );
}

// ---------------------------------------------------------------------
// The Main Component
// ---------------------------------------------------------------------
function DragDropWidget() {
  // Track which tab is active
  const [activeIndex, setActiveIndex] = useState(0);
  // Items that have been dropped along with their internal data
  const [droppedItems, setDroppedItems] = useState([]);
  // Track which dropped widget is “active” (to show/hide form)
  const [activeWidgetIndex, setActiveWidgetIndex] = useState(null);

  // Called when a drag event ends
  const onDragEnd = (result) => {
    const { source, destination } = result;
    if (!destination) return;

    // Reorder items within the drop zone
    if (source.droppableId === 'droppedItems' && destination.droppableId === 'droppedItems') {
      setDroppedItems((prev) => reorder(prev, source.index, destination.index));
      return;
    }

    // Drag from Communication tab to drop zone
    if (source.droppableId === 'communication' && destination.droppableId === 'droppedItems') {
      setDroppedItems((prev) => copy(communicationWidgets, prev, source, destination));
      return;
    }

    // Drag from Feedback tab to drop zone
    if (source.droppableId === 'feedback' && destination.droppableId === 'droppedItems') {
      setDroppedItems((prev) => copy(feedbackWidgets, prev, source, destination));
      return;
    }
  };

  // Remove an item from droppedItems
  const handleRemove = (index) => {
    setDroppedItems((prev) => {
      const newList = [...prev];
      newList.splice(index, 1);
      return newList;
    });
  };

  // Toggle the expanded form for a widget
  const handleWidgetClick = (index) => {
    setActiveWidgetIndex((prev) => (prev === index ? null : index));
  };

  // Update the data for a given dropped widget at index
  const updateWidgetData = (index, newData) => {
    setDroppedItems((prev) => {
      const updated = [...prev];
      updated[index] = { ...updated[index], data: newData };
      return updated;
    });
  };

  // Conditionally render form for a widget based on its type
  const renderForm = (item, index) => {
    if (item.title.trim() === 'Text') {
      return (
        <TextWidget
          data={item.data}
          onChangeData={(newData) => updateWidgetData(index, newData)}
        />
      );
    }
    // You can add cases for other widget types here...
    return (
      <div style={{ marginTop: '10px', backgroundColor: '#fafafa', padding: '1rem' }}>
        <p>
          <strong>{item.title}</strong> details here...
        </p>
      </div>
    );
  };

  // Function to save the droppedItems data (e.g., send to API)
  const handleSave = () => {
    console.log("Saving dropped items data:", droppedItems);
    // Example: API.saveDroppedItems(droppedItems);
  };

  return (
    <DragDropContext onDragEnd={onDragEnd}>
      <div style={{ padding: '1rem' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          {/* Left: TabView with two TabPanels (Sticky) */}
          <div className="col-2" style={{
            position: 'fixed',
            width: '16.66%', // roughly equivalent to col-2 in a 12-column layout
            zIndex: 1000
          }}>
            <TabView className="my-tabs" activeIndex={activeIndex} onTabChange={(e) => setActiveIndex(e.index)}>
              <TabPanel header={<CustomTabHeader icon="fa fa-comments fa-2x" label="Communication" />}>
                <Droppable droppableId="communication" isDropDisabled={true}>
                  {(provided) => (
                    <div
                      ref={provided.innerRef}
                      {...provided.droppableProps}
                      style={{ minHeight: 300, borderRadius: 8 }}
                    >
                      {communicationWidgets.map((widget, index) => (
                        <Draggable key={widget.id} draggableId={widget.id} index={index}>
                          {(provided) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                              style={{
                                userSelect: 'none',
                                marginBottom: '8px',
                                paddingTop: 10,
                                borderRadius: 4,
                                display: 'flex',
                                alignItems: 'center',
                                ...provided.draggableProps.style
                              }}
                            >
                              <i
                                className={widget.icon}
                                style={{
                                  marginRight: 8,
                                  padding: 10,
                                  background: "#F5F0EA",
                                  borderRadius: 5,
                                  boxShadow: "0px 0px 3px 0px #c6c6c6",
                                  width: 35,
                                  height: 35,
                                  textAlign: 'center'
                                }}
                              />
                              <span className="widget-name">{widget.title}</span>
                            </div>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              </TabPanel>

              <TabPanel header={<CustomTabHeader icon="fa fa-comment fa-2x" label="Feedback" />}>
                <Droppable droppableId="feedback" isDropDisabled={true}>
                  {(provided) => (
                    <div
                      ref={provided.innerRef}
                      {...provided.droppableProps}
                      style={{ minHeight: 300, borderRadius: 4 }}
                    >
                      {feedbackWidgets.map((widget, index) => (
                        <Draggable key={widget.id} draggableId={widget.id} index={index}>
                          {(provided) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                              style={{
                                userSelect: 'none',
                                paddingTop: 10,
                                marginBottom: '8px',
                                borderRadius: 4,
                                display: 'flex',
                                alignItems: 'center',
                                ...provided.draggableProps.style
                              }}
                            >
                              <i
                                className={widget.icon}
                                style={{
                                  marginRight: 8,
                                  padding: 10,
                                  background: "#F5F0EA",
                                  borderRadius: 5,
                                  boxShadow: "0px 0px 3px 0px #c6c6c6",
                                  width: 35,
                                  height: 35,
                                  textAlign: 'center'
                                }}
                              />
                              <span className="widget-name">{widget.title}</span>
                            </div>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              </TabPanel>
            </TabView>
          </div>

          {/* Right: Drop Zone */}
          <div className="col-10 ps-3" style={{
            marginLeft: '20%', // adjust space for fixed left column
            paddingLeft: '1rem',
            width: '80%',
            marginBottom: 40

          }}>
            <Droppable droppableId="droppedItems">
              {(provided) => (
                <div
                  ref={provided.innerRef}
                  {...provided.droppableProps}
                  style={{
                    padding: '1rem',
                    minHeight: 500,
                    border: '2px dashed #c6c6c6',
                    borderRadius: 12,
                    display: droppedItems.length === 0 ? 'flex' : 'block',
                    justifyContent: droppedItems.length === 0 ? 'center' : 'initial',
                    alignItems: droppedItems.length === 0 ? 'center' : 'initial'
                  }}
                >
                  {droppedItems.length === 0 && (
                    <div className="d-flex flex-column justify-center align-items-center">
                      <h3 className="fw-bold">Drop Here</h3>
                      <p style={{ color: '#888' }}>Drag and drop widgets here</p>
                    </div>
                  )}

                  {droppedItems.map((item, index) => (
                    <Draggable key={item.id} draggableId={item.id} index={index}>
                      {(provided) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          {...provided.dragHandleProps}
                          style={{
                            userSelect: 'none',
                            marginBottom: '8px',
                            backgroundColor: '#fff',
                            borderRadius: 4,
                            position: 'relative',
                            ...provided.draggableProps.style
                          }}
                        >
                          <div className="card" style={{ border: '1px solid #ccc', borderRadius: 4 }}>
                            <div
                              className="card-header"
                              style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', backgroundColor: '#f8f9fa', padding: '0.5rem 1rem' }}
                            >
                              <div style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }} onClick={() => handleWidgetClick(index)}>
                                <i className={item.icon} style={{ marginRight: 8 }} />
                                <span>{item.title}</span>
                              </div>
                              <i
                                className="fa fa-trash"
                                style={{
                                  cursor: 'pointer',
                                  color: '#dc3545'
                                }}
                                onClick={() => handleRemove(index)}
                              />
                            </div>
                            {/* {activeWidgetIndex === index && ( */}
                              <div className="card-body p-0">
                                {renderForm(item, index)}
                              </div>
                            {/* )} */}
                          </div>
                        </div>
                      )}
                    </Draggable>
                  ))}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </div>
        </div>

        {/* Save Button */}
        <div style={{
          marginTop: '1rem', textAlign: 'right', position: "fixed", left: 0, right: 0,
          background: "#fff",
          bottom: 0,
          padding: 10,
          zIndex: 9,
        }}>
          <Button label="Save" onClick={handleSave} />
        </div>
      </div>
    </DragDropContext>
  );
}

export default DragDropWidget;
