import React, { useEffect, useState } from "react";
import ToolBox from "./components/TopicalTool";
import FormContainer from "./components/FormContainer";
import {
    STEP_BY_ID,
    DOCUMENTS_WITH_ID_URL,
    DOC_REVIEWER_SET_BY_CREATOR
} from "../constants";
import API from "../services/API";
import { singlePopup, secondaryPopup } from "./../notifications/Swal";
import { useLocation, useHistory } from "react-router-dom/cjs/react-router-dom.min";
import DocFormContainer from "./components/DocFormContainer";
import TopicalFormContainer from "./components/TopicalFormContainer";
const TopicalCuration = (props) => {

    const location = useLocation();
    console.log(props)
    const history = useHistory()
    const [isLoading, setIsLoading] = useState(true);
    const [checklistData, setChecklistData] = useState({ name: "", value: {} });
    const [datas,setDatas] =useState([])


    const myForm = async (form, type) => {
        props.onSave(form)

    };
    const updateForm = async (callback) => {

        callback(props.data);

    };
    const goBack = () => {
        history.goBack();
    }
    return (
        <div className="row">
            <div className="col-12">
                <div className="card">
                    <div className="card-body p-0" >
                        <div className="row">

                            <div
                                className="col-md-2 pr-0">
                                <ToolBox />

                            </div>
                            <div className="col-md-10 pl-0">
                                <TopicalFormContainer
                                    data={[]}
                                    loader={false}
                                    debug={false}
                                    updateOnMount={true}
                                    updateForm={updateForm}
                                    onSave={myForm}
                                    goBack={goBack}
                                />
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default TopicalCuration;
