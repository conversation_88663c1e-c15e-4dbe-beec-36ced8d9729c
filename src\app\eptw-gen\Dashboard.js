import React, { useState, useEffect } from 'react';
import Tabs from '@mui/material/Tabs';
import MTab from '@mui/material/Tab';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import PropTypes from 'prop-types';
import CircularProgress from '@mui/material/CircularProgress';
import AppSwitch from '../pages/AppSwitch';
// import Invest from './Invest';
// import Investigation from './Investigation';
import Action from './Action';
import API from '../services/API';
import { ASSIGNED_ACTION_URL, INCIDENT, INVERSTIGATION, PERMIT_REPORTS } from '../constants';
import Permit from './Permit';
import ArchivedPermit from './ArchivedPermit';
const customFontStyle = {
    fontFamily: 'Lato, sans-serif',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
};

function CustomTabPanel(props) {
    const { children, value, tabValue, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== tabValue}
            id={`incident-tabpanel-${tabValue}`}
            aria-labelledby={`incident-tab-${tabValue}`}
            {...other}
        >
            {value === tabValue && <Box bgcolor={'#fff'}>{children}</Box>}
        </div>
    );
}

CustomTabPanel.propTypes = {
    children: PropTypes.node,
    tabValue: PropTypes.string.isRequired,
    value: PropTypes.string.isRequired,
};

const Dashboard = () => {
    const [value, setValue] = useState('MY ACTIONS');

    const [actions, setActions] = useState([]);
    const [incidents, setIncidents] = useState([]);
    const [investigations, setInvestigations] = useState([]);

    const [loadingActions, setLoadingActions] = useState(true);
    const [loadingIncidents, setLoadingIncidents] = useState(true);
    const [loadingInvestigations, setLoadingInvestigations] = useState(true);

    const [actionCount, setActionCount] = useState(0);
    const [incidentCount, setIncidentCount] = useState(0);
    const [investigationCount, setInvestigationCount] = useState(0);

    const [archivedPermits, setArchivedPermits] = useState([]);
    const [archivedCount, setArchivedCount] = useState(0);
    const [loadingArchived, setLoadingArchived] = useState(true);

    // Pagination states specifically for archived permits
    const [archivedPage, setArchivedPage] = useState(1);   // "page" to fetch
    const [archivedRows, setArchivedRows] = useState(10);  // how many rows per page

    const TABS = {
        ACTIONS: 'MY ACTIONS',
        DASHBOARD: 'DASHBOARD',
        ARCHIVED: 'ARCHIVED'
    };

    useEffect(() => {
        fetchActions();
        fetchIncident();
        // fetchArchivedPermits();
    }, []);

    const fetchActions = async () => {
        setLoadingActions(true); // Start loading

        const uriString = {
            include: [{ relation: "submittedBy" }]
        };
        const url = `${ASSIGNED_ACTION_URL('EPTW-GEN')}?filter=${encodeURIComponent(
            JSON.stringify(uriString)
        )}`;

        try {
            const response = await API.get(url);
            if (response.status === 200) {
                setActions(response.data);
                setActionCount(response.data.length);
            } else {
                console.error('Unexpected response status:', response.status);
            }
        } catch (error) {
            console.error('Error fetching actions:', error);
        } finally {
            setLoadingActions(false); // Stop loading regardless of success or error
        }
    };


    const fetchIncident = async () => {
        setLoadingIncidents(true);
        try {
            const uriString = {
                include: [
                    { relation: "locationOne" },
                    { relation: "locationTwo" },
                    { relation: "locationThree" },
                    { relation: "locationFour" },
                    { relation: "locationFive" },
                    { relation: "locationSix" },
                    { relation: "applicant" },
                    { relation: "assessor" },
                    { relation: "approver" },
                    { relation: "reviewer" },
                ]
            };
            const url = `${PERMIT_REPORTS}?filter=${encodeURIComponent(
                JSON.stringify(uriString)
            )}`;

            const response = await API.get(url);
            if (response.status === 200) {
                setIncidents(response.data);
                setIncidentCount(response.data.length);
            } else {
                console.error('Unexpected response status:', response.status);
            }
        } catch (error) {
            console.error('Error fetching incidents:', error);
        } finally {
            setLoadingIncidents(false); // Stop loading regardless of success or error
        }
    };


    // ========== Fetch Archived Permits (NEW) ==========
    const fetchArchivedPermits = async (page, limit) => {
        setLoadingArchived(true);

        const uriString = {
            include: [
                { relation: 'locationOne' },
                { relation: 'locationTwo' },
                { relation: 'locationThree' },
                { relation: 'locationFour' },
                { relation: 'locationFive' },
                { relation: 'locationSix' },
                { relation: 'applicant' },
                { relation: 'assessor' },
                { relation: 'approver' },
                { relation: 'reviewer' },
            ]
        };

        // Example: GET /archived-permits?page=2&limit=10&filter=<encodedFilter>
        // Convert page & limit to your server's expected parameters
        const archivedUrl = `/archived-permits?page=${page}&limit=${limit}&filter=${encodeURIComponent(
            JSON.stringify(uriString)
        )}`;

        try {
            const response = await API.get(archivedUrl);
            // Suppose your backend returns: { data: [..], total: 42 }
            const { data, total } = response.data;
            setArchivedPermits(data || []);
            setArchivedCount(total || 0);
        } catch (error) {
            console.error('Error fetching archived permits:', error);
        } finally {
            setLoadingArchived(false);
        }
    };

    const handleChange = (event, newValue) => {
        setValue(newValue);
    };

    const handleFilterUpdate = (count, type) => {
        if (type === 'actions') {
            setActionCount(count);
        } else if (type === 'incidents') {
            setIncidentCount(count);
        } else if (type === 'archived') {
            setArchivedCount(count);
        }
    };
    useEffect(() => {
        fetchArchivedPermits(archivedPage, archivedRows);
    }, [archivedPage, archivedRows]);

    const handleArchivedPageChange = (newPage, newRows) => {
        setArchivedPage(newPage);
        setArchivedRows(newRows);
    };
    return (
        <>
            <AppSwitch value={{ label: 'ePermit to Work', value: 'ePermit to Work' }} />

            <Tabs value={value} onChange={handleChange} aria-label="incident report table" className="risk">
                <MTab
                    label={
                        <Typography variant="body1" style={customFontStyle}>
                            My Actions <span className="headerCount">{actionCount}</span>
                        </Typography>
                    }
                    value={TABS.ACTIONS}
                />

                <MTab
                    label={
                        <Typography variant="body1" style={customFontStyle}>
                            All Permits <span className="headerCount">{incidentCount}</span>
                        </Typography>
                    }
                    value={TABS.DASHBOARD}
                />

                <MTab
                    label={
                        <Typography variant="body1" style={customFontStyle}>
                            Archived Permits <span className="headerCount">{archivedCount}</span>
                        </Typography>
                    }
                    value={TABS.ARCHIVED}
                />


            </Tabs>

            <CustomTabPanel value={value} tabValue={TABS.ACTIONS}>
                {loadingActions ? (
                    <Box display="flex" justifyContent="center" alignItems="center" height="100%">
                        <CircularProgress />
                    </Box>
                ) : (
                    <Action action={actions} onFilterUpdate={(count) => handleFilterUpdate(count, 'actions')} />
                )}
            </CustomTabPanel>

            <CustomTabPanel value={value} tabValue={TABS.DASHBOARD}>
                {loadingIncidents ? (
                    <Box display="flex" justifyContent="center" alignItems="center" height="100%">
                        <CircularProgress />
                    </Box>
                ) : (
                    <Permit data={incidents} onFilterUpdate={(count) => handleFilterUpdate(count, 'incidents')} from={'Non'} />
                )}
            </CustomTabPanel>

            <CustomTabPanel value={value} tabValue={TABS.ARCHIVED}>
                {loadingArchived ? (
                    <Box display="flex" justifyContent="center" alignItems="center" height="100%">
                        <CircularProgress />
                    </Box>
                ) : (
                    <ArchivedPermit
                        data={archivedPermits}
                        totalRecords={archivedCount}
                        currentPage={archivedPage}
                        rows={archivedRows}
                        onFilterUpdate={(count) => handleFilterUpdate(count, 'archived')}
                        onPageChange={handleArchivedPageChange}
                        from="Non"
                    />
                )}
            </CustomTabPanel>


        </>
    );
};

export default Dashboard;
