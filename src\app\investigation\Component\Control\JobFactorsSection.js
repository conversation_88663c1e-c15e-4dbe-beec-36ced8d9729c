// JobFactorsSection.js

import React from 'react';
import { Row, Col, Form, Button } from 'react-bootstrap';
import Select from 'react-select';
import { RadioButton } from 'primereact/radiobutton';

const JobFactorsSection = ({
  control,
  controlIndex,
  section,
  getControlIdPrefix,
  incJobFactorOptions,
  incFatorOptions,
  incFallibilityOptions,
  handleSelectChange,
  handleJobFactorChange,
  handleAddJobFactor,
  handleDeleteJobFactor,
  unincJobFactorOptions
}) => {

  const controlIdPrefix = getControlIdPrefix(section);
  return (
    <>
      {/* Job Factors */}
      <Col>
        <h6 className="fw-bold">Job Factors</h6>
        {(section === 'unidentifiedPreventiveControls' || section === 'unidentifiedMitigativeControls') ? (
          <p className="fst-italic">
            Were there any job factors that prevented the organization to not consider these controls during planning or risk assessment?
          </p>
        ) : (
          <p className="fst-italic">
            Conditions related to people, equipment, environment, procedures, or supervision that made the job harder or
            influenced the failure or absence of control measures. These factors increase the likelihood of errors,
            non-compliances, or both the breakdown or absence of safety controls, contributing to incidents.
          </p>
        )}

      </Col>
      {control.jobFactors.length > 0 ? (
        control.jobFactors.map((jobFactor, jobFactorIndex) => (
          <div key={jobFactorIndex} className="mb-4 p-3 boxShadow">
            <Row className="mb-3">
              <Col>
                <h6 className="fw-bold mb-2">  JF-{controlIdPrefix}-{controlIndex + 1}.{jobFactorIndex + 1}</h6>
              </Col>
              <Col className="text-end">
                <span
                  className="pi pi-trash"
                  style={{ cursor: 'pointer', color: 'red' }}
                  onClick={() => handleDeleteJobFactor(section, controlIndex, jobFactorIndex)}
                  title="Delete Job Factor"
                />
              </Col>
            </Row>
            <Row className="mb-3">
              <Col md="6">
                <Form.Group>
                  <Form.Label className="fw-bold">Factor</Form.Label>
                  <Select
                    value={incJobFactorOptions.find((option) => option.label === jobFactor.jobFactor)}
                    onChange={(selectedOption) =>
                      handleSelectChange(section, controlIndex, jobFactorIndex, 'jobFactor', selectedOption)
                    }
                    options={incJobFactorOptions}
                  />
                </Form.Group>
                {jobFactor.jobFactor && (
                  <p class="fst-italic">{incJobFactorOptions.find((option) => option.label === jobFactor.jobFactor)?.desc}</p>
                )}
              </Col>
              <Col md="6">
                <Form.Group>
                  <Form.Label className="fw-bold">Sub Factor</Form.Label>
                  <Select
                    value={incFatorOptions.find((option) => option.label === jobFactor.contributorFactor)}
                    onChange={(selectedOption) =>
                      handleSelectChange(section, controlIndex, jobFactorIndex, 'contributorFactor', selectedOption)
                    }
                    options={incFatorOptions}
                  />
                </Form.Group>
                {jobFactor.contributorFactor && (
                  <p class="fst-italic">{incFatorOptions.find((option) => option.label === jobFactor.contributorFactor)?.desc}</p>
                )}
              </Col>
            </Row>
            {jobFactor.jobFactor === 'People (Human Factors)' && (
              <Row className="mt-4 mb-4">
                <Col md="6">
                  <Form.Group>
                    <Form.Label className="fw-bold">Fallibility</Form.Label>
                    <p class="fst-italic">
                      Fallibility refers to the tendency or ability to make mistakes or be wrong. It highlights the
                      inherent human limitation where errors in judgment, decision-making, or action can occur.
                    </p>
                    <Select
                      value={incFallibilityOptions.find((option) => option.label === jobFactor.fallibility)}
                      onChange={(selectedOption) =>
                        handleSelectChange(section, controlIndex, jobFactorIndex, 'fallibility', selectedOption)
                      }
                      options={incFallibilityOptions}
                    />
                  </Form.Group>
                  {jobFactor.fallibility && (
                    <p class="fst-italic">{incFallibilityOptions.find((option) => option.label === jobFactor.fallibility)?.desc}</p>
                  )}
                </Col>
                <Col md="6" className="d-flex flex-column justify-content-start align-items-center">
                  <Form.Label className="fw-bold">Is this a Routine / Repetitive issue?</Form.Label>
                  <div className="d-flex justify-content-start">
                    <label
                      className={`d-flex align-items-center me-1 boxEff ${jobFactor.isRoutine ? 'boxUnEffective' : ''
                        }`}
                      style={{
                        backgroundColor: jobFactor.isRoutine ? '#ff07073d' : 'white',
                        borderColor: jobFactor.isRoutine ? 'rgb(248 0 0)' : '#ccc',
                        cursor: 'pointer',
                      }}
                      onClick={() =>
                        handleJobFactorChange(section, controlIndex, jobFactorIndex, 'isRoutine', true)
                      }
                    >
                      <RadioButton
                        value="Yes"
                        name={`isRoutine-${section}-${controlIndex}-${jobFactorIndex}`}
                        checked={jobFactor.isRoutine === true}
                        onChange={() =>
                          handleJobFactorChange(section, controlIndex, jobFactorIndex, 'isRoutine', true)
                        }
                        style={{ display: 'none' }}
                      />
                      <span style={{ color: jobFactor.isRoutine ? 'red' : 'black' }}>Yes</span>
                    </label>
                    <label
                      className={`d-flex align-items-center boxEff ${!jobFactor.isRoutine ? 'boxEffective' : ''}`}
                      style={{
                        backgroundColor: !jobFactor.isRoutine ? 'lightgreen' : 'white',
                        borderColor: !jobFactor.isRoutine ? 'green' : '#ccc',
                        cursor: 'pointer',
                      }}
                      onClick={() =>
                        handleJobFactorChange(section, controlIndex, jobFactorIndex, 'isRoutine', false)
                      }
                    >
                      <RadioButton
                        value="No"
                        name={`isRoutine-${section}-${controlIndex}-${jobFactorIndex}`}
                        checked={jobFactor.isRoutine === false}
                        onChange={() =>
                          handleJobFactorChange(section, controlIndex, jobFactorIndex, 'isRoutine', false)
                        }
                        style={{ display: 'none' }}
                      />
                      <span style={{ color: !jobFactor.isRoutine ? 'green' : 'black' }}>No</span>
                    </label>
                  </div>
                </Col>
              </Row>
            )}
            <Row className="mb-3">

              <Col>
                <Form.Group>
                  <Form.Label className="fw-bold">
                    Is this a significant contributory factor to the incident?
                  </Form.Label>
                  <div className="d-flex justify-content-start">

                    <label
                      className={`d-flex align-items-center boxEff ${jobFactor.extentOfContribution === 'Not Significant' ? 'boxEffective' : ''
                        }`}
                      style={{
                        backgroundColor: jobFactor.extentOfContribution === 'Not Significant' ? 'lightgreen' : 'white',
                        borderColor: jobFactor.extentOfContribution === 'Not Significant' ? 'green' : '#ccc',
                        cursor: 'pointer',
                        padding: '5px 10px',
                        borderRadius: '4px',
                      }}
                      onClick={() =>
                        handleJobFactorChange(
                          section,
                          controlIndex,
                          jobFactorIndex,
                          'extentOfContribution',
                          'Not Significant'
                        )
                      }
                    >
                      <RadioButton
                        value="Not Significant"
                        name={`extentOfContribution-${section}-${controlIndex}-${jobFactorIndex}`}
                        checked={jobFactor.extentOfContribution === 'Not Significant'}
                        onChange={() =>
                          handleJobFactorChange(
                            section,
                            controlIndex,
                            jobFactorIndex,
                            'extentOfContribution',
                            'Not Significant'
                          )
                        }
                        style={{ display: 'none' }}
                      />
                      <span
                        style={{
                          color: jobFactor.extentOfContribution === 'Not Significant' ? 'green' : 'black',
                        }}
                      >
                        Not Significant
                      </span>
                    </label>

                    <label
                      className={`d-flex align-items-center ms-1 boxEff ${jobFactor.extentOfContribution === 'Significant' ? 'boxUnEffective' : ''
                        }`}
                      style={{
                        backgroundColor: jobFactor.extentOfContribution === 'Significant' ? '#ff07073d' : 'white',
                        borderColor: jobFactor.extentOfContribution === 'Significant' ? 'rgb(248 0 0)' : '#ccc',
                        cursor: 'pointer',
                        padding: '5px 10px',
                        borderRadius: '4px',
                      }}
                      onClick={() =>
                        handleJobFactorChange(
                          section,
                          controlIndex,
                          jobFactorIndex,
                          'extentOfContribution',
                          'Significant'
                        )
                      }
                    >
                      <RadioButton
                        value="Significant"
                        name={`extentOfContribution-${section}-${controlIndex}-${jobFactorIndex}`}
                        checked={jobFactor.extentOfContribution === 'Significant'}
                        onChange={() =>
                          handleJobFactorChange(
                            section,
                            controlIndex,
                            jobFactorIndex,
                            'extentOfContribution',
                            'Significant'
                          )
                        }
                        style={{ display: 'none' }}
                      />
                      <span style={{ color: jobFactor.extentOfContribution === 'Significant' ? 'red' : 'black' }}>
                        Significant
                      </span>
                    </label>
                  </div>
                </Form.Group>
              </Col>
              {jobFactor.extentOfContribution === 'Significant' &&
                <Col md={8}>
                  <Form.Group>
                    <Form.Label className="fw-bold">
                      Describe why this was a significant factor in the context of this incident. Reference the relevant investigation information and/or records that support your reasoning. Providing this context will help justify the importance of this factor in relation to the failure of controls.
                    </Form.Label>
                    <Form.Control
                      type="text"
                      value={jobFactor.description || ''}
                      onChange={(e) =>
                        handleJobFactorChange(section, controlIndex, jobFactorIndex, 'description', e.target.value)
                      }
                    />
                  </Form.Group>
                </Col>
              }
            </Row>
          </div>
        ))
      ) : (
        <p>

        </p>
      )}
      <Button
        variant="outline-primary"
        onClick={() => handleAddJobFactor(section, controlIndex)}
        type="button"
        className="mb-3"
      >
        Add Job Factor
      </Button>
      <hr />
    </>
  );
};

export default JobFactorsSection;
