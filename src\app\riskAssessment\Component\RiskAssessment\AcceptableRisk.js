import React from 'react';


const AcceptableRisk = ({ item, onChangeReAss }) => {
    return (


        <div className='d-flex flex-column '>
            <div className='row justify-content-center'>
                <div className='col-6'>
                    <div className='row align-items-center boxShadow p-4'>
                        <div className='col-6 text-end'>
                            <h6 className='fw-bold'>is this Risk Level Acceptable ?</h6>
                        </div>
                        <div className='col-6 '>
                            <div className='col-12'>
                                <div className="box-outer ">
                                    <label
                                        htmlFor="user_name mt-3"

                                        onClick={(e) => onChangeReAss(true)}
                                        className={
                                            item[5].accept === true
                                                ? "box_select active"
                                                : "box_select "
                                        }
                                    >
                                        Yes
                                    </label>
                                    <label
                                        htmlFor="user_name mt-3"

                                        onClick={(e) => onChangeReAss(false)}
                                        className={
                                            item[5].accept === false
                                                ? "box_select active red"
                                                : "box_select "
                                        }
                                    >
                                        No
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
            {item[5].accept === false &&
                <p className='fw-bold text-center mt-2'>Go to Section 5 and add “Additional Controls” to reduce the risk from this sub-activity to acceptable levels.
                </p>
            }
        </div>


    );
};

export default AcceptableRisk;
