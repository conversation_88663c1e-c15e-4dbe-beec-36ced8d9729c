import React, { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON>, Mo<PERSON> } from "react-bootstrap";
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { MultiSelect } from 'primereact/multiselect';
import API from "../services/API";
import Swal from "sweetalert2";
import moment from "moment";
import { format } from 'date-fns';
import { FilterMatchMode } from 'primereact/api';
import { useSelector } from "react-redux";
import { sortDate } from "../services/SortDate";
import { sortNumbers } from "../services/NumberSort";
import AddTask from "./Components/AddTask";
import DisplayTask from "./Components/ViewTask";
import { DROPDOWNS, OTT_WITH_ID, OTT_ACTION_ASSIGN_CREATOR, RISK_DELETE_WITH_ID_URL, OTT_TASKS_WITH_ID, OTT_OTTTASKS_WITH_ID, ADMI<PERSON><PERSON><PERSON>OWNS } from "../constants";
import { Tag } from 'primereact/tag';
import ArchiveTaskModal from "./Components/ArchiveTaskModal";

const customSwal2 = Swal.mixin({
    customClass: {
        confirmButton: "btn btn-primary",
    },
    buttonsStyling: false,
});

const OttList = ({ data, onRefresh, onFilterUpdate }) => {
    const user = useSelector((state) => state.login.user);
    const hasOttCreator = user && user.roles && user.roles.some(item => item.maskId === "ott_creator");
    const [archiveModal, setArchiveModal] = useState(false)
    const [risk, setRisk] = useState([]);
    const [project, setProject] = useState([]);
    const [category, setCategory] = useState([]);
    const [assignee, setAssignee] = useState([]);
    const [assignor, setAssignor] = useState([]);
    const [reviewer, setReviewer] = useState([]);
    const [show, setShow] = useState(false);
    const [initialData, setInitialData] = useState(null);
    const [viewModal, setViewModal] = useState(false);
    const [filters, setFilters] = useState({
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        'project.name': { value: null, matchMode: FilterMatchMode.IN },
        'category.name': { value: null, matchMode: FilterMatchMode.IN },
        priority: { value: null, matchMode: FilterMatchMode.IN },
        'assignee.firstName': { value: null, matchMode: FilterMatchMode.IN },
        'creator.firstName': { value: null, matchMode: FilterMatchMode.IN },
        'reviewer.firstName': { value: null, matchMode: FilterMatchMode.IN },
        nextdate: { value: null, matchMode: FilterMatchMode.IN },
        'type.label': { value: null, matchMode: FilterMatchMode.IN },
        status: { value: null, matchMode: FilterMatchMode.IN },
        'timelineCategory': { value: null, matchMode: FilterMatchMode.IN },
        department: { value: null, matchMode: FilterMatchMode.IN },
        timeline: { value: null, matchMode: FilterMatchMode.IN },
    });

    const overallStatus = [
        { value: 'Yet to Start', name: 'Yet to Start' },
        { value: 'Planning', name: 'Planning' },
        { value: 'In Progress: On Track', name: 'In Progress: On Track' },
        { value: 'At Risk', name: 'At Risk' },
        { value: 'On Hold', name: 'On Hold' },
        { value: 'Under Review', name: 'Under Review' },
        { value: 'Testing / QA', name: 'Testing / QA' },
        { value: 'Ready for Deployment', name: 'Ready for Deployment' },
        { value: 'Completed', name: 'Completed' },
        { value: 'Returned', name: 'Returned' },
    ];

    const timelineOptions = [
        { label: 'Overdue', value: 'Overdue', color: 'red' },
        { label: 'Due Soon', value: 'Due Soon', color: 'orange' },
        { label: 'Upcoming', value: 'Upcoming', color: 'green' },
    ];

    useEffect(() => {
        if (data) {
            const computedData = data.map(item => ({
                ...item,
                timelineCategory: getTimelineCategory(item.dueDate),
            }));
            setRisk(computedData);

            const obs = computedData.map(item => ({
                name: item.assignee?.firstName || '',
                value: item.assignee?.firstName || '',
            }));
            setAssignee(obs.filter((ele, ind) => ind === obs.findIndex(elem => elem.value === ele.value && elem.name === ele.name)));

            const obs1 = computedData.map(item => ({
                name: item.creator?.firstName || '',
                value: item.creator?.firstName || '',
            }));
            setAssignor(obs1.filter((ele, ind) => ind === obs1.findIndex(elem => elem.value === ele.value && elem.name === ele.name)));

            const obs2 = computedData.map(item => ({
                name: item.reviewer?.firstName || '',
                value: item.reviewer?.firstName || '',
            }));
            setReviewer(obs2.filter((ele, ind) => ind === obs2.findIndex(elem => elem.value === ele.value && elem.name === ele.name)));
        }
        fetchDropdownData('project', setProject);
        fetchDropdownData('category', setCategory);
    }, [data]);

    const fetchDropdownData = useCallback(async (maskId, setState) => {
        try {
            const uriString = {
                where: { maskId: maskId },
                include: [{ relation: "dropdownItems" }],
            };
            const url = `${ADMINDROPDOWNS}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
            const response = await API.get(url);

            if (response.status === 200) {
                const data = response.data[0].dropdownItems.map((item) => ({
                    label: item.name,
                    value: item.name,
                }));
                setState(data);
            }
        } catch (error) {
            console.error(`Error fetching ${maskId} list:`, error);
        }
    }, []);

    const getTimelineCategory = (dueDate) => {
        const today = moment().startOf('day');
        const dueDateMoment = moment(dueDate).startOf('day');

        if (dueDateMoment.isSame(today)) {
            return 'Due Soon';
        } else if (dueDateMoment.isBefore(today)) {
            return 'Overdue';
        } else {
            return 'Upcoming';
        }
    };

    const timelineBodyTemplate = (row) => {
        const category = row.timelineCategory;
        const color = category === 'Overdue' ? 'red' : category === 'Due Soon' ? 'orange' : 'green';
        return <Tag value={category} style={{ backgroundColor: color, color: 'white' }} />;
    };

    const timelineFilterTemplate = (options) => {
        return (
            <MultiSelect
                value={options.value}
                options={timelineOptions}
                onChange={(e) => options.filterCallback(e.value)}
                itemTemplate={(option) => (
                    <div className="flex align-items-center gap-2">
                        <Tag value={option.label} style={{ backgroundColor: option.color, color: 'white' }} />
                    </div>
                )}
                placeholder="Any"
                className="p-column-filter"
            />
        );
    };

    const header = (
        <div className='d-flex justify-content-end'>
            {hasOttCreator &&
                <Button className="btn btn-primary  mb-3" onClick={() => openModal()}>Add Task</Button>
            }
        </div>
    );

    const openModal = () => {
        setInitialData(null);
        setShow(true);
    };

    const handleClose = () => {
        setShow(false);
    };

    const handleSave = async ({ action, newTasks = [], deletedTasks = [], modifiedTasks = [], ...formData }) => {
        if (action === 'add') {
            try {
                const response1 = await API.post(OTT_ACTION_ASSIGN_CREATOR, formData);
                if (response1.status === 200) {
                    customSwal2.fire("Task Created!", "", "success").then(() => {
                        setShow(false);
                        onRefresh();
                    });
                }
            } catch (error) {
                customSwal2.fire("Error Creating Task", "", "error");
            }
        } else if (action === 'edit') {
            try {
                const ottPromises = formData.ott ? [API.patch(OTT_WITH_ID(initialData.id), formData.ott)] : [];
                const newTaskPromises = newTasks.length > 0 ? newTasks.map((newTask) => API.post(OTT_OTTTASKS_WITH_ID(initialData.id), newTask)) : [];
                const modifiedTaskPromises = modifiedTasks.length > 0 ? modifiedTasks.map((modifiedTask) => API.patch(OTT_TASKS_WITH_ID(modifiedTask.id), modifiedTask)) : [];
                const deletedTaskPromises = deletedTasks.length > 0 ? deletedTasks.map((deletedTask) => API.delete(OTT_TASKS_WITH_ID(deletedTask.id))) : [];

                await Promise.all([...ottPromises, ...newTaskPromises, ...modifiedTaskPromises, ...deletedTaskPromises]);

                customSwal2.fire("Task Updated!", "", "success").then(() => {
                    setShow(false);
                    onRefresh();
                });
            } catch (error) {
                customSwal2.fire("Error Updating Task", "", "error");
            }
        }
    };

    const actionBodyTemplate = (row) => {
        if (user.id === row.creator?.id) {
            return (
                <div className="table-action d-flex">
                    <i className="pi pi-pencil" onClick={() => editRisk(row)}></i>
                </div>
            );
        }
    };

    const assigneeFilterTemplate = (options) => {
        return (
            <MultiSelect
                value={options.value}
                options={assignee}
                itemTemplate={representativesItemTemplate}
                onChange={(e) => options.filterCallback(e.value)}
                optionLabel="value"
                placeholder="Any"
                className="p-column-filter"
            />
        );
    };
    const assignorFilterTemplate = (options) => {
        return (
            <MultiSelect
                value={options.value}
                options={assignor}
                itemTemplate={representativesItemTemplate}
                onChange={(e) => options.filterCallback(e.value)}
                optionLabel="value"
                placeholder="Any"
                className="p-column-filter"
            />
        );
    }

    const reviewerFilterTemplate = (options) => {
        return (
            <MultiSelect
                value={options.value}
                options={reviewer}
                itemTemplate={representativesItemTemplate}
                onChange={(e) => options.filterCallback(e.value)}
                optionLabel="value"
                placeholder="Any"
                className="p-column-filter"
            />
        );
    }
    const statusFilterTemplate = (options) => {
        return (
            <MultiSelect
                value={options.value}
                options={overallStatus}
                itemTemplate={representativesItemTemplate}
                onChange={(e) => options.filterCallback(e.value)}
                optionLabel="name"
                placeholder="Any"
                className="p-column-filter"
            />
        );
    };

    const categoryBodyTemplate = (options) => {
        return (
            <MultiSelect
                value={options.value}
                options={category}
                itemTemplate={representativesItemTemplate}
                onChange={(e) => options.filterCallback(e.value)}
                optionLabel="value"
                placeholder="Any"
                className="p-column-filter"
            />
        );
    };

    const priorityBodyTemplate = (options) => {
        return (
            <MultiSelect
                value={options.value}
                options={[{ 'name': 'Low', 'value': 'Low' }, { 'name': 'Medium', 'value': 'Medium' }, { 'name': 'High', 'value': 'High' }]}
                itemTemplate={representativesItemTemplate}
                onChange={(e) => options.filterCallback(e.value)}
                optionLabel="value"
                placeholder="Any"
                className="p-column-filter"
            />
        );
    };

    const projectBodyTemplate = (options) => {
        return (
            <MultiSelect
                value={options.value}
                options={project}
                itemTemplate={representativesItemTemplate}
                onChange={(e) => options.filterCallback(e.value)}
                optionLabel="value"
                placeholder="Any"
                className="p-column-filter"
            />
        );
    };

    const representativesItemTemplate = (option) => {
        return (
            <div className="flex align-items-center gap-2">
                <span>{option.value}</span>
            </div>
        );
    };

    const createdBodyTemplate = (data) => {
        return format(new Date(data.dueDate), 'dd-MM-yyyy');
    };
    

    const openViewModal = (row) => {
        setInitialData(row);
        setViewModal(true);
    };

    const maskBodyTemplate = (row) => {
        return <div className='maskid' onClick={() => openViewModal(row)}>{row.maskId}</div>;
    };

    const editRisk = (row) => {
        setInitialData(row);
        setShow(true);
    };

    const onArchived = async (data) => {
        const response = await API.patch(OTT_WITH_ID(initialData.id), { isArchive: true, isTaskIsCompletedForArchive: data.isTaskIsCompletedForArchive, archiveComments: data.archiveComments });
        if (response.status === 204) {
            customSwal2.fire('Updated!', '', 'success');
            onRefresh();
            setViewModal(false);
        }
    };
    const handleFilterChange = (filteredData) => {
      

        // Update the count of filtered data in the parent component
        if (onFilterUpdate) {
            onFilterUpdate(filteredData.length);
        }
    };
    return (
        <>
            <div>
                <div className="row">
                    <div className="col-12">
                        <div className="card">
                            <div className="card-body p-0">
                                <div className="row">
                                    <div className="col-12">
                                        <DataTable value={risk} paginator rows={10}  onValueChange={handleFilterChange} header={header} filters={filters} onFilter={(e) => setFilters(e.filters)} paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                                            rowsPerPageOptions={[10, 25, 50]}
                                            rowClassName={rowData => rowData.status === 'At Risk' ? 'red-background' : ''}
                                            emptyMessage="No Data found." tableStyle={{ minWidth: '50rem' }}>
                                            <Column field="timelineCategory" header="Timeline" body={timelineBodyTemplate} filter showFilterMatchModes={false} filterElement={timelineFilterTemplate}></Column>
                                            <Column field="maskId" header="Task ID" sortable body={maskBodyTemplate}></Column>
                                            <Column field="taskName" header="Task Name"></Column>
                                            <Column field="category.name" header="Category" filterElement={categoryBodyTemplate} showFilterMatchModes={false} filter></Column>
                                            <Column field="project.name" header="Project" filterElement={projectBodyTemplate} showFilterMatchModes={false} filter></Column>
                                            <Column field="priority" header="Priority" filterElement={priorityBodyTemplate} showFilterMatchModes={false} filter></Column>
                                            <Column field="creator.firstName" header="Assignor" filterElement={assignorFilterTemplate} showFilterMatchModes={false} filter></Column>
                                            <Column field="assignee.firstName" header="Assignee" filterElement={assigneeFilterTemplate} showFilterMatchModes={false} filter></Column>
                                            <Column field="reviewer.firstName" header="Reviewer" filterElement={reviewerFilterTemplate} showFilterMatchModes={false} filter></Column>
                                            <Column field="dueDate" body={createdBodyTemplate} header="Due Date" sortFunction={sortDate} sortable></Column>

                                            <Column field="status" header="Status" filterElement={statusFilterTemplate} showFilterMatchModes={false} filter></Column>
                                            <Column field="estimatedPercentage" header="% Done" sortable sortFunction={sortNumbers}></Column>
                                            {hasOttCreator &&

                                                <Column header="Edit" body={actionBodyTemplate}></Column>
                                            }
                                        </DataTable>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {show &&
                        <Modal show={show} onHide={handleClose}>
                            <Modal.Header closeButton>
                                <Modal.Title>Add Task</Modal.Title>
                            </Modal.Header>
                            <Modal.Body>
                                <AddTask initialData={initialData} onSave={handleSave} />
                            </Modal.Body>
                        </Modal>
                    }
                    {viewModal &&
                        <Modal show={viewModal} onHide={() => setViewModal(false)}>
                            <Modal.Header closeButton>
                                <Modal.Title>View Task</Modal.Title>
                            </Modal.Header>
                            <Modal.Body>
                                <DisplayTask initialData={initialData} onSave={handleSave} />
                            </Modal.Body>
                            {/* {hasOttCreator &&
                                <Modal.Footer className="flex-wrap">

                                    <Button className="btn btn-secondary" onClick={() => setArchiveModal(true)}>
                                        Archived
                                    </Button>

                                </Modal.Footer>
                            } */}
                        </Modal>
                    }

                    <ArchiveTaskModal
                        show={archiveModal}
                        handleClose={() => setArchiveModal(false)}
                        handleSave={onArchived}
                    />
                </div>
            </div>
        </>
    );
};

export default OttList;
