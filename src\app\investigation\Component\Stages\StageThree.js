import React from 'react';
import { RadioButton } from 'primereact/radiobutton';
import { InputTextarea } from 'primereact/inputtextarea';
import { Button } from 'primereact/button';

function StageThree({ formData, handleAddIdentifiedPreventiveControl, handleDeleteIdentifiedPreventiveControl, handleToggle, errors, handleChange, handleToggleImplementation, handleDeleteUnidentifiedPreventiveControl, handleToggleEffectiveness, handleAddUnidentifiedPreventiveControl, disable }) {
    return (
        <div>
            <p className=''>

                Preventative controls are designed to reduce the likelihood of a workplace incident or accident occurring in the first place. They focus on eliminating or controlling hazards before they can lead to an incident.
            </p>
            <h4 className='mt-3 mb-3 font-weight-bold'>Controllability of the Incident</h4>

            <div className='row mb-3 mt-4'>
                <div className='col-6'>
                    <div className=''>Was this hazard controllable? </div>
                </div>
                <div className='col-6'>
                    <div className="d-flex">
                        <label
                            className={`d-flex align-items-center me-2 boxEff ${formData.preventiveControls.wasHazardControllable ? 'boxEffective' : ''}`}
                            style={{
                                backgroundColor: formData.preventiveControls.wasHazardControllable ? 'lightgreen' : 'white',
                                borderColor: formData.preventiveControls.wasHazardControllable ? 'green' : '#ccc',
                                cursor: disable ? 'not-allowed' : 'pointer'
                            }}
                            onClick={() => !disable && handleToggle('preventiveControls', 'wasHazardControllable', true)}
                        >
                            <RadioButton
                                value="Yes"
                                name="wasHazardControllable"
                                checked={formData.preventiveControls.wasHazardControllable === true}
                                onChange={() => handleToggle('preventiveControls', 'wasHazardControllable', true)}
                                style={{ display: 'none' }} // Hidden radio button
                                disabled={disable}  // Disable based on the disable prop
                            />
                            <span style={{ color: formData.preventiveControls.wasHazardControllable ? 'green' : 'black' }}>Yes</span>
                        </label>

                        <label
                            className={`d-flex align-items-center boxEff ${!formData.preventiveControls.wasHazardControllable ? 'boxUnEffective' : ''}`}
                            style={{
                                backgroundColor: !formData.preventiveControls.wasHazardControllable ? '#ff07073d' : 'white',
                                borderColor: !formData.preventiveControls.wasHazardControllable ? 'red' : '#ccc',
                                cursor: disable ? 'not-allowed' : 'pointer'
                            }}
                            onClick={() => !disable && handleToggle('preventiveControls', 'wasHazardControllable', false)}
                        >
                            <RadioButton
                                value="No"
                                name="wasHazardControllable"
                                checked={formData.preventiveControls.wasHazardControllable === false}
                                onChange={() => handleToggle('preventiveControls', 'wasHazardControllable', false)}
                                style={{ display: 'none' }} // Hidden radio button
                                disabled={disable}  // Disable based on the disable prop
                            />
                            <span style={{ color: !formData.preventiveControls.wasHazardControllable ? 'red' : 'black' }}>No</span>
                        </label>


                    </div>
                </div>
            </div>

            {formData.preventiveControls.wasHazardControllable ? (<>

                <h4 className='font-weight-bold mt-4 mt-2'>Identified Preventative Controls</h4>
                <div className='row mb-3 mt-4'>
                    <div className='col-6'>
                        <div className=''>Were any preventative controls identified during the planning / risk assessment process?</div>
                    </div>
                    <div className='col-6'>
                        <div className="d-flex">

                            <label
                                className={`d-flex align-items-center me-2 boxEff ${formData.preventiveControls.controlIdentified ? 'boxEffective' : ''}`}
                                style={{
                                    backgroundColor: formData.preventiveControls.controlIdentified ? 'lightgreen' : 'white',
                                    borderColor: formData.preventiveControls.controlIdentified ? 'green' : '#ccc',
                                    cursor: disable ? 'not-allowed' : 'pointer'
                                }}
                                onClick={() => !disable && handleToggle('preventiveControls', 'controlIdentified', true)}
                            >
                                <RadioButton
                                    value="Yes"
                                    name="controlIdentified"
                                    checked={formData.preventiveControls.controlIdentified === true}
                                    onChange={() => handleToggle('preventiveControls', 'controlIdentified', true)}
                                    style={{ display: 'none' }} // Hidden radio button
                                    disabled={disable}  // Disable based on the disable prop
                                />
                                <span style={{ color: formData.preventiveControls.controlIdentified ? 'green' : 'black' }}>Yes</span>
                            </label>
                            <label
                                className={`d-flex align-items-center boxEff ${!formData.preventiveControls.controlIdentified ? 'boxUnEffective' : ''}`}
                                style={{
                                    backgroundColor: !formData.preventiveControls.controlIdentified ? '#ff07073d' : 'white',
                                    borderColor: !formData.preventiveControls.controlIdentified ? 'red' : '#ccc',
                                    cursor: disable ? 'not-allowed' : 'pointer'
                                }}
                                onClick={() => !disable && handleToggle('preventiveControls', 'controlIdentified', false)}
                            >
                                <RadioButton
                                    value="No"
                                    name="controlIdentified"
                                    checked={formData.preventiveControls.controlIdentified === false}
                                    onChange={() => handleToggle('preventiveControls', 'controlIdentified', false)}
                                    style={{ display: 'none' }} // Hidden radio button
                                    disabled={disable}  // Disable based on the disable prop
                                />
                                <span style={{ color: !formData.preventiveControls.controlIdentified ? 'red' : 'black' }}>No</span>
                            </label>

                        </div>
                    </div>
                </div>

                {formData.preventiveControls.controlIdentified && (() => {
                    // Check if any control is implemented
                    const anyControlImplemented = formData.preventiveControls.identifiedPreventiveControls.some(
                        (control) => control.isControlImplemented
                    );

                    return (
                        <>
                            <div className="d-flex flex-column mt-3">
                                {/* Header */}
                                <div className="d-flex font-weight-bold mb-2">
                                    <div className="col-1 text-center"></div>
                                    <div className="col-5 px-2">
                                        List each of the Preventative Controls identified during the Planning / Risk Assessment process to minimize the likelihood of such an incident?
                                    </div>
                                    <div
                                        className={
                                            anyControlImplemented
                                                ? 'col-3 px-2 '
                                                : 'col-5 px-2 '
                                        }
                                    >
                                        Was the identified control implemented?
                                    </div>
                                    {anyControlImplemented && (
                                        <div className="col-2 px-2 text-center">Was it effective?</div>
                                    )}
                                    <div className="col-1 px-2 text-center"></div>
                                </div>

                                {/* Body */}
                                {formData.preventiveControls.identifiedPreventiveControls.map(
                                    (control, index) => (
                                        <div
                                            className="d-flex mb-3 align-items-center"
                                            key={index}
                                        >
                                            <div className="col-1 px-2 text-end">{'PC ' + (index + 1)}</div>
                                            <div className="col-5 px-2">
                                                <InputTextarea
                                                    style={{ width: '100%' }}
                                                    rows={2}
                                                    autoResize
                                                    name={`preventiveControls.identifiedPreventiveControls.${index}.controlStatement`}
                                                    value={control.controlStatement}
                                                    onChange={handleChange}
                                                    disabled={disable}  // Disable based on the disable prop
                                                />
                                            </div>
                                            <div
                                                className={
                                                    anyControlImplemented
                                                        ? 'col-3 px-2 text-center'
                                                        : 'col-5 px-2 text-center'
                                                }
                                            >
                                                <div className="d-flex p-2">

                                                    <label
                                                        className={`d-flex align-items-center me-2 boxEff ${control.isControlImplemented ? 'boxEffective' : ''
                                                            }`}
                                                        style={{
                                                            backgroundColor: control.isControlImplemented
                                                                ? 'lightgreen'
                                                                : 'white',
                                                            borderColor: control.isControlImplemented
                                                                ? 'green'
                                                                : '#ccc',
                                                            cursor: disable ? 'not-allowed' : 'pointer',
                                                        }}
                                                        onClick={() =>
                                                            !disable &&
                                                            handleToggleImplementation(
                                                                'preventiveControls',
                                                                'identifiedPreventiveControls',
                                                                index,
                                                                true
                                                            )
                                                        }
                                                    >
                                                        <RadioButton
                                                            value="Yes"
                                                            name={`implemented-${index}`}
                                                            checked={control.isControlImplemented === true}
                                                            onChange={() =>
                                                                handleToggleImplementation(
                                                                    'preventiveControls',
                                                                    'identifiedPreventiveControls',
                                                                    index,
                                                                    true
                                                                )
                                                            }
                                                            style={{ display: 'none' }} // Hides the radio button
                                                            disabled={disable} // Disable based on the disable prop
                                                        />
                                                        <span
                                                            style={{
                                                                color: control.isControlImplemented ? 'green' : 'black',
                                                            }}
                                                        >
                                                            Yes
                                                        </span>
                                                    </label>
                                                    <label
                                                        className={`d-flex align-items-center boxEff ${control.isControlImplemented === false
                                                            ? 'boxUnEffective'
                                                            : ''
                                                            }`}
                                                        style={{
                                                            backgroundColor:
                                                                control.isControlImplemented === false
                                                                    ? '#ff07073d'
                                                                    : 'white',
                                                            borderColor:
                                                                control.isControlImplemented === false
                                                                    ? 'rgb(248 0 0)'
                                                                    : '#ccc',
                                                            cursor: disable ? 'not-allowed' : 'pointer',
                                                        }}
                                                        onClick={() =>
                                                            !disable &&
                                                            handleToggleImplementation(
                                                                'preventiveControls',
                                                                'identifiedPreventiveControls',
                                                                index,
                                                                false
                                                            )
                                                        }
                                                    >
                                                        <RadioButton
                                                            value="No"
                                                            name={`implemented-${index}`}
                                                            checked={control.isControlImplemented === false}
                                                            onChange={() =>
                                                                handleToggleImplementation(
                                                                    'preventiveControls',
                                                                    'identifiedPreventiveControls',
                                                                    index,
                                                                    false
                                                                )
                                                            }
                                                            style={{ display: 'none' }} // Hides the radio button
                                                            disabled={disable} // Disable based on the disable prop
                                                        />
                                                        <span
                                                            style={{
                                                                color:
                                                                    control.isControlImplemented === false
                                                                        ? 'red'
                                                                        : 'black',
                                                            }}
                                                        >
                                                            No
                                                        </span>
                                                    </label>


                                                </div>
                                            </div>

                                            {anyControlImplemented && (
                                                <>
                                                    {control.isControlImplemented ? (
                                                        <div className="col-2 px-2 text-center">
                                                            <div className="d-flex justify-content-evenly">
                                                                <label
                                                                    className={`d-flex align-items-center me-1 boxEff ${control.isEffective ? 'boxEffective' : ''
                                                                        }`}
                                                                    style={{
                                                                        backgroundColor: control.isEffective
                                                                            ? 'lightgreen'
                                                                            : 'white',
                                                                        borderColor: control.isEffective
                                                                            ? 'green'
                                                                            : '#ccc',
                                                                        cursor: disable ? 'not-allowed' : 'pointer',
                                                                    }}
                                                                    onClick={() =>
                                                                        !disable &&
                                                                        handleToggleEffectiveness(
                                                                            'preventiveControls',
                                                                            'identifiedPreventiveControls',
                                                                            index,
                                                                            true
                                                                        )
                                                                    }
                                                                >
                                                                    <RadioButton
                                                                        value="Effective"
                                                                        name={`identifiedEffective-${index}`}
                                                                        checked={control.isEffective === true}
                                                                        onChange={() =>
                                                                            handleToggleEffectiveness(
                                                                                'preventiveControls',
                                                                                'identifiedPreventiveControls',
                                                                                index,
                                                                                true
                                                                            )
                                                                        }
                                                                        style={{ display: 'none' }} // Hides the radio button
                                                                        disabled={disable} // Disable based on the disable prop
                                                                    />
                                                                    <span
                                                                        style={{
                                                                            color: control.isEffective ? 'green' : 'black',
                                                                        }}
                                                                    >
                                                                        Effective
                                                                    </span>
                                                                </label>

                                                                <label
                                                                    className={`d-flex align-items-center boxEff ${control.isEffective === false
                                                                        ? 'boxUnEffective'
                                                                        : ''
                                                                        }`}
                                                                    style={{
                                                                        backgroundColor:
                                                                            control.isEffective === false
                                                                                ? '#ff07073d'
                                                                                : 'white',
                                                                        borderColor:
                                                                            control.isEffective === false
                                                                                ? 'rgb(248 0 0)'
                                                                                : '#ccc',
                                                                        cursor: disable ? 'not-allowed' : 'pointer',
                                                                    }}
                                                                    onClick={() =>
                                                                        !disable &&
                                                                        handleToggleEffectiveness(
                                                                            'preventiveControls',
                                                                            'identifiedPreventiveControls',
                                                                            index,
                                                                            false
                                                                        )
                                                                    }
                                                                >
                                                                    <RadioButton
                                                                        value="NonEffective"
                                                                        name={`identifiedNonEffective-${index}`}
                                                                        checked={control.isEffective === false}
                                                                        onChange={() =>
                                                                            handleToggleEffectiveness(
                                                                                'preventiveControls',
                                                                                'identifiedPreventiveControls',
                                                                                index,
                                                                                false
                                                                            )
                                                                        }
                                                                        style={{ display: 'none' }} // Hides the radio button
                                                                        disabled={disable} // Disable based on the disable prop
                                                                    />
                                                                    <span
                                                                        style={{
                                                                            color:
                                                                                control.isEffective === false
                                                                                    ? 'red'
                                                                                    : 'black',
                                                                        }}
                                                                    >
                                                                        Not Effective
                                                                    </span>
                                                                </label>
                                                            </div>
                                                        </div>
                                                    ) : (
                                                        // Maintain column alignment
                                                        <div className="col-2"></div>
                                                    )}
                                                </>
                                            )}

                                            <div className="col-1 px-2 text-center">
                                                {!disable && (
                                                    <i
                                                        className="pi pi-trash"
                                                        onClick={() =>
                                                            handleDeleteIdentifiedPreventiveControl(index)
                                                        }
                                                    ></i>
                                                )}
                                            </div>
                                        </div>
                                    )
                                )}
                            </div>

                            <div className="row">
                                {errors.identifiedPreventiveControls && (
                                    <small className="p-error">{errors.identifiedPreventiveControls}</small>
                                )}
                                <div className="col-1"></div>
                                <div className="col-4">
                                    {!disable && (
                                        <Button
                                            outlined
                                            label="Add More"
                                            onClick={handleAddIdentifiedPreventiveControl}
                                        />
                                    )}
                                </div>
                            </div>
                        </>
                    );
                })()}



                <h4 className='font-weight-bold mt-4 mt-2'>Preventative Controls not Identified</h4>
                <div className='row mb-3 mt-4'>

                    <div className='col-6'>
                        <div className=''>Based on standard practices for this type of process or work activity, should any additional Preventative Controls have been identified during the Planning or Risk Assessment process?</div>
                    </div>
                    <div className='col-6'>
                        <div className="d-flex">

                            <label
                                className={`d-flex align-items-center me-2 boxEff ${!formData.preventiveControls.controlNotIdentified ? 'boxUnEffective' : ''}`}
                                style={{
                                    backgroundColor: !formData.preventiveControls.controlNotIdentified ? 'lightgreen' : 'white',
                                    borderColor: !formData.preventiveControls.controlNotIdentified ? 'green' : '#ccc',
                                    cursor: disable ? 'not-allowed' : 'pointer'
                                }}
                                onClick={() => !disable && handleToggle('preventiveControls', 'controlNotIdentified', false)}
                            >
                                <RadioButton
                                    value="No"
                                    name="controlIdentified"
                                    checked={formData.preventiveControls.controlIdentified === false}
                                    onChange={() => handleToggle('preventiveControls', 'controlNotIdentified', false)}
                                    style={{ display: 'none' }} // Hidden radio button
                                    disabled={disable}  // Disable based on the disable prop
                                />
                                <span style={{ color: !formData.preventiveControls.controlNotIdentified ? 'green' : 'black' }}>No</span>
                            </label>
                            <label
                                className={`d-flex align-items-center  boxEff ${formData.preventiveControls.controlNotIdentified ? 'boxEffective' : ''}`}
                                style={{
                                    backgroundColor: formData.preventiveControls.controlNotIdentified ? '#ff07073d' : 'white',
                                    borderColor: formData.preventiveControls.controlNotIdentified ? 'red' : '#ccc',
                                    cursor: disable ? 'not-allowed' : 'pointer'
                                }}
                                onClick={() => !disable && handleToggle('preventiveControls', 'controlNotIdentified', true)}
                            >
                                <RadioButton
                                    value="Yes"
                                    name="controlIdentified"
                                    checked={formData.preventiveControls.controlNotIdentified === true}
                                    onChange={() => handleToggle('preventiveControls', 'controlNotIdentified', true)}
                                    style={{ display: 'none' }} // Hidden radio button
                                    disabled={disable}  // Disable based on the disable prop
                                />
                                <span style={{ color: formData.preventiveControls.controlNotIdentified ? 'red' : 'black' }}>Yes</span>
                            </label>


                        </div>
                    </div>
                </div>
                {formData.preventiveControls.controlNotIdentified && <>
                    <div className="d-flex flex-column mt-4">
                        {/* Header */}
                        <div className="d-flex font-weight-bold mb-2">
                            <div className="col-1 px-2 text-center"></div>
                            <div className="col-10 px-2">In hindsight, what other Preventative Controls should have been identified and implemented, but were overlooked during the Planning / Risk Assessment process ?</div>
                            <div className="col-1 px-2 text-center"></div>
                        </div>

                        {/* Body */}
                        {formData.preventiveControls.unIdentifiedPreventiveControls.map((control, index) => (
                            <div className="d-flex mb-3 align-items-center" key={index}>
                                <div className="col-1 px-2 text-end">{'UPC ' + (index + 1)}</div>
                                <div className="col-10 px-2">
                                    <InputTextarea
                                        style={{ width: '100%' }}
                                        name={`preventiveControls.unIdentifiedPreventiveControls.${index}.controlStatement`}
                                        value={control.controlStatement}
                                        autoResize
                                        onChange={handleChange}
                                        disabled={disable}  // Disable based on the disable prop
                                    />
                                </div>
                                <div className="col-1 px-2 text-center">
                                    {!disable && <i className="pi pi-trash" onClick={() => handleDeleteUnidentifiedPreventiveControl(index)}></i>}
                                </div>
                            </div>
                        ))}
                    </div>
                    <div className="row">
                        <div className="col-1"></div>
                        <div className="col-4">
                            {!disable && <Button outlined label="Add More" onClick={handleAddUnidentifiedPreventiveControl} />}
                        </div>
                    </div>
                </>}
            </>) : (
                <div className="col-12">
                    <div htmlFor="workPerformed" className="mb-2">Explain why this hazard was not controllable</div>
                    <InputTextarea
                        style={{ width: '100%' }}
                        autoResize
                        name={`preventiveControls.explainHazardControllable`}
                        value={formData.preventiveControls.explainHazardControllable}
                        onChange={handleChange}
                        disabled={disable}  // Disable based on the disable prop
                    />
                </div>

            )}
        </div>
    )
}

export default StageThree;
