import React, { useEffect, useState } from 'react';
import pdfMake, { async } from 'pdfmake/build/pdfmake';
import pdfFonts from 'pdfmake/build/vfs_fonts';
import moment from 'moment'; // Assuming you use moment for date formatting
import { FILE_DOWNLOAD } from '../../constants';
import axios from 'axios';
import Swal from 'sweetalert2';
pdfMake.vfs = pdfFonts.pdfMake.vfs;
const customSwal2 = Swal.mixin({
    customClass: {
        confirmButton: 'btn btn-primary',

    },
    buttonsStyling: false
})
const GenerateLandPdf = ({ pdf }) => {

    const [logo, setLogo] = useState('')
    const [data, setData] = useState([])

    useEffect(() => {
        getFetchLogo();
    }, [])

    const removeDuplicate = (data) => {
        return data.filter((value, index) => data.indexOf(value) === index);
    }

    const getFetchLogo = async () => {
        try {
            const response = await axios.get(FILE_DOWNLOAD(localStorage.getItem('logo')), {
                headers: {
                    'Content-Type': 'application/json'
                },
            });
            const data = response.data;
            const logoUrl = data // Assuming the API returns an object with a `url` field
            setLogo(logoUrl);

        } catch (error) {
            console.error('Error fetching logo:', error);
        }
    }

    function renderRows(row) {
        let result = [];
        row.forEach((item, index) => {
            result.push([
                { text: index + 1, alignment: 'center' },
                { text: item[0]?.name ?? 'N/A', alignment: 'center' },
                {
                    ol: item[1].selected.map(it => it.name ?? 'N/A')
                },
                {
                    ol: item[2].option.map(it => {
                        let currentType = it.current_type || 'N/A'; // Fallback to 'N/A' if current_type is undefined
                        let value = it.value || 'N/A'; // Fallback to 'N/A' if value is undefined

                        // Check if either current_type or value is available
                        return [currentType, value].join(' - ');
                    })
                },

                {
                    ol: item[3].option.map(it => {
                        let currentType = it.current_type || '';
                        let method = it.method || '';
                        let value = it.value || '';
                        console.log(currentType, method, value)
                        // Check if all three are available
                        if (currentType || method || value) {
                            return [currentType, method, value].join(' - ');
                        } else {
                            // Return 'N/A' if any of them is missing
                            return 'N/A';
                        }
                    })
                },

                { text: item[4]?.severity ?? 'N/A', alignment: 'center' },
                { text: item[4]?.likelyhood ?? 'N/A', alignment: 'center' },
                { text: (item[4]?.likelyhood * item[4]?.severity) ?? 'N/A', alignment: 'center' },
                {
                    ol: item[6].option.map(it => {
                        let currentType = it.current_type || 'N/A'; // Fallback to 'N/A' if current_type is undefined
                        let value = it.value || 'N/A'; // Fallback to 'N/A' if value is undefined

                        // Check if either current_type or value is available
                        return [currentType, value].join(' - ');
                    })
                },
                { text: item[7]?.severity === '' ? "Nil" : item[7]?.severity, alignment: 'center' },
                { text: item[7]?.likelyhood === '' ? "Nil" : item[7]?.likelyhood, alignment: 'center' },
                { text: (item[7]?.likelyhood * item[7]?.severity) || 'Nil', alignment: 'center' },
                {
                    ol: item[6].option.map(it => it.person.name ? (it.person.name || it.person.name) : "Nil")
                },
                {
                    ol: item[6].option.map(it => it.date ? moment(it.date).format('YYYY-MM-DD') : "Nil")
                },
                { text: '', alignment: 'center' },
            ]);
        });
        return result;
    }



    console.log(pdf)
    const generatePdf = () => {

        if (pdf.status === 'Published') {




            const dd = {
                content: [
                    // { image: 'path/to/logo', style: 'logoImg', width: 50 },
                    { text: 'Risk Assessment', style: 'header' },
                    {
                        table: {
                            widths: ['*', '*', '*', '*'],
                            body: [
                                [
                                    ...(pdf.type === 'Routine'
                                        ? [
                                            { columns: [{ text: 'Department:', bold: true }] },
                                            { columns: [{ text: pdf.department.name }] },
                                            { columns: [{ text: 'Work Activity:', bold: true }] },
                                            { columns: [{ text: pdf.workActivity.name }] }
                                        ]
                                        : [
                                            { columns: [{ text: 'Work Activity:', bold: true }] },
                                            { columns: [{ text: pdf.nonRoutineWorkActivity }] },
                                            {}, // Empty columns to maintain table structure
                                            {}
                                        ]
                                    ),
                                ],
                                [
                                    { columns: [{ text: 'Type:', bold: true }] },
                                    { columns: [{ text: pdf.type }] },
                                    { columns: [{ text: 'Leader:', bold: true }] },
                                    { columns: [{ text: pdf.teamLeader.firstName }] },
                                ],
                                [
                                    { columns: [{ text: 'RA ID:', bold: true }] },
                                    { columns: [{ text: pdf.maskId }] },
                                    { columns: [{ text: 'Date of first Release:', bold: true }] },
                                    { columns: [{ text: pdf.publishedDate || '' }] },
                                ],
                                [
                                    { columns: [{ text: 'Updated Date:', bold: true }] },
                                    { columns: [{ text: pdf.updated }] },
                                    { columns: [{ text: 'Date of Next Review Date:', bold: true }] },
                                    { columns: [{ text: pdf.nextReviewDate }] },
                                ],
                                [
                                    { columns: [{ text: 'Team Members:', bold: true }] },
                                    {
                                        columns: [{
                                            text: pdf.raTeamMembers && pdf.raTeamMembers.map(item => item.user.firstName).join(' - ')
                                        }], colSpan: 3
                                    },
                                    {}, {},
                                ],
                            ],
                        },
                    },
                    { text: '', margin: [0, 2] },
                    {
                        table: {
                            headerRows: 2,

                            body: [
                                [
                                    { text: 'HAZARD IDENTIFICATION', bold: true, alignment: 'center', colSpan: 4 }, {}, {}, {},
                                    { text: 'RISK EVALUATION', bold: true, alignment: 'center', colSpan: 4 }, {}, {}, {},
                                    { text: 'RISK CONTROL', bold: true, alignment: 'center', colSpan: 7 }, {}, {}, {}, {}, {}, {}
                                ],
                                [
                                    { text: 'Ref', bold: true, alignment: 'center' },
                                    { text: 'Sub-Activity', bold: true, alignment: 'center' },
                                    { text: 'Hazard', bold: true, alignment: 'center' },
                                    { text: 'Consequence', bold: true, alignment: 'center' },
                                    { text: 'Current Controls', bold: true, alignment: 'center' },
                                    { text: 'S', bold: true, alignment: 'center' },
                                    { text: 'L', bold: true, alignment: 'center' },
                                    { text: 'RPN', bold: true, alignment: 'center' },
                                    { text: 'Additional Controls', bold: true, alignment: 'center' },
                                    { text: 'S', bold: true, alignment: 'center' },
                                    { text: 'L', bold: true, alignment: 'center' },
                                    { text: 'RPN', bold: true, alignment: 'center' },
                                    { text: 'Implementation Person', bold: true, alignment: 'center' },
                                    { text: 'Due Date', bold: true, alignment: 'center' },
                                    { text: 'Remarks', bold: true, alignment: 'center' },
                                ],
                                ...renderRows(pdf.tasks),
                            ],
                        },
                    },
                    { text: 'Recommendations of RA Team', style: 'subheader' },
                    {
                        table: {
                            widths: ['*'],
                            body: [
                                [
                                    { columns: [{ text: '1.' + pdf.overallRecommendationOne.label, fillColor: pdf.overallRecommendationOne.value === "0" ? "#8cc14b" : pdf.overallRecommendationOne.value === "1" ? "#ffef00" : "#ff1900" }] },
                                ],
                            ],
                        },
                        layout: 'noBorders',
                    },
                    {
                        table: {
                            widths: ['*'],
                            body: [
                                [
                                    { columns: [{ text: '2.' + pdf.overallRecommendationTwo.label }] },
                                ],
                            ],
                        },
                        layout: 'noBorders',
                    },
                    {
                        table: {
                            widths: ['*'],
                            body: [
                                [
                                    { columns: [{ text: '3.' + pdf.additonalRemarks }] },
                                ],
                            ],
                        },
                        layout: 'noBorders',
                    },
                ],
                pageSize: 'A3',
                pageOrientation: 'landscape',
                pageMargins: [15, 15, 15, 20],
                footer: function (currentPage, pageCount) {
                    return { alignment: 'right', margin: [5, 0], text: `Page ${currentPage} of ${pageCount} pages(s)` };
                },
                styles: {
                    header: {
                        fontSize: 18,
                        bold: true,
                        margin: [0, 0, 0, 10],
                        alignment: 'center',
                    },
                    subheader: {
                        fontSize: 16,
                        bold: true,
                        margin: [0, 10, 0, 5],
                    },
                    logoImg: {
                        alignment: 'center',
                        width: 60,
                    },
                },
            };

            pdfMake.createPdf(dd).download(`${pdf.maskId}-${moment().format('YYYYMMDD HH:mm')}.pdf`);
        } else {
            customSwal2.fire(
                'Printing disabled',
                'The RA needs to be affirmed by all team members before it can be printed!',
                'warning'
            )
        }
    };

    return (

        <i className="fa fa-download" onClick={generatePdf} style={{ fontSize: 30, borderRadius: 50, boxShadow: '0px 0px 10px 3px #dadada', padding: 10, cursor: 'pointer', color: pdf.status == 'Published' ? 'green' : '#d62828', }} ></i>
    );
};

export default GenerateLandPdf;
