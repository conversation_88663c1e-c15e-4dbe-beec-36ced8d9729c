import React from 'react';
import { RadioButton } from 'primereact/radiobutton';
import { InputTextarea } from 'primereact/inputtextarea';
import { Button } from 'primereact/button';

function StageFive({ formData, handleAddIdentifiedMitigativeControl, handleDeleteIdentifiedMitigativeControl, handleToggle, errors, handleChange, handleToggleImplementation, handleToggleEffectiveness, handleDeleteUnidentifiedMitigativeControl, handleAddUnidentifiedMitigativeControl, disable }) {
    return (
        <div>
            <p>Mitigative controls are designed to reduce the severity or consequences of an incident if it does occur. They do not prevent incidents but aim to limit the damage or harm caused.</p>

            <h4 className='mt-3 mb-3 font-weight-bold'>Controllability of consequences of the Incident</h4>

            <div className='row mb-3 mt-4'>
                <div className='col-6'>
                    <div className=''>Was it possible to control the consequences of the incident ? </div>
                </div>
                <div className='col-6'>
                    <div className="d-flex">

                        <label
                            className={`d-flex align-items-center me-2 boxEff ${formData.mitigativeControls.wasHazardControllable ? 'boxEffective' : ''}`}
                            style={{
                                backgroundColor: formData.mitigativeControls.wasHazardControllable ? 'lightgreen' : 'white',
                                borderColor: formData.mitigativeControls.wasHazardControllable ? 'green' : '#ccc',
                                cursor: disable ? 'not-allowed' : 'pointer'
                            }}
                            onClick={() => !disable && handleToggle('mitigativeControls', 'wasHazardControllable', true)}
                        >
                            <RadioButton
                                value="Yes"
                                name="wasHazardControllable"
                                checked={formData.mitigativeControls.wasHazardControllable === true}
                                onChange={() => handleToggle('mitigativeControls', 'wasHazardControllable', true)}
                                style={{ display: 'none' }} // Hidden radio button
                                disabled={disable}  // Disable based on the disable prop
                            />
                            <span style={{ color: formData.mitigativeControls.wasHazardControllable ? 'green' : 'black' }}>Yes</span>
                        </label>
                        <label
                            className={`d-flex align-items-center boxEff ${!formData.mitigativeControls.wasHazardControllable ? 'boxUnEffective' : ''}`}
                            style={{
                                backgroundColor: !formData.mitigativeControls.wasHazardControllable ? '#ff07073d' : 'white',
                                borderColor: !formData.mitigativeControls.wasHazardControllable ? 'red' : '#ccc',
                                cursor: disable ? 'not-allowed' : 'pointer'
                            }}
                            onClick={() => !disable && handleToggle('mitigativeControls', 'wasHazardControllable', false)}
                        >
                            <RadioButton
                                value="No"
                                name="wasHazardControllable"
                                checked={formData.mitigativeControls.wasHazardControllable === false}
                                onChange={() => handleToggle('preventiveControls', 'wasHazardControllable', false)}
                                style={{ display: 'none' }} // Hidden radio button
                                disabled={disable}  // Disable based on the disable prop
                            />
                            <span style={{ color: !formData.mitigativeControls.wasHazardControllable ? 'red' : 'black' }}>No</span>
                        </label>


                    </div>
                </div>
            </div>
            {formData.mitigativeControls.wasHazardControllable ? (<>
                <h4 className='mt-4 mb-4 font-weight-bold'>Identified Mitigative Controls</h4>

                <div className="row mb-3">
                    <div className="col-6">
                        <div className=''>Were any mitigative controls identified during the planning / risk assessment process?</div>
                    </div>
                    <div className="col-6">
                        <div className="d-flex">
                            <label
                                className={`d-flex align-items-center me-2 boxEff ${formData.mitigativeControls.controlIdentified ? 'boxEffective' : ''}`}
                                style={{
                                    backgroundColor: formData.mitigativeControls.controlIdentified ? 'lightgreen' : 'white',
                                    borderColor: formData.mitigativeControls.controlIdentified ? 'green' : '#ccc',
                                    cursor: disable ? 'not-allowed' : 'pointer'
                                }}
                                onClick={() => !disable && handleToggle('mitigativeControls', 'controlIdentified', true)}
                            >
                                <RadioButton
                                    value="Yes"
                                    name="mitigativeControlIdentified"
                                    checked={formData.mitigativeControls.controlIdentified === true}
                                    onChange={() => handleToggle('mitigativeControls', 'controlIdentified', true)}
                                    style={{ display: 'none' }} // Hidden radio button
                                    disabled={disable} // Disable radio button
                                />
                                <span style={{ color: formData.mitigativeControls.controlIdentified ? 'green' : 'black' }}>Yes</span>
                            </label>

                            <label
                                className={`d-flex align-items-center boxEff ${!formData.mitigativeControls.controlIdentified ? 'boxUnEffective' : ''}`}
                                style={{
                                    backgroundColor: !formData.mitigativeControls.controlIdentified ? '#ff07073d' : 'white',
                                    borderColor: !formData.mitigativeControls.controlIdentified ? 'red' : '#ccc',
                                    cursor: disable ? 'not-allowed' : 'pointer'
                                }}
                                onClick={() => !disable && handleToggle('mitigativeControls', 'controlIdentified', false)}
                            >
                                <RadioButton
                                    value="No"
                                    name="mitigativeControlIdentified"
                                    checked={formData.mitigativeControls.controlIdentified === false}
                                    onChange={() => handleToggle('mitigativeControls', 'controlIdentified', false)}
                                    style={{ display: 'none' }} // Hidden radio button
                                    disabled={disable} // Disable radio button
                                />
                                <span style={{ color: !formData.mitigativeControls.controlIdentified ? 'red' : 'black' }}>No</span>
                            </label>


                        </div>
                    </div>
                </div>

                {formData.mitigativeControls.controlIdentified && (() => {
                    // Check if any control is implemented
                    const anyControlImplemented = formData.mitigativeControls.identifiedMitigativeControls.some(
                        (control) => control.isControlImplemented
                    );

                    return (
                        <>
                            <div className="d-flex flex-column mt-3">
                                {/* Header */}
                                <div className="d-flex font-weight-bold mb-2">
                                    <div className="col-1"></div>
                                    <div className="col-5 px-2">
                                        List each of the Mitigative Controls identified during the Planning / Risk Assessment process to minimize the impact of such an incident?
                                    </div>
                                    <div
                                        className={
                                            anyControlImplemented
                                                ? 'col-3 px-2 '
                                                : 'col-5 px-2 '
                                        }
                                    >
                                        Was the identified control implemented?
                                    </div>
                                    {anyControlImplemented && (
                                        <div className="col-2 px-2 text-center">Was it effective?</div>
                                    )}
                                    <div className="col-1 px-2 text-center"></div>
                                </div>

                                {formData.mitigativeControls.identifiedMitigativeControls.map(
                                    (control, index) => (
                                        <div
                                            className="row mt-4 mb-4 d-flex align-items-stretch"
                                            key={index}
                                        >
                                            <div className="col-1 d-flex align-items-center justify-content-end">
                                                {'MC ' + (index + 1)}
                                            </div>
                                            <div className="col-5 d-flex flex-column justify-content-center">
                                                <InputTextarea
                                                    style={{ width: '100%' }}
                                                    name={`mitigativeControls.identifiedMitigativeControls.${index}.controlStatement`}
                                                    value={control.controlStatement}
                                                    autoResize
                                                    onChange={handleChange}
                                                    disabled={disable} // Disable text area
                                                />
                                            </div>
                                            <div
                                                className={
                                                    anyControlImplemented
                                                        ? 'col-3 d-flex flex-column justify-content-center'
                                                        : 'col-5 d-flex flex-column justify-content-center'
                                                }
                                            >
                                                <div className="d-flex ">

                                                    <label
                                                        className={`d-flex align-items-center me-2 boxEff ${control.isControlImplemented ? 'boxEffective' : ''
                                                            }`}
                                                        style={{
                                                            backgroundColor: control.isControlImplemented
                                                                ? 'lightgreen'
                                                                : 'white',
                                                            borderColor: control.isControlImplemented
                                                                ? 'green'
                                                                : '#ccc',
                                                            cursor: disable ? 'not-allowed' : 'pointer',
                                                        }}
                                                        onClick={() =>
                                                            !disable &&
                                                            handleToggleImplementation(
                                                                'mitigativeControls',
                                                                'identifiedMitigativeControls',
                                                                index,
                                                                true
                                                            )
                                                        }
                                                    >
                                                        <RadioButton
                                                            value="Yes"
                                                            name={`mitigativeImplemented-${index}`}
                                                            checked={control.isControlImplemented === true}
                                                            onChange={() =>
                                                                handleToggleImplementation(
                                                                    'mitigativeControls',
                                                                    'identifiedMitigativeControls',
                                                                    index,
                                                                    true
                                                                )
                                                            }
                                                            style={{ display: 'none' }} // Hidden radio button
                                                            disabled={disable} // Disable radio button
                                                        />
                                                        <span
                                                            style={{
                                                                color: control.isControlImplemented ? 'green' : 'black',
                                                            }}
                                                        >
                                                            Yes
                                                        </span>
                                                    </label>
                                                    <label
                                                        className={`d-flex align-items-center boxEff ${control.isControlImplemented === false
                                                            ? 'boxUnEffective'
                                                            : ''
                                                            }`}
                                                        style={{
                                                            backgroundColor:
                                                                control.isControlImplemented === false
                                                                    ? '#ff07073d'
                                                                    : 'white',
                                                            borderColor:
                                                                control.isControlImplemented === false
                                                                    ? 'rgb(248 0 0)'
                                                                    : '#ccc',
                                                            cursor: disable ? 'not-allowed' : 'pointer',
                                                        }}
                                                        onClick={() =>
                                                            !disable &&
                                                            handleToggleImplementation(
                                                                'mitigativeControls',
                                                                'identifiedMitigativeControls',
                                                                index,
                                                                false
                                                            )
                                                        }
                                                    >
                                                        <RadioButton
                                                            value="No"
                                                            name={`mitigativeImplemented-${index}`}
                                                            checked={control.isControlImplemented === false}
                                                            onChange={() =>
                                                                handleToggleImplementation(
                                                                    'mitigativeControls',
                                                                    'identifiedMitigativeControls',
                                                                    index,
                                                                    false
                                                                )
                                                            }
                                                            style={{ display: 'none' }} // Hidden radio button
                                                            disabled={disable} // Disable radio button
                                                        />
                                                        <span
                                                            style={{
                                                                color:
                                                                    control.isControlImplemented === false
                                                                        ? 'red'
                                                                        : 'black',
                                                            }}
                                                        >
                                                            No
                                                        </span>
                                                    </label>


                                                </div>
                                            </div>

                                            {anyControlImplemented && (
                                                <>
                                                    {control.isControlImplemented ? (
                                                        <div className="col-2 d-flex flex-column justify-content-center">
                                                            <div className="d-flex justify-content-center">
                                                                <label
                                                                    className={`d-flex align-items-center me-1 boxEff ${control.isEffective ? 'boxEffective' : ''
                                                                        }`}
                                                                    style={{
                                                                        backgroundColor: control.isEffective
                                                                            ? 'lightgreen'
                                                                            : 'white',
                                                                        borderColor: control.isEffective
                                                                            ? 'green'
                                                                            : '#ccc',
                                                                        cursor: disable ? 'not-allowed' : 'pointer',
                                                                    }}
                                                                    onClick={() =>
                                                                        !disable &&
                                                                        handleToggleEffectiveness(
                                                                            'mitigativeControls',
                                                                            'identifiedMitigativeControls',
                                                                            index,
                                                                            true
                                                                        )
                                                                    }
                                                                >
                                                                    <RadioButton
                                                                        value="Effective"
                                                                        name={`identifiedMitigativeEffective-${index}`}
                                                                        checked={control.isEffective === true}
                                                                        onChange={() =>
                                                                            handleToggleEffectiveness(
                                                                                'mitigativeControls',
                                                                                'identifiedMitigativeControls',
                                                                                index,
                                                                                true
                                                                            )
                                                                        }
                                                                        style={{ display: 'none' }} // Hidden radio button
                                                                        disabled={disable} // Disable radio button
                                                                    />
                                                                    <span
                                                                        style={{
                                                                            color: control.isEffective ? 'green' : 'black',
                                                                        }}
                                                                    >
                                                                        Effective
                                                                    </span>
                                                                </label>

                                                                <label
                                                                    className={`d-flex align-items-center boxEff ${control.isEffective === false
                                                                        ? 'boxUnEffective'
                                                                        : ''
                                                                        }`}
                                                                    style={{
                                                                        backgroundColor:
                                                                            control.isEffective === false
                                                                                ? '#ff07073d'
                                                                                : 'white',
                                                                        borderColor:
                                                                            control.isEffective === false
                                                                                ? 'rgb(248 0 0)'
                                                                                : '#ccc',
                                                                        cursor: disable ? 'not-allowed' : 'pointer',
                                                                    }}
                                                                    onClick={() =>
                                                                        !disable &&
                                                                        handleToggleEffectiveness(
                                                                            'mitigativeControls',
                                                                            'identifiedMitigativeControls',
                                                                            index,
                                                                            false
                                                                        )
                                                                    }
                                                                >
                                                                    <RadioButton
                                                                        value="NonEffective"
                                                                        name={`identifiedMitigativeNonEffective-${index}`}
                                                                        checked={control.isEffective === false}
                                                                        onChange={() =>
                                                                            handleToggleEffectiveness(
                                                                                'mitigativeControls',
                                                                                'identifiedMitigativeControls',
                                                                                index,
                                                                                false
                                                                            )
                                                                        }
                                                                        style={{ display: 'none' }} // Hidden radio button
                                                                        disabled={disable} // Disable radio button
                                                                    />
                                                                    <span
                                                                        style={{
                                                                            color:
                                                                                control.isEffective === false
                                                                                    ? 'red'
                                                                                    : 'black',
                                                                        }}
                                                                    >
                                                                        Not Effective
                                                                    </span>
                                                                </label>
                                                            </div>
                                                        </div>
                                                    ) : (
                                                        // Maintain column alignment
                                                        <div className="col-2"></div>
                                                    )}
                                                </>
                                            )}

                                            <div className="col-1 d-flex align-items-center justify-content-center text-center">
                                                {!disable && (
                                                    <i
                                                        className="pi pi-trash mb-3"
                                                        onClick={() =>
                                                            handleDeleteIdentifiedMitigativeControl(index)
                                                        }
                                                    ></i>
                                                )}
                                            </div>
                                        </div>
                                    )
                                )}
                            </div>
                            <div className="row">
                                {errors.identifiedMitigativeControls && (
                                    <small className="p-error">{errors.identifiedMitigativeControls}</small>
                                )}
                                <div className="col-1"></div>
                                <div className="col-4">
                                    {!disable && (
                                        <Button
                                            outlined
                                            label="Add More"
                                            onClick={handleAddIdentifiedMitigativeControl}
                                        />
                                    )}
                                </div>
                            </div>
                        </>
                    );
                })()}


                <h4 className="mt-4 mb-4 font-weight-bold">Unidentified Mitigative Controls</h4>

                <div className='row mb-3 mt-4'>

                    <div className='col-6'>
                        <div className=''>Should there have been additional Mitigative Controls identified during the Planning / Risk Assessment process?</div>
                    </div>
                    <div className='col-6'>
                        <div className="d-flex">
                            <label
                                className={`d-flex align-items-center me-2 boxEff ${!formData.mitigativeControls.controlNotIdentified ? 'boxUnEffective' : ''}`}
                                style={{
                                    backgroundColor: !formData.mitigativeControls.controlNotIdentified ? 'lightgreen' : 'white',
                                    borderColor: !formData.mitigativeControls.controlNotIdentified ? 'green' : '#ccc',
                                    cursor: disable ? 'not-allowed' : 'pointer'
                                }}
                                onClick={() => !disable && handleToggle('mitigativeControls', 'controlNotIdentified', false)}
                            >
                                <RadioButton
                                    value="No"
                                    name="controlIdentified"
                                    checked={formData.mitigativeControls.controlIdentified === false}
                                    onChange={() => handleToggle('mitigativeControls', 'controlNotIdentified', false)}
                                    style={{ display: 'none' }} // Hidden radio button
                                    disabled={disable}  // Disable based on the disable prop
                                />
                                <span style={{ color: !formData.mitigativeControls.controlNotIdentified ? 'green' : 'black' }}>No</span>
                            </label>
                            <label
                                className={`d-flex align-items-center  boxEff ${formData.mitigativeControls.controlNotIdentified ? 'boxEffective' : ''}`}
                                style={{
                                    backgroundColor: formData.mitigativeControls.controlNotIdentified ? '#ff07073d' : 'white',
                                    borderColor: formData.mitigativeControls.controlNotIdentified ? 'red' : '#ccc',
                                    cursor: disable ? 'not-allowed' : 'pointer'
                                }}
                                onClick={() => !disable && handleToggle('mitigativeControls', 'controlNotIdentified', true)}
                            >
                                <RadioButton
                                    value="Yes"
                                    name="controlIdentified"
                                    checked={formData.mitigativeControls.controlNotIdentified === true}
                                    onChange={() => handleToggle('mitigativeControls', 'controlNotIdentified', true)}
                                    style={{ display: 'none' }} // Hidden radio button
                                    disabled={disable}  // Disable based on the disable prop
                                />
                                <span style={{ color: formData.mitigativeControls.controlNotIdentified ? 'red' : 'black' }}>Yes</span>
                            </label>


                        </div>
                    </div>
                </div>
                {formData.mitigativeControls.controlNotIdentified && <>

                    <div className="row font-weight-bold mb-2">
                        <div className="col-1"></div>
                        <div className="col-10">In hindsight, what other Mitigative Controls should have been identified, but were overlooked during the Planning / Risk Assessment process ?</div>
                        <div className="col-1"></div>
                    </div>

                    {formData.mitigativeControls.unIdentifiedMitigativeControls.map((control, index) => (
                        <div className="row mt-4 mb-4 align-items-center" key={index}>
                            <div className="col-1 text-end">
                                {'UMC ' + (index + 1)}
                            </div>
                            <div className="col-10">
                                <InputTextarea
                                    style={{ width: '100%' }}
                                    name={`mitigativeControls.unIdentifiedMitigativeControls.${index}.controlStatement`}
                                    value={control.controlStatement}
                                    autoResize
                                    onChange={handleChange}
                                    disabled={disable} // Disable text area
                                />
                            </div>
                            <div className="col-1 text-center">
                                {!disable && <i className="pi pi-trash mb-3" onClick={() => handleDeleteUnidentifiedMitigativeControl(index)}></i>}
                            </div>
                        </div>
                    ))}

                    <div className="row">
                        <div className="col-1"></div>
                        <div className="col-4">
                            {!disable && <Button outlined label="Add More" onClick={handleAddUnidentifiedMitigativeControl} />}
                        </div>
                    </div>
                </>}
            </>) : (
                <div className="col-12">
                    <div htmlFor="workPerformed" className="mb-2">Explain why it was not possible to control the consequences of the incident</div>
                    <InputTextarea
                        style={{ width: '100%' }}
                        name={`mitigativeControls.explainHazardControllable`}
                        value={formData.mitigativeControls.explainHazardControllable}
                        autoResize
                        onChange={handleChange}
                        disabled={disable}  // Disable based on the disable prop
                    />
                </div>

            )}
        </div>
    )
}

export default StageFive;
