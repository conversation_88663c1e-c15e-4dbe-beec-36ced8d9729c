import React, { useState, useEffect, useCallback } from "react";
import { Mo<PERSON>, Button, Form } from 'react-bootstrap';
import Box from '@mui/material/Box';
import Stepper from '@mui/material/Stepper';
import Step from '@mui/material/Step';
import Step<PERSON>abel from '@mui/material/StepLabel';
import StepContent from '@mui/material/StepContent';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import Switch from "react-switch";
import API from "../services/API";
import { AIR_MEDICAL_WITH_ID_URL, AIR_WITH_ID_URL, ALL_LOCATIONTHREE_LOCATIONFOUR_URL, PPES_URL, INJURY_URL, GENERAL_USER_URL, EQUIPMENT_CATEGORIES_URL, GHS_ONE_URL, GHS_TWO_URL, LIGHTING_URL, LOCATION3_URL, SURFACE_CONDITION_URL, SURFACE_TYPE_URL, TIER2_TIER3_URL, WEATHER_CONDITION_URL, WORKING_GROUP_URL, WORK_ACTIVITIES_URL, AIR_MEDICAL_WORK_REPORT_WITH_ID_URL } from "../constants";
import IncidentStory from "./IncidentStory";
import cogoToast from "cogo-toast";
import { DropzoneArea } from 'material-ui-dropzone';
import { BodyComponent } from "reactjs-human-body";
import Select from "react-select";
import DatePicker from "react-datepicker";
import moment from 'moment';
import "react-datepicker/dist/react-datepicker.css";

import { format } from 'date-fns';

const AirMedicalWorkReportCard = ({ showModal, setShowModal, data }) => {


    function initializeDateWithOffset(date) {
        return moment(date).utcOffset('+0530');
    }
    const [generalUsers, setGeneralUsers] = useState([]);
    const getGeneralUsers = useCallback(async () => {
        const response = await API.get(GENERAL_USER_URL);
        if (response.status === 200) {
            setGeneralUsers(response.data);
        }
    }, []);
    useEffect(() => {

        getGeneralUsers();

    }, [getGeneralUsers]);



    const [formData, setFormData] = useState({
        description: '',
        timeOfFirstAid: initializeDateWithOffset(new Date()),
        vmo: false,
        ambulance: false,
        nhs: false,
        departureFromTerminal: initializeDateWithOffset(new Date()),
        admissionAtNhs: initializeDateWithOffset(new Date()),
        treatment: '',
        otherComments: ''
    });


    const [witnessInvolved, setWitnessInvolved] = useState({
        witnessInvolved: [
            {
                "internal": true,
                "selectedEmp": {

                },
                "name": "",
                "empId": "",
                "designation": "",
                "comments": ""
            }
        ],
        personInvolved: [
            {
                "name": "",
                "empId": "",
                "designation": "",
                "comments": "",
                "internal": true,
                "selectedEmp": {
                },
                "injured": false,
                "injuryParts": [],
                "injuryDetails": [],
                "isPPE": false,
                "ppes": [],
                "backToWorkDate": null,
                "medicalLeaveFromDate": null,
                "medicalLeaveToDate": null,
                "estimatedLossWorkingDays": 0,
                "medicalReportFiles": [],
                "vmoComments": ""
            }
        ],
        personnelImpacted: [
            {
                "name": "",
                "empId": "",
                "designation": "",
                "comments": "",
                "internal": true,
                "selectedEmp": {
                },
                "injured": false,
                "injuryParts": [],
                "injuryDetails": [],
                "isPPE": false,
                "ppes": [],
                "backToWorkDate": null,
                "medicalLeaveFromDate": null,
                "medicalLeaveToDate": null,
                "estimatedLossWorkingDays": 0,
                "medicalReportFiles": [],
                "vmoComments": ""
            }
        ]

    })


    useEffect(() => {
        if (data) {


            setWitnessInvolved({ witnessInvolved: data.witnessInvolved, personnelImpacted: data.personnelImpacted, personInvolved: data.personInvolved })

        }
    }, [data]);


    const [files, setFiles] = useState([]);

    const handleFileChange = (file) => {
        setFiles(file)

    }
    const handlePersonImpactedChange = (index, field, value, flag) => {
        let newValue = value;

        if (flag && field === 'selectedEmp') {
            const data = generalUsers.find(i => i.id === value)

            newValue = {
                "name": "",
                "empId": "",
                "designation": "",
                "comments": "",
                "internal": true,
                "selectedEmp": data,
                "injured": false,
                "injuryParts": [],
                "injuryDetails": [],
                "isPPE": false,
                "ppes": [],
                "backToWorkDate": null,
                "medicalLeaveFromDate": null,
                "medicalLeaveToDate": null,
                "estimatedLossWorkingDays": 0,
                "medicalReportFiles": [],
                "vmoComments": ""
            }
            setWitnessInvolved(prevState => {
                const updatedActions = [...prevState.personInvolved];
                updatedActions[index] = newValue;

                return { ...prevState, personInvolved: updatedActions };
            });
        } else {
            setWitnessInvolved(prevState => {
                const updatedActions = [...prevState.personInvolved];
                updatedActions[index][field] = newValue;

                return { ...prevState, personInvolved: updatedActions };
            });
        }

    };

    const handlePersonnelInvolvedChange = (index, field, value, flag) => {
        let newValue = value;

        if (flag && field === 'selectedEmp') {
            const data = generalUsers.find(i => i.id === value)

            newValue = {
                "name": "",
                "empId": "",
                "designation": "",
                "comments": "",
                "internal": true,
                "selectedEmp": data,
                "injured": false,
                "injuryParts": [],
                "injuryDetails": [],
                "isPPE": false,
                "ppes": [],
                "backToWorkDate": null,
                "medicalLeaveFromDate": null,
                "medicalLeaveToDate": null,
                "estimatedLossWorkingDays": 0,
                "medicalReportFiles": [],
                "vmoComments": ""
            }
            setWitnessInvolved(prevState => {
                const updatedActions = [...prevState.personnelImpacted];
                updatedActions[index] = newValue;

                return { ...prevState, personnelImpacted: updatedActions };
            });
        } else {
            setWitnessInvolved(prevState => {
                const updatedActions = [...prevState.personnelImpacted];
                updatedActions[index][field] = newValue;

                return { ...prevState, personnelImpacted: updatedActions };
            });
        }

    };


    const [isClicked, setIsClicked] = useState(false);
    const handleSubmit = async () => {
        setIsClicked(true);
        try {
            // Patch Request to AIR_WITH_ID_URL
            const response = await API.patch(AIR_MEDICAL_WORK_REPORT_WITH_ID_URL(data.id, data.actionId), {

                witnessInvolved: witnessInvolved.witnessInvolved,
                personInvolved: witnessInvolved.personInvolved,
                personnelImpacted: witnessInvolved.personnelImpacted
            });

            // If the patch request fails, no need to proceed further
            if (response.status !== 204) {
                setShowModal(false)
                console.error('Failed to patch data. Status:', response.status);
                return;  // or handle this error appropriately
            }

            // Sending POST requests for each driver using for...of loop to ensure each request completes before the next

            cogoToast.success(`Action for IR ${data.maskId} Completed`)
            // Proceed to the next step
            setShowModal(false)
            setIsClicked(false);

        } catch (error) {
            setIsClicked(false);
            console.error('An error occurred:', error);

        }
        setIsClicked(false);
    };



    const personInvolvedList = (persons) => {
        const filteredPersons = persons.filter(person => person && person.selectedEmp && person.selectedEmp.name);
        const externalPersons = persons.filter(person => !person.internal)
        return (
            <>
                {(filteredPersons.length > 0 || externalPersons.length > 0) && <div>
                    {filteredPersons.map((person, index) => {


                        return (

                            <>
                                <p>{index + 1}. {person.selectedEmp.name} - {person.selectedEmp.designation}</p>
                                <div className="col-md-12">
                                    <label>Back to Work Date</label>
                                    <DatePicker

                                        className="form-control w-25"
                                        selected={person.backToWorkDate}
                                        onChange={(date) => handlePersonImpactedChange(index, 'backToWorkDate', date, false)}
                                    />
                                </div>
                                <div className="col-md-6">
                                    <label>Medical Leave From Date</label>
                                    <DatePicker
                                        className="form-control w-50"
                                        selected={person.medicalLeaveFromDate}
                                        onChange={(date) => handlePersonImpactedChange(index, 'medicalLeaveFromDate', date)}
                                    />
                                </div>
                                <div className="col-md-6">
                                    <label>Medical Leave To Date</label>
                                    <DatePicker
                                        className="form-control w-50"
                                        selected={person.medicalLeaveToDate}
                                        onChange={(date) => handlePersonImpactedChange(index, 'medicalLeaveToDate', date)}
                                    />
                                </div>
                                <div className="col-md-12">
                                    <label>Estimated Loss of Working Days</label>
                                    <input
                                        className="form-control w-25"
                                        type="number"
                                        min={0}
                                        value={person.estimatedLossWorkingDays}
                                        onChange={(e) => handlePersonImpactedChange(index, 'estimatedLossWorkingDays', e.target.value)}
                                    />
                                </div>
                                <div className="col-md-12">
                                    <label>Upload Medical Reports</label>
                                    <DropzoneArea
                                        onChange={(files) => handlePersonImpactedChange(index, 'medicalReportFiles', files)}
                                    />
                                </div>
                                <div className="col-md-12">
                                    <label>VMO Comments</label>
                                    <textarea

                                        value={person.vmoComments}
                                        className="form-control"
                                        onChange={(e) => handlePersonImpactedChange(index, 'vmoComments', e.target.value)}
                                    >
                                    </textarea>
                                </div>


                            </>

                        )
                    })}


                    {externalPersons.map((person, index) => {



                        return (
                            <>
                                <p>{index + 1}. {person.name} - {person.designation}</p>
                                <div className="col-md-12">
                                    <label>Back to Work Date</label>
                                    <DatePicker

                                        className="form-control w-25"
                                        selected={person.backToWorkDate}
                                        onChange={(date) => handlePersonImpactedChange(index, 'backToWorkDate', date, false)}
                                    />
                                </div>
                                <div className="col-md-6">
                                    <label>Medical Leave From Date</label>
                                    <DatePicker
                                        className="form-control w-50"
                                        selected={person.medicalLeaveFromDate}
                                        onChange={(date) => handlePersonImpactedChange(index, 'medicalLeaveFromDate', date)}
                                    />
                                </div>
                                <div className="col-md-6">
                                    <label>Medical Leave To Date</label>
                                    <DatePicker
                                        className="form-control w-50"
                                        selected={person.medicalLeaveToDate}
                                        onChange={(date) => handlePersonImpactedChange(index, 'medicalLeaveToDate', date)}
                                    />
                                </div>
                                <div className="col-md-12">
                                    <label>Estimated Loss of Working Days</label>
                                    <input
                                        className="form-control w-25"
                                        type="number"
                                        min={0}
                                        value={person.estimatedLossWorkingDays}
                                        onChange={(e) => handlePersonImpactedChange(index, 'estimatedLossWorkingDays', e.target.value)}
                                    />
                                </div>
                                <div className="col-md-12">
                                    <label>Upload Medical Reports</label>
                                    <DropzoneArea
                                        onChange={(files) => handlePersonImpactedChange(index, 'medicalReportFiles', files)}
                                    />
                                </div>
                                <div className="col-md-12">
                                    <label>VMO Comments</label>

                                    <textarea

                                        value={person.vmoComments}
                                        className="form-control"
                                        onChange={(e) => handlePersonImpactedChange(index, 'vmoComments', e.target.value)}
                                    >
                                    </textarea>
                                </div>
                            </>
                        )
                    })}
                </div>
                }

                {
                    (!filteredPersons.length > 0 && !externalPersons.length > 0) && <p className="text-center">No Person Involved</p>
                }

            </>
        );
    };




    const personnelImpactedList = (persons) => {
        const filteredPersons = persons.filter(person => person && person.selectedEmp && person.selectedEmp.name);
        const externalPersons = persons.filter(person => !person.internal)


        return (
            <>


                {(filteredPersons.length > 0 || externalPersons.length > 0) &&
                    <div>
                        {filteredPersons.map((person, index) => {


                            return (
                                <>
                                    <p>{index + 1}. {person.selectedEmp.name} - {person.selectedEmp.designation}</p>
                                    <div className="col-md-12">
                                        <label>Back to Work Date</label>
                                        <DatePicker


                                            className="form-control w-25"
                                            selected={person.backToWorkDate}
                                            onChange={(date) => handlePersonnelInvolvedChange(index, 'backToWorkDate', date, false)}
                                        />
                                    </div>
                                    <div className="col-md-6">
                                        <label>Medical Leave From Date</label>
                                        <DatePicker
                                            className="form-control w-50"
                                            selected={person.medicalLeaveFromDate}
                                            onChange={(date) => handlePersonnelInvolvedChange(index, 'medicalLeaveFromDate', date)}
                                        />
                                    </div>
                                    <div className="col-md-6">
                                        <label>Medical Leave To Date</label>
                                        <DatePicker
                                            className="form-control w-50"
                                            selected={person.medicalLeaveToDate}
                                            onChange={(date) => handlePersonnelInvolvedChange(index, 'medicalLeaveToDate', date)}
                                        />
                                    </div>
                                    <div className="col-md-12">
                                        <label>Estimated Loss of Working Days</label>
                                        <input
                                            className="form-control w-25"
                                            type="number"
                                            min={0}
                                            value={person.estimatedLossWorkingDays}
                                            onChange={(e) => handlePersonnelInvolvedChange(index, 'estimatedLossWorkingDays', e.target.value)}
                                        />
                                    </div>

                                    <div className="col-md-12">
                                        <label>Upload Medical Reports</label>
                                        <DropzoneArea
                                            onChange={(files) => handlePersonnelInvolvedChange(index, 'medicalReportFiles', files)}
                                        />
                                    </div>
                                    <div className="col-md-12">
                                        <label>VMO Comments</label>

                                        <textarea

                                            value={person.vmoComments}
                                            className="form-control"
                                            onChange={(e) => handlePersonnelInvolvedChange(index, 'vmoComments', e.target.value)}
                                        >
                                        </textarea>
                                    </div>
                                </>
                            )
                        })}

                        {externalPersons.map((person, index) => {



                            return (

                                <>
                                    <p>{index + 1}. {person.name}</p>
                                    <div className="col-md-12">
                                        <label>Back to Work Date</label>
                                        <DatePicker


                                            className="form-control w-25"
                                            selected={person.backToWorkDate}
                                            onChange={(date) => handlePersonnelInvolvedChange(index, 'backToWorkDate', date, false)}
                                        />
                                    </div>
                                    <div className="col-md-6">
                                        <label>Medical Leave From Date</label>
                                        <DatePicker
                                            className="form-control w-50"
                                            selected={person.medicalLeaveFromDate}
                                            onChange={(date) => handlePersonnelInvolvedChange(index, 'medicalLeaveFromDate', date)}
                                        />
                                    </div>
                                    <div className="col-md-6">
                                        <label>Medical Leave To Date</label>
                                        <DatePicker
                                            className="form-control w-50"
                                            selected={person.medicalLeaveToDate}
                                            onChange={(date) => handlePersonnelInvolvedChange(index, 'medicalLeaveToDate', date)}
                                        />
                                    </div>
                                    <div className="col-md-12">
                                        <label>Estimated Loss of Working Days</label>
                                        <input
                                            className="form-control w-25"
                                            type="number"
                                            min={0}
                                            value={person.estimatedLossWorkingDays}
                                            onChange={(e) => handlePersonnelInvolvedChange(index, 'estimatedLossWorkingDays', e.target.value)}
                                        />
                                    </div>
                                    <div className="col-md-12">
                                        <label>Upload Medical Reports</label>
                                        <DropzoneArea
                                            onChange={(files) => handlePersonnelInvolvedChange(index, 'medicalReportFiles', files)}
                                        />
                                    </div>
                                    <div className="col-md-12">
                                        <label>VMO Comments</label>
                                        
                                        <textarea

                                            value={person.vmoComments}
                                            className="form-control"
                                            onChange={(e) => handlePersonnelInvolvedChange(index, 'vmoComments', e.target.value)}
                                        >
                                        </textarea>
                                    </div>
                                </>
                            )
                        })}


                    </div>
                }

                {
                    (!filteredPersons.length > 0 && !externalPersons.length > 0) && <p className="text-center">No Personnel Injured</p>
                }

            </>
        );
    };



    return (
        <>
            {data && <Modal
                show={showModal}
                size="md"
                onHide={() => setShowModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
                backdrop="static"
            >
                <Modal.Header>
                    <div className="w-100 d-flex align-items-center justify-content-between">
                        <h4 >
                            IR Information - {data.maskId}
                        </h4 >
                        <h4 >
                            Incident Date & Time: {data.incidentDate}
                        </h4 >
                    </div>

                </Modal.Header>

                <Modal.Body>
                    <div className="row">
                        {/* <div className="col-md-6">
                            <IncidentStory data={data} />
                        </div> */}

                        <Form>


                            <div className="row">
                                <label className="col-sm-12 col-form-label">Person(s) Involved</label>
                                {data.personInvolved && (


                                    <div>{personInvolvedList(data.personInvolved)}</div>

                                )}
                            </div>

                            <div className="row">
                                <label className="col-sm-12 col-form-label">Personnel Injured</label>
                                {data.personnelImpacted && (


                                    <div>{personnelImpactedList(data.personnelImpacted)}</div>

                                )}
                            </div>


                            <Button variant="primary" onClick={handleSubmit} disabled={isClicked}>Submit</Button>

                        </Form>
                    </div>



                </Modal.Body>

                <Modal.Footer className="flex-wrap">

                    <Button variant="light" onClick={() => { setShowModal(false); }}>Close</Button>

                </Modal.Footer>
            </Modal>}
        </>
    )
}

export default AirMedicalWorkReportCard;