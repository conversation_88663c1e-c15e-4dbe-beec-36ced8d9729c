import React, { useEffect, useState } from 'react'
import $ from "jquery";
import API from '../services/API';
import { useHistory } from "react-router-dom";
import { AIR_URL, AIR_WITH_ID_URL, ACTION_URL, STATIC_URL, API_URL, UPDATE_TRUCK_STATUS_WITH_ID_URL, AIR_REVIEW_URL, AIR_INVESTIGATE_URL, REVIEWER_AIR_URL, GET_USER_BY_ROLES_URL, AIR_INITIATE_INVESTIGATION_WITH_ID_URL } from '../constants';
import { Modal, Button, Form } from 'react-bootstrap';
import { DropzoneArea } from 'material-ui-dropzone';
import MaterialTable from 'material-table';
import { ThemeProvider, createTheme, Box, Tabs, Tab } from '@mui/material';
import { FilterMatchMode, FilterOperator } from 'primereact/api';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { InputText } from 'primereact/inputtext';
import { MultiSelect } from 'primereact/multiselect';
import { Calendar } from 'primereact/calendar';
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";

import axios from 'axios';
import AirReviewerCard from './AirReviewerCard';
import AirInvestigationCard from './AirInvestigationCard';
import AirViewCard from './AirViewCard';
import Action from './Action';
import cogoToast from 'cogo-toast';
import AirTriggerInvestigationCard from './AirTriggerInvestigationCard';
import moment from 'moment';
import Select from 'react-select'
import AirReviewerEditCard from './AirReviewerEditCard';
import AirDocumentCard from './AirDocumentCard';
// @ts-ignore
window.jQuery = $;
// @ts-ignore
window.$ = $;

function TabPanel(props) {
    const { children, value, index, ...other } = props;

    // Only render the children if this panel's index matches the current value
    if (value !== index) return null;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`tabpanel-${index}`}
            aria-labelledby={`tab-${index}`}
            {...other}
        >
            <Box sx={{ p: 3 }}>
                {children}
            </Box>
        </div>
    );
}

const IncidentTable = ({ stage, mode }) => {


    const [investigationDate, setInvestigationDate] = useState('');
    const handleUserSelectChange = (selectedOptions) => {
        setSelectedUsers(selectedOptions);
    };

    const [selectedUsers, setSelectedUsers] = useState([]);
    const [value, setValue] = useState(0);
    const [pendingUploads, setPendingUploads] = useState([false, false, false]); // Tracks pending uploads for each tab
    const [showAlert, setShowAlert] = useState(false);  // State to control the visibility of the alert
    const [investigationDocuments, setInvestigationDocuments] = useState([]);
    const [surveyorDocuments, setSurveyorDocuments] = useState([]);
    const [receiptDocuments, setReceiptDocuments] = useState([]);
    const [amendShowModal, setAmendShowModal] = useState(false);
    const [data, setData] = useState([]);
    const [incidentData, setIncidentData] = useState({});
    const [modalState, setModalState] = useState({ showModal: false, triggerInvestigationModal: false, actionModal: false, truckModal: false });
    const [airId, setAirId] = useState('');
    const [onSaved, setOnSaved] = useState(false)
    const [filters, setFilters] = useState({
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        maskId: { value: null, matchMode: FilterMatchMode.IN },
        status: { value: null, matchMode: FilterMatchMode.IN },
        'reporter.firstName': { value: null, matchMode: FilterMatchMode.IN },
    });


    const tableActions = [

        // {
        //     icon: 'history',
        //     tooltip: 'Actions',
        //     onClick: (event, rowData) => {
        //         // Do save operation
        //         // console.log(rowData)
        //         viewActions(rowData.id)
        //     }
        // },


    ]
    const getAirData = async () => {
        try {
            const uriString = { include: ['reporter'] };
            let mainURL = AIR_URL;
            switch (stage) {
                case 'review': mainURL = AIR_REVIEW_URL; break;
                case 'classified_documents': mainURL = REVIEWER_AIR_URL; break;
            }

            const url = `${mainURL}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
            const response = await API.get(url);
            if (response.status === 200) {

                setData(response.data.filter(data => data.stage === stage));

            }
        } catch (error) {
            console.error("Error fetching data:", error);
        }
    };
    useEffect(() => {

        getAirData();
    }, [stage]);

    useEffect(() => {
        if (onSaved) {
            const getAirData = async () => {
                try {
                    const uriString = { include: ['reporter'] };
                    let mainURL = AIR_URL;
                    switch (stage) {
                        case 'review': mainURL = AIR_REVIEW_URL; break;
                        case 'classified_documents': mainURL = REVIEWER_AIR_URL; break;
                        case 'trigger_investigation': mainURL = AIR_INVESTIGATE_URL; break;
                    }

                    const url = `${mainURL}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
                    const response = await API.get(url);
                    if (response.status === 200) {
                        setData(response.data.filter(data => data.stage === stage));
                    }
                } catch (error) {
                    console.error("Error fetching data:", error);
                }
            };
            getAirData();
            setOnSaved(false)
        }
    }, [onSaved])
    const handleModalOpen = (type, id = '') => {
        setModalState({ ...modalState, [type]: true });
        setAirId(id);
    };

    const handleModalClose = (type) => {
        setModalState({ ...modalState, [type]: false });
    };

    switch (stage) {
        case 'review':
            tableActions.push({
                icon: 'edit',
                tooltip: 'Review',
                onClick: (event, rowData) => {
                    // Do save operation
                    // console.log(rowData)
                    viewAir(rowData.id)
                }
            }, {
                icon: 'block',
                tooltip: 'Truck Status',
                onClick: (event, rowData) => {
                    // Do save operation
                    // console.log(rowData)
                    viewTruckStatus(rowData.id)
                }
            }); break;
        case 'trigger_investigation':

            if (mode && mode === 'reviewer') {
                tableActions.push({
                    icon: 'edit',
                    tooltip: 'Withdraw and Amend',
                    onClick: (event, rowData) => {
                        // Do save operation
                        // console.log(rowData)
                        viewAir(rowData.id)
                    }
                })
            } else {
                tableActions.push({
                    icon: 'edit',
                    tooltip: 'Trigger Investigation',
                    onClick: (event, rowData) => {
                        // Do save operation
                        // console.log(rowData)
                        triggerAir(rowData.id)
                    }
                })
            }


            break;
        case 'classified_documents':

            if (mode && mode === 'reviewer') {
                tableActions.push({
                    icon: 'edit',
                    tooltip: 'Withdraw and Amend',
                    onClick: (event, rowData) => {
                        // Do save operation
                        // console.log(rowData)
                        viewAir(rowData.id)
                    }
                })
            }
            break;
        default: break;
    }




    const tableStyle = {
        borderRadius: '0',
        boxShadow: 'none',
    };

    const [showModal, setShowModal] = useState(false)
    const [triggerInvestigationModal, setTriggerInvestigationModal] = useState(false)


    const viewAir = async (id) => {

        getReportIncident(id);

    }

    const triggerAir = async (id) => {

        getReportIncident(id);

    }


    const [actionModal, setActionModal] = useState(false)

    const viewActions = async (id) => {
        setAirId(id)
        setActionModal(true)

    }


    const [truckModal, setTruckModal] = useState(false)
    const viewTruckStatus = async (id) => {
        setAirId(id)
        setTruckModal(true)

    }

    const [documentModal, setDocumentModal] = useState(false)
    const [airDocuments, setAirDocuments] = useState('')
    const viewDocument = async (id, data, investigationDocuments, receiptDocuments, surveyorDocuments) => {
        setAirId(id)
        setAirDocuments(data)
        setInvestigationDocuments(investigationDocuments ? investigationDocuments : [])
        setReceiptDocuments(receiptDocuments ? receiptDocuments : [])
        setSurveyorDocuments(surveyorDocuments ? surveyorDocuments : [])
        setDocumentModal(true)

    }
    useEffect(() => {
        if (airId) {
            getReportIncident(airId)
        }

    }, [airId, documentModal])
    const getReportIncident = async (id) => {

        const uriString = {
            include: [
                'locationOne',
                'locationTwo',
                'locationThree',
                'locationFour',
                'locationFive',
                'locationSix',
                'lighting',
                'surfaceCondition',
                'surfaceType',
                'workActivityDepartment',
                'workActivity',
                'reporter',
                'workingGroup',
                'weatherCondition',
                'reviewer',
                'drivers',
                'surveyor',
                'estimator',
                'trainee',
                'gmOps',
                'thirdParty',
                'security',
                'costReviewer',
                'financer',
                'dutyEngManager',
                'medicalOfficer',
                'incidentTypeName'
            ]
        };

        const url = `${AIR_WITH_ID_URL(id)}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;

        const response = await API.get(url);
        if (response.status === 200) {

            const data = response.data;
            response.data.evidence = response.data.evidence ? response.data.evidence.map(i => {
                return { src: `${STATIC_URL}/${i}`, width: 4, height: 3 }
            }) : []
            setIncidentData(data)


        }
    }
    useEffect(() => {
        if (incidentData && Object.keys(incidentData).length > 0) {
            if (stage === 'review') {
                setShowModal(true);
            } else if (stage === 'trigger_investigation') {
                if (mode && mode === 'reviewer') {
                    setShowModal(true);
                } else {
                    setTriggerInvestigationModal(true);
                }

            }
            else if (stage === 'classified_documents') {
                if (mode && mode === 'reviewer') {
                    setAmendShowModal(true);
                }

            }
        }
    }, [incidentData, stage]);

    const handleChange = (event, newValue) => {
        if (pendingUploads[value]) {
            cogoToast.error("Please click the upload button before you change the tab if you need to upload any file.", { position: 'top-center' });
            return;  // Prevent tab change
        }
        setValue(newValue);
        setShowAlert(false);  // Hide alert when successfully changing tabs
    };

    const [files, setFiles] = useState([]);
    const [type, setType] = useState('')
    const handleFileChange = (file, type) => {
        setFiles(file);
        setType(type);
        const tabIndex = { 'investigation': 0, 'receipts': 1, 'surveyor': 2 }[type];
        setPendingUploads(prev => {
            const newPending = [...prev];
            newPending[tabIndex] = file.length > 0;  // Update pending status based on files present
            return newPending;
        });
    };

    const requestWithdraw = async (id) => {
        const response = await API.patch(AIR_WITH_ID_URL(id), { withdrawRequested: 'pending' });
        if (response.status === 204) {
            cogoToast.success('Withdraw Request Sent!')
            getAirData()
        }

    }

    const releaseTruck = async () => {
        try {

            const formData = new FormData();
            files.forEach((file, index) => {
                formData.append('file', file);
            });
            const token = localStorage.getItem('access_token');
            const fileResponse = await axios.post(`${API_URL}/files`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                    'Authorization': `Bearer ${token}`,
                },
            });

            if (fileResponse.status === 200) {
                const originalNames = fileResponse.data.files.map(file => file.originalname);
                const response = await API.patch(UPDATE_TRUCK_STATUS_WITH_ID_URL(airId), {
                    truckStatus: true,
                    truckDocuments: originalNames,
                    cllInvolved: false
                })

                if (response.status === 204) {
                    setTruckModal(false);
                    cogoToast.success('Truck Released and Notified Custodian')
                }
            }
        }
        catch (e) {
            console.log(e)
        }
    }


    const uploadDocuments = async () => {
        try {

            let otherFiles = [];
            let payloadKey = ''; // This will store the key for the payload
            let payload = {}
            let uploadDocuments = [...airDocuments];
            let uploadInvestigationDocuments = [...investigationDocuments];
            let uploadReceiptDocuments = [...receiptDocuments];
            let uploadSurveyorDocuments = [...surveyorDocuments];
            // Select files and payload key based on document type

            const formData = new FormData();
            files.forEach((file, index) => {
                formData.append('file', file);
            });
            const token = localStorage.getItem('access_token');
            const fileResponse = await axios.post(`${API_URL}/files`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                    'Authorization': `Bearer ${token}`,
                },
            });

            if (fileResponse.status === 200) {
                const originalNames = fileResponse.data.files.map(file => file.originalname);
                switch (type) {
                    case 'investigation':
                        uploadDocuments = [...airDocuments, ...originalNames]
                        uploadInvestigationDocuments = [...uploadInvestigationDocuments, ...originalNames]
                        break;
                    case 'receipts':
                        uploadReceiptDocuments = [...uploadReceiptDocuments, ...originalNames]
                        break;
                    case 'surveyor':
                        uploadSurveyorDocuments = [...uploadSurveyorDocuments, ...originalNames]
                        break;
                    default:
                        console.error("Unknown document type");
                        return;
                }
                const response = await API.patch(UPDATE_TRUCK_STATUS_WITH_ID_URL(airId), {
                    documents: uploadDocuments,
                    investigationDocuments: uploadInvestigationDocuments,
                    receiptDocuments: uploadReceiptDocuments,
                    surveyorDocuments: uploadSurveyorDocuments


                })

                if (response.status === 204) {
                    cogoToast.success('Document Uploaded')
                    getAirData()
                    setDocumentModal(false)
                }
            }
        }
        catch (e) {
            console.log(e)
        }
    }

    useEffect(() => {
        getInvestigators()
    }, [])
    const [investigators, setInvestigators] = useState([])
    const getInvestigators = async () => {
        const supervisors = await API.post(GET_USER_BY_ROLES_URL, { locationOneId: '', locationTwoId: '', locationThreeId: '', locationFourId: '', mode: 'ir-investigator' });
        setInvestigators(supervisors.data);
    }
    const [investigateModal, setInvestigateModal] = useState(false);

    const initiateInvestigation = async () => {

        const response = await API.patch(AIR_INITIATE_INVESTIGATION_WITH_ID_URL(airId), {
            investigationDate: investigationDate,
            investigation: {
                investigationTeam: selectedUsers
            },
        })

        if (response.status === 204) {
            cogoToast.success('Investigation Initiated!')
            getAirData()
            setInvestigateModal(false)
            setDocumentModal(false)
        }

    }
    const actionBodyTemplate = (row) => {
        if (stage === 'review') {
            return (
                <div className="table-action d-flex ">

                    <i className="mdi mdi-lead-pencil" onClick={() => viewAir(row.id)}></i>
                    {/*  */}


                </div>
            )
        }
        else if (stage === 'classified_documents' && mode === 'reviewer') {

            if (!row.withdrawRequested || row.withdrawRequested === '') {
                // If withdrawRequested is not present or empty, show a specific icon for requesting withdraw
                return (
                    <div className="table-action d-flex">
                        <i className="mdi mdi-send" onClick={() => requestWithdraw(row.id)}></i>
                    </div>
                );
            } else if (row.withdrawRequested === 'pending') {
                // If withdrawRequested is pending, show request sent text
                return (
                    <div className="table-action d-flex">
                        Request Sent
                    </div>
                );
            } else if (row.withdrawRequested === 'approved') {
                // If withdrawRequested is approved, show the pencil icon
                return (
                    <div className="table-action d-flex">
                        <i className="mdi mdi-lead-pencil" onClick={() => viewAir(row.id)}></i>
                    </div>
                );
            }

        }
        else if (stage === 'classified_documents') {

            return (<div className="table-action d-flex ">
                <i className="mdi mdi-file-upload" onClick={() => viewDocument(row.id, row.documents, row.investigationDocuments, row.receiptDocuments, row.surveyorDocuments)}></i>
                {row.cllInvolved && <i className="mdi mdi-block-helper" onClick={() => viewTruckStatus(row.id)}></i>}
            </div>)

        }

        else if (stage === 'trigger_investigation') {
            if (mode && mode === 'reviewer') {
                return (
                    <div className="table-action d-flex ">


                        <i className="mdi mdi-lead-pencil" onClick={() => viewAir(row.id)}></i>


                    </div>
                )
            }

            else {
                return (
                    <div className="table-action d-flex ">


                        <i className="mdi mdi-lead-pencil" onClick={() => triggerAir(row.id)}></i>


                    </div>
                )
            }
        }
    }
    const idBodyTemplate = (row) => {
        return <div className={`maskid ${row.created && moment().diff(moment(row.created, "DD-MM-YYYY HH:mm"), 'hours') > 12 && row.status === 'Stage I: Preliminary Notification' ? 'red' : ''}`} onClick={() => viewAir(row.id)}>{row.maskId}</div>;
    }

    const renderHeader = () => {
        const value = filters['global'] ? filters['global'].value : '';

        return (
            <div className='d-flex justify-content-end'>
                <span className="p-input-icon-left">
                    <i className="fa fa-search" />
                    <InputText type="search" value={value || ''} onChange={(e) => onGlobalFilterChange(e)} placeholder="Global Search" />
                </span>
            </div>
        );
    };

    const header = renderHeader();

    const onGlobalFilterChange = (event) => {
        const value = event.target.value;
        let _filters = { ...filters };

        _filters['global'].value = value;

        setFilters(_filters);
    };

    return (
        <>


            <div>

                {/* <ThemeProvider theme={defaultMaterialTheme}>
                    <MaterialTable
                        columns={incidentColumns}
                        data={data}
                        title="IR Reports"
                        style={tableStyle}
                        actions={tableActions}
                        options={tableOptions}


                    />
                </ThemeProvider> */}

                <DataTable value={data} paginator rows={10} header={header} filters={filters} onFilter={(e) => setFilters(e.filters)}
                    emptyMessage="No Data found." tableStyle={{ minWidth: '50rem' }} sortField="maskId" sortOrder="-1">

                    <Column field="maskId" header="ID" body={idBodyTemplate} style={{ width: '22%' }} sortable  ></Column>

                    <Column field="description" header="Description" sortable style={{ width: '25%' }} ></Column>

                    <Column field='reporter.firstName' header="Reported By" sortable filter ></Column>

                    <Column field='status' header="Status" sortable filter ></Column>

                    <Column header="Action" body={actionBodyTemplate} ></Column>

                </DataTable>

            </div>

            {amendShowModal && <AirReviewerEditCard showModal={amendShowModal} setShowModal={setAmendShowModal} data={incidentData} />}

            {showModal && <AirReviewerCard showModal={showModal} setShowModal={setShowModal} data={incidentData} setData={setIncidentData} setOnSaved={setOnSaved} />}
            {triggerInvestigationModal && <AirTriggerInvestigationCard showModal={triggerInvestigationModal} setShowModal={setTriggerInvestigationModal} incidentData={incidentData} />}


            {(airId && actionModal) && <Modal
                show={actionModal}
                size="md"
                onHide={() => setActionModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
                backdrop="static"
            >
                <Modal.Header>
                    Actions
                </Modal.Header>

                <Modal.Body>
                    <div className="row">
                        <div className="col-md-12">
                            <Action application={'AIR'} id={airId} />
                        </div>

                    </div>



                </Modal.Body>

                <Modal.Footer className="flex-wrap">

                    <Button variant="light" onClick={() => { setActionModal(false); }}>Close</Button>

                </Modal.Footer>
            </Modal>}


            {(airId && truckModal) && <Modal
                show={truckModal}
                size="md"
                onHide={() => setTruckModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
                backdrop="static"
            >
                <Modal.Header>
                    Release Truck
                </Modal.Header>

                <Modal.Body>
                    <div className="row">
                        <div className="col-md-12">
                            <DropzoneArea
                                acceptedFiles={[
                                    'application/pdf',
                                    'image/jpeg',
                                    'image/png'

                                ]}
                                dropzoneText={"Drag and drop Receipt"}
                                filesLimit={5}
                                maxFileSize={104857600}
                                onChange={(files) => handleFileChange(files, 'truckStatus')}
                            />
                        </div>

                    </div>



                </Modal.Body>

                <Modal.Footer className="flex-wrap">
                    <Button variant="primary" onClick={() => { releaseTruck() }}>Release Truck</Button>
                    <Button variant="light" onClick={() => { setTruckModal(false); }}>Close</Button>

                </Modal.Footer>
            </Modal>}


            {(airId && documentModal) &&
                <>
                    <Modal
                        show={documentModal}
                        onHide={() => setDocumentModal(false)}
                        size="md"
                        aria-labelledby="example-modal-sizes-title-md"
                        backdrop="static"
                    >
                        <Modal.Header closeButton>
                            Upload Classified Documents
                        </Modal.Header>
                        <Modal.Body>
                            <Box sx={{ width: '100%' }}>
                                <Tabs value={value} onChange={handleChange} aria-label="document tabs">
                                    <Tab label="Investigation Documents" />
                                    <Tab label="Receipts" />
                                    <Tab label="Survey Report" />
                                </Tabs>
                                <TabPanel value={value} index={0}>
                                    {/* Investigation Documents DropzoneArea */}
                                    <DropzoneArea
                                        acceptedFiles={['application/pdf', 'image/jpeg', 'image/png', 'video/mp4']}
                                        dropzoneText={"Drag and drop files here or click"}
                                        filesLimit={5}
                                        maxFileSize={104857600}
                                        onChange={(files) => handleFileChange(files, 'investigation')}
                                    />
                                    <AirDocumentCard type="investigation" data={incidentData} />
                                </TabPanel>
                                <TabPanel value={value} index={1}>
                                    {/* Investigation Documents DropzoneArea */}
                                    <DropzoneArea
                                        acceptedFiles={['application/pdf', 'image/jpeg', 'image/png', 'video/mp4']}
                                        dropzoneText={"Drag and drop files here or click"}
                                        filesLimit={5}
                                        maxFileSize={104857600}
                                        onChange={(files) => handleFileChange(files, 'receipts')}
                                    />
                                    <AirDocumentCard type="receipts" data={incidentData} />
                                </TabPanel>
                                <TabPanel value={value} index={2}>
                                    {/* Investigation Documents DropzoneArea */}
                                    <DropzoneArea
                                        acceptedFiles={['application/pdf', 'image/jpeg', 'image/png', 'video/mp4']}
                                        dropzoneText={"Drag and drop files here or click"}
                                        filesLimit={5}
                                        maxFileSize={104857600}
                                        onChange={(files) => handleFileChange(files, 'surveyor')}
                                    />
                                    <AirDocumentCard type="surveyor" data={incidentData} />
                                </TabPanel>

                            </Box>
                        </Modal.Body>
                        <Modal.Footer className="flex-wrap">
                            <Button variant="success" onClick={() => { setInvestigateModal(true) }}>Initiate Investigation</Button>
                            <Button variant="primary" onClick={() => { uploadDocuments() }}>Upload Documents</Button>
                            <Button variant="light" onClick={() => setDocumentModal(false)}>Close</Button>
                        </Modal.Footer>
                    </Modal>

                </>}

            <Modal
                show={investigateModal}
                onHide={() => setInvestigateModal(false)}
                size="md"
                aria-labelledby="example-modal-sizes-title-md"
                backdrop="static"
            >
                <Modal.Header closeButton>
                    Initiate Investigation
                </Modal.Header>
                <Modal.Body>
                    <Box sx={{ width: '100%' }}>

                        <div className='row'>
                            <div className='col'>
                                <div className="form-group">
                                    <label>Investigation Start Date</label>
                                    <input type="date" value={investigationDate} onChange={(e) => setInvestigationDate(e.target.value)} className="form-control" />
                                </div>
                                <div className='form-group'>
                                    <label>Select Investigation Team Members:</label>

                                    <Select
                                        id="user_description"
                                        isMulti={true} // Allow multiple selections
                                        onChange={handleUserSelectChange} // Handle selection changes
                                        options={investigators.map(user => ({ value: user.id, label: user.firstName }))} // Map users to options
                                        value={selectedUsers} // Set selected options
                                        placeholder="Type..."
                                    />
                                </div>
                            </div>
                        </div>

                    </Box>
                </Modal.Body>
                <Modal.Footer className="flex-wrap">
                    <Button variant="success" onClick={() => { initiateInvestigation() }}>Initiate Investigation</Button>
                    <Button variant="light" onClick={() => setInvestigateModal(false)}>Close</Button>
                </Modal.Footer>
            </Modal>
        </>
    )
}

export default IncidentTable;
