import React, { useEffect, useState } from 'react';
import { ProgressBar, Form, Col, Row, Accordion, Button, Modal } from 'react-bootstrap';
import API from '../../services/API';
import { GET_USER_ROLE_BY_MODE, OTT_TASKS_WITH_ID, OTT_OTTTASKS_WITH_ID, OTT_WITH_ID, FILE_URL } from '../../constants';
import Select from 'react-select';
import { DropzoneArea } from 'material-ui-dropzone';
import Swal from 'sweetalert2';
import ImageComponent from '../../services/FileDownlodS3';

const customSwal2 = Swal.mixin({
    customClass: {
        confirmButton: 'btn btn-primary',

    },
    buttonsStyling: false
})
const TaskComponent = ({
    showModal,
    applicationDetails,
    type,
    data,
    setFormData,
    handleSelectChange,
    setShowModal,
    submitActionReviewer,
    submitActionApprove,
    sendOverallStatus,
    sendCompletedPercentage,
    sendAssigneeComments
}) => {
    const [reviewer, setReviewer] = useState([]);
    const [completedPercentage, setCompletedPercentage] = useState(applicationDetails.estimatedPercentage || 0); // Default percentage value
    const [activities, setActivities] = useState(data ||[]); // Activities state initialized with data
    const [newActivityName, setNewActivityName] = useState(''); // State for new activity name
    const [assigneeComments, setAssigneeComments] = useState('');
    const [overallStatusValue, setOverallStatusValue] = useState(applicationDetails.status); // Track overall status

    const allActivitiesCompleted = activities && activities.length > 0 ? activities.every(item => item.status === 'Completed') : true;

    const statusOptions = [
        { value: 'Yet to Start', label: 'Yet to Start' },
        { value: 'In Progress', label: 'In Progress' },
        { value: 'Completed', label: 'Completed' },
        { value: 'On Hold', label: 'On Hold' },
        { value: 'Waiting', label: 'Waiting' }
    ];

    const overallStatus = [
        { value: 'Yet to Start', label: 'Yet to Start' },
        { value: 'Planning', label: 'Planning' },
        { value: 'In Progress: On Track', label: 'In Progress: On Track' },
        { value: 'At Risk', label: 'At Risk' },
        { value: 'On Hold', label: 'On Hold' },
        // { value: 'Under Review', label: 'Under Review' },
        { value: 'Testing / QA', label: 'Testing / QA' },
        { value: 'Ready for Deployment', label: 'Ready for Deployment' },
        { value: 'Completed', label: 'Completed' },
        // { value: 'Archived', label: 'Archived' },
    ];

    useEffect(() => {
        getOTTReviewer();
    }, []);

    const getOTTReviewer = async () => {
        try {
            const response = await API.post(GET_USER_ROLE_BY_MODE, {
                locationOneId: "",
                locationTwoId: "",
                locationThreeId: "",
                locationFourId: "",
                mode: 'ott_reviewer'
            });

            if (response.status === 200) {
                const data = response.data.map((item) => ({
                    label: item.firstName,
                    value: item.id
                }));

                setReviewer(data);
            }
        } catch (error) {
            console.error("Error fetching crew list:", error);
        }
    };

    // const totalItems = activities.length;
    // const selectedItems = activities.filter(item => item.status === 'Completed').length;
    // const progressPercentage = (selectedItems / totalItems) * 100;

    const getPriorityClass = (priority) => {
        switch (priority.toLowerCase()) {
            case "high":
                return "text-danger"; // Red color for high priority
            case "medium":
                return "text-warning"; // Yellow/Orange color for medium priority
            case "low":
                return "text-success"; // Green color for low priority
            default:
                return "";
        }
    };



    const handleFileChange = async (index, files) => {
        if (files.length > 0) {
            const latestFile = files[files.length - 1];
            const formData1 = new FormData();
            formData1.append('file', latestFile);


            try {
                const response = await API.post(FILE_URL, formData1, {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                    }
                });

                if (response && response.status === 200) {

                    const updatedActivities = [...activities];
                    updatedActivities[index].evidence.push(response.data.files[0].originalname)
                    setActivities(updatedActivities);
                    setFormData(updatedActivities);
                }
            } catch (error) {
                // Log the error response for debugging purposes
                console.error("File upload error: ", error);
            }


        }
    };

    const handleFileImgChange = async (index, files) => {
        if (files.length > 0) {
            const latestFile = files[files.length - 1];
            const formData1 = new FormData();
            formData1.append('file', latestFile);


            try {
                const response = await API.post(FILE_URL, formData1, {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                    }
                });

                if (response && response.status === 200) {

                    const updatedActivities = [...activities];
                    updatedActivities[index].img.push(response.data.files[0].originalname)
                    setActivities(updatedActivities);
                    setFormData(updatedActivities);
                }
            } catch (error) {
                // Log the error response for debugging purposes
                console.error("File upload error: ", error);
            }


        }
    };

    const handleStatusChange = (index, selectedOption) => {
        const updatedActivities = [...activities];
        updatedActivities[index].status = selectedOption ? selectedOption.value : 'Yet to Start';
        setActivities(updatedActivities);
        setFormData(updatedActivities);
    };

    const handleRemarksChange = (index, event) => {
        const updatedActivities = [...activities];
        updatedActivities[index].remarks = event.target.value;
        setActivities(updatedActivities);
        setFormData(updatedActivities);
    };

    const handleSaveTask = () => {
        console.log('Saving task:', activities);
        console.log('Saving task:', applicationDetails);
        let patchPromises = [];
        if (data && data.length > 0) {
            const patchPromises = data.map(element => {
                if (element.id) {
                    // If the element has an ID, make a PATCH request with the entire element
                    return API.patch(OTT_TASKS_WITH_ID(element.id), element);
                } else {
                    // If the element does not have an ID, make a POST request with the entire element
                    const { isNew, ...elementWithoutIsNew } = element;
                    return API.post(OTT_OTTTASKS_WITH_ID(applicationDetails.id), elementWithoutIsNew);
                }
            });
        }
        // Add the API patch for applicationDetails to the promises array
        const updateApplicationPromise = API.patch(OTT_WITH_ID(applicationDetails.id), {
            status: applicationDetails.status,
            estimatedPercentage: applicationDetails.estimatedPercentage
        });

        // Include the updateApplicationPromise in the Promise.all array
        Promise.all([...patchPromises, updateApplicationPromise])
            .then(responses => {
                console.log('All tasks and application details saved successfully:', responses);
                // Handle success (e.g., update state, show a success message)
                customSwal2.fire("All activities saved successfully", "", "sucecss");
                if (overallStatusValue === 'Completed' && allActivitiesCompleted) {
                    submitActionReviewer()
                }
            })
            .catch(error => {
                console.error('Error saving tasks and application details:', error);
                // Handle error (e.g., show an error message)
            });
    };




    const getStatusClass = (status) => {
        switch (status) {
            case 'Completed':
                return 'status-completed'; // Green for completed
            case 'In Progress':
                return 'status-inprogress'; // Yellow/Orange for in progress
            case 'On Hold':
                return 'status-onhold'; // Red for on hold
            case 'Waiting':
                return 'status-waiting'; // Blue for waiting
            case 'Yet to Start':
            default:
                return 'status-yettostart'; // Gray for yet to start
        }
    };

    const handleOverAllStatusChange = (selectedOption) => {
        if (selectedOption && selectedOption.value === 'Completed') {
            // Check if all activities are completed before allowing overall status to be set to 'Completed'
            const allActivitiesCompleted = activities && activities.length > 0 ? activities.every(item => item.status === 'Completed') : true;
            console.log(allActivitiesCompleted)
            if (allActivitiesCompleted) {
                setOverallStatusValue(selectedOption.value);
                sendOverallStatus(selectedOption.value);
            } else {
                customSwal2.fire("All activities must be completed before setting the overall status to 'Completed!", "", "error");
                // alert("All activities must be completed before setting the overall status to 'Completed'.");
            }
        } else {
            setOverallStatusValue(selectedOption ? selectedOption.value : '');
            sendOverallStatus(selectedOption ? selectedOption.value : '');
        }
    };

    const handleCompletedPercentageChange = (value) => {
        setCompletedPercentage(value);
        sendCompletedPercentage(value)
    };

    // Function to add a new activity
    const addNewActivity = () => {
        if (newActivityName.trim() !== '') { // Check if the input is not empty
            const newActivity = {
                name: newActivityName,
                status: 'Yet to Start',
                remarks: '',
                img: [],
                evidence: [],
                isNew: true, // Flag to mark newly created activities
            };
            const updatedActivities = [...activities, newActivity];
            setActivities(updatedActivities);
            setFormData(updatedActivities); // Update the formData as well if needed
            setNewActivityName(''); // Clear the input field after adding
        }
    };

    // Function to remove an activity
    const removeActivity = (index) => {
        const updatedActivities = activities.filter((_, i) => i !== index); // Remove activity at the specified index
        setActivities(updatedActivities);
        setFormData(updatedActivities); // Update the formData as well if needed
    };

    // Check if all activities are completed
    const handleRemoveImage = (activityIndex, imgIndex) => {
        const updatedActivities = [...activities];
        updatedActivities[activityIndex].img.splice(imgIndex, 1); // Remove the image at the specified index
        setActivities(updatedActivities);
        setFormData(updatedActivities); // Update the formData as well if needed
    };

    const handleRemoveEviImage = (activityIndex, imgIndex) => {
        const updatedActivities = [...activities];
        updatedActivities[activityIndex].evidence.splice(imgIndex, 1); // Remove the image at the specified index
        setActivities(updatedActivities);
        setFormData(updatedActivities); // Update the formData as well if needed
    };

    const handleAssigneeCommentChange = (value) => {
        setAssigneeComments(value)
        sendAssigneeComments(value)
    }

    return (
        <>
            <Modal
                show={showModal}
                size="md"
                onHide={() => setShowModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
                backdrop="static"
            >
                <Modal.Header>

                    <div className="w-100 d-flex justify-content-between align-items-center">
                        <h2 className="m-0">{type === 'verify_task' ? "Please Approve" : type === 'perform_task' ? "Update Task Status" : "Re-Perform Task"}</h2>
                        <div className="text-center mt-0">

                        </div>
                    </div>

                </Modal.Header>

                <Modal.Body>
                    <div className='task-details-container'>
                        <h5>Task Name: {applicationDetails.taskName}</h5>
                        <p><b>Description: </b>{applicationDetails.taskDescription}</p>
                        <p><b>Project: </b>{applicationDetails.project?.name}</p>
                        <p><b>Category: </b>{applicationDetails.category?.name}</p>
                        <p className={getPriorityClass(applicationDetails.priority)}>
                            <b>Priority:</b> {applicationDetails.priority}
                        </p>

                        {applicationDetails.img && applicationDetails.img.length > 0 && (
                            <div className="d-flex">
                                {applicationDetails.img.map((item, m) => (
                                    <div key={m} className="  " style={{ position: 'relative' }}>
                                        <div className="boxShadow d-flex align-items-center" >
                                            <ImageComponent fileName={item} size={'100'} name={true}/>

                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}
                        {/* Completed percentage selector */}
                        <Form.Group as={Row} className="mb-3 d-flex align-items-center">
                            <Form.Label column sm="2" className=''>
                                <b>Completed Percentage</b>
                            </Form.Label>
                            <Col sm="10">
                                <Form.Range
                                    value={completedPercentage}
                                    min={0}
                                    max={100}
                                    onChange={(e) => handleCompletedPercentageChange(e.target.value)}
                                    className="percentage-slider"
                                    style={{
                                        background: `linear-gradient(to right, #007bff ${completedPercentage}%, #e9ecef ${completedPercentage}%)`,
                                    }}
                                />
                                <Form.Text muted>Selected Percentage: {completedPercentage}%</Form.Text>
                            </Col>
                        </Form.Group>

                        {type === 'reperform_task' && (
                            <Row>
                                <Col>
                                    <Form.Label className='fw-bold'>Reviewer Comments:</Form.Label>
                                    <p>{applicationDetails.reviewerComments}</p>
                                </Col>
                            </Row>


                        )}

                        <Row className="mt-2 mb-3">
                            <Col>
                                <Form.Label className='fw-bold'>Comments</Form.Label>
                                <Form.Control
                                    as="textarea"
                                    value={assigneeComments}
                                    onChange={(event) => handleAssigneeCommentChange(event.target.value)}
                                />
                            </Col>
                        </Row>
                        <Row className="mt-2 mb-3">
                            <Col>
                                <Form.Label className='fw-bold'>Overall Status</Form.Label>
                                <Select
                                    value={overallStatus.find(option => option.value === overallStatusValue)} // Track overall status
                                    onChange={handleOverAllStatusChange} // Update the status when selected
                                    options={overallStatus}
                                    isClearable
                                />
                            </Col>
                        </Row>
                        <div className="p-2 mb-3" style={{ border: '1px solid #cfcfcf', borderRadius: 10 }}>
                            <h6 className="fw-bold m-3">Activities / Dependencies</h6>

                            <Accordion>
                                {activities && activities.map((item, index) => (
                                    <Accordion.Item eventKey={index.toString()} key={index}>
                                        <Accordion.Header >
                                            <div className='row'>
                                                <div className='col-9'>
                                                    <span className={item.status === 'Completed' ? 'strike-through' : ''}>
                                                        {`${index + 1}. ${item.name}`}
                                                    </span>
                                                </div>
                                                <div className='col-3'>
                                                    <span className={`status-badge ms-auto ${getStatusClass(item.status)}`}>
                                                        {item.status || 'Yet to Start'}
                                                    </span>
                                                    {/* Conditionally render the remove icon only for newly created activities */}
                                                    {item.isNew && (
                                                        <i
                                                            className="pi pi-trash ms-3"
                                                            onClick={() => removeActivity(index)}
                                                            style={{ marginLeft: 'auto' }} // Align the remove icon to the right
                                                        />
                                                    )}
                                                </div>
                                            </div>
                                        </Accordion.Header>
                                        <Accordion.Body>
                                            <Form.Group className="mb-3">
                                                <Row className="mt-2">
                                                    <Col>
                                                        <Form.Label>Status</Form.Label>
                                                        <Select
                                                            value={statusOptions.find(option => option.value === item.status)}
                                                            onChange={(selectedOption) => handleStatusChange(index, selectedOption)}
                                                            options={statusOptions}
                                                            isClearable
                                                        />
                                                    </Col>
                                                </Row>

                                                <Row className="mt-2">
                                                    <Col>
                                                        <Form.Label>Remarks</Form.Label>
                                                        <Form.Control
                                                            as="textarea"
                                                            value={item.remarks || ''}
                                                            onChange={(event) => handleRemarksChange(index, event)}
                                                        />
                                                    </Col>
                                                </Row>

                                                <Row className="mt-2">
                                                    <Col>
                                                        <Form.Label>Images</Form.Label>
                                                        {item.isNew && (
                                                            <DropzoneArea
                                                                acceptedFiles={['image/*']}
                                                                dropzoneText={"Drag 'n' drop some files here, or click to select files (Optional)"}
                                                                onChange={(files) => handleFileImgChange(index, files)}
                                                                filesLimit={5}
                                                                maxFileSize={104857600}
                                                                showPreviewsInDropzone={false}
                                                                showPreviews={false}
                                                                dropzoneClass="dropzoneText d-flex align-items-center justify-content-center"
                                                            />
                                                        )}
                                                        {item.img && item.img.length > 0 && (
                                                            <div className="d-flex">
                                                                {item.img.map((item, m) => (
                                                                    <div key={m} className="  " style={{ position: 'relative' }}>
                                                                        <div className="boxShadow d-flex align-items-center" >
                                                                            <ImageComponent fileName={item} size={'100'} name={true}/>
                                                                            <i
                                                                                className="pi pi-trash"
                                                                                onClick={() => handleRemoveImage(index, m)}
                                                                                style={{
                                                                                    position: 'absolute',
                                                                                    top: '5px',
                                                                                    right: '5px',
                                                                                    cursor: 'pointer',
                                                                                    color: 'red',
                                                                                }}
                                                                            />
                                                                        </div>
                                                                    </div>
                                                                ))}
                                                            </div>
                                                        )}
                                                    </Col>
                                                </Row>


                                                <Row className="mt-2">
                                                    <Col>
                                                        <Form.Label>Upload Evidence</Form.Label>
                                                        <DropzoneArea
                                                            acceptedFiles={['image/*']}
                                                            dropzoneText={"Drag 'n' drop some files here, or click to select files (Optional)"}
                                                            onChange={(files) => handleFileChange(index, files)}
                                                            filesLimit={5}
                                                            maxFileSize={104857600}
                                                            showPreviewsInDropzone={false}
                                                            showPreviews={false}
                                                            dropzoneClass="dropzoneText d-flex align-items-center justify-content-center"
                                                        />
                                                        {item.evidence && item.evidence.length > 0 && (
                                                            <div className="col-12 mt-3 mb-3">
                                                                <div className="d-flex">
                                                                    {item.evidence.map((item, m) => (
                                                                        <div key={m} className="  " style={{ position: 'relative' }}>
                                                                            <div className="boxShadow d-flex align-items-center" >
                                                                                <ImageComponent fileName={item} size={'100'} name={true}/>
                                                                                <i
                                                                                    className="pi pi-trash"
                                                                                    onClick={() => handleRemoveEviImage(index, m)}
                                                                                    style={{
                                                                                        position: 'absolute',
                                                                                        top: '5px',
                                                                                        right: '5px',
                                                                                        cursor: 'pointer',
                                                                                        color: 'red',
                                                                                    }}
                                                                                />
                                                                            </div>
                                                                        </div>
                                                                    ))}
                                                                </div>
                                                            </div>
                                                        )}
                                                    </Col>
                                                </Row>

                                                {type === 'reperform_task' && (
                                                    <Row>
                                                        <Col className='mt-2'>
                                                            <Form.Label>Reviewer Comments</Form.Label>
                                                            <p>{item.reviewerComments}</p>
                                                        </Col>
                                                    </Row>
                                                )}


                                            </Form.Group>
                                        </Accordion.Body>
                                    </Accordion.Item>
                                ))}
                            </Accordion>
                        </div>
                        {/* Input for new activity name */}
                        <Form.Group as={Row} className="mt-3">
                            <Col sm="10">
                                <Form.Control
                                    type="text"
                                    placeholder="Enter Activity Name"
                                    value={newActivityName}
                                    onChange={(e) => setNewActivityName(e.target.value)}
                                />
                            </Col>
                            <Col sm="2">
                                <Button variant="success" onClick={addNewActivity}>
                                    <i className='pi pi-plus' />
                                </Button>
                            </Col>
                        </Form.Group>
                    </div>

                    {/* Conditionally render the Reviewer select dropdown */}
                    {overallStatusValue === 'Completed' && allActivitiesCompleted ? (
                        <Form.Group controlId="assigneeId" className="mb-3">
                            <Form.Label>Reviewer <span className="text-danger">*</span></Form.Label>
                            <Select
                                name="reviewerId"
                                onChange={handleSelectChange}
                                options={reviewer}
                                isClearable
                                required
                            />
                        </Form.Group>
                    ) : (
                        <Row className="mt-3">
                            <Col className="d-flex justify-content-end">
                                <Button variant="primary" onClick={() => handleSaveTask()}>
                                    Update Status
                                </Button>
                            </Col>
                        </Row>

                    )}

                </Modal.Body>

                <Modal.Footer className="flex-wrap">
                    {type === 'perform_task' || type === 'reperform_task' ? (
                        overallStatusValue === 'Completed' && allActivitiesCompleted && (
                            <Button className="btn btn-primary" onClick={() => handleSaveTask()}>
                                Submit to Reviewer
                            </Button>
                        )
                    ) : type === 'verify_task' ? (
                        <>
                            <Button className="btn btn-secondary" onClick={() => submitActionApprove('approve')}>
                                Approve
                            </Button>
                            <Button className="btn btn-primary" onClick={() => submitActionApprove('return')}>
                                Return To Assignee
                            </Button>
                        </>
                    ) : null}
                    <Button variant="light" onClick={() => setShowModal(false)}>Close</Button>
                </Modal.Footer>

            </Modal>
        </>
    );
};

export default TaskComponent;
