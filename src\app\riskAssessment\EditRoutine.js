import React, { useEffect, useState, useContext, useRef } from 'react'
import { Dropdown } from 'primereact/dropdown';
import { Button } from 'primereact/button';
import { MultiSelect } from 'primereact/multiselect';
import { InputTextarea } from 'primereact/inputtextarea'
import { InputText } from 'primereact/inputtext'
import { Checkbox } from 'primereact/checkbox';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
// import 'primeflex/primeflex.css'; 
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";
// import 'primereact/resources/themes/lara-light-indigo/theme.css';
import 'primeicons/primeicons.css';
import { GMS1_URL, GET_USER_ROLE_BY_MODE, HAZARDS_CATEGOTY, GET_ALL_USER,GET_RISK_HAZARD_URL, FILE_URL, RISK_WITH_ID_URL, GET_RISK_WITH_ID_URL, RISKASSESSMENT_LIST, RISK_UPDATE_WITH_ID_URL } from '../constants';
import API from '../services/API';
import { useSelector } from 'react-redux';
import { InputSwitch } from 'primereact/inputswitch';
import { DropzoneArea } from 'material-ui-dropzone';
import { Dialog } from 'primereact/dialog';
import { TabView, TabPanel } from 'primereact/tabview';
import { Stepper, Step, StepLabel, Typography } from '@mui/material';
import { Calendar } from 'primereact/calendar';
import Accordion from 'react-bootstrap/Accordion';
import { useAccordionButton } from 'react-bootstrap/AccordionButton';
import AccordionContext from 'react-bootstrap/AccordionContext';
import { RadioButton } from 'primereact/radiobutton';
import ImageComponent from '../services/FileDownlodS3';
import Select from 'react-select'
import SignatureCanvas from "react-signature-canvas";
import Swal from 'sweetalert2';
import { useLocation, useHistory } from 'react-router-dom/cjs/react-router-dom';
import RiskUpdate from './Component/RiskUpdate';
import UpdateTable from './Component/UpdateTable';
import moment from 'moment';
import TaskItem from "./Component/TaskItem"
import GenerateLandPdf from './Component/PDF';
const customSwal2 = Swal.mixin({
    customClass: {
        confirmButton: "btn btn-primary",
    },
    buttonsStyling: false,
});

function EditRoutine({ data, type, domain }) {
    const url = window.location.hostname
    const user = useSelector((state) => state.login.user);
    const location = useLocation();
    const history = useHistory()
    console.log(location)
    const signRef = useRef();
    const [files, setFiles] = useState([])
    const [depart, setDepart] = useState([])
    const [activity, setActivity] = useState([])
    const [crew, setCrew] = useState([])
    const [selectedDepart, setSelectedDepart] = useState(null)
    const [selectedActivity, setSelectedActivity] = useState(null)
    const [selectedCrew, setSelectedCrew] = useState([])
    const [addSubActivity, setAddSubActivity] = useState(false)
    const [activityDesc, setActivityDesc] = useState(null)
    const [task, setTask] = useState([])
    const [subActivityName, setSubActivityName] = useState('')
    const [visible, setVisible] = useState(false)
    const [item, setItem] = useState('')
    const [index, setIndex] = useState('')
    const [activeIndex, setActiveIndex] = useState(0);
    const [hazards, setHazards] = useState([])
    const [activeTabIndex, setActiveTabIndex] = useState(0);
    const [severityTable, setSeverityTable] = useState(false)
    const [likelyhoodTable, setLikelyhoodTable] = useState(false)
    const [riskTable, setRiskTable] = useState(false)
    const [responsibility, setResponsibility] = useState([])
    const [required, setRequired] = useState(true)
    const [recommendationOne, setRecommendationOne] = useState({})
    const [recommendationTwo, setRecommendationTwo] = useState({})
    const [nonRoutineDepartment, setNonRoutineDepartment] = useState('')
    const [nonRoutineActivity, setNonRoutineActivity] = useState('')
    const [additionalRecommendation, setAdditionalRecommendation] = useState('')
    const [isLoading, setIsLoading] = useState(true)
    const [hazardName, setHazardName] = useState('')
    const [riskUpdate, setRiskUpdate] = useState(false)
    const [Update, setUpdate] = useState([])
    const [risk, setRisk] = useState([])
    const [eptwHot, setEptwHot] = useState([])
    const [raTeamMember, setRATeamMember] = useState([])
    const [shortName, setShortName] = useState('')

    const severityData = [
        {
            id: '5 (A)',
            severity: 'Catastrophic',
            personnel: 'Serious injury with long-term or permanent disability or death.',
            property: 'Significant damage leading to major repairs.',
            environment: 'Significant environmental damage requiring regulatory reporting and cleanup.',
            serviceLoss: 'Major disruption to service operations, extended recovery time.'
        },
        {
            id: '4 (B)',
            severity: 'Major',
            personnel: 'Serious injury with long-term recovery or permanent disability.',
            property: 'Noticeable damage requiring repair, causing moderate downtime or repair costs.',
            environment: 'Moderate, recoverable environmental impact, requiring external agency notification or cleanup.',
            serviceLoss: 'Significant downtime with substantial recovery efforts.'
        },
        {
            id: '3 (C)',
            severity: 'Moderate',
            personnel: 'Injury requiring medical treatment with potential for short-term lost workdays or restricted duties.',
            property: 'Noticeable damage requiring repair, causing moderate downtime or repair costs.',
            environment: 'Moderate environmental impact, manageable on-site with potential regulatory notification.',
            serviceLoss: 'Moderate service interruption with short recovery.'
        },
        {
            id: '2 (D)',
            severity: 'Minor',
            personnel: 'Minor injury requiring first-aid or outpatient treatment, with minimal lost time.',
            property: 'Slight damage requiring minor repairs without significant downtime.',
            environment: 'Small localized impact, manageable on-site, without long-term environmental damage.',
            serviceLoss: 'Brief disruption to services, easily restored.'
        },
        {
            id: '1 (E)',
            severity: 'Insignificant',
            personnel: 'Minor first-aid required with no lost time or long-term health impacts.',
            property: 'Minimal damage or wear that does not require repair or interruption to operations.',
            environment: 'Negligible environmental impact with no regulatory involvement needed.',
            serviceLoss: 'No impact on services.'
        }
    ];

    const levelData = [
        {
            level: '1',
            descriptor: 'Rare',
            detailedDescription: 'The event is highly unlikely to occur under normal circumstances, with little to no historical precedent.'
        },
        {
            level: '2',
            descriptor: 'Unlikely',
            detailedDescription: 'The event is improbable but could potentially happen under unusual conditions, though there is limited historical data to support this.'
        },
        {
            level: '3',
            descriptor: 'Possible',
            detailedDescription: 'The event could happen, with moderate chances of occurring based on historical records or foreseeable conditions.'
        },
        {
            level: '4',
            descriptor: 'Likely',
            detailedDescription: 'The event is expected to occur in the normal course of operations, with a significant history of similar incidents.'
        },
        {
            level: '5',
            descriptor: 'Almost Certain',
            detailedDescription: 'The event is highly likely to occur and is anticipated in the near future without further intervention.'
        }
    ];

    const tableData = [
        { id: '5(A)', severity: 'Catastrophic', rare: '5(A)', unlikely: '10(A)', possible: '15(A)', likely: '20(A)', almostCertain: '25(A)' },
        { id: '4(B)', severity: 'Major', rare: '4(B)', unlikely: '8(B)', possible: '12(B)', likely: '16(B)', almostCertain: '20(B)' },
        { id: '3(C)', severity: 'Moderate', rare: '3(C)', unlikely: '6(C)', possible: '9(C)', likely: '12(C)', almostCertain: '15(C)' },
        { id: '2(D)', severity: 'Minor', rare: '2(D)', unlikely: '4(D)', possible: '6(D)', likely: '8(D)', almostCertain: '10(D)' },
        { id: '1(E)', severity: 'Insignificant', rare: '1(E)', unlikely: '2(E)', possible: '3(E)', likely: '4(E)', almostCertain: '5(E)' },
    ];


    useEffect(() => {
        getWorkActivity()
        getCrewList()
        getHazardList()
        getAllResponsibility();
        getRiskUpdate();
        getHighRiskHazardList();
    }, [data])

    const getHighRiskHazardList = async () => {
        // Define the filter criteria
        const uriString = {
            where: { type: 'High-Risk Hazard' },
            fields: { id: true, hazardName: true }
        };

        // Construct the URL with the encoded filter
        const url = `${RISKASSESSMENT_LIST}?filter=${encodeURIComponent(
            JSON.stringify(uriString)
        )}`;

        try {
            // Make the GET request to the API
            const response = await API.get(url);

            // Check if the response status is 200 (OK)
            if (response.status === 200) {
                // Update the state with the retrieved data
                setRisk(response.data);
            } else {
                // Handle other response statuses (e.g., 404, 500)
                console.error(`Error: Received status code ${response.status}`);
            }
        } catch (error) {
            // Handle errors (e.g., network issues, invalid URL)
            console.error('Error fetching high-risk hazard list:', error);
        }
    };

    const getRiskUpdate = async () => {
        const response = await API.get(RISK_UPDATE_WITH_ID_URL(data?.id));
        if (response.status === 200) {

            setUpdate(response.data)
        }
    }
    useEffect(() => {
        setIsLoading(true)
        const fetchData = async () => {
            const uriString = {
                include: [
                    { relation: "department" },
                    { relation: "teamLeader" },
                    { relation: "workActivity" },
                    {
                        relation: "raTeamMembers",
                        scope: {
                            include: [{ relation: "user" }]
                        }
                    }
                ]
            };

            const url = `${GET_RISK_WITH_ID_URL(data.id)}?filter=${encodeURIComponent(
                JSON.stringify(uriString)
            )}`;
            try {
                const response = await API.get(url);
                if (response.status === 200) {
                    const data = response.data;
                    setTask(data.tasks);
                    setAdditionalRecommendation(data.additonalRemarks);
                    setRecommendationOne(data.overallRecommendationOne);
                    setRecommendationTwo(data.overallRecommendationTwo);
                    setSelectedCrew(data.raTeamMembers.map(option => ({
                        name: option.user.firstName,
                        id: option.user.id
                    })));
                    setActivityDesc(data.description);
                    setNonRoutineActivity(data.nonRoutineWorkActivity);
                    setNonRoutineDepartment(data.nonRoutineDepartment);
                    setHazardName(data.hazardName);
                    setEptwHot(data.highRisk);
                    setRATeamMember(data.raTeamMembers)
                    setShortName(data.shortName)
                    setIsLoading(false)
                }
            } catch (error) {
                console.error("Error fetching data:", error);
            }
        };

        if (data) {
            fetchData();
        }
    }, [data]);
    const getAllResponsibility = async () => {
        const uriString = { include: [{ "relation": "workingGroup" }, { "relation": "designation" }, { "relation": "department" }] }
        const url = `${GET_ALL_USER}?filter=${encodeURIComponent(
            JSON.stringify(uriString)
        )}`;
        const response = await API.get(url);
        if (response.status === 200) {
            const depart = response.data.map(item => {
                return { id: item.id, name: item.firstName, email: item.email }
            })
            setResponsibility(depart)
        }

    }
      const getHazardList = async () => {
    
            if (url === 'kbat.acuizen.com') {
                const uriString = {
                    include: [
                        {
                            relation: "hazardCategories",
                            scope: {
                                include: [{ relation: "hazardItems" }]
                            }
                        }
                    ]
                };
                const url = `${GET_RISK_HAZARD_URL}?filter=${encodeURIComponent(
                    JSON.stringify(uriString)
                )}`;
                const response = await API.get(url);
                if (response.status === 200) {
                    // const data = response.data.filter((item) => item.name !== 'Hazard-Based')
                    setHazards(response.data[0].hazardCategories);
                }
            } else {
                const uriString = { include: ["hazards"] };
                const url = `${HAZARDS_CATEGOTY}?filter=${encodeURIComponent(
                    JSON.stringify(uriString)
                )}`;
                const response = await API.get(url);
                if (response.status === 200) {
                    const data = response.data.filter((item) => item.name !== 'Hazard-Based')
                    setHazards(data);
                }
            }
        }


    const getWorkActivity = async () => {
        const uriString = { include: ["workActivities"] };

        const url = `${GMS1_URL}?filter=${encodeURIComponent(
            JSON.stringify(uriString)
        )}`;
        const response = await API.get(url);
        if (response.status === 200) {

            const transformedOptions = response.data.map(option => ({
                label: option.name,
                value: option.id,
                workActivities: option.workActivities
            }));
            setDepart(transformedOptions)

            if (data) {
                const defaultOption = transformedOptions.find(option => option.value === data.departmentId);
                console.log(defaultOption)
                setSelectedDepart(defaultOption);
            }
        }
    };
    useEffect(() => {
        if (selectedDepart !== null) {
            if (data.type === 'Routine') {
                const active = depart.find(item => item.value === selectedDepart?.value);
                console.log(selectedDepart)
                console.log(active)
                const transformedOptions = active?.workActivities?.map(option => ({
                    label: option.name,
                    value: option.id,
                }));
                const defaultOption = transformedOptions?.find(option => option.value === data.workActivityId);
                setSelectedActivity(defaultOption)

                setActivity(transformedOptions)
            }
        }
    }, [selectedDepart])
    const getCrewList = async () => {

        const response = await API.post(GET_USER_ROLE_BY_MODE, {
            locationOneId: "",
            locationTwoId: "",
            locationThreeId: "",
            locationFourId: "",
            mode: 'ra_member'
        });
        if (response.status === 200) {
            let data = [];
            response.data.map((item) => {

                if (item.id !== user.id) {
                    data.push({ name: item.firstName, id: item.id });
                }
            });

            setCrew(data);
        }
    };





    const openDialog = (item, i) => {
        setItem('')
        setItem(item);
        setIndex(i)
        setVisible(true)

    }

    const checkRequiredStepField = () => {

        let required = true;
        if (item[8].step === 0) {

            if (item[1].selected.length === 0) {
                required = false
                setRequired(false)
            }

        } else if (item[8].step === 1) {
            for (let option of item[2].option) {
                if (option.value === "" && option.current_type === '') {
                    required = false
                    setRequired(false)
                }
            }

        } else if (item[8].step === 2) {
            for (let option of item[3].option) {
                if (option.value === "" && option.current_type === '') {
                    required = false
                    setRequired(false)
                }
            }
        } else if (item[8].step === 3) {
            if (item[4].severity === '' && item[4].likelyhood === '') {
                required = false
                setRequired(false)
            }

        }
        else if (item[8].step === 4) {
            if (item[7].severity === '' && item[7].likelyhood === '') {
                required = false
                setRequired(false)
            }

        }


        return required

    }

    const headerTemplate = (
        <div className="d-flex flex-column">
            <div className='col-12 mb-3'>
                <p>Add Sub Activity for</p>
                <h6>{item !== '' && item[0] && item[0].name}</h6>
            </div>
            {/* {item !== '' && item[8] && item[8].step !== undefined && (
                <Stepper activeStep={item[8].step} className='mb-4'>
                    {item[9].level.map((label, index) => (
                        <Step key={index}>
                            <StepLabel className='step-label d-flex flex-column'>{label}</StepLabel>
                        </Step>
                    ))}
                </Stepper>
            )} */}
        </div>
    );


    const footerTemplate = (<></>
        // <div className="d-flex justify-content-between">
        //     <div>
        //         <Button
        //             className='me-2'
        //             outlined
        //             label="Back"
        //             onClick={handleBack}
        //             disabled={item[8] && item[8].step !== undefined && item[8].step === 0}
        //         />
        //     </div>
        //     <div>
        //         {/* <Button className='me-2' outlined label="Cancel" /> */}
        //         <Button className='me-2' outlined label="Save Progress" onClick={() => setVisible(false)} />
        //         <Button
        //             label={item[8] && item[8].step !== undefined && item[8].step === item[9].level.length - 1 ? "Finish" : "Next"}
        //             onClick={handleNext}
        //         />
        //     </div>
        // </div>
    );
    const rowClassName = (data) => {
        switch (data.level[0]) {
            case '1':
                return 'level-1';
            case '2':
                return 'level-2';
            case '3':
                return 'level-3';
            case '4':
                return 'level-4';
            case '5':
                return 'level-5';
            default:
                return '';
        }
    };
    const cellClassName = (value) => {

        const numericValue = parseInt(String(value).replace(/[^\d]/g, ''), 10);

        // Check if the numeric value is 0
        if (numericValue === 0) return '';

        if (numericValue === 1 || numericValue === 2 || numericValue === 3 || numericValue === 4) return 'cell-green';
        if (numericValue === 15 || numericValue === 20 || numericValue === 25 || numericValue === 16) return 'cell-red';

        return 'cell-yellow';
    };

    const cellStyle = (data, field) => cellClassName(data[field]);







    const ContextAwareToggle = ({ children, eventKey, callback }) => {

        const { activeEventKey } = useContext(AccordionContext);

        const decoratedOnClick = useAccordionButton(
            eventKey,
            () => callback && callback(eventKey),
        );

        const isCurrentEventKey = activeEventKey === eventKey;

        return (

            <i className={`pi ${isCurrentEventKey ? 'pi-angle-up' : 'pi-angle-down'}`} onClick={decoratedOnClick}></i>

        )
    }



    const onSubmitUpdate = () => {
        getRiskUpdate();
        createUserHandler();


    }
    const createUserHandler = async () => {

        setIsLoading(true);

        const response1 = await API.patch(RISK_WITH_ID_URL(data.id), {

            tasks: task,
            workActivityId: selectedActivity,
            departmentId: selectedDepart,
            overallRecommendationOne: recommendationOne,
            overallRecommendationTwo: recommendationTwo,
            additonalRemarks: additionalRecommendation,
            highRisk: eptwHot,
            nonRoutineDepartment: nonRoutineDepartment,
            nonRoutineWorkActivity: nonRoutineActivity,
            hazardName: hazardName,
            raTeamMembersList: selectedCrew,
            description: activityDesc


        });

        if (response1.status === 204) {
            setIsLoading(false);

            customSwal2.fire("Risk Assessment Updated!", "", "success").then((result) => {
                if (result.isConfirmed) {
                    history.goBack()
                }
            });

        } else {
            //show error
            customSwal2.fire("Please Try Again!", "", "error");
            setIsLoading(false);
        }


    }

    const findMatrixValue = (idValue, columnValue) => {
        // Map the numeric column value to the respective field name
        const columnMap = {
            1: 'rare',
            2: 'unlikely',
            3: 'possible',
            4: 'likely',
            5: 'almostCertain'
        };

        // Get the actual column name based on the columnValue passed
        const columnKey = columnMap[columnValue];

        // Find the row that matches the given id value (e.g., "2(D)")
        const row = tableData.find(item => item.id.startsWith(`${idValue}(`));

        // If row and column exist, return the corresponding value
        if (row && row[columnKey]) {
            return row[columnKey];
        }

        return 0; // Return null if no match is found
    };

    return (

        <div className="row">
            <div className="col-12">
                <div className="card">
                    <div className='card-body p-0'>
                        <div className='col-12' style={{ display: 'flex', justifyContent: 'flex-end' }}>
                            {/* <i className="fa fa-download " style={{ fontSize: 30, color: '#d62828', }} onClick={() => generatePdf()}></i> */}
                            {data?.type !== 'High-Risk Hazard' && <GenerateLandPdf pdf={data} />}
                        </div>
                        <div className='borderSection p-4'>

                            {data?.type === 'Routine' ? (
                                <div className='row mb-4'>
                                    <div className='col-4'>
                                        <h6 className='fw-bold'>Operational Risk Area</h6>
                                        <p>{selectedDepart?.label}</p>
                                    </div>
                                    <div className='col-8'>
                                        <h6 className='fw-bold'>Work Activity</h6>
                                        <p>{selectedActivity?.label}</p>
                                    </div>
                                </div>
                            ) : data?.type === 'Non Routine' ? (
                                <div className='row mb-4'>
                                    {/* <div className='col-4'>
                                        <h6 className='fw-bold'>Department</h6>
                                        <p>{nonRoutineDepartment}</p>
                                    </div> */}
                                    <div className='col-12'>
                                        <h6 className='fw-bold'>Work Activity</h6>
                                        <p>{nonRoutineActivity}</p>
                                    </div>
                                </div>
                            ) : data?.type === 'High-Risk Hazard' ? (
                                <div className='row mb-4'>
                                    <div className='col-8'>
                                        <h6 className='fw-bold'>Hazard Name</h6>
                                        <p>{hazardName}</p>
                                    </div>
                                </div>
                            ) : null}

                            {/* <div className='row mb-4'>
                                <div className='col-8'>
                                    <label htmlFor="username" className='mb-2'>Permit Short Name</label>
                                    <p>{shortName}</p>

                                </div>

                            </div> */}

                            <div className='row mb-4'>
                                <div className='col-12'>
                                    <h6 className='fw-bold'>Team Members</h6>
                                    <ol>
                                        {selectedCrew.map((item) => (
                                            <li>{item.name}</li>
                                        ))}
                                    </ol>

                                </div>
                            </div>

                        </div>

                        <div className='borderSection p-4'>

                            <div className='row mb-4'>
                                <div className="d-flex flex-column col-8">
                                    <h6 className='fw-bold'>Description</h6>
                                    <p>{activityDesc}</p>

                                </div>
                            </div>

                        </div>
                        <div className='borderSection p-4'>
                            {type === 'hazard' &&
                                task.map((item, i) => {
                                    return (

                                        <>

                                            <div className='boxShadow p-4 mt-3'>
                                                <h6 className='fw-bold'>Consequences</h6>
                                                <p>Identify the potential consequence on Satefy, Environment, Financial , Security and Community/ Brand Exposure due to presence of hazards</p>

                                                {item[0].option.map((con, i) => {
                                                    return (<>

                                                        <div class="row mt-4 align-items-end">


                                                            <div className='col-3'>
                                                                <h6 className='fw-bold'>Impact on</h6>
                                                                <p>{con.current_type} </p>

                                                            </div>
                                                            <div className='col-8'>
                                                                <h6 className='fw-bold'>Description</h6>
                                                                <p>{con.value} </p>

                                                            </div>

                                                        </div>


                                                        <div className='col-12 mt-3'>
                                                            <label htmlFor="username" className='mb-2'>Uploaded</label>
                                                        </div>
                                                        <div className='col-12 mt-3 mb-3'>
                                                            <div className='row'>
                                                                {con.files && con.files.map((item, m) => {
                                                                    return (
                                                                        <div key={m} className="col-3  " style={{ position: 'relative' }}>
                                                                            <div className="boxShadow d-flex align-items-center justify-content-center" >
                                                                                <ImageComponent fileName={item} size={'100'} name={true} />
                                                                            </div>
                                                                        </div>
                                                                    )
                                                                })}
                                                            </div>
                                                        </div>
                                                    </>

                                                    )
                                                })}
                                            </div>
                                            <div className='boxShadow p-4 mt-3'>
                                                <h6 className='fw-bold'>Current Controls</h6>
                                                <p>Existing measures in place to mitigate or manage the above identified hazards and potential consequnces.</p>
                                                {item[1].option.map((con, i) => {
                                                    return (
                                                        <>
                                                            <div class="row mt-4 align-items-end">


                                                                <div className='col-3'>
                                                                    <h6 className='fw-bold'>Type</h6>
                                                                    <p>{con.current_type} </p>

                                                                </div>
                                                                <div className='col-3'>
                                                                    <h6 className='fw-bold'>Method</h6>
                                                                    <p>{con.method} </p>

                                                                </div>
                                                                <div className='col-6'>
                                                                    <h6 className='fw-bold'>Description</h6>
                                                                    <p>{con.value} </p>

                                                                </div>

                                                            </div>


                                                            <div className='col-12 mt-3'>
                                                                <label htmlFor="username" className='mb-2'>Uploaded</label>
                                                            </div>
                                                            <div className='col-12 mt-3 mb-3'>
                                                                <div className='row'>
                                                                    {con.files && con.files.map((item, m) => {
                                                                        return (
                                                                            <div key={m} className="col-3  " style={{ position: 'relative' }}>
                                                                                <div className="boxShadow d-flex align-items-center justify-content-center" >
                                                                                    <ImageComponent fileName={item} size={'100'} name={true} />
                                                                                </div>
                                                                            </div>
                                                                        )
                                                                    })}
                                                                </div>
                                                            </div>
                                                        </>
                                                    )

                                                })}


                                            </div>
                                        </>)
                                })}
                            {type !== 'hazard' &&
                                <div className='row mb-4'>
                                    <div className='col-12'>
                                        <h4 className="risk-title-sub m-0">Sub Activities</h4>


                                    </div>
                                </div>
                            }
                            <div className='col-12 mb-4'>
                                {task.length === 0 ?
                                    <p>No sub-activities added</p>
                                    :
                                    type === "hazard" ?
                                        <></>
                                        :

                                        task.map((item, i) => {
                                            return (<>
                                                <TaskItem
                                                    key={i}
                                                    item={item}
                                                    index={i}
                                                    openDialog={openDialog}
                                                    type={type}
                                                    cellClassName={cellClassName}


                                                />

                                            </>)
                                        })
                                    // <Accordion>
                                    //     {task.map((item, i) => {
                                    //         return (<>
                                    //             <div className='d-flex align-items-center mb-3'>
                                    //                 <div className='col-1 d-flex justify-content-center'>
                                    //                     <span className='d-flex justify-content-center align-items-center' style={{ height: 35, width: 35, borderRadius: 20, border: '1px solid #000' }}>{i + 1}</span>
                                    //                 </div>
                                    //                 <div className='col-11 subBox p-3 d-flex'>
                                    //                     <div className='d-flex align-items-center col-11'>
                                    //                         <div className='col-4 d-flex align-items-center'>
                                    //                             <i className='pi pi-arrows-v'></i>
                                    //                             <h6 className='m-0 ps-2 pointer text-decoration-underline text-secondary' onClick={() => openDialog(item, i)}>{item[0].name}</h6>
                                    //                         </div>
                                    //                         <div className='col-8 d-flex align-items-center justify-content-end fsteps'>
                                    //                             {item[9].level.map((level, l) => {
                                    //                                 return (
                                    //                                     <div className="d-flex align-items-center">
                                    //                                         <RadioButton checked={item[10].level.includes(l)} />
                                    //                                         <span className="ms-2 me-2">{level}</span>
                                    //                                     </div>
                                    //                                     // <h6 className='d-flex align-items-center m-0'><i className='pi pi-circle'></i> <span>{level}</span> </h6>
                                    //                                 )
                                    //                             })}
                                    //                         </div>
                                    //                     </div>
                                    //                     <div className='col-1 text-center'>
                                    //                         <ContextAwareToggle eventKey={i} />
                                    //                     </div>


                                    //                 </div>

                                    //             </div>
                                    //             <Accordion.Collapse eventKey={i}>
                                    //                 <div className='p-4 mb-4 col-11' style={{ border: '1px solid rgba(209, 213, 219, 1)' }}>


                                    //                     <div className='col-12 mt-3'>
                                    //                         <label htmlFor="username" className='mb-2'>Uploaded</label>
                                    //                     </div>
                                    //                     <div className='col-12 mt-3'>
                                    //                         <div className='row'>
                                    //                             {item[0].images && item[0].images.map((item, m) => {
                                    //                                 return (
                                    //                                     <div className='col-2 d-flex align-items-center justify-content-center' style={{ position: 'relative' }}>
                                    //                                         <div className='boxShadow d-flex align-items-center' style={{ height: '100px' }}>

                                    //                                             <ImageComponent fileName={item} size={'100'} />
                                    //                                         </div>

                                    //                                     </div>
                                    //                                 )
                                    //                             })}
                                    //                         </div>
                                    //                     </div>


                                    //                 </div>
                                    //             </Accordion.Collapse>
                                    //         </>)
                                    //     })}
                                    // </Accordion>
                                }


                            </div>

                        </div>

                        {type !== 'hazard' && <>
                            <div className='borderSection p-4'>
                                <h5 className="mb-4 fw-bold">Overall recommendations of the RA Team</h5>

                                <div className='row mb-4'>
                                    <div className='col-8'>
                                        <div className='row'>
                                            <div className='col-12 mb-3'>
                                                <h6 className='fw-bold'>Recommendation One</h6>
                                                <p>{data?.overallRecommendationOne?.label || 'No Recommendation'}</p>
                                            </div>
                                            <div className='col-12'>
                                                <h6 className='fw-bold'>Recommendation Two</h6>
                                                <p>{data?.overallRecommendationTwo?.label || 'No Recommendation'}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div className='borderSection p-4'>
                                <div className='row mb-4'>
                                    <h6 className='fw-bold'>Considering the hazards and risks associated with this work activity, the RA team requires the following hish-risk permits to be approved and active when applying for permit for this specific activity</h6>
                                    <div className="d-flex flex-column col-8 p-3">

                                        {risk.length !== 0 &&

                                            risk.map(item => {
                                                if (eptwHot && eptwHot.some(item1 => item1.id === item.id)) {
                                                    return (

                                                        <label className='label-role checkbox-bootstrap checkbox-lg col-4 me-3' >

                                                            {item.hazardName}
                                                        </label>


                                                    )
                                                }
                                            })

                                        }
                                    </div>
                                </div>
                            </div>


                            <div className='borderSection p-4'>
                                <div className='row mb-4'>
                                    <div className="d-flex flex-column col-8">
                                        <label htmlFor="username" className='mb-2 fw-bold'>Additional Recommentation</label>
                                        <p>{additionalRecommendation}</p>

                                    </div>
                                </div>


                            </div>
                        </>}
                        <div className='borderSection p-4'>
                            <div className='row mb-4'>
                                <div className="d-flex flex-column col-12">
                                    <h6 htmlFor="username" className='mb-2 fw-bold'>Team Members Declaration</h6>
                                    {type === 'routine' ?
                                        <p>As a member of the team for this Routine Risk Assessment, I confirm that I have participated in identifying potential risks and controls associated with this activity. The identified controls are based on our collective judgment and professional experience, and we believe they are necessary to mitigate any foreseeable risks. The conclusions were reached collaboratively and through consensus.</p>
                                        : type === 'nonroutine' ?
                                            <p>As part of the team conducting this Non-Routine Risk Assessment, I confirm my role in assessing the risks and identifying necessary controls for an activity that is not included in the organization’s routine work activity inventory. Since this activity is not performed regularly, we have given extra attention to identifying and managing its risks. The controls listed are critical for safety, and this assessment reflects the team’s collective professional judgment and agreement.</p>
                                            : type === 'hazard' ?
                                                <p>As a member of the team for this exercise, I confirm that I have actively contributed to identifying the potential consequences of this Critical High Risk Activity and in determining the necessary controls. These controls reflect our collective professional judgment and are essential for ensuring safety. The conclusions we reached were the result of collaboration and consensus, utilizing our team’s full range of expertise.
                                                </p> : ''
                                    }
                                    {/* <p>As a member of the team for this exercise, I confirm that I have actively contributed to identifying the potential consequences of this Critical High Risk Activity and in determining the necessary controls. These controls reflect our collective professional judgment and are essential for ensuring safety. The conclusions we reached were the result of collaboration and consensus, utilizing our team’s full range of expertise.</p> */}
                                    <table className="table ">
                                        <thead>
                                            <th>Name</th>
                                            <th>Sign / Date of Affimation</th>
                                        </thead>
                                        <tbody>
                                            {raTeamMember.length !== 0 && raTeamMember.map((item) => (
                                                <tr>
                                                    <td>{item.user.firstName}</td>

                                                    <td>{item.signature ? <div className='d-flex flex-column align-items-start'>
                                                        <ImageComponent fileName={item.signature} size={'200'} name={false} /><span>{moment(item.signatureDate).format('DD-MM-YYYY')}</span></div> : "Pending"}</td>

                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>

                                </div>
                            </div>


                        </div>

                        <div className='borderSection p-4'>
                            <h5 className="mb-4 fw-bold">Declaration</h5>

                            <div className='row mb-2'>
                                {type === 'routine' ?
                                    <p>As the Team Leader for this Routine Risk Assessment, I affirm my role in guiding the identification of potential risks and necessary controls. The controls listed have been evaluated by the team based on their professional expertise and are essential for ensuring safety in this activity. This outcome reflects the collective judgment of the team, reached through collaboration and consensus.
                                        .</p>
                                    : type === 'nonroutine' ?
                                        <p>As the Team Leader for this Non-Routine Risk Assessment, I confirm my responsibility in guiding the team through the identification of potential risks and necessary controls for activities that are not part of the organization’s routine work activity inventory. Given that these activities are not regularly undertaken, we are placing additional focus on documenting the risks and ensuring that appropriate controls are in place. The controls identified are essential for maintaining safety, and this conclusion has been reached through consensus based on the team’s collective professional judgment and experience.
                                        </p>
                                        : type === 'hazard' ?
                                            <p>As the Team Leader for this exercise, I confirm my role in identifying the potential consequences of this Critical High Risk Activity and in outlining the necessary controls. Our team has used its professional judgment to determine that these controls are essential to ensuring safety during this type of work. The results are based on the team’s collective expertise and consensus, drawing on our full capabilities.


                                            </p> : ''
                                }
                                {/* <p>As the Team Leader for this exercise, I confirm my role in identifying the potential consequences of this Critical High Risk Activity and in outlining the necessary controls. Our team has used its professional judgment to determine that these controls are essential to ensuring safety during this type of work. The results are based on the team’s collective expertise and consensus, drawing on our full capabilities.</p> */}
                            </div>

                            <div className='row mb-4 text-center'>
                                <div className="d-flex flex-column col-12">
                                    <div className="row mt-4">
                                        <div className="col-12">

                                            {data?.teamLeaderDeclaration?.sign ? (
                                                <>
                                                    <ImageComponent fileName={data?.teamLeaderDeclaration?.sign} size={'300'} name={false} />
                                                    <p>{data?.teamLeaderDeclaration?.name}</p>
                                                </>
                                            ) : (
                                                <p>Team Leader Declaration not available</p>
                                            )}

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>



                    </div>
                </div>
            </div>
            {riskUpdate && <RiskUpdate show={riskUpdate} onChangeModel={setRiskUpdate} id={data.id} onSubmitUpdate={onSubmitUpdate} />}
            {Update.length !== 0 && <UpdateTable data={Update} />}
            {type !== 'hazard' && <>
                {item !== '' && <>
                    <Dialog visible={visible} header={headerTemplate} modal footer={footerTemplate} style={{ width: '70rem' }} onHide={() => setVisible(false)}>



                        {/* <h6>Hazards Identification</h6> */}
                        <div className='boxShadow p-4 mt-3'>
                            <h6 className=' mb-3'>Hazards Identified</h6>
                            <div className='row'>
                                {item[1].selected.map((item) => {
                                    return (
                                        <div className='col-3 mb-3'>
                                            <div className='d-flex justify-content-between align-items-center p-2' style={{ border: '1px solid #E5E7EB', borderRadius: 8 }}>
                                                <img
                                                    src={
                                                        "https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/hazards/" +
                                                        item.image
                                                    }
                                                    style={{
                                                        height: 40,
                                                    }}
                                                    alt="sample"
                                                />
                                                <p>{item.name}</p>
                                                {/* <i className='pi pi-times' onClick={() => onDeleteHaz(item)}></i> */}
                                            </div>


                                        </div>
                                    )

                                })}

                            </div>
                        </div>


                        <div className='boxShadow p-4 mt-3'>
                            <h6 className='fw-bold'>Consequences</h6>
                            <p>Identify the potential consequence on Satefy, Environment, Financial , Security and Community/ Brand Exposure due to presence of hazards</p>

                            {item[2].option.map((con, i) => {
                                return (<>

                                    <div class="row mt-4 align-items-end">


                                        <div className='col-3'>
                                            <h6 className='fw-bold'>Impact on</h6>
                                            <p>{con.current_type} </p>

                                        </div>
                                        <div className='col-8'>
                                            <h6 className='fw-bold'>Description</h6>
                                            <p>{con.value} </p>

                                        </div>

                                    </div>


                                    <div className='col-12 mt-3'>
                                        <label htmlFor="username" className='mb-2'>Uploaded</label>
                                    </div>
                                    <div className='col-12 mt-3 mb-3'>
                                        <div className='row'>
                                            {con.files && con.files.map((item, m) => {
                                                return (
                                                    <div key={m} className="col-3  " style={{ position: 'relative' }}>
                                                        <div className="boxShadow d-flex align-items-center justify-content-center" >
                                                            <ImageComponent fileName={item} size={'100'} name={true} />
                                                        </div>
                                                    </div>
                                                )
                                            })}
                                        </div>
                                    </div>
                                </>

                                )
                            })}
                        </div>
                        <div className='boxShadow p-4 mt-3'>
                            <h6 className='fw-bold'>Current Controls</h6>
                            <p>Existing measures in place to mitigate or manage the above identified hazards and potential consequnces.</p>
                            {item[3].option.map((con, i) => {
                                return (
                                    <>
                                        <div class="row mt-4 align-items-end">


                                            <div className='col-3'>
                                                <h6 className='fw-bold'>Type</h6>
                                                <p>{con.current_type} </p>

                                            </div>
                                            <div className='col-3'>
                                                <h6 className='fw-bold'>Method</h6>
                                                <p>{con.method} </p>

                                            </div>
                                            <div className='col-6'>
                                                <h6 className='fw-bold'>Description</h6>
                                                <p>{con.value} </p>

                                            </div>

                                        </div>


                                        <div className='col-12 mt-3'>
                                            <label htmlFor="username" className='mb-2'>Uploaded</label>
                                        </div>
                                        <div className='col-12 mt-3 mb-3'>
                                            <div className='row'>
                                                {con.files && con.files.map((item, m) => {
                                                    return (
                                                        <div key={m} className="col-3  " style={{ position: 'relative' }}>
                                                            <div className="boxShadow d-flex align-items-center justify-content-center" >
                                                                <ImageComponent fileName={item} size={'100'} name={true} />
                                                            </div>
                                                        </div>
                                                    )
                                                })}
                                            </div>
                                        </div>
                                    </>
                                )

                            })}


                        </div>
                        <div className='boxShadow p-4 mt-3'>
                            <h6>Risk Assessment</h6>
                            <p>Risk level based on the presence of the identified controls</p>

                            <div class="row mt-4 mb-3 pb-4" style={{ borderBottom: '1px solid #E0E0E0' }}>


                                <div className='col-8'>
                                    <h6>Severity</h6>
                                    <p>Degree of harm or impact that could result from a hazardous event or situation</p>

                                </div>
                                <div className='col-4 text-center'>
                                    {/* <Dropdown className={`d-flex ${(required === false && item[4].severity === '') ? 'borderRed' : ''}`} options={severity} value={item[4].severity} onChange={(e) => onChangeSeverity(e, 'assessment')} /> */}
                                    <p className='fw-bold'>{item[4].severity}</p>
                                </div>
                                <h6 className='mt-3 pointer' onClick={() => setSeverityTable(!severityTable)}> Understand Severity Levels <i className={`pi ${severityTable ? 'pi-angle-up' : 'pi-angle-down'} ms-1`}></i></h6>
                                {severityTable &&
                                    <div className='col-12 mt-3'>
                                        <div className="card">
                                            <DataTable value={severityData} className="table-bordered">
                                                <Column field="id" header="Severity Level"></Column>
                                                <Column field="severity" header="Descriptor"></Column>
                                                <Column field="personnel" header="Personnel"></Column>
                                                <Column field="property" header="Equipment / Property"></Column>
                                                <Column field="environment" header="Environment"></Column>
                                                <Column field="serviceLoss" header="Service Loss"></Column>
                                            </DataTable>
                                        </div>
                                    </div>}

                            </div>
                            <div class="row mt-4 mb-3 pb-4" style={{ borderBottom: '1px solid #E0E0E0' }}>


                                <div className='col-8'>
                                    <h6>Likelihood</h6>
                                    <p>Frequency with which a hazardous event or situation could happen</p>
                                </div>
                                <div className='col-4 text-center'>
                                    <p className='fw-bold'>{item[4].likelyhood}</p>

                                </div>

                                <h6 className='mt-3 pointer' onClick={() => setLikelyhoodTable(!likelyhoodTable)}> Understand Likelihood Levels <i className={`pi ${likelyhoodTable ? 'pi-angle-up' : 'pi-angle-down'} ms-1`}></i></h6>
                                {likelyhoodTable &&
                                    <div className='col-12 mt-3'>
                                        <div className="card">
                                            <DataTable value={levelData} className="table-bordered" rowClassName={rowClassName}>
                                                <Column field="level" header="Level"></Column>
                                                <Column field="descriptor" header="Descriptor"></Column>
                                                <Column field="detailedDescription" header="Detailed Description"></Column>
                                            </DataTable>
                                        </div>
                                    </div>}

                            </div>
                            <div class="row mt-4 mb-3 pb-4" >


                                <div className='col-8'>
                                    <h6>Risk Level</h6>

                                </div>
                                <div className='col-4'>
                                    <div className={`boxShadow p-2 text-center fw-bold ${cellClassName(item[4].severity * item[4].likelyhood)}`}>
                                        {findMatrixValue(item[4].severity, item[4].likelyhood)}
                                    </div>
                                </div>
                                <h6 className='mt-3 pointer' onClick={() => setRiskTable(!riskTable)}> Understand Risk Levels <i className={`pi ${riskTable ? 'pi-angle-up' : 'pi-angle-down'} ms-1`}></i></h6>
                                {riskTable &&
                                    <div className='col-12 mt-3'>
                                        <div className="card">
                                            <DataTable value={tableData} className="table-bordered">
                                                <Column field="id" header=""></Column>
                                                <Column field="severity" header=""></Column>
                                                <Column field="rare" header="1 (A) Rare" bodyClassName={(data) => cellStyle(data, 'rare')}></Column>
                                                <Column field="unlikely" header="2 (B) Unlikely" bodyClassName={(data) => cellStyle(data, 'unlikely')}></Column>
                                                <Column field="possible" header="3 (C) Possible" bodyClassName={(data) => cellStyle(data, 'possible')}></Column>
                                                <Column field="likely" header="4 (D) Likely" bodyClassName={(data) => cellStyle(data, 'likely')}></Column>
                                                <Column field="almostCertain" header="5 (E) Almost Certain" bodyClassName={(data) => cellStyle(data, 'almostCertain')}></Column>
                                            </DataTable>
                                        </div>
                                    </div>}
                            </div>

                            <h5 className="mb-4 fw-bold ">Risk Level</h5>
                            <div className='row mb-4 '>
                                <div className='col-12 '>

                                    <h6 className='fw-bold  mb-3 ms-2'>is this Risk Level Acceptable ?</h6>
                                    <div className='col-12'>
                                        <div className=" box-outer mb-4">
                                            <label
                                                htmlFor="user_name mt-3"


                                                className={
                                                    item[5].accept === true
                                                        ? "box_select active"
                                                        : "box_select "
                                                }
                                            >
                                                Yes
                                            </label>
                                            <label
                                                htmlFor="user_name mt-3"


                                                className={
                                                    item[5].accept === false
                                                        ? "box_select active red"
                                                        : "box_select "
                                                }
                                            >
                                                No
                                            </label>
                                        </div>
                                    </div>
                                    {/* <InputSwitch checked={item[5].accept} onChange={(e) => onChangeReAss(e.value)} /> <span className='bold ms-2'>This Risk Level is acceptable</span> */}
                                </div>

                            </div>


                        </div>




                        {item[5].accept === false && <>
                            <div className='boxShadow p-4 mt-3'>
                                <div className=' '>

                                    <h5 className="mb-4 fw-bold">Proposed Additional Controls</h5>
                                    {item[6].option.map((item, i) => {
                                        return (
                                            <div className='row pb-4 mt-3' >

                                                <div className='col-12 mb-4'>
                                                    <div className='row'>
                                                        <div className='col-5'>
                                                            <h6>Type</h6>
                                                            <p>{item.current_type}</p>

                                                        </div>
                                                        <div className='col-7'>
                                                            <h6>Description</h6>
                                                            <p>{item.value}</p>

                                                        </div>

                                                    </div>

                                                </div>
                                                <div className='col-12 '>
                                                    <div className='row'>
                                                        <div className='col-5'>
                                                            <h6>Responsibility</h6>
                                                            <p>{item.person.name}</p>

                                                        </div>
                                                        <div className='col-7'>
                                                            <h6>Date</h6>
                                                            <p>{item.date}</p>

                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        )
                                    })}




                                </div>
                                <h5 className='mt-4'>Residual Risk Assessment</h5>
                                <p>Expected risk based on the implementation of the identified additional controls</p>

                                <div class="row mt-4 mb-3 pb-4" style={{ borderBottom: '1px solid #E0E0E0' }}>


                                    <div className='col-8'>
                                        <h6>Severity</h6>
                                        <p>Degree of harm or impact that could result from a hazardous event or situation</p>

                                    </div>
                                    <div className='col-4 text-center'>
                                        <p className="fw-bold">{item[7].severity}</p>

                                    </div>
                                    <h6 className='mt-3 pointer' onClick={() => setSeverityTable(!severityTable)}> Understand Severity Levels <i className={`pi ${severityTable ? 'pi-angle-up' : 'pi-angle-down'} ms-1`}></i></h6>
                                    {severityTable &&
                                        <div className='col-12 mt-3'>
                                            <div className="card">
                                                <DataTable value={severityData} className="table-bordered">
                                                    <Column field="id" header="Severity Level"></Column>
                                                    <Column field="severity" header="Descriptor"></Column>
                                                    <Column field="personnel" header="Personnel"></Column>
                                                    <Column field="property" header="Equipment / Property"></Column>
                                                    <Column field="environment" header="Environment"></Column>
                                                    <Column field="serviceLoss" header="Service Loss"></Column>
                                                </DataTable>
                                            </div>
                                        </div>}

                                </div>
                                <div class="row mt-4 mb-3 pb-4" style={{ borderBottom: '1px solid #E0E0E0' }}>


                                    <div className='col-8'>
                                        <h6>Likelihood</h6>
                                        <p>Frequency with which a hazardous event or situation could happen</p>
                                    </div>
                                    <div className='col-4 text-center'>
                                        <p className="fw-bold">{item[7].likelyhood}</p>

                                    </div>

                                    <h6 className='mt-3 pointer' onClick={() => setLikelyhoodTable(!likelyhoodTable)}> Understand Likelihood Levels <i className={`pi ${likelyhoodTable ? 'pi-angle-up' : 'pi-angle-down'} ms-1`}></i></h6>
                                    {likelyhoodTable &&
                                        <div className='col-12 mt-3'>
                                            <div className="card">
                                                <DataTable value={levelData} className="table-bordered" rowClassName={rowClassName}>
                                                    <Column field="level" header="Level"></Column>
                                                    <Column field="descriptor" header="Descriptor"></Column>
                                                    <Column field="detailedDescription" header="Detailed Description"></Column>
                                                </DataTable>
                                            </div>
                                        </div>}

                                </div>
                                <div class="row mt-4 mb-3 pb-4" >


                                    <div className='col-8'>
                                        <h6>Risk Level</h6>
                                        <p></p>
                                    </div>
                                    <div className='col-4'>
                                        <div className={`boxShadow p-2 text-center fw-bold ${cellClassName(item[7].severity * item[7].likelyhood)}`}>
                                            {findMatrixValue(item[7].severity, item[7].likelyhood)}

                                        </div>
                                    </div>
                                    <h6 className='mt-3 pointer' onClick={() => setRiskTable(!riskTable)}> Understand Risk Levels <i className={`pi ${riskTable ? 'pi-angle-up' : 'pi-angle-down'} ms-1`}></i></h6>
                                    {riskTable &&
                                        <div className='col-12 mt-3'>
                                            <div className="card">
                                                <DataTable value={tableData} className="table-bordered">
                                                    <Column field="id" header=""></Column>
                                                    <Column field="severity" header=""></Column>
                                                    <Column field="rare" header="1 Rare" bodyClassName={(data) => cellStyle(data, 'rare')}></Column>
                                                    <Column field="unlikely" header="2 Unlikely" bodyClassName={(data) => cellStyle(data, 'unlikely')}></Column>
                                                    <Column field="possible" header="3 Possible" bodyClassName={(data) => cellStyle(data, 'possible')}></Column>
                                                    <Column field="likely" header="4 Likely" bodyClassName={(data) => cellStyle(data, 'likely')}></Column>
                                                    <Column field="almostCertain" header="5 (E) Almost Certain" bodyClassName={(data) => cellStyle(data, 'almostCertain')}></Column>
                                                </DataTable>
                                            </div>
                                        </div>}
                                </div>
                            </div>
                        </>}

                    </Dialog>
                </>}
            </>}
        </div>


    )
}

export default EditRoutine