import React, { useState, useEffect } from 'react';
import Tabs from '@mui/material/Tabs';
import MTab from '@mui/material/Tab';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import PropTypes from 'prop-types';
import CircularProgress from '@mui/material/CircularProgress';
import AppSwitch from '../pages/AppSwitch';
import API from '../services/API';
import { ASSIGNED_ACTION_URL } from '../constants';
import Actions from './Actions';
import ChecklistLibrary from './Checklist';
import InspectionSchedule from './Inspection';
import OpenActions from './OpenAction';
import InspectionReports from './Report';
import ClosedActions from './CloseAction';

const customFontStyle = {
    fontFamily: 'Lato, sans-serif',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
};

function CustomTabPanel(props) {
    const { children, value, tabValue, ...other } = props;
    return (
        <div
            role="tabpanel"
            hidden={value !== tabValue}
            id={`incident-tabpanel-${tabValue}`}
            aria-labelledby={`incident-tab-${tabValue}`}
            {...other}
        >
            {value === tabValue && <Box bgcolor={'#fff'}>{children}</Box>}
        </div>
    );
}

CustomTabPanel.propTypes = {
    children: PropTypes.node,
    tabValue: PropTypes.string.isRequired,
    value: PropTypes.string.isRequired,
};

const Dashboard = () => {
    const [value, setValue] = useState('INSPECTION');
    const [actions, setActions] = useState([]);
    const [checklists, setChecklists] = useState([]);
    const [loadingActions, setLoadingActions] = useState(true);
    const [loadingChecklists, setLoadingChecklists] = useState(true);

    const [actionCount, setActionCount] = useState(0);
    const [checklistCount, setChecklistCount] = useState(0);
    const [inspectionCount, setInspectionCount] = useState(0); // You can fetch inspection data separately later
    const [openActionCount, setOpenActionCount] = useState(0);
    const [closeActionCount, setCloseActionCount] = useState(0);
    const [reportCount, setReportCount] = useState(0);
    const [loadingInspections, setLoadingInspections] = useState(true);
    const [inspections, setInspections] = useState([]);
    const [loadingCompleted, setLoadingCompleted] = useState(true);
    const [completedInspections, setCompletedInspections] = useState([]);
    const [loadingScheduled, setLoadingScheduled] = useState(true);


    const TABS = {
        ACTIONS: 'ACTIONS',
        CHECKLIST: 'CHECKLIST',
        INSPECTION: 'INSPECTION',
        OPEN_ACTION: 'OPEN_ACTION',
        CLOSE_ACTION: 'CLOSE_ACTION',
        REPORT: 'REPORT',
    };

    useEffect(() => {
        fetchActions();
        fetchChecklistData();
        fetchInspectionData()

        fetchInspectionReportData()
    }, []);

    const fetchInspectionData = async () => {
        setLoadingInspections(true);
        try {
            const uriString = {
                where: { status: 'Scheduled' },
                include: [
                    { relation: "locationOne" },
                    { relation: "locationTwo" },
                    { relation: "locationThree" },
                    { relation: "locationFour" },
                    { relation: "locationFive" },
                    { relation: "locationSix" },
                    { relation: "checklist" },
                    { relation: "inspector" },
                    { relation: 'assignedBy' }

                ]
            };

            const url = `/inspections?filter=` + encodeURIComponent(JSON.stringify(uriString));
            const response = await API.get(url);
            if (response.status === 200) {
                setInspections(response.data); // Store the actual data
                setInspectionCount(response.data.length); // Store the count separately
            } else {
                console.error('Failed to fetch inspections');
            }
        } catch (error) {
            console.error('Error fetching inspections:', error);
        } finally {
            setLoadingInspections(false);
        }
    };

    const fetchInspectionReportData = async () => {
        setLoadingCompleted(true);
        try {
            const uriString = {
              
                where: {
                    status: { neq: 'Scheduled' } // or use $ne if your backend expects Mongo-style
                },
                include: [
                    { relation: "locationOne" },
                    { relation: "locationTwo" },
                    { relation: "locationThree" },
                    { relation: "locationFour" },
                    { relation: "locationFive" },
                    { relation: "locationSix" },
                    { relation: "checklist" },
                    { relation: "inspector" },
                    { relation: 'assignedBy' }

                ]
            };

            const url = `/inspections?filter=` + encodeURIComponent(JSON.stringify(uriString));
            const response = await API.get(url);
            if (response.status === 200) {
                setCompletedInspections(response.data);
                setReportCount(response.data.length);
            } else {
                console.error('Failed to fetch inspections');
            }
        } catch (error) {
            console.error('Error fetching inspections:', error);
        } finally {
            setLoadingCompleted(false);
        }
    };



    const fetchActions = async () => {
        setLoadingActions(true);
        const uriString = {
            include: [{ relation: "submittedBy" }]
        };
        const url = `${ASSIGNED_ACTION_URL('INS')}?filter=${encodeURIComponent(
            JSON.stringify(uriString)
        )}`;
        try {
            const response = await API.get(url);
            if (response.status === 200) {
                setActions(response.data);
                setActionCount(response.data.length);
            } else {
                console.error('Unexpected response status:', response.status);
            }
        } catch (error) {
            console.error('Error fetching actions:', error);
        } finally {
            setLoadingActions(false);
        }
    };

    const fetchChecklistData = async () => {
        setLoadingChecklists(true);
        try {
            const uriString = {
                include: [{ relation: "curator" }]
            };
            const url = '/checklists?filter=' + encodeURIComponent(JSON.stringify(uriString));
            const response = await API.get(url);
            if (response.status === 200) {
                setChecklists(response.data);
                setChecklistCount(response.data.length);
            } else {
                console.error('Failed to fetch checklist data');
            }
        } catch (error) {
            console.error('Error fetching checklist:', error);
        } finally {
            setLoadingChecklists(false);
        }
    };

    const handleChange = (event, newValue) => {
        setValue(newValue);
    };

    return (
        <>
            <AppSwitch value={{ label: 'Inspection', value: 'inspection' }} />

            <Tabs value={value} onChange={handleChange} aria-label="dashboard-tabs" className="risk">
                <MTab label={
                    <Typography variant="body1" style={customFontStyle}>
                        Actions <span className="headerCount">{actionCount}</span>
                    </Typography>
                } value={TABS.ACTIONS} />

                <MTab label={
                    <Typography variant="body1" style={customFontStyle}>
                        Checklist <span className="headerCount">{checklistCount}</span>
                    </Typography>
                } value={TABS.CHECKLIST} />

                <MTab label={
                    <Typography variant="body1" style={customFontStyle}>
                        Scheduled Inspection <span className="headerCount">{inspectionCount}</span>
                    </Typography>
                } value={TABS.INSPECTION} />

                {/* <MTab label={
                    <Typography variant="body1" style={customFontStyle}>
                        Open Action <span className="headerCount">{openActionCount}</span>
                    </Typography>
                } value={TABS.OPEN_ACTION} />

                <MTab label={
                    <Typography variant="body1" style={customFontStyle}>
                        Close Action <span className="headerCount">{closeActionCount}</span>
                    </Typography>
                } value={TABS.CLOSE_ACTION} /> */}

                <MTab label={
                    <Typography variant="body1" style={customFontStyle}>
                        Report <span className="headerCount">{reportCount}</span>
                    </Typography>
                } value={TABS.REPORT} />
            </Tabs>

            <CustomTabPanel value={value} tabValue={TABS.ACTIONS}>
                {loadingActions ? (
                    <Box display="flex" justifyContent="center" alignItems="center" height="100%">
                        <CircularProgress />
                    </Box>
                ) : (
                    <Actions action={actions} />
                )}
            </CustomTabPanel>

            <CustomTabPanel value={value} tabValue={TABS.CHECKLIST}>
                {loadingChecklists ? (
                    <Box display="flex" justifyContent="center" alignItems="center" height="100%">
                        <CircularProgress />
                    </Box>
                ) : (
                    <ChecklistLibrary checklists={checklists} fetchChecklistData={fetchChecklistData} />
                )}
            </CustomTabPanel>

            <CustomTabPanel value={value} tabValue={TABS.INSPECTION}>
                {loadingInspections ? (
                    <Box display="flex" justifyContent="center" alignItems="center" height="100%">
                        <CircularProgress />
                    </Box>
                ) : (
                    <Box p={3}>
                        <InspectionSchedule inspections={inspections} fetchInspectionData={fetchInspectionData} />

                    </Box>
                )}
            </CustomTabPanel>


            <CustomTabPanel value={value} tabValue={TABS.OPEN_ACTION}>
                <Box p={3}>
                    <OpenActions />
                </Box>
            </CustomTabPanel>

            <CustomTabPanel value={value} tabValue={TABS.CLOSE_ACTION}>
                <Box p={3}>
                    <ClosedActions />
                </Box>
            </CustomTabPanel>

            <CustomTabPanel value={value} tabValue={TABS.REPORT}>
                {loadingCompleted ? (
                    <Box display="flex" justifyContent="center" alignItems="center" height="100%">
                        <CircularProgress />
                    </Box>
                ) : (
                    <Box p={3}>
                        <InspectionReports reports={completedInspections} />
                    </Box>
                )}
            </CustomTabPanel>
        </>
    );
};

export default Dashboard;
