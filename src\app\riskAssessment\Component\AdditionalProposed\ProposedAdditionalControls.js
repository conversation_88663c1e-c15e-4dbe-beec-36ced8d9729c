import React from 'react';
import Select from 'react-select'; // Import react-select's Select component
import { InputText } from 'primereact/inputtext';
import { Button } from 'primereact/button';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css'; // Import the CSS for styling


const ProposedAdditionalControls = ({ item, control, responsibility, onControlAddion, onControlAddionText, onDeleteConseq, onResponsePerson, onResponseDate, addAdditionalControl, required }) => {
    console.log(item[6].option)
    // Transform control and responsibility options for react-select
    const controlOptions = control.map(option => ({
        value: option.value || option, // Use option.value if available, otherwise use option directly
        label: option.label || option // Use option.label if available, otherwise use option directly
    }));

    const responsibilityOptions = responsibility.map(option => ({
        value: option.value || option,
        label: option.label || option.name // Use option.name for the label as mentioned in the prop
    }));

    console.log(responsibilityOptions)

    return (
        <div>
            <h5 className="mb-4 fw-bold">Proposed Additional Controls</h5>
            {item[6].option.map((controlItem, i) => (
                <div className='row pb-4 mt-3' key={i}>
                    <div className='col-12 mb-4'>
                        <div className='row'>
                            <div className='col-4'>
                                <p>Type</p>
                                <Select

                                    options={controlOptions} // Pass control options to react-select
                                    value={controlOptions.find(option => option.value === controlItem.current_type)} // Match value with selected control type
                                    onChange={(e) => onControlAddion(e.value, i)} // Pass selected control to handler
                                    styles={{ container: (base) => ({ ...base, width: '100%' }) }} // Full width
                                />
                            </div>
                            <div className='col-7'>
                                <p>Purpose of Control</p>
                                <InputText
                                    style={{ width: '100%' }}
                                    value={controlItem.value}
                                    onChange={(e) => onControlAddionText(e.target.value, i)}
                                />
                            </div>
                            <div className='col-1 text-center'>
                                <i className="pi pi-trash mb-3" onClick={() => onDeleteConseq(i, 'responsibility')}></i>
                            </div>
                        </div>
                    </div>
                    <div className='col-8'>
                        <div className='row'>
                            <div className='col-6'>
                                <p>Responsibility</p>
                                <Select
                                    options={responsibilityOptions} // Pass responsibility options to react-select
                                    value={responsibilityOptions.find(option => option.value.id === controlItem.person.id)} // Match value with selected responsibility person
                                    onChange={(e) => onResponsePerson(e.value, i)} // Pass selected person to handler
                                    styles={{ container: (base) => ({ ...base, width: '100%' }) }} // Full width
                                    filter
                                />
                            </div>
                            <div className='col-6'>
                                <p>Date</p>
                                <DatePicker
                                    selected={controlItem.date ? new Date(controlItem.date) : null}
                                    onChange={(date) => onResponseDate(date, i)}
                                    dateFormat="dd/MM/yyyy"
                                    placeholderText='Date'
                                    className='d-flex form-control'
                                    wrapperClassName='d-flex'
                                />

                            </div>
                        </div>
                    </div>
                </div>
            ))}
            <div className='row'>
                <div className='col-4'>
                    <Button label="Add Additional Control" outlined className='d-flex' onClick={addAdditionalControl} />
                </div>
            </div>
        </div>
    );
};

export default ProposedAdditionalControls;
