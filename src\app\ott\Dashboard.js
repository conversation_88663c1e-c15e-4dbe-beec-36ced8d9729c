import React, { useState, useEffect } from "react";
import { Tabs, CircularProgress } from '@mui/material';
import MTab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import PropTypes from 'prop-types';
import { useSelector } from "react-redux";
import { fetchActionsAPI, fetchOttListAPI } from "./Components/dashboardAPI";  // Import API functions
import Action from "./Actions";
import OttList from "./OttList";
import Archived from "./Archived";
import AppSwitch from "../pages/AppSwitch";

function CustomTabPanel(props) {
  const { children, value, tabValue, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== tabValue}
      id={`incident-tabpanel-${tabValue}`}
      aria-labelledby={`incident-tab-${tabValue}`}
      {...other}
    >
      {value === tabValue && (
        <Box>
          {children}
        </Box>
      )}
    </div>
  );
}

CustomTabPanel.propTypes = {
  children: PropTypes.node,
  value: PropTypes.string.isRequired,
  tabValue: PropTypes.string.isRequired,
};

const customFontStyle = {
  fontFamily: 'Lato, sans-serif',
  display: "flex",
  alignItems: 'center',
  justifyContent: 'center'
};

const Dashboard = () => {
  const user = useSelector((state) => state.login.user);
  const [action, setAction] = useState([]);
  const [ott, setOtt] = useState([]);
  const [archive, setArchive] = useState([]);
  const [value, setValue] = useState('DASHBOARD');
  const [loadingAction, setLoadingAction] = useState(true);
  const [loadingOtt, setLoadingOtt] = useState(true);
  const [loadingArchive, setLoadingArchive] = useState(true);
  const [filteredActionCount, setFilteredActionCount] = useState(0);
  const [filteredOttCount, setFilteredOttCount] = useState(0);
  const [filteredArchiveCount, setFilteredArchiveCount] = useState(0);

  const TABS = {
    ACTIONS: "MY ACTIONS",
    DASHBOARD: "DASHBOARD",
    ROUTINE: "ROUTINE"
  };

  useEffect(() => {
    // Fetch all data initially
    fetchActions();
    fetchOtt();
  }, []);

  // Fetch Action Data
  const fetchActions = () => {
    setLoadingAction(true);  // Show loading for Actions
    fetchActionsAPI()
      .then((data) => {
        setAction(data);
        setFilteredActionCount(data.length);
      })
      .finally(() => setLoadingAction(false));  // Stop loading after fetching data
  };

  // Fetch OttList Data
  const fetchOtt = () => {
    setLoadingOtt(true);   // Show loading for OttList
    setLoadingArchive(true); // Show loading for Archived Tasks
    fetchOttListAPI()
      .then(({ activeTasks, archivedTasks }) => {
        setOtt(activeTasks);
        setFilteredOttCount(activeTasks.length);

        setArchive(archivedTasks);
        setFilteredArchiveCount(archivedTasks.length);
      })
      .finally(() => {
        setLoadingOtt(false);
        setLoadingArchive(false);
      });
  };

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  return (
    <>
      <AppSwitch value={{ label: 'Operational Tasks', value: 'ra' }} />

      <Tabs value={value} onChange={handleChange} aria-label="incident report table">
        <MTab
          label={
            <Typography variant="body1" style={customFontStyle}>
              My Actions <span className='headerCount'>{filteredActionCount}</span>
            </Typography>
          }
          value={TABS.ACTIONS} />
        <MTab
          label={
            <Typography variant="body1" style={customFontStyle}>
              All Active Tasks <span className='headerCount'>{filteredOttCount}</span>
            </Typography>
          }
          value={TABS.DASHBOARD} />
        <MTab
          label={
            <Typography variant="body1" style={customFontStyle}>
              Archived Tasks <span className='headerCount'>{filteredArchiveCount}</span>
            </Typography>
          }
          value={TABS.ROUTINE} />
      </Tabs>

      {/* My Actions Tab */}
      <CustomTabPanel value={value} tabValue={TABS.ACTIONS}>
        {loadingAction ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
            <CircularProgress />
          </Box>
        ) : (
          <Action action={action} onRefresh={fetchActions} onFilterUpdate={setFilteredActionCount} />
        )}
      </CustomTabPanel>

      {/* All Active Tasks Tab */}
      <CustomTabPanel value={value} tabValue={TABS.DASHBOARD}>
        {loadingOtt ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
            <CircularProgress />
          </Box>
        ) : (
          <OttList data={ott} onRefresh={fetchOtt} onFilterUpdate={setFilteredOttCount} />
        )}
      </CustomTabPanel>

      {/* Archived Tasks Tab */}
      <CustomTabPanel value={value} tabValue={TABS.ROUTINE}>
        {loadingArchive ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
            <CircularProgress />
          </Box>
        ) : (
          <Archived data={archive} onRefresh={fetchOtt} onFilterUpdate={setFilteredArchiveCount} />
        )}
      </CustomTabPanel>
    </>
  );
};

export default Dashboard;
