import React from 'react';
import { Accordion, ProgressBar, Form, Row, Col, Modal, Button } from 'react-bootstrap';
import ImageComponent from '../../services/FileDownlodS3';

const TaskApproveComponent = ({
    showModal,
    setShowModal,
    applicationDetails,
    data,
    setFormData,
    handleReviewerComment,
    submitActionApprove
}) => {
    const totalItems = data && Array.isArray(data) ? data.length : 0;
    const selectedItems = data && Array.isArray(data) ? data.filter(item => item.checked).length : 0;
    const progressPercentage = totalItems ? (selectedItems / totalItems) * 100 : 0;
    const getPriorityClass = (priority) => {
        switch (priority.toLowerCase()) {
            case "high":
                return "text-danger"; // Red color for high priority
            case "medium":
                return "text-warning"; // Yellow/Orange color for medium priority
            case "low":
                return "text-success"; // Green color for low priority
            default:
                return "";
        }
    };

    const getProgressBarClass = () => {
        if (progressPercentage === 100) {
            return 'custom-progress-bar-completed';
        } else if (progressPercentage > 0) {
            return 'custom-progress-bar-inprogress';
        } else {
            return 'custom-progress-bar-empty';
        }
    };

    const handleCheckboxChange = (index) => {
        const updatedFormData = [...data];
        updatedFormData[index].checked = !updatedFormData[index].checked;
        setFormData(updatedFormData);
    };

    const handleCommentChange = (index, value) => {
        const updatedFormData = [...data];
        updatedFormData[index].reviewerComments = value;
        setFormData(updatedFormData);
    };

    const handleFileChange = (index, files) => {
        const updatedFormData = [...data];
        updatedFormData[index].evidence = Array.from(files);
        setFormData(updatedFormData);
    };

    return (
        <Modal
            show={showModal}
            size="md"
            onHide={() => setShowModal(false)}
            aria-labelledby="example-modal-sizes-title-md"
            backdrop="static"
        >
            <Modal.Header>
                <div className="w-100 d-flex justify-content-between align-items-center">
                    <h2 className="m-0">Please Approve</h2>
                    <div className="text-center mt-0"></div>
                </div>
            </Modal.Header>
            <Modal.Body>
                <div className="task-approve-component">
                    <div className="task-details-container">
                        <h5>Task Name: {applicationDetails.taskName}</h5>
                        <p>Description: {applicationDetails.taskDescription}</p>
                        <p><b>Project: </b>{applicationDetails.project?.name}</p>
                        <p><b>Category: </b>{applicationDetails.category?.name}</p>
                        <p><b>Assignor: </b>{applicationDetails.creator?.firstName}</p>
                        <p><b>Assignee: </b>{applicationDetails.assignee?.firstName}</p>
                        <p className={getPriorityClass(applicationDetails.priority)}>
                            Priority: {applicationDetails.priority}
                        </p>

                        <p>
                            <b>Assignee Comments: </b>
                        </p>
                        <p>{applicationDetails.assingneeComments}</p>

                        {applicationDetails.img && applicationDetails.img.length > 0 && (
                            <div className="d-flex">
                                {applicationDetails.img.map((item, m) => (
                                    <div key={m} className="  " style={{ position: 'relative' }}>
                                        <div className="boxShadow d-flex align-items-center" >
                                            <ImageComponent fileName={item} size={'100'} name={true}/>

                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}
                        {data &&
                            <h6 className="fw-bold m-3">Activities / Dependencies</h6>
                        }
                        <Accordion >
                            {data && data.map((item, index) => (
                                <Accordion.Item eventKey={index.toString()} key={index}>
                                    <Accordion.Header>
                                        <Form.Check
                                            type="checkbox"
                                            label={
                                                <span className={item.checked ? 'strike-through' : ''}>
                                                    {`${index + 1}. ${item.name}`}
                                                </span>
                                            }
                                            className="fw-bold"
                                            checked={item.checked}
                                            onChange={() => handleCheckboxChange(index)}
                                        />
                                    </Accordion.Header>
                                    <Accordion.Body>
                                        <Row className="mt-2">
                                            <Col>
                                                {item.img && item.img.length > 0 && (
                                                    <div className="col-12 mt-3 mb-3">
                                                        <Form.Label>Upload Images</Form.Label>
                                                        <div className="d-flex">
                                                            {item.img.map((imgItem, m) => (
                                                                <div key={m} className="" style={{ position: 'relative' }}>
                                                                    <div className="boxShadow d-flex align-items-center" style={{ height: '100px' }}>
                                                                        <ImageComponent fileName={imgItem} size={'100'} name={true}/>
                                                                    </div>
                                                                </div>
                                                            ))}
                                                        </div>
                                                    </div>
                                                )}
                                            </Col>
                                        </Row>
                                        <Row className="mt-2">
                                            <Col>
                                                {item.evidence && item.evidence.length > 0 && (
                                                    <div className="col-12 mt-3 mb-3">
                                                        <Form.Label>Upload Evidence</Form.Label>
                                                        <div className="d-flex">
                                                            {item.evidence.map((evidenceItem, m) => (
                                                                <div key={m} className="" style={{ position: 'relative' }}>
                                                                    <div className="boxShadow d-flex align-items-center">
                                                                        <ImageComponent fileName={evidenceItem} size={'100'} name={true}/>
                                                                    </div>
                                                                </div>
                                                            ))}
                                                        </div>
                                                    </div>
                                                )}
                                            </Col>
                                        </Row>
                                        <Row>
                                            <Col>
                                                <Form.Control
                                                    as="textarea"
                                                    rows={3}
                                                    placeholder="Enter Activity comments"
                                                    value={item.reviewerComments}
                                                    onChange={(e) => handleCommentChange(index, e.target.value)}
                                                />
                                            </Col>
                                        </Row>
                                    </Accordion.Body>
                                </Accordion.Item>
                            ))}
                        </Accordion>
                    </div>

                    <Form.Group controlId="reviewerComment" className="mb-3">
                        <Form.Label>Enter Reviewer Comments <span className="text-danger">*</span></Form.Label>
                        <Form.Control
                            as="textarea"
                            rows={3}
                            placeholder="Enter Reviewer comments"
                            onChange={(e) => handleReviewerComment(e.target.value)}
                        />
                    </Form.Group>
                </div>
            </Modal.Body>
            <Modal.Footer className="flex-wrap">
                <Button className="btn btn-secondary" onClick={() => submitActionApprove('approve')}>
                    Approve
                </Button>
                <Button className="btn btn-primary" onClick={() => submitActionApprove('return')}>
                    Return To Assignee
                </Button>
                <Button variant="light" onClick={() => setShowModal(false)}>Close</Button>
            </Modal.Footer>
        </Modal>
    );
};

export default TaskApproveComponent;
