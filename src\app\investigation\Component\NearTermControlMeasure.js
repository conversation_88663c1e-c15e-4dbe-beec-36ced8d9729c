import React, { useState, useEffect } from "react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import Select from "react-select";
import { Form, Row, Col, Modal } from "react-bootstrap";
import { But<PERSON> } from "primereact/button";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { GET_USER_ROLE_BY_MODE, INCIDENT_NEARTERM_WITH_ID, NEAR_TERM_WITH_ID } from "../../constants";
import API from "../../services/API";

const ControlMeasureForm = ({ id, disable }) => {
    const [formDataList, setFormDataList] = useState([]);
    const [incidentAssignee, setIncidentAssignee] = useState([]);
    const [showModal, setShowModal] = useState(false);
    const [editingIndex, setEditingIndex] = useState(null);
    const [currentFormData, setCurrentFormData] = useState({
        controlMeasure: "",
        dueDate: new Date(),
        responsibleId: null,
    });
    const [loading, setLoading] = useState(false); 
    const [deleteLoading, setDeleteLoading] = useState(false);

    useEffect(() => {
        fetchFormDataFromAPI();
        fetchIncidentAssignees();
    }, [id]);

    const fetchFormDataFromAPI = async () => {
        try {
            const uriString = { include: [{ relation: "responsible" }] };
            const url = `${INCIDENT_NEARTERM_WITH_ID(id)}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
            const response = await API.get(url);
            if (response.status === 200) {
                setFormDataList(response.data);
            }
        } catch (error) {
            console.error("Error fetching data:", error);
        }
    };

    const fetchIncidentAssignees = async () => {
        try {
            const response = await API.post(GET_USER_ROLE_BY_MODE, {
                locationOneId: "",
                locationTwoId: "",
                locationThreeId: "",
                locationFourId: "",
                mode: "incident_action_assignee",
            });
            if (response.status === 200) {
                const data = response.data.map(item => ({
                    label: item.firstName,
                    value: item.id,
                }));
                setIncidentAssignee(data);
            }
        } catch (error) {
            console.error("Error fetching assignees:", error);
        }
    };

    const handleModalOpen = (index = null) => {
        console.log(index)
        if (index !== null) {
            const selectedFormData = formDataList[index];
            const parsedDueDate = selectedFormData.dueDate ? new Date(selectedFormData.dueDate) : new Date();
            setCurrentFormData({
                ...selectedFormData,
                dueDate: parsedDueDate,
            });
            setEditingIndex(index);
        } else {
            setCurrentFormData({
                controlMeasure: "",
                dueDate: new Date(),
                responsibleId: null,
            });
            setEditingIndex(null);
        }
        setShowModal(true);
    };

    const handleModalClose = () => {
        setShowModal(false);
    };

    const handleInputChange = (field, value) => {
        setCurrentFormData(prevData => ({ ...prevData, [field]: value }));
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        try {
            if (editingIndex !== null) {
                await API.patch(NEAR_TERM_WITH_ID(currentFormData.id), {
                    controlMeasure: currentFormData.controlMeasure,
                    dueDate: currentFormData.dueDate,
                    responsibleId: currentFormData.responsibleId.value,
                });
            } else {
                await API.post(INCIDENT_NEARTERM_WITH_ID(id), {
                    controlMeasure: currentFormData.controlMeasure,
                    dueDate: currentFormData.dueDate,
                    responsibleId: currentFormData.responsibleId.value,
                });
            }
            await fetchFormDataFromAPI();
            setShowModal(false);
        } catch (error) {
            console.error("Error submitting form:", error);
        } finally {
            setLoading(false);
        }
    };

    const handleRemoveField = async (index) => {
        setDeleteLoading(true);
        try {
            const controlMeasureId = formDataList[index].id;
            await API.delete(NEAR_TERM_WITH_ID(controlMeasureId));
            await fetchFormDataFromAPI();
        } catch (error) {
            console.error("Error deleting control measure:", error);
        } finally {
            setDeleteLoading(false);
        }
    };

    const sNoTemplate = (rowData, { rowIndex }) => <span>{rowIndex + 1}</span>;

    const actionTemplate = (rowData, { rowIndex }) => (
        <>
            <Button
                icon="pi pi-pencil"
                className="p-button-warning p-button-rounded me-2"
                onClick={() => handleModalOpen(rowIndex)}
                disabled={disable}  // Disable editing if disable is true
            />
            <Button
                icon="pi pi-trash"
                className="p-button-danger p-button-rounded"
                onClick={() => handleRemoveField(rowIndex)}
                disabled={deleteLoading || disable}  // Disable deleting if disable is true
            />
        </>
    );

    return (
        <div>
            <Button
                label="Add Control"
                icon="pi pi-plus"
                className="p-button-primary mt-3"
                onClick={()=>handleModalOpen(null)}
                disabled={disable}  // Disable adding new control if disable is true
            />
            <DataTable value={formDataList} paginator rows={5} className="datatable-responsive styled-table">
                <Column field="sno" header="S.No" body={sNoTemplate} style={{ width: "5%" }} />
                <Column field="controlMeasure" header="Control Measure" style={{ width: "35%" }} />
                <Column
                    field="dueDate"
                    header="Due Date"
                    body={rowData => new Date(rowData.dueDate).toLocaleDateString()}
                    style={{ width: "20%" }}
                />
                <Column
                    field="responsibleId"
                    header="Responsibility"
                    body={rowData => (rowData.responsibleId ? rowData.responsible.firstName : "N/A")}
                    style={{ width: "25%" }}
                />
                <Column header="Actions" body={actionTemplate} style={{ width: "15%" }} />
            </DataTable>

            <Modal show={showModal} onHide={handleModalClose} className="grey-background-modal">
                <Modal.Header closeButton>
                    <Modal.Title>{editingIndex !== null ? "Edit Near Term Control Measure" : "Add Near Term Control Measure"}</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <Form onSubmit={handleSubmit}>
                        <Row className="mb-3">
                            <Col md={12}>
                                <Form.Group controlId="controlMeasure">
                                    <Form.Label>Control Measure</Form.Label>
                                    <Form.Control
                                        type="text"
                                        value={currentFormData.controlMeasure}
                                        onChange={(e) => handleInputChange("controlMeasure", e.target.value)}
                                        required
                                        disabled={disable}  // Disable input when disable is true
                                    />
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row className="mb-3">
                            <Col md={6}>
                                <Form.Group controlId="dueDate">
                                    <Form.Label>Due Date</Form.Label>
                                    <DatePicker
                                        selected={currentFormData.dueDate}
                                        onChange={(date) => handleInputChange("dueDate", date)}
                                        dateFormat="yyyy-MM-dd"
                                        className="form-control"
                                        required
                                        disabled={disable}  // Disable DatePicker when disable is true
                                    />
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group controlId="responsibleId">
                                    <Form.Label>Responsibility</Form.Label>
                                    <Select
                                        value={incidentAssignee.find(option => option.value === currentFormData.responsibleId)}
                                        onChange={(selectedOption) => handleInputChange("responsibleId", selectedOption)}
                                        options={incidentAssignee}
                                        isClearable
                                        classNamePrefix="react-select"
                                        isDisabled={disable}  // Disable select when disable is true
                                    />
                                </Form.Group>
                            </Col>
                        </Row>
                        <Button
                            label={editingIndex !== null ? "Save Changes" : "Add Control"}
                            icon="pi pi-check"
                            className="p-button-primary"
                            type="submit"
                            disabled={loading || disable}  // Disable form submit when loading or disable is true
                        />
                    </Form>
                </Modal.Body>
            </Modal>
        </div>
    );
};

export default ControlMeasureForm;
