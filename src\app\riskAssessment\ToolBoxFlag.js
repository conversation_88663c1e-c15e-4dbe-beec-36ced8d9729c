import React, { useState } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Dropdown } from 'primereact/dropdown';
import { InputText } from 'primereact/inputtext';

export default function ToolBoxFlag() {
  const [filters, setFilters] = useState({});
  const [globalFilter, setGlobalFilter] = useState('');

  const flagTypeOptions = [
    { label: 'Additional Control', value: 'Additional Control' },
    { label: 'Difficulty', value: 'Difficulty' },
    { label: 'Both', value: 'Both' }
  ];

  const conductedByOptions = [
    { label: 'Supervisor', value: 'Supervisor' },
    { label: 'Safety Officer', value: 'Safety Officer' },
    { label: 'Manager', value: 'Manager' }
  ];

  const statusOptions = [
    { label: 'Open', value: 'Open' },
    { label: 'Under Review', value: 'Under Review' },
    { label: 'Closed', value: 'Closed' }
  ];

  const [data, setData] = useState([
  
    // Add more sample data as needed
  ]);

  const tbtIdTemplate = (rowData) => {
    return (
      <a href="#" onClick={() => alert(`Viewing TBT: ${rowData.tbtId}`)}>
        {rowData.tbtId}
      </a>
    );
  };

  const actionsTakenEditor = (options) => {
    return (
      <InputText 
        value={options.value} 
        onChange={(e) => options.editorCallback(e.target.value)} 
        placeholder="Enter actions taken"
      />
    );
  };

  return (
    <div className="card p-3">
      {/* Description Section */}
      <div className="mb-4">
        <p>
          This table captures real-world insights from safety briefings / TBT to <strong>improve Risk Assessments (RAs)</strong> and build a <strong>centralized repository of practical learnings</strong>. Information logged here serves as critical feedback for RA amendments, highlighting gaps between planned controls and on-ground realities (e.g., unanticipated hazards, procedural challenges). RA Team Leaders can review these flags, edit entries, and document actions taken—such as updating controls or revising workflows—to address flagged issues. Over time, this repository helps quantify RA effectiveness; frequent flags may indicate shallow assessments, while resolved flags demonstrate iterative improvements. Metrics from this module (e.g., flag frequency, type) will feed into dashboards to track RA practicality and compliance.
        </p>
      </div>

      {/* Search Box */}
      <div className="d-flex justify-content-end mb-2">
        <span className="p-input-icon-left">
          <i className="pi pi-search" />
          <InputText 
            value={globalFilter} 
            onChange={(e) => setGlobalFilter(e.target.value)} 
            placeholder="Search" 
          />
        </span>
      </div>

      {/* Data Table */}
      <DataTable 
        value={data} 
        paginator 
        rows={5} 
        rowsPerPageOptions={[5, 10, 25]} 
        globalFilter={globalFilter} 
        tableStyle={{ minWidth: '90rem' }} 
        editable
      >

        <Column field="tbtId" header="TBT ID " sortable body={tbtIdTemplate}></Column>

        <Column field="workArea" header="Work Area "></Column>

        <Column 
          field="conductedBy" 
          header="Conducted By " 
          filter 
          filterElement={
            <Dropdown 
              value={filters.conductedBy} 
              options={conductedByOptions} 
              onChange={(e) => setFilters(prev => ({ ...prev, conductedBy: e.value }))}
              placeholder="Select" 
              showClear
            />
          }
        ></Column>

        <Column field="participants" header="No of Participants"></Column>

        <Column field="safetyBriefingStart" header="Safety Briefing / TBT Start Time " sortable></Column>

        <Column field="workStartTime" header="Work Start Time"></Column>

        <Column field="closeOutTime" header="TBT Close Out Time"></Column>

        <Column 
          field="flagType" 
          header="Flag Type " 
          filter 
          filterElement={
            <Dropdown 
              value={filters.flagType} 
              options={flagTypeOptions} 
              onChange={(e) => setFilters(prev => ({ ...prev, flagType: e.value }))}
              placeholder="Select" 
              showClear
            />
          }
        ></Column>

        <Column field="description" header="Description"></Column>

        <Column 
          field="status" 
          header="Status " 
          filter 
          filterElement={
            <Dropdown 
              value={filters.status} 
              options={statusOptions} 
              onChange={(e) => setFilters(prev => ({ ...prev, status: e.value }))}
              placeholder="Select" 
              showClear
            />
          }
        ></Column>

        <Column 
          field="actionsTaken" 
          header="Actions Taken" 
          editor={(options) => actionsTakenEditor(options)} 
        ></Column>

      </DataTable>
    </div>
  );
}
