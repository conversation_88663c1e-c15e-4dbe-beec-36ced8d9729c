import React, { useEffect, useState, useCallback, useRef } from 'react'
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";
// import 'primereact/resources/themes/lara-light-indigo/theme.css';
import 'primeicons/primeicons.css';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { MultiSelect } from 'primereact/multiselect';
import { FilterMatchMode, FilterOperator } from 'primereact/api';
import { Button } from 'primereact/button';
import ObservationReportModal from './Component/ObservationReportModal';
import { GET_USER_ROLE_BY_MODE, OBSERVATION_REPORT_URL, FILE_DOWNLOAD } from '../constants';
import API from '../services/API';
import Swal from 'sweetalert2';
import moment from 'moment';
import ViewObs from './Component/ViewObs';
import { Modal } from 'react-bootstrap';
import MyLogoComponent from '../eptw-gen/Component/MyLogoComponent';
import axios from 'axios';
import { useSelector } from 'react-redux';


const initialFormData = {

    observationCategory: 'Environment', // Default value
    observationType: 'Safe', // Default value
    observationActOrCondition: 'Act', // Default value
    description: '',
    comments: '',
    dueDate: new Date().toISOString(),
    rectifiedOnSpot: false,
    isQR: false,
    actionToBeTaken: '',
    isReviewerRequired: false,
    evidence: [],
    uploads: [],
    actionTaken: '',
    locationFiveId: "",
    locationFourId: "",
    locationOneId: "",
    locationSixId: "",
    locationThreeId: "",
    locationTwoId: "",
    actionOwnerId: "",
    reviewerId: ""
};

function Observation({ data, handleFilterChange }) {
    const user = useSelector((state) => state.login.user);
    const [risk, setRisk] = useState([])
    const [formData, setFormData] = useState([])
    const [show, setShow] = useState(false)
    const [record, setRecord] = useState([])
    const [viewShow, setViewShow] = useState(false)
    const [logo, setLogo] = useState('')
    const [filters, setFilters] = useState({
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        'reporter.firstName': { value: null, matchMode: FilterMatchMode.IN },
        'reviewer.firstName': { value: null, matchMode: FilterMatchMode.IN },
        'actionOwner.firstName': { value: null, matchMode: FilterMatchMode.IN },

        status: { value: null, matchMode: FilterMatchMode.IN },

        nextdate: { value: null, matchMode: FilterMatchMode.IN },
        permitType: { value: null, matchMode: FilterMatchMode.CUSTOM },
        status: { value: null, matchMode: FilterMatchMode.IN },
        captain: { value: null, matchMode: FilterMatchMode.IN },
        department: { value: null, matchMode: FilterMatchMode.IN },
    });
    const [reviewer, setReviewer] = useState([])
    const [actionOwner, setActionOwner] = useState([])
    const [status, setStatus] = useState([])
    const [reporter, setReporter] = useState([])

    const [review, setReview] = useState([])
    const [owner, setOwner] = useState([])


    useEffect(() => {
        setRisk(data)
        // getActionOwner()
        // getObsReviewer()
        getFetchLogo()
        initialData()
    }, [])

    const initialData = () => {
        const obs = data.map(item => {
            return { name: item.status || '', value: item.status || '' }
        })
        setStatus(obs.filter((ele, ind) => ind === obs.findIndex(elem => elem.value === ele.value && elem.name === ele.name)))

        const obs1 = data.map(item => {
            return { name: item.reporter?.firstName || '', value: item.reporter?.firstName || '' }
        })
        setReporter(obs1.filter((ele, ind) => ind === obs1.findIndex(elem => elem.value === ele.value && elem.name === ele.name)))
        const obs2 = data.map(item => {
            return { name: item.reviewer?.firstName || '', value: item.reviewer?.firstName || '' }
        });
        setReview(obs2.filter((ele, ind) => ind === obs2.findIndex(elem => elem.value === ele.value && elem.name === ele.name)));


        const obs3 = data.map(item => {
            return { name: item.actionOwner?.firstName || '', value: item.actionOwner?.firstName || '' }
        })
        setOwner(obs3.filter((ele, ind) => ind === obs3.findIndex(elem => elem.value === ele.value && elem.name === ele.name)))

        // const obs4 = data.map(item => {
        //     return { name: item.reviewer?.firstName || '', value: item.reviewer?.firstName || '' }
        // })
        // setReviewer(obs4.filter((ele, ind) => ind === obs4.findIndex(elem => elem.value === ele.value && elem.name === ele.name)))

        // const obs5 = data.map(item => {
        //     return { name: item.permitType[0] || '', value: item.permitType[0] || '' }
        // })
        // setType(obs5.filter((ele, ind) => ind === obs5.findIndex(elem => elem.value === ele.value && elem.name === ele.name)))
    }

    const getFetchLogo = async () => {
        try {
            const response = await axios.get(FILE_DOWNLOAD(localStorage.getItem('logo')), {
                headers: {
                    'Content-Type': 'application/json'
                },
            });
            const data = response.data;
            const logoUrl = data // Assuming the API returns an object with a `url` field
            setLogo(logoUrl);

        } catch (error) {
            console.error('Error fetching logo:', error);
        }
    }
    const getActionOwner = useCallback(async () => {
        try {
            const response = await API.post(GET_USER_ROLE_BY_MODE, {
                locationOneId: "",
                locationTwoId: "",
                locationThreeId: "",
                locationFourId: "",
                mode: 'obsactionowner'
            });

            if (response.status === 200) {
                const data = response.data.map((item) => ({
                    label: item.firstName,
                    value: item.id
                }));
                setActionOwner(data);
            }
        } catch (error) {
            console.error("Error fetching crew list:", error);
        }
    }, []);

    const getObsReviewer = useCallback(async () => {
        try {
            const response = await API.post(GET_USER_ROLE_BY_MODE, {
                locationOneId: "",
                locationTwoId: "",
                locationThreeId: "",
                locationFourId: "",
                mode: 'obsreviewer'
            });

            if (response.status === 200) {
                const data = response.data.map((item) => ({
                    label: item.firstName,
                    value: item.id
                }));
                setReviewer(data);
            }
        } catch (error) {
            console.error("Error fetching crew list:", error);
        }
    }, []);

    const renderHeader = () => {
        const value = filters['global'] ? filters['global'].value : '';

        return (

            <div className='d-flex justify-content-end'>
                {user.roles.some(role => ['obsreporter'].includes(role.maskId)) && (
                    <Button className="btn btn-primary mb-3" onClick={() => { setFormData(initialFormData); setShow(true); }}>
                        Record Observation
                    </Button>
                )}
            </div>
        );
    };
    const header = renderHeader()

    const locationBodyTemplate = (row) => {
        if (row.isCustomLocation) {
            return row.customLocation;
        } else {
            return (
                <>

                    {row?.locationOne?.name && (
                        <>
                            {row.locationOne.name}
                            {row.locationTwo?.name && ' > '}
                        </>
                    )}
                    {row?.locationTwo?.name && (
                        <>
                            {row.locationTwo.name}
                            {row.locationThree?.name && ' > '}
                        </>
                    )}
                    {row?.locationThree?.name && (
                        <>
                            {row.locationThree.name}
                            {row.locationFour?.name && ' > '}
                        </>
                    )}
                    {row?.locationFour?.name && row.locationFour.name}
                </>
            );
        }
    };

    const onSubmitOBS = async () => {
        console.log("Submitting Observation Report:", formData);

        try {
            const response = await API.post(OBSERVATION_REPORT_URL, formData);

            if (response.status === 200) {
                Swal.fire(
                    "Observation Report",
                    "Submitted Successfully",
                    "success"
                );
                handleClose();
                // window.location.reload(); // Uncomment if you want to refresh the page after submission
            }
        } catch (error) {
            console.error("Error submitting observation report:", error);
            Swal.fire("Error", "Something went wrong. Please try again.", "error");
        }
    };


    const handleClose = () => {
        setShow(false)
    }
    const dateBodyTemplate = (row) => {
        return moment(row.created).format('DD-MM-YYYY')
    }
    const openViewModal = (row) => {
        console.log(row)
        setRecord(row)
        setViewShow(true)
    }
    const maskBodyTemplate = (row) => {
        return <div className='maskid' onClick={() => openViewModal(row)}>{row.maskId}</div>
    }
    const reporterFilterTemplate = (options) => {

        return (
            <React.Fragment>
                {/* <div className="mb-3 font-bold">Type</div> */}
                <MultiSelect value={options.value} options={reporter} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }
    const statusFilterTemplate = (options) => {

        return (
            <React.Fragment>
                {/* <div className="mb-3 font-bold">Type</div> */}
                <MultiSelect value={options.value} options={status} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }
    const actionOwnerFilterTemplate = (options) => {

        return (
            <React.Fragment>
                {/* <div className="mb-3 font-bold">Type</div> */}
                <MultiSelect value={options.value} options={owner} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }
    const reviewerFilterTemplate = (options) => {

        return (
            <MultiSelect
                value={options.value}
                options={review}
                itemTemplate={representativesItemTemplate}
                onChange={(e) => options.filterCallback(e.value)}
                optionLabel="name"
                placeholder="Any"
                className="p-column-filter"
            />
        );
    };

    const representativesItemTemplate = (option) => {
        return <span>{option.name}</span>;
    };
    return (<>
        <div>
            <div className="row  ">
                <div className="col-12">
                    <div className="card">
                        <div className="card-body p-0">
                            <div className="row">
                                <div className="col-12">
                                    <div>
                                        <>
                                            <DataTable value={risk} paginator onValueChange={handleFilterChange} rows={10} header={header} filters={filters} onFilter={(e) => setFilters(e.filters)} paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                                                rowsPerPageOptions={[10, 25, 50]}
                                                emptyMessage="No Data found." tableStyle={{ minWidth: '50rem' }}>
                                                <Column field="maskId" header="ID" sortable body={maskBodyTemplate}></Column>
                                                <Column field="" header="Location" body={locationBodyTemplate}></Column>
                                                <Column field="observationCategory" header="Category" ></Column>
                                                <Column field="observationType" header="Type" ></Column>
                                                <Column field="status" header="Status" filterElement={statusFilterTemplate} showFilterMatchModes={false} filter ></Column>


                                                <Column field="created" header="Reported Date" body={dateBodyTemplate} sortable ></Column>

                                                <Column field="reporter.firstName" header="Reported By" filterElement={reporterFilterTemplate} showFilterMatchModes={false} filter></Column>
                                                <Column field="actionOwner.firstName" header="Action Assignee" filterElement={actionOwnerFilterTemplate} showFilterMatchModes={false} filter></Column>
                                                <Column field="reviewer.firstName" header="Reviewed By" filterElement={reviewerFilterTemplate} showFilterMatchModes={false} filter></Column>



                                            </DataTable>
                                        </>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
        <ObservationReportModal onSubmit={onSubmitOBS} show={show} handleClose={handleClose} formData={formData} setFormData={setFormData}  />


        {viewShow &&
            <Modal show={viewShow} onHide={() => setViewShow(false)} size="lg" id="pdf-content">
                <Modal.Header closeButton>
                    {record && (
                        <div className="row" style={{ width: '100%' }}>
                            <div className="col-9">
                                <div className="row">
                                    <div className="col-3" style={{ borderRight: '1px solid #D1D5DB' }}>
                                        {/* <img src={logo} className="me-3" alt="logo" style={{ maxWidth: '125px' }} /> */}
                                        <MyLogoComponent logo={logo} />
                                    </div>
                                    <div className="col-9">
                                        <h4>Observation</h4>
                                        <div className="d-flex align-items-center">
                                            <p className="me-2">#{record.maskId || ''} </p>
                                            <p className="card-eptw">{record.status} </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="col-3 d-flex justify-content-end align-items-center">
                                {/* {(record.status === "Active" || record.status === "Closed") && (
                                    <Button1 type="button" className='btn-download' label="Download Permit" outlined icon="pi pi-download"
                                        onClick={generatePdf}

                                    />
                                )} */}
                            </div>

                        </div>
                    )}
                </Modal.Header>
                <Modal.Body>
                    <ViewObs reportData={record} />
                </Modal.Body>
                <Modal.Footer>

                    <Button severity="secondary" label="Close" onClick={() => setViewShow(false)} />

                </Modal.Footer>
            </Modal>
        }

    </>
    )
}

export default Observation