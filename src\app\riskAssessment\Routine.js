import React, { useEffect, useState, useContext, useRef } from 'react'
import { Dropdown } from 'primereact/dropdown';
import { Button } from 'primereact/button';
import { MultiSelect } from 'primereact/multiselect';
import { InputTextarea } from 'primereact/inputtextarea'
import { InputText } from 'primereact/inputtext'
import { Checkbox } from 'primereact/checkbox';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
// import 'primeflex/primeflex.css'; 
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";
// import 'primereact/resources/themes/lara-light-indigo/theme.css';
import 'primeicons/primeicons.css';
import { GMS1_URL, GET_USER_ROLE_BY_MODE, HAZARDS_CATEGOTY, SENT_NOTIFICATION_MAIL, RISK_UPDATE_WITH_ID_URL, GET_ALL_USER, FILE_URL, RISKASSESSMENT_LIST, GET_RISK_WITH_ID_URL, RISK_WITH_ID_URL, DRAFT_RA, RISK_UPDATE_DRAFT_WITH_ID, GET_RISK_HAZARD_URL } from '../constants';
import API from '../services/API';
import { useSelector } from 'react-redux';
import { InputSwitch } from 'primereact/inputswitch';
import { DropzoneArea } from 'material-ui-dropzone';
import { Dialog } from 'primereact/dialog';
import { TabView, TabPanel } from 'primereact/tabview';
import { Stepper, Step, StepLabel, Typography } from '@mui/material';
import { Calendar } from 'primereact/calendar';
import Accordion from 'react-bootstrap/Accordion';
import { useAccordionButton } from 'react-bootstrap/AccordionButton';
import AccordionContext from 'react-bootstrap/AccordionContext';
import { RadioButton } from 'primereact/radiobutton';
import ImageComponent from '../services/FileDownlodS3';
import Select from 'react-select'
import SignatureCanvas from "react-signature-canvas";
import Swal from 'sweetalert2';
import { useLocation, useHistory } from 'react-router-dom/cjs/react-router-dom';
import AddActivityModal from './Component/SubActivityComponent';
import HazardAccordion from './Component/Hazards/HazardAccordion';
import IdentifiedHazards from './Component/Hazards/IdentifiedHazards';
import Consequence from './Component/Consequence/Consequence';
import CurrentControl from './Component/CurrentControl/CurrentControl';
import RiskAssessment from './Component/RiskAssessment/RiskAssessment';
import ProposedRiskManagement from './Component/AdditionalProposed/RiskManagement';
import TaskItem from './Component/TaskItem';
import RiskUpdate from './Component/RiskUpdate';
import UpdateTable from './Component/UpdateTable';
import moment from 'moment';
import { Modal } from 'react-bootstrap';
import HeadStepper from './Component/HeadStepper';
import SubActivityComponent from './Component/SubActivityComponent';

import FullLoader from '../shared/FullLoader';

const customSwal2 = Swal.mixin({
    customClass: {
        confirmButton: "btn btn-primary",
    },
    buttonsStyling: false,
});

function Routine({ data, type, domain }) {

    const url = window.location.hostname

    const user = useSelector((state) => state.login.user);
    const location = useLocation();
    console.log(location)
    const history = useHistory();
    const signRef = useRef();
    const [files, setFiles] = useState([])
    const [depart, setDepart] = useState([])
    const [activity, setActivity] = useState([])
    const [crew, setCrew] = useState([])
    const [selectedDepart, setSelectedDepart] = useState(null)
    const [selectedActivity, setSelectedActivity] = useState(null)
    const [selectedCrew, setSelectedCrew] = useState([])
    const [addSubActivity, setAddSubActivity] = useState(false)
    const [activityDesc, setActivityDesc] = useState('')
    const [task, setTask] = useState([])
    const [subActivityName, setSubActivityName] = useState('')
    const [visible, setVisible] = useState(false)
    const [item, setItem] = useState('')
    const [index, setIndex] = useState('')
    const [activeIndex, setActiveIndex] = useState(0);
    const [hazards, setHazards] = useState([])
    const [activeTabIndex, setActiveTabIndex] = useState(0);
    const [severityTable, setSeverityTable] = useState(false)
    const [likelyhoodTable, setLikelyhoodTable] = useState(false)
    const [riskTable, setRiskTable] = useState(false)
    const [responsibility, setResponsibility] = useState([])
    const [required, setRequired] = useState(true)
    const [recommendationOne, setRecommendationOne] = useState(null)
    const [recommendationTwo, setRecommendationTwo] = useState(null)
    const [nonRoutineDepartment, setNonRoutineDepartment] = useState('')
    const [nonRoutineActivity, setNonRoutineActivity] = useState('')
    const [additionalRecommendation, setAdditionalRecommendation] = useState('')
    const [isLoading, setIsLoading] = useState(false)
    const [hazardName, setHazardName] = useState('')
    const [risk, setRisk] = useState([])
    const [eptwHot, setEptwHot] = useState([])
    const [raTeamMember, setRATeamMember] = useState([])
    const [subActivityModal, setSubActivityModal] = useState(false)
    const [riskUpdate, setRiskUpdate] = useState(false)
    const [Update, setUpdate] = useState([])
    const [draggedItemIndex, setDraggedItemIndex] = useState(null);
    const [riskRoutine, setRiskRoutine] = useState([])
    const [onEdit, setOnEdit] = useState(false)
    const [shortName, setShortName] = useState('')
    const severity = [
        { "value": "1", "label": "1(E) - Negligible" },
        { "value": "2", "label": "2(D) - Minor" },
        { "value": "3", "label": "3(C) - Moderate" },
        { "value": "4", "label": "4(B) - Major" },
        { "value": "5", "label": "5(A) - Catastrophic" }
    ]
    const likelyhood = [
        { label: "Rare (1)", value: "1" },
        { label: "Unlikely (2)", value: "2" },
        { label: "Possible (3)", value: "3" },
        { label: "Likely (4)", value: "4" },
        { label: "Almost Certain (5)", value: "5" },
    ]
    const impactOn = [
        { 'label': 'Personnel', 'value': 'Personnel' },
        { 'label': 'Environment', 'value': 'Environment' },
        { 'label': 'Property / Equipment', 'value': 'Property / Equipment' },
        { 'label': 'Operations', 'value': 'Operations' },
    ]
    const control = [
        { 'label': 'No Control', 'value': 'No Control' },
        { 'label': 'Engineering', 'value': 'Engineering' },
        { 'label': 'Administrative', 'value': 'Administrative' },
        { 'label': 'PPE', 'value': 'PPE' }

    ]

    const controlAdditional = [
        { 'label': 'Elimination', 'value': 'Elimination' },
        { 'label': 'Substitution', 'value': 'Substitution' },
        { 'label': 'Engineering', 'value': 'Engineering' },
        { 'label': 'Administrative', 'value': 'Administrative' },
        { 'label': 'PPE', 'value': 'PPE' }

    ]

    const controlType = [
        { 'label': 'Preventative', 'value': 'Preventative' },
        { 'label': 'Mitigative', 'value': 'Mitigative' }

    ]

    const severityData = [
        {
            id: '5 (A)',
            severity: 'Catastrophic',
            personnel: 'Serious injury with long-term or permanent disability or death.',
            property: 'Significant damage leading to major repairs.',
            environment: 'Significant environmental damage requiring regulatory reporting and cleanup.',
            serviceLoss: 'Major disruption to service operations, extended recovery time.'
        },
        {
            id: '4 (B)',
            severity: 'Major',
            personnel: 'Serious injury with long-term recovery or permanent disability.',
            property: 'Noticeable damage requiring repair, causing moderate downtime or repair costs.',
            environment: 'Moderate, recoverable environmental impact, requiring external agency notification or cleanup.',
            serviceLoss: 'Significant downtime with substantial recovery efforts.'
        },
        {
            id: '3 (C)',
            severity: 'Moderate',
            personnel: 'Injury requiring medical treatment with potential for short-term lost workdays or restricted duties.',
            property: 'Noticeable damage requiring repair, causing moderate downtime or repair costs.',
            environment: 'Moderate environmental impact, manageable on-site with potential regulatory notification.',
            serviceLoss: 'Moderate service interruption with short recovery.'
        },
        {
            id: '2 (D)',
            severity: 'Minor',
            personnel: 'Minor injury requiring first-aid or outpatient treatment, with minimal lost time.',
            property: 'Slight damage requiring minor repairs without significant downtime.',
            environment: 'Small localized impact, manageable on-site, without long-term environmental damage.',
            serviceLoss: 'Brief disruption to services, easily restored.'
        },
        {
            id: '1 (E)',
            severity: 'Insignificant',
            personnel: 'Minor first-aid required with no lost time or long-term health impacts.',
            property: 'Minimal damage or wear that does not require repair or interruption to operations.',
            environment: 'Negligible environmental impact with no regulatory involvement needed.',
            serviceLoss: 'No impact on services.'
        }
    ];

    const levelData = [
        {
            level: '1',
            descriptor: 'Rare',
            detailedDescription: 'The event is highly unlikely to occur under normal circumstances, with little to no historical precedent.'
        },
        {
            level: '2',
            descriptor: 'Unlikely',
            detailedDescription: 'The event is improbable but could potentially happen under unusual conditions, though there is limited historical data to support this.'
        },
        {
            level: '3',
            descriptor: 'Possible',
            detailedDescription: 'The event could happen, with moderate chances of occurring based on historical records or foreseeable conditions.'
        },
        {
            level: '4',
            descriptor: 'Likely',
            detailedDescription: 'The event is expected to occur in the normal course of operations, with a significant history of similar incidents.'
        },
        {
            level: '5',
            descriptor: 'Almost Certain',
            detailedDescription: 'The event is highly likely to occur and is anticipated in the near future without further intervention.'
        }
    ];

    const tableData = [
        { id: '5(A)', severity: 'Catastrophic', rare: '5(A)', unlikely: '10(A)', possible: '15(A)', likely: '20(A)', almostCertain: '25(A)' },
        { id: '4(B)', severity: 'Major', rare: '4(B)', unlikely: '8(B)', possible: '12(B)', likely: '16(B)', almostCertain: '20(B)' },
        { id: '3(C)', severity: 'Moderate', rare: '3(C)', unlikely: '6(C)', possible: '9(C)', likely: '12(C)', almostCertain: '15(C)' },
        { id: '2(D)', severity: 'Minor', rare: '2(D)', unlikely: '4(D)', possible: '6(D)', likely: '8(D)', almostCertain: '10(D)' },
        { id: '1(E)', severity: 'Insignificant', rare: '1(E)', unlikely: '2(E)', possible: '3(E)', likely: '4(E)', almostCertain: '5(E)' },
    ];



    useEffect(() => {
        const fetchData = async () => {
            try {
                // Fetch all data asynchronously in parallel
                await Promise.all([
                    getRoutineList(),
                    getWorkActivity(),
                    getCrewList(),
                    getHazardList(),
                    getAllResponsibility(),
                    getHighRiskHazardList()
                ]);
            } catch (error) {
                console.error('Error fetching data:', error);
            }
        };

        fetchData(); // Call the async function to execute all requests

    }, []);


    const onSubmitUpdate = () => {
        getRiskUpdate();
        editUserHandler();

    }

    useEffect(() => {

        setOnEdit(true)

    }, [domain == "edit"])

    const handleDragStart = (event, index) => {
        setDraggedItemIndex(index);
        event.dataTransfer.effectAllowed = 'move';
    };

    // Handles the drop event
    const handleDrop = (event, dropIndex) => {
        event.preventDefault();
        const draggedItem = task[draggedItemIndex];
        const updatedTaskList = [...task];

        // Remove the dragged item from its current position
        updatedTaskList.splice(draggedItemIndex, 1);

        // Insert it at the new position
        updatedTaskList.splice(dropIndex, 0, draggedItem);

        // Update the state with the reordered task list
        setTask(updatedTaskList);
        setDraggedItemIndex(null);
    };

    // Prevents the default drag over behavior
    const handleDragOver = (event) => {
        event.preventDefault();
    };
    useEffect(() => {
        if (onEdit === true && depart.length !== 0 && domain === 'edit') {
            // Find the default option based on departmentId
            const defaultOption = depart.find(option => option.value === data.departmentId);

            // Ensure defaultOption is not undefined before proceeding
            if (defaultOption) {
                console.log(defaultOption);
                setSelectedDepart(defaultOption);

                // Check if workActivities exist before mapping
                const transformedOptions = defaultOption.workActivities?.map(option => ({
                    label: option.name,
                    value: option.id,
                })) || []; // Fallback to an empty array if workActivities is undefined

                const defaultOption1 = transformedOptions.find(option => option.value === data.workActivityId);
                console.log(defaultOption1);
                setSelectedActivity(defaultOption1);

                setActivity(transformedOptions);
            } else {
                console.error("Department not found for the given departmentId.");
            }

            // Reset onEdit flag
            setOnEdit(false);
        }
    }, [onEdit, depart]);



    useEffect(() => {
        const fetchData = async () => {
            const uriString = {
                include: [
                    { relation: "department" },
                    { relation: "teamLeader" },
                    { relation: "workActivity" },
                    {
                        relation: "raTeamMembers",
                        scope: {
                            include: [{ relation: "user" }]
                        }
                    }
                ]
            };

            const url = `${GET_RISK_WITH_ID_URL(data.id)}?filter=${encodeURIComponent(
                JSON.stringify(uriString)
            )}`;
            try {
                const response = await API.get(url);
                if (response.status === 200) {
                    const data = response.data;
                    setTask(data.tasks);
                    setAdditionalRecommendation(data.additonalRemarks);
                    setRecommendationOne(data.overallRecommendationOne);
                    setRecommendationTwo(data.overallRecommendationTwo);
                    setSelectedCrew(data.raTeamMembers.map(option => ({
                        name: option.user.firstName,
                        id: option.user.id,

                    })));
                    setActivityDesc(data.description);
                    setNonRoutineActivity(data.nonRoutineWorkActivity);
                    setNonRoutineDepartment(data.nonRoutineDepartment);
                    setHazardName(data.hazardName);
                    setEptwHot(data.highRisk);
                    setRATeamMember(data.raTeamMembers)
                    setShortName(data.shortName)
                }
            } catch (error) {
                console.error("Error fetching data:", error);
            }
        };

        if (data) {
            fetchData();
            getRiskUpdate();
        }

    }, [domain]);
    const getRiskUpdate = async () => {
        try {
            const response = await API.get(RISK_UPDATE_WITH_ID_URL(data.id));
            if (response.status === 200) {

                setUpdate(response.data)
            }
        } catch (error) {
            // Catch any errors during the API call
            console.error("Error fetching risk update:", error);
        }
    }

    const getHighRiskHazardList = async () => {
        // Define the filter criteria
        const uriString = {
            where: {
                $and: [
                    {
                        $or: [
                            { status: 'Pending' },
                            { status: 'Draft' },
                            { status: 'Published' }
                        ]
                    },
                    {
                        $or: [
                            { type: 'High-Risk Hazard' }
                        ]
                    }
                ]
            },

            fields: { id: true, hazardName: true }
        };

        // Construct the URL with the encoded filter
        const url = `${RISKASSESSMENT_LIST}?filter=${encodeURIComponent(
            JSON.stringify(uriString)
        )}`;

        try {
            // Make the GET request to the API
            const response = await API.get(url);

            // Check if the response status is 200 (OK)
            if (response.status === 200) {
                // Update the state with the retrieved data
                setRisk(response.data);
            } else {
                // Handle other response statuses (e.g., 404, 500)
                console.error(`Error: Received status code ${response.status}`);
            }
        } catch (error) {
            // Handle errors (e.g., network issues, invalid URL)
            console.error('Error fetching high-risk hazard list:', error);
        }
    };

    const getRoutineList = async () => {
        // Define the filter criteria
        const uriString = {
            where: {
                $and: [
                    {
                        $or: [
                            { status: 'Pending' },
                            { status: 'Draft' },
                            { status: 'Published' }
                        ]
                    },
                    {
                        $or: [
                            { type: 'Routine' }
                        ]
                    }
                ]
            },

            fields: { workActivityId: true }
        };

        // Construct the URL with the encoded filter
        const url = `${RISKASSESSMENT_LIST}?filter=${encodeURIComponent(
            JSON.stringify(uriString)
        )}`;

        try {
            // Make the GET request to the API
            const response = await API.get(url);

            // Check if the response status is 200 (OK)
            if (response.status === 200) {
                // Update the state with the retrieved data
                setRiskRoutine(response.data);
            } else {
                // Handle other response statuses (e.g., 404, 500)
                console.error(`Error: Received status code ${response.status}`);
            }
        } catch (error) {
            // Handle errors (e.g., network issues, invalid URL)
            console.error('Error fetching high-risk hazard list:', error);
        }
    };

    const getAllResponsibility = async () => {
        const uriString = { include: [{ "relation": "workingGroup" }, { "relation": "designation" }, { "relation": "department" }] }
        const url = `${GET_ALL_USER}?filter=${encodeURIComponent(
            JSON.stringify(uriString)
        )}`;
        const response = await API.get(url);
        if (response.status === 200) {
            const depart = response.data.map(item => {
                return { id: item.id, name: item.firstName, email: item.email }
            })
            setResponsibility(depart)
        }

    }
    const getHazardList = async () => {

        const selectedIndustry = localStorage.getItem('SELECTED_INDUSTRIES');

        if (selectedIndustry) {
            const selectedIndustryNames = selectedIndustry
                .split(',')
                .map(name => name.trim()); // Handle "Default Hazards, Diving & ROV"

            const uriString = {
                include: [
                    {
                        relation: "hazardCategories",
                        scope: {
                            include: [{ relation: "hazardItems" }]
                        }
                    }
                ]
            };

            const url = `${GET_RISK_HAZARD_URL}?filter=${encodeURIComponent(
                JSON.stringify(uriString)
            )}`;

            try {
                const response = await API.get(url);
                if (response.status === 200) {
                    const industryList = response.data;

                    const matchedIndustries = industryList.filter(item =>
                        selectedIndustryNames.includes(item.name)
                    );

                    // Combine all hazardCategories from matched industries
                    const allHazards = matchedIndustries.flatMap(
                        industry => industry.hazardCategories || []
                    );

                    setHazards(allHazards);
                }
            } catch (err) {
                console.error('Error fetching industry-based hazards:', err);
            }
        }
        else {
            // Default hazard category fallback
            const uriString = { include: ["hazards"] };
            const url = `${HAZARDS_CATEGOTY}?filter=${encodeURIComponent(
                JSON.stringify(uriString)
            )}`;

            try {
                const response = await API.get(url);
                if (response.status === 200) {
                    const data = response.data.filter(
                        (item) => item.name !== 'Hazard-Based'
                    );
                    setHazards(data);
                }
            } catch (err) {
                console.error('Error fetching default hazard categories:', err);
            }
        }


    }

    const handleFileChange = async (files) => {

        setFiles(files)

    };
    const getWorkActivity = async () => {
        const uriString = { include: ["workActivities"] };

        const url = `${GMS1_URL}?filter=${encodeURIComponent(
            JSON.stringify(uriString)
        )}`;
        const response = await API.get(url);
        if (response.status === 200) {
            const transformedOptions = response.data.map(option => {
                const transformedOption = {
                    label: option.name,
                    value: option.id,
                };

                if (option.workActivities && option.workActivities.length > 0) {
                    transformedOption.workActivities = option.workActivities;
                }

                return transformedOption;
            });
            setDepart(transformedOptions)

        }
    };

    useEffect(() => {
        if (selectedDepart !== null) {
            setActivity([])

            const active = depart.find(item => item.value === selectedDepart?.value);
            console.log(active)
            if (active.workActivities) {

                const workActivityIdSet = new Set(riskRoutine.map(item => item.workActivityId));
                const filteredWorkActivities = active.workActivities.filter(activity => !workActivityIdSet.has(activity.id));

                if (filteredWorkActivities) {
                    const transformedOptions = filteredWorkActivities.map(option => ({
                        label: option.name,
                        value: option.id,
                    }));
                    setActivity(transformedOptions)
                }
            } else {
                setActivity([])
            }
        }
    }, [selectedDepart])

    const getCrewList = async () => {

        const response = await API.post(GET_USER_ROLE_BY_MODE, {
            locationOneId: "",
            locationTwoId: "",
            locationThreeId: "",
            locationFourId: "",
            mode: 'ra_member'
        });
        if (response.status === 200) {
            let data = [];
            response.data.map((item) => {

                if (item.id !== user.id) {
                    data.push({ name: item.firstName, id: item.id });
                }
            });

            setCrew(data);
        }
    };

    const AddSubActivityTitle = async () => {

        let image = []

        if (files.length !== 0) {
            files.map(async (item) => {
                const formData1 = new FormData();
                formData1.append('file', item);

                try {
                    const response = await API.post(FILE_URL, formData1, {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                        }
                    });

                    if (response && response.status === 200) {
                        image.push(response.data.files[0].originalname)
                    }
                } catch (error) {
                    // Log the error response for debugging purposes
                    console.error("File upload error: ", error);
                }
            })


        }

        if (subActivityName !== '') {
            const t = [
                { type: 'activity', name: subActivityName, images: image },
                { type: 'hazards', selected: [] },
                { type: 'consequence', option: [{ value: "", files: [], current_type: '', }] },
                { type: 'current_control', option: [{ value: "", files: [], current_type: '', method: '' }] },
                { type: 'assessment', severity: '', likelyhood: '', level: '' },
                { type: 'additional', accept: true },
                { type: 'responsibility', option: [{ current_type: '', person: '', date: null, value: '' }] },
                { type: 'reassessment', severity: '', likelyhood: '', level: '' },
                { type: 'activeStep', step: 0 },
                { type: 'stage', level: ['Hazards Identification', 'Consequences', 'Current Controls', 'Risk Estimation'] },
                { type: 'completed_stage', level: [] },
                {
                    type: 'status', value: {
                        hazardsIdentification: '',
                        consequences: '',
                        currentControls: '',
                        riskEstimation: '',
                        additionalControls: '',

                    }
                }
            ]
            setTask((prev) => [...prev, t]);
            setSubActivityName('');
            setFiles([])
            setAddSubActivity(false)
        } else {

        }

    }

    const handleMainImage = (m) => {
        const t = task
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === 'activity') {
                        ite.images.splice(m, 1)

                    }
                })
            }
            return item
        }
        )
        setTask(text)
        setItem(text[index])
    }
    const deleteTask = (e, i) => {
        e.stopPropagation()
        const t = task;
        const newTasks = task.filter((_, idx) => idx !== i);
        setTask(newTasks);
    }
    const openDialog = (item, i) => {
        setItem('')
        setItem(item);
        setIndex(i)
        setVisible(true)

    }
    const subActivity = (item, i) => {
        setItem('')
        setItem(item);
        setIndex(i)
        setSubActivityModal(true)
    }

    const checkRequiredStepField = () => {

        let required = true;
        if (item[8].step === 0) {

            if (item[1].selected.length === 0) {
                required = false
                setRequired(false)
            }

        } else if (item[8].step === 1) {
            for (let option of item[2].option) {
                if (option.value === "" && option.current_type === '') {
                    required = false
                    setRequired(false)
                }
            }

        } else if (item[8].step === 2) {
            for (let option of item[3].option) {
                if (option.value === "" && option.current_type === '') {
                    required = false
                    setRequired(false)
                }
            }
        } else if (item[8].step === 3) {
            if (item[4].severity === '' && item[4].likelyhood === '') {
                required = false
                setRequired(false)
            }

        }
        else if (item[8].step === 4) {
            if (item[7].severity === '' && item[7].likelyhood === '') {
                required = false
                setRequired(false)
            }

        }


        return required

    }

    const headerTemplate = (
        <div className="d-flex flex-column">
            <div className='col-12 '>
                Assessment

            </div>

        </div>
    );
    const handleNext = () => {
        console.log(checkRequiredStepField());
        if (checkRequiredStepField()) {
            // Add current step to completed_stage and ensure no duplicates
            item[10].level.push(item[8].step);
            item[10].level = [...new Set(item[10].level)];

            // Check if the last step is reached
            // if (item[8].step === item[9].level.length - 1) {
            //     setVisible(false);
            // } else {
            // Update task array with status
            const updatedTask = task.map((item, i) => {
                if (i === index) {
                    item.map((ite) => {
                        // if (ite.type === 'activeStep') {
                        //     ite.step = ite.step + 1; // Move to the next step
                        // }

                        if (ite.type === 'status') {
                            // Update status based on the active step
                            switch (item[8].step) {
                                case 0:
                                    ite.value.hazardsIdentification = 'completed';
                                    break;
                                case 1:
                                    ite.value.consequences = 'completed';
                                    break;
                                case 2:
                                    ite.value.currentControls = 'completed';
                                    break;
                                case 3:
                                    ite.value.riskEstimation = 'completed';
                                    break;
                                case 4:
                                    ite.value.additionalControls = 'completed';
                                    break;
                                default:
                                    break;
                            }
                        }
                    });
                }
                return item;
            });

            // Update state with the modified task and item
            setTask(updatedTask);
            setItem(updatedTask[index]);

            if (item && item[8] && typeof item[8].step !== 'undefined') {
                const currentSectionName = sectionNames[item[8].step];
                Swal.fire({
                    toast: true,
                    icon: 'success',
                    title: 'Saved & Finalized',
                    text: `${currentSectionName} saved & finalized.`,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                });
            }
            // }
        } else {
            alert('Please fill in the required fields');
        }
    };
    const saveProgress = () => {



        const updatedTask = task.map((item, i) => {
            if (i === index) {
                item.map((ite) => {

                    if (ite.type === 'status') {
                        // Update status based on the active step
                        switch (item[8].step) {
                            case 0:
                                ite.value.hazardsIdentification = 'inprogress';
                                break;
                            case 1:
                                ite.value.consequences = 'inprogress';
                                break;
                            case 2:
                                ite.value.currentControls = 'inprogress';
                                break;
                            case 3:
                                ite.value.riskEstimation = 'inprogress';
                                break;
                            case 4:
                                ite.value.additionalControls = 'inprogress';
                                break;
                            default:
                                break;
                        }
                    }
                });
            }
            return item;
        });

        // Update state with the modified task and item
        setTask(updatedTask);
        setItem(updatedTask[index]);

        if (item && item[8] && typeof item[8].step !== 'undefined') {
            const currentSectionName = sectionNames[item[8].step];
            Swal.fire({
                toast: true,
                icon: 'success',
                title: 'Saved as Draft',
                text: `${currentSectionName} saved as draft.`,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
            });
        }
    };

    const handleBack = () => {

        item[10].level = item[10].level.filter(item1 => item1 !== item[8].step);
        const t = task;
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === 'activeStep') {
                        ite.step = ite.step - 1

                    }
                    if (ite.type === 'completed_stage') {
                        ite.level.pop(ite.step)
                    }
                })
            }
            return item
        })
        setTask(text)
        setItem(text[index])


    };

    const handleStageClick = (step) => {

        item[10].level = item[10].level.filter(item1 => item1 !== item[8].step);
        const t = task;
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === 'activeStep') {
                        ite.step = step
                    }

                })
            }
            return item
        })
        setTask(text)
        setItem(text[index])


    };
    const sectionNames = [
        "Hazard Identification",
        "Consequences",
        "Current Controls",
        "Risk Estimation",
        "Additional Controls"
    ];
    const footerTemplate = (
        <div className="d-flex justify-content-between align-items-center">
            {/* Safely access item[8] and item[8].step */}
            {item && item[8] && typeof item[8].step !== 'undefined' && (
                <>



                    <div className="d-flex">
                        {/* Save Progress button with section name */}
                        <Button
                            className='me-2'
                            outlined
                            label={`Save Progress`}
                            onClick={saveProgress}
                        />
                        {/* Save & Finalize button with section name */}
                        <Button
                            label={`Save & Finalize ${sectionNames[item[8].step]} for Sub Activity`}
                            onClick={handleNext}
                        />
                    </div>
                </>
            )}
        </div>
    );

    const rowClassName = (data) => {
        switch (data.level[0]) {
            case '1':
                return 'level-1';
            case '2':
                return 'level-2';
            case '3':
                return 'level-3';
            case '4':
                return 'level-4';
            case '5':
                return 'level-5';
            default:
                return '';
        }
    };
    const cellClassName = (value) => {

        const numericValue = parseInt(String(value).replace(/[^\d]/g, ''), 10);

        // Check if the numeric value is 0
        if (numericValue === 0) return '';

        if (numericValue === 1 || numericValue === 2 || numericValue === 3 || numericValue === 4) return 'cell-green';
        if (numericValue === 15 || numericValue === 20 || numericValue === 25 || numericValue === 16) return 'cell-red';

        return 'cell-yellow';
    };

    const cellStyle = (data, field) => cellClassName(data[field]);

    const onClickHazards = (ha, j) => {
        const t = task;
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === 'hazards') {
                        if (ite.selected.some(hazards => hazards.id === ha.id)) {
                            const index = ite.selected.findIndex(hazard => hazard.id === ha.id);
                            if (index !== -1) {
                                const newHazards = [...ite.selected];
                                newHazards.splice(index, 1);
                                ite.selected = newHazards;
                            }
                        } else {
                            ite.selected.push(ha)
                        }
                    }
                })
            }
            return item
        })
        setTask(text)
        setItem(text[index])
    }
    const onChangeSeverity = (e, type) => {

        console.log(e, type)
        const t = task;
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === type) {

                        ite.severity = e.value

                    }
                })
            }
            return item
        })
        setTask(text)
        setItem(text[index])
    }
    const onChangeLikelyhood = (e, type) => {
        const t = task;
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === type) {

                        ite.likelyhood = e.value

                    }
                })
            }
            return item
        })
        setTask(text)
        setItem(text[index])
    }
    const onDeleteHaz = (item1) => {
        const t = task;
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === 'hazards') {
                        const index = ite.selected.findIndex(hazard => hazard.id === item1.id);
                        if (index !== -1) {
                            const newHazards = [...ite.selected];
                            newHazards.splice(index, 1);
                            ite.selected = newHazards;
                        }
                    }
                })
            }
            return item
        })
        setTask(text)
        setItem(text[index])
    }
    const onImapactOn = (value, j, type) => {
        const t = task
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === type) {

                        ite.option.map((con, c) => {
                            if (c === j) {
                                con.current_type = value
                            }


                        })

                    }
                })
            }
            return item
        }
        )
        setTask(text)
        setItem(text[index])
    }

    const onMethodOn = (value, j, type) => {
        const t = task
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === type) {

                        ite.option.map((con, c) => {
                            if (c === j) {
                                con.method = value
                            }


                        })

                    }
                })
            }
            return item
        }
        )
        setTask(text)
        setItem(text[index])
    }
    const onControlAddion = (value, j) => {

        const t = task
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === 'responsibility') {
                        ite.option.map((con, c) => {
                            if (c === j) {
                                con.current_type = value
                            }
                        })

                    }
                })
            }
            return item
        }
        )
        setTask(text)
        setItem(text[index])
    }
    const onControlAddionText = (value, j) => {

        const t = task
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === 'responsibility') {
                        ite.option.map((con, c) => {
                            if (c === j) {
                                con.value = value
                            }
                        })

                    }
                })
            }
            return item
        }
        )
        setTask(text)
        setItem(text[index])
    }
    const onResponsePerson = (value, j) => {

        const t = task
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === 'responsibility') {
                        ite.option.map((con, c) => {
                            if (c === j) {
                                con.person = value
                            }
                        })

                    }
                })
            }
            return item
        }
        )
        setTask(text)
        setItem(text[index])
    }
    const onResponseDate = (value, j) => {

        const t = task
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === 'responsibility') {
                        ite.option.map((con, c) => {
                            if (c === j) {
                                con.date = value
                            }
                        })

                    }
                })
            }
            return item
        }
        )
        setTask(text)
        setItem(text[index])
    }
    const onChangeReAss = (value) => {
        console.log(value);
        const t = task; // Create a copy of the task array

        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === 'additional') {
                        ite.accept = value;
                    }
                    if (ite.type === 'stage') {
                        if (value === false) {
                            // Push "Additional Controls" only if it does not already exist
                            if (!ite.level.includes('Additional Controls')) {
                                ite.level.push('Additional Controls');
                            }
                        } else {
                            ite.level = ite.level.filter(item => item !== 'Additional Controls');
                        }
                    }
                });
            }
            return item;
        });

        setTask(text);
        setItem(text[index]);
    }

    const onConseqText = (value, j, type) => {
        console.log(value)
        const t = task
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === type) {
                        ite.option.map((con, c) => {
                            if (c === j) {
                                con.value = value
                            }

                        })

                    }
                })
            }
            return item
        }
        )
        setTask(text)
        setItem(text[index])
    }

    const onConseqRequired = (value, j, type, type1) => {
        const t = task
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === type) {
                        ite.option.map((con, c) => {
                            if (c === j) {
                                if (type1 === 'required') {
                                    con.required = !value
                                } else {
                                    con.validity = !value
                                }

                            }

                        })

                    }
                })
            }
            return item
        }
        )

        console.log(text)
        setTask(text)
        setItem(text[index])
    }

    const handleTaskFileChange = async (value, j, type) => {

        if (value.length > 0) {
            const latestFile = value[value.length - 1];
            const formData1 = new FormData();
            formData1.append('file', latestFile);

            try {
                const response = await API.post(FILE_URL, formData1, {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                    }
                });

                if (response && response.status === 200) {
                    const t = task
                    const text = t.map((item, i) => {
                        if (i === index) {
                            item.map((ite) => {
                                if (ite.type === type) {
                                    ite.option.map((con, c) => {
                                        if (c === j) {
                                            con.files.push(response.data.files[0].originalname)
                                        }

                                    })

                                }
                            })
                        }
                        return item
                    }
                    )
                    setTask(text)
                    setItem(text[index])
                }
            } catch (error) {
                // Log the error response for debugging purposes
                console.error("File upload error: ", error);
            }

        }
    }
    const handleRemoveImage = (m, index, type) => {
        const t = task
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === type) {
                        ite.option.map((con, c) => {

                            con.files.splice(m, 1)


                        })

                    }
                })
            }
            return item
        }
        )
        setTask(text)
        setItem(text[index])
    }

    const addConsequence = (type) => {
        const t = task
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === type) {
                        ite.option.push({ value: "", files: [], current_type: '', method: '' })
                    }
                })
            }
            return item
        }
        )
        setTask(text)
        setItem(text[index])
    }

    const addAdditionalControl = () => {
        const t = task
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === 'responsibility') {
                        ite.option.push({ current_type: '', person: '', date: null, value: '' })
                    }
                })
            }
            return item
        }
        )
        setTask(text)
        setItem(text[index])
    }
    const onDeleteConseq = (j, type) => {
        const t = task
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === type) {

                        const newHazards = [...ite.option];
                        newHazards.splice(j, 1);
                        ite.option = newHazards;

                    }
                })
            }
            return item
        }
        )
        setTask(text)
        setItem(text[index])
    }

    const ContextAwareToggle = ({ children, eventKey, callback }) => {

        const { activeEventKey } = useContext(AccordionContext);

        const decoratedOnClick = useAccordionButton(
            eventKey,
            () => callback && callback(eventKey),
        );

        const isCurrentEventKey = activeEventKey === eventKey;

        return (

            <i className={`pi ${isCurrentEventKey ? 'pi-angle-up' : 'pi-angle-down'}`} onClick={decoratedOnClick}></i>

        )
    }
    const handleActivityImage = async (files) => {
        if (files.length > 0) {
            const latestFile = files[files.length - 1];
            const formData1 = new FormData();
            formData1.append('file', latestFile);

            try {
                const response = await API.post(FILE_URL, formData1, {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                    }
                });

                if (response && response.status === 200) {

                    const t = task;
                    const text = t.map((item, i) => {
                        if (i === index) {

                            item[0].images.push(response.data.files[0].originalname)
                        }
                        return item
                    })
                    setTask(text)
                    setItem(text[index])

                }
            } catch (error) {
                // Log the error response for debugging purposes
                console.error("File upload error: ", error);
            }

        }
    }
    const changeActivityName = (e) => {

        const t = task
        const text = t.map((item, i) => {
            if (i === index) {
                item[0].name = e
            }
            return item
        }
        )
        setTask(text)
        setItem(text[index])


    }
    const dataURItoFile = (dataURI, filename) => {
        var byteString = atob(dataURI.split(",")[1]);
        // separate out the mime component
        var mimeString = dataURI.split(",")[0].split(":")[1].split(";")[0];
        // write the bytes of the string to an ArrayBuffer
        var ab = new ArrayBuffer(byteString.length);
        var dw = new DataView(ab);
        for (var i = 0; i < byteString.length; i++) {
            dw.setUint8(i, byteString.charCodeAt(i));
        }

        // write the ArrayBuffer to a blob, and you're done
        return new File([ab], filename, { type: mimeString });
    };

    const editUserHandler = async () => {
        setIsLoading(true);

        let uploadedSignature = '';

        try {
            // If the signature is not empty, upload it

            if (data.teamLeaderDeclaration.sign) {
                uploadedSignature = data.teamLeaderDeclaration.sign
            } else {
                if (!signRef.current.isEmpty()) {
                    uploadedSignature = await uploadSignature();
                }
            }

            // Proceed with the patch request, including the signature if available
            const response1 = await API.patch(RISK_WITH_ID_URL(data.id), {
                type: type === 'routine' ? 'Routine' : type === 'nonroutine' ? 'Non Routine' : 'High-Risk Hazard',
                tasks: task,
                shortName: shortName,
                workActivityId: type === 'nonroutine' ? "" : selectedActivity.value,
                departmentId: type === 'nonroutine' ? "" : selectedDepart.value,
                teamLeaderDeclaration: { name: user.firstName, sign: uploadedSignature || '' }, // Include signature if uploaded
                overallRecommendationOne: recommendationOne,
                overallRecommendationTwo: recommendationTwo,
                additonalRemarks: additionalRecommendation,
                highRisk: eptwHot,
                nonRoutineDepartment: nonRoutineDepartment,
                nonRoutineWorkActivity: nonRoutineActivity,
                status: 'Pending',
                hazardName: hazardName,
                raTeamMembersList: selectedCrew,
                description: activityDesc,

            });

            if (response1.status === 204) {
                setIsLoading(false);
                customSwal2.fire("Risk Assessment Updated!", "", "success").then((result) => {
                    if (result.isConfirmed) {
                        window.location.reload();
                    }
                });
            } else {
                customSwal2.fire("Please Try Again!", "", "error");
                setIsLoading(false);
            }
        } catch (error) {
            console.error("Error updating risk assessment: ", error);
            customSwal2.fire("Please Try Again!", "", "error");
            setIsLoading(false);
        }
    };
    const createUserHandler = async () => {
        setIsLoading(true);

        // Check if the signature is empty
        if (signRef.current.isEmpty()) {
            customSwal2.fire("Please Sign!", "", "error");
            setIsLoading(false);
            return; // Stop the function execution if signature is missing
        }

        let uploadedSignature = '';

        // If the signature is not empty, upload the signature
        const filename = new Date().getTime() + "captin_sign.png";
        const formData1 = new FormData();
        formData1.append('file', dataURItoFile(signRef.current.getTrimmedCanvas().toDataURL("image/png"), filename));

        try {
            const response = await API.post(FILE_URL, formData1, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                }
            });

            if (response && response.status === 200) {
                uploadedSignature = response.data.files[0].originalname; // Store the uploaded signature
            } else {
                throw new Error("File upload failed.");
            }
        } catch (error) {
            console.error("File upload error: ", error);
            setIsLoading(false);
            customSwal2.fire("Please Try Again!", "", "error");
            return; // Stop execution if the upload fails
        }

        try {
            // Proceed with the API to create the risk assessment, pass the signature
            const response1 = await API.post(RISKASSESSMENT_LIST, {
                type: type === 'routine' ? 'Routine' : type === 'nonroutine' ? 'Non Routine' : 'High-Risk Hazard',
                tasks: task,
                teamLeaderDeclaration: { name: user.firstName, sign: uploadedSignature }, // Signature is required
                workActivityId: type === 'nonroutine' ? "" : selectedActivity.value,
                departmentId: type === 'nonroutine' ? "" : selectedDepart.value,
                teamLeaderId: user.id,
                overallRecommendationOne: recommendationOne,
                overallRecommendationTwo: recommendationTwo,
                additonalRemarks: additionalRecommendation,
                highRisk: eptwHot,
                nonRoutineDepartment: nonRoutineDepartment,
                nonRoutineWorkActivity: nonRoutineActivity,
                status: 'Pending',
                hazardName: hazardName,
                raTeamMembersList: selectedCrew,
                description: activityDesc,
                shortName: shortName
            });

            if (response1.status === 200) {
                setIsLoading(false);
                customSwal2.fire("Risk Assessment Created!", "", "success").then((result) => {
                    if (result.isConfirmed) {
                        window.location.reload();
                    }
                });
            } else {
                customSwal2.fire("Please Try Again!", "", "error");
                setIsLoading(false);
            }
        } catch (error) {
            console.error("Risk assessment creation error: ", error);
            customSwal2.fire("Please Try Again!", "", "error");
            setIsLoading(false);
        }
    };


    const uploadSignature = async () => {
        const filename = new Date().getTime() + "captin_sign.png";
        const formData1 = new FormData();
        formData1.append('file', dataURItoFile(signRef.current.getTrimmedCanvas().toDataURL("image/png"), filename));

        try {
            const response = await API.post(FILE_URL, formData1, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                }
            });

            if (response && response.status === 200) {
                return response.data.files[0].originalname; // Return the uploaded file name
            } else {
                throw new Error("File upload failed.");
            }
        } catch (error) {
            console.error("File upload error: ", error);
            throw error; // Rethrow to handle it in the main function
        }
    };

    const saveDraftRiskAssessment = async (sign) => {
        try {
            const response1 = await API.post(DRAFT_RA, {
                type: type === 'routine' ? 'Routine' : type === 'nonroutine' ? 'Non Routine' : 'High-Risk Hazard',
                tasks: task,
                teamLeaderDeclaration: { name: user.firstName, sign: sign || '' }, // Use empty string if sign is not available
                workActivityId: type === 'nonroutine' ? "" : selectedActivity.value,
                departmentId: type === 'nonroutine' ? "" : selectedDepart.value,
                teamLeaderId: user.id,
                overallRecommendationOne: recommendationOne || {},
                overallRecommendationTwo: recommendationTwo || {},
                additonalRemarks: additionalRecommendation,
                highRisk: eptwHot,
                nonRoutineDepartment: nonRoutineDepartment,
                nonRoutineWorkActivity: nonRoutineActivity,
                status: 'Draft',
                hazardName: hazardName,
                raTeamMembersList: selectedCrew,
                description: activityDesc
            });

            if (response1.status === 200) {
                return true;
            } else {
                throw new Error("Risk Assessment Draft failed.");
            }
        } catch (error) {
            console.error("Risk assessment save error: ", error);
            throw error; // Rethrow to handle it in the main function
        }
    };

    const draftUserHandler = async () => {
        setIsLoading(true);

        try {
            let uploadedSignature = '';

            // Check if the signature is not empty, if it's not, upload the signature
            if (!signRef.current.isEmpty()) {
                uploadedSignature = await uploadSignature();
            }

            // Run the second API even if the signature is empty
            const saveDraft = await saveDraftRiskAssessment(uploadedSignature);

            if (saveDraft) {
                setIsLoading(false);
                customSwal2.fire("Risk Assessment Drafted!", "", "success").then((result) => {
                    if (result.isConfirmed) {
                        window.location.reload();
                    }
                });
            }
        } catch (error) {
            customSwal2.fire("Please Try Again!", "", "error");
            setIsLoading(false);
        }
    };
    const draftUserEditHandler = async () => {
        setIsLoading(true);

        try {
            let uploadedSignature = '';

            // If the signature is not empty, upload the signature first
            if (data.teamLeaderDeclaration.sign) {
                uploadedSignature = data.teamLeaderDeclaration.sign
            } else {
                if (!signRef.current.isEmpty()) {
                    uploadedSignature = await uploadSignature();
                }
            }


            // Now patch the risk assessment draft with the signature if available
            const response1 = await API.patch(RISK_UPDATE_DRAFT_WITH_ID(data.id), {
                type: type === 'routine' ? 'Routine' : type === 'nonroutine' ? 'Non Routine' : 'High-Risk Hazard',
                tasks: task,
                teamLeaderDeclaration: { name: user.firstName, sign: uploadedSignature || '' }, // Pass the signature if available
                workActivityId: type === 'nonroutine' ? "" : selectedActivity.value,
                departmentId: type === 'nonroutine' ? "" : selectedDepart.value,
                overallRecommendationOne: recommendationOne,
                overallRecommendationTwo: recommendationTwo,
                additonalRemarks: additionalRecommendation,
                highRisk: eptwHot,
                nonRoutineDepartment: nonRoutineDepartment,
                nonRoutineWorkActivity: nonRoutineActivity,
                status: 'Draft',
                hazardName: hazardName,
                raTeamMembersList: selectedCrew,
                description: activityDesc
            });

            if (response1.status === 204) {
                setIsLoading(false);
                customSwal2.fire("Risk Assessment Drafted!", "", "success").then((result) => {
                    if (result.isConfirmed) {
                        window.location.reload();
                    }
                });
            } else {
                // Show error
                customSwal2.fire("Please Try Again!", "", "error");
                setIsLoading(false);
            }
        } catch (error) {
            console.error("Error updating risk assessment: ", error);
            customSwal2.fire("Please Try Again!", "", "error");
            setIsLoading(false);
        }
    };

    const checkHotWork = (e, item) => {

        if (e.target.checked) {
            setEptwHot((prev) => [...prev, item]);

        } else {

            setEptwHot(prevData => prevData.filter(item1 => item1.id !== item.id));
        }


    }

    const editSubActivityTitle = (activity, file) => {
        console.log(activity, file)

        const t = task
        const text = t.map((item, i) => {
            if (i === index) {
                item[0].name = activity
                item[0].images = file
            }
            return item
        }
        )
        setTask(text)
        setItem(text[index])

    }

    const getStatusClass = (status) => {
        if (status === 'completed') return 'completed-stage';
        if (status === 'inprogress') return 'incomplete-stage';
        return 'not-started-stage';
    };

    const sendNotification = async () => {
        let check = true
        if (selectedActivity.length === 0) {
            check = false
        } else if (selectedDepart.length === 0) {
            check = false
        } else if (selectedCrew.length === 0) {
            check = false
        }
        if (check) {
            const response = await API.post(SENT_NOTIFICATION_MAIL, {

                activity: selectedActivity,
                depart: selectedDepart,
                member: selectedCrew,
                leader: user.firstName,
            })
            console.log(response.status)
            if (response.status === 200) {
                customSwal2.fire("Notification Sent!", "", "success");

            }
        } else {
            customSwal2.fire("Please Select Activity or department or Team Member!", "", "warning");
        }
    }

    return (
        <div className="row">
            <div className="col-12">
                <div className="card">
                    <div className='card-body p-0'>
                        <div className=' p-4 pt-0'>

                            {type === 'routine' ?
                                <>
                                    <p className='fst-italic'>A risk evaluation conducted for activities that are regularly performed and require controls in place within the organization’s operations.</p>
                                    <p className=''>To proceed, select the relevant Department and Work Activity from the drop-down list of pre-configured options. If you cannot find a specific department or work activity, check the Risk Register, as a risk assessment may have already been completed for that activity. If it is not listed, please contact the Enterprise Administrator to have it added.</p>
                                    <p className=''>If the work activity is not routinely conducted by the organization and is therefore not found in the work activity register, switch to Non-Routine Risk Assessment and manually enter the title of the work to continue.</p>
                                </>
                                :
                                <>
                                    <p className='fst-italic '>An evaluation focused on activities that are not regularly performed and do not have established controls, requiring a detailed assessment to ensure appropriate safeguards are identified and implemented.</p>
                                </>
                            }
                        </div>

                        <div className='borderSection p-4'>
                            {type === 'routine' ?
                                <div className='row mb-4'>
                                    <div className='col-4'>
                                        <div className='mb-2'>Choose Operational Risk Area...</div>
                                        <Select
                                            labelKey="name"
                                            id="user_description"
                                            onChange={(e) => { console.log(e); setSelectedDepart(e); setSelectedActivity(null) }}
                                            options={depart}
                                            placeholder="Operational Risk Area"
                                            value={selectedDepart}
                                        />
                                    </div>
                                    <div className='col-8'>
                                        <div className='mb-2'>Choose Work Activity...</div>
                                        <Select
                                            labelKey="name"
                                            id="user_description"
                                            onChange={(e) => setSelectedActivity(e)}
                                            options={activity}
                                            placeholder="WorkActivity"
                                            value={selectedActivity}
                                        />

                                    </div>
                                </div>
                                :
                                type === 'nonroutine' ?
                                    <div className='row mb-4'>
                                        {/* <div className='col-4'>
                                            <InputTextarea rows={3} placeholder='Department' value={nonRoutineDepartment} onChange={(e) => setNonRoutineDepartment(e.target.value)} style={{ width: '100%' }} />

                                        </div> */}
                                        <div className='col-12'>
                                            <div className='mb-2'>To proceed, enter the title of the work to continue.</div>
                                            <InputTextarea autoResize rows={3} placeholder='Work Activity' value={nonRoutineActivity} onChange={(e) => setNonRoutineActivity(e.target.value)} style={{ width: '100%' }} />

                                        </div>
                                    </div>
                                    :
                                    <div className='row mb-4'>
                                        <div className='col-8'>
                                            <InputText placeholder='Hazard Name' onChange={(e) => setHazardName(e.target.value)} style={{ width: '100%' }} />

                                        </div>

                                    </div>
                            }
                            {/* <div className='row mb-4'>
                                <div className='col-8'>
                                    <label htmlFor="username" className='mb-2'>Permit Short Name</label>
                                    <InputText placeholder='Short Name' onChange={(e) => setShortName(e.target.value)} style={{ width: '100%' }} />

                                </div>

                            </div> */}
                            <div className='row mb-4'>
                                <div className='col-12'>
                                    <div className='mb-2'>Identify the qualified RA Team Members to include in this Risk Assessment using the drop-down selector; only qualified members will be listed. If a required member is not listed, please contact the Administrator to have them added. Once you’ve made your selections, click the Send Notification button to notify them via email about their inclusion in the team.</div>

                                    <Select
                                        labelKey="name"
                                        valueKey="id"
                                        id="user_description"
                                        onChange={(e) => setSelectedCrew(e)}
                                        options={crew}
                                        isMulti={true}
                                        value={selectedCrew}
                                        getOptionLabel={(option) => option.name}
                                        getOptionValue={(option) => option.id}
                                        placeholder="Choose Members.."
                                    />

                                </div>
                            </div>
                            <div className='row '>
                                <div className='col-4'>
                                    <Button label="Send Notification" outlined className='d-flex' onClick={() => sendNotification()} />
                                </div>
                            </div>

                        </div>

                        <div className='borderSection p-4'>

                            <div className='row mb-4'>
                                <div className="d-flex flex-column col-12">
                                    <label htmlFor="username" className='mb-2'>Provide additional information about the work activity to clarify scope (Optional)</label>

                                    <InputTextarea rows={3} autoResize cols={30} value={activityDesc} onChange={(e) => setActivityDesc(e.target.value)} />

                                </div>
                            </div>

                        </div>
                        <div className='borderSection p-4'>

                            <div className='row mb-4'>
                                <div className='col-12'>
                                    <h4 className="risk-title-sub mb-3">Sub Activities</h4>
                                    <div className='mb-2'>Identify and list all sub-activities associated with the selected process. For each sub-activity, provide a clear description and, where feasible, upload any relevant images. These images will be utilized in the risk communication modules to enhance understanding and awareness of the process and associated risks.
                                    </div>
                                    <div className='mb-2'>Click on each listed sub-activity and follow the provided guidance to identify the hazards, potential consequences, current controls, and assess the risks specific to that sub-activity.</div>

                                </div>
                            </div>
                            <div className='col-12 mb-4'>
                                {task.length === 0 ?
                                    <p>No sub-activities added</p>
                                    :

                                    task.map((item, i) => {
                                        return (<>
                                            <TaskItem
                                                key={i}
                                                item={item}
                                                index={i}
                                                openDialog={openDialog}
                                                subActivity={subActivity}
                                                deleteTask={deleteTask}
                                                onDragStart={handleDragStart}
                                                onDrop={handleDrop}
                                                onDragOver={handleDragOver}
                                                cellClassName={cellClassName}
                                            />

                                        </>)
                                    })}



                            </div>
                            <div className='row '>
                                <div className='col-4'>
                                    <Button label="Add Sub-Activity" outlined className='d-flex' onClick={() => setAddSubActivity(!addSubActivity)} />
                                </div>
                            </div>
                            {addSubActivity &&
                                <div className='p-4 mt-4' style={{ border: '1px solid rgba(209, 213, 219, 1)' }}>
                                    <div className='col-12'>
                                        <div className="d-flex flex-column col-12">
                                            <label htmlFor="username" className='mb-2'>Sub-Activity Name</label>

                                            <InputText onChange={(e) => setSubActivityName(e.target.value)} />

                                        </div>
                                    </div>
                                    <div className='col-12 mt-3'>
                                        <label htmlFor="username" className='mb-2'>Image Uploads</label>
                                        <div className="mb-3">
                                            <DropzoneArea
                                                acceptedFiles={[
                                                    'image/jpeg',
                                                    'image/png'
                                                ]}
                                                dropzoneText={"Drag 'n' drop some files here, or click to select files"}
                                                filesLimit={5}
                                                maxFileSize={104857600}
                                                onChange={(files) => handleFileChange(files)}
                                                showPreviewsInDropzone={false}
                                                showPreviews={true}
                                                dropzoneClass={'dropzoneText d-flex align-items-center justify-content-center '}
                                            />
                                        </div>
                                    </div>

                                    <div className='col-12 mt-3'>
                                        <label htmlFor="username" className='mb-2'>Uploaded</label>

                                    </div>
                                    <div className='col-12 mt-3'>

                                        <Button label="Save" onClick={() => AddSubActivityTitle()} />

                                    </div>

                                </div>
                            }
                        </div>


                        <div className='borderSection p-4'>
                            <h5 className="mb-4 fw-bold">Overall recommendations of the RA Team</h5>

                            <div className='row mb-4'>
                                <div className='col-12'>
                                    <div className='row'>
                                        {/* 1. First component */}
                                        <div className='col-12 mb-3 d-flex align-items-start'>
                                            <div className="number-circle me-3">1</div>
                                            <div className='flex-grow-1'>
                                                <Select
                                                    labelKey="label"
                                                    id="user_description"
                                                    value={recommendationOne}
                                                    onChange={(e) => setRecommendationOne(e)}
                                                    options={[
                                                        { label: 'The overall risk level for this work activity is LOW. No further additional controls actions are necessary. Monitoring is required to ensure that the controls are maintained.', value: '0' },
                                                        { label: 'The overall risk level for this work activity is MEDIUM. Work can progress with close supervision and monitoring of current controls. Additional controls identified should be implemented within the defined period of time.', value: '1' },
                                                        { label: 'The overall risk level for this work activity is HIGH. Work should not be started or continued until the risk level has been reduced and risk numbers enters the LOW or MEDIUM zone.', value: '2' }
                                                    ]}
                                                    placeholder="Choose ..."
                                                />
                                            </div>
                                        </div>

                                        {/* 2. Second component */}
                                        <div className='col-12 mb-3 d-flex align-items-start'>
                                            <div className="number-circle me-3">2</div>
                                            <div className='flex-grow-1'>
                                                <Select
                                                    labelKey="label"
                                                    id="user_description"
                                                    value={recommendationTwo}
                                                    onChange={(e) => setRecommendationTwo(e)}
                                                    options={[
                                                        { label: 'Since this is routine activity with low risk, no formal permit is recommended. However, a dynamic review is advised in case of changing circumstances.', value: '0' },
                                                        { label: 'A formal permit to work is required before work can commence. Duration will be specified by the permit approver.', value: '1' },
                                                        { label: 'A formal permit to work that is required. This is valid for a specific task and limited duration. The permit shall include the names of individuals involved in the work.', value: '2' }
                                                    ]}
                                                    placeholder="Choose ..."
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* 3. Third component */}
                            <div className='row  mb-4 '>
                                <div className='col-12 d-flex align-items-start'>
                                    <div className="number-circle me-3">3</div>
                                    <div className='flex-grow-1'>

                                        <div className='col-12 ' style={{ border: '1px solid #ced4da' }}>
                                            <p className='p-2'>Considering the hazards and risks associated with this work activity, the RA team requires the following high-risk permits to be approved and active when applying for a permit for this specific activity:</p>
                                            <div className="d-flex col-12 p-3">
                                                {risk.length !== 0 &&
                                                    risk.map(item => {
                                                        return (
                                                            <label className='label-role checkbox-bootstrap checkbox-lg col-4 me-3'>
                                                                <input value={item.hazardName} onChange={(e) => checkHotWork(e, item)} checked={eptwHot && eptwHot.some(item1 => item1.id === item.id)} type='checkbox' className='me-1' />
                                                                <span className="checkbox-placeholder"></span>
                                                                {item.hazardName}
                                                            </label>
                                                        );
                                                    })
                                                }
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* 4. Fourth component */}
                            <div className='row mb-4'>
                                <div className='col-12 d-flex align-items-start'>
                                    <div className="number-circle me-3">4</div>
                                    <div className='flex-grow-1'>
                                        <div className="d-flex flex-column col-12">
                                            <label htmlFor="username" className='mb-2'>Additional Recommendation</label>
                                            <InputTextarea autoResize rows={3} cols={30} value={additionalRecommendation} onChange={(e) => setAdditionalRecommendation(e.target.value)} />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>



                        {domain === 'edit' &&
                            <div className='borderSection p-4'>
                                <div className='row mb-4'>
                                    <div className="d-flex flex-column col-12">
                                        <h6 htmlFor="username" className='mb-2 fw-bold'>Team Members Declaration</h6>
                                        <p>As a member of the team for this exercise, I confirm that I have actively contributed to identifying the potential consequences of this Critical High Risk Activity and in determining the necessary controls. These controls reflect our collective professional judgment and are essential for ensuring safety. The conclusions we reached were the result of collaboration and consensus, utilizing our team’s full range of expertise.</p>
                                        <table className="table ">
                                            <thead>
                                                <th>Name</th>
                                                <th>Sign / Date of Affimation</th>
                                            </thead>
                                            <tbody>
                                                {raTeamMember.length !== 0 && raTeamMember.map((item) => (
                                                    <tr>
                                                        <td>{item.user.firstName}</td>

                                                        <td>{item.signature ? <div className='d-flex flex-column align-items-start'>
                                                            <ImageComponent fileName={item.signature} size={'200'} name={false} /><span>{moment(item.signatureDate).format('DD-MM-YYYY')}</span></div> : "Pending"}</td>

                                                    </tr>
                                                ))}
                                            </tbody>
                                        </table>

                                    </div>
                                </div>


                            </div>
                        }


                        <div className='borderSection p-4'>
                            <h5 className="mb-4 fw-bold ">Declaration</h5>


                            <div className='row mb-2'>
                                {type === 'routine' ?
                                    <p>As the Team Leader for this Routine Risk Assessment, I affirm my role in guiding the identification of potential risks and necessary controls. The controls listed have been evaluated by the team based on their professional expertise and are essential for ensuring safety in this activity. This outcome reflects the collective judgment of the team, reached through collaboration and consensus.
                                    </p> : type === 'nonroutine' ?
                                        <p>As the Team Leader for this Non-Routine Risk Assessment, I confirm my responsibility in guiding the team through the identification of potential risks and necessary controls for activities that are not part of the organization’s routine work activity inventory. Given that these activities are not regularly undertaken, we are placing additional focus on documenting the risks and ensuring that appropriate controls are in place. The controls identified are essential for maintaining safety, and this conclusion has been reached through consensus based on the team’s collective professional judgment and experience.
                                        </p> : ''
                                }
                              

                            </div>
                            <div className='row mb-4 text-center'>
                                <div className="d-flex flex-column col-12">
                                    <div className="row mt-4">
                                        <div className="col-12 =">
                                            {domain === 'edit' ? (
                                                data && data.teamLeaderDeclaration && data.teamLeaderDeclaration?.sign ? (
                                                    <>
                                                        <ImageComponent fileName={data.teamLeaderDeclaration.sign} size={'300'} name={false} />
                                                        <p>{data.teamLeaderDeclaration.name}</p>
                                                    </>
                                                ) : (
                                                    <>
                                                        <SignatureCanvas
                                                            penColor="#1F3BB3"
                                                            canvasProps={{
                                                                width: 450,
                                                                height: 120,
                                                                className: "sigCanvas",
                                                                style: {
                                                                    boxShadow: "0px 0px 10px 3px rgb(189 189 189)",
                                                                },
                                                            }}
                                                            ref={signRef}
                                                        />
                                                        <i className="fa fa-undo undo" onClick={() => signRef.current.clear()}></i>
                                                        <p>{user.firstName}</p>
                                                    </>
                                                )
                                            ) : (
                                                <>
                                                    <SignatureCanvas
                                                        penColor="#1F3BB3"
                                                        canvasProps={{
                                                            width: 450,
                                                            height: 120,
                                                            className: "sigCanvas",
                                                            style: {
                                                                boxShadow: "0px 0px 10px 3px rgb(189 189 189)",
                                                            },
                                                        }}
                                                        ref={signRef}
                                                    />
                                                    <i className="fa fa-undo undo" onClick={() => signRef.current.clear()}></i>
                                                    <p>{user.firstName}</p>
                                                </>
                                            )}

                                        </div>

                                    </div>
                                </div>
                            </div>

                        </div>

                        <div className="col-12 text-center" style={{ padding: 20 }}>
                            <button style={{ marginRight: 10 }}
                                type="button"
                                className="btn btn-primary mb-3 "
                                onClick={(e) => {
                                    e.preventDefault();
                                    if (domain === 'edit') {
                                        draftUserEditHandler();
                                    } else {
                                        draftUserHandler();
                                    }

                                }}
                            >
                                Save as Draft
                            </button>
                            <button
                                type="button"
                                className="btn btn-secondary  mb-3 "
                                onClick={(e) => {
                                    e.preventDefault();
                                    if (domain === 'edit') {
                                        if (data.status === 'Draft') {
                                            editUserHandler();
                                        } else {
                                            setRiskUpdate(true)
                                        }

                                    } else {
                                        createUserHandler();
                                    }
                                }}
                            >
                                Release Draft for Affirmation
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            {riskUpdate && <RiskUpdate show={riskUpdate} onChangeModel={setRiskUpdate} id={data.id} onSubmitUpdate={onSubmitUpdate} />}
            {Update.length !== 0 && <UpdateTable data={Update} />}
          
            {isLoading &&
                <FullLoader />
            }
            {console.log(task)}
            {item !== '' && <>
                <Modal show={visible} onHide={() => setVisible(false)} size='lg'>
                    <Modal.Header closeButton>
                        <Modal.Title>Sub-activity Risk Assessment </Modal.Title>
                    </Modal.Header>
                    <Modal.Body>

                       

                        <SubActivityComponent
                            item={item}
                            onSave={editSubActivityTitle}
                        />


                        <hr />
                        {item !== '' && item[8] && item[8].step !== undefined && (

                            <HeadStepper activeStage={item[8].step} stages={item[9].level} stageStatus={item[11].value} handleStageClick={handleStageClick} getStatusClass={getStatusClass} />

                        )}
                        <hr />
                        {item[8].step === 0 && <>

                            <p>For this sub-activity, identify associated hazards by selecting from the various hazard types displayed. Each type will feature icons representing specific hazards. Once selected, these hazards will automatically appear in other modules, such as Risk Communication, Permit to Work, and Toolbox Talks</p>
                            <p>If you encounter a specific hazard that is not included in the library, please send an <NAME_EMAIL> so that it can be promptly added to the hazard library.</p>
                            <HazardAccordion
                                hazards={hazards}
                                activeTabIndex={activeTabIndex}
                                setActiveTabIndex={setActiveTabIndex}
                                selectedHazards={item[1].selected}
                                onClickHazards={onClickHazards}
                                required={required}
                                item={item}
                            />
                            <IdentifiedHazards
                                selectedHazards={item[1].selected}
                                onDeleteHaz={onDeleteHaz}
                            />
                        </>}

                        {item[8].step === 1 && <>
                            <h6 className='fw-bold'>Consequences</h6>
                            <p>For this sub-activity, list and elaborate on all potential consequences associated with the identified hazards, covering impacts on Personnel, Environment, Property/Equipment, and Operations, as applicable. Include all relevant areas that apply to the specific hazards of the sub-activity.</p>
                            {item[2].option.map((con, i) => (
                                <Consequence
                                    key={i}
                                    con={con}
                                    i={i}
                                    impactOn={impactOn}
                                    onImapactOn={onImapactOn}
                                    onConseqText={onConseqText}
                                    onDeleteConseq={onDeleteConseq}
                                    handleTaskFileChange={handleTaskFileChange}
                                    required={required}
                                    type={'routine'}
                                    handleRemoveImage={handleRemoveImage}
                                />
                            ))}
                            <Button variant="outline-primary" onClick={() => addConsequence('consequence')}>Add Consequence</Button>
                        </>}

                        {item[8].step === 2 && <>
                            <h6 className='fw-bold'>Current Controls</h6>
                            <p>Identify and describe in detail the current controls in place to manage the hazards and minimize their consequences. Current controls refer to existing safety measures, procedures, and other implemented actions to reduce risks associated with the sub-activity.</p>
                            {item[3].option.map((con, i) => (
                                <CurrentControl
                                    key={i}
                                    con={con}
                                    i={i}
                                    control={control}
                                    controlType={controlType}
                                    onMethodOn={onMethodOn}
                                    onImapactOn={onImapactOn}
                                    onConseqText={onConseqText}
                                    onDeleteConseq={onDeleteConseq}
                                    onConseqRequired={onConseqRequired}
                                    handleTaskFileChange={handleTaskFileChange}
                                    required={required}
                                    type={'routine'}
                                    handleRemoveImage={handleRemoveImage}
                                />
                            ))}

                            {console.log(item[3].option.some(item => item.current_type === "No Control"))}

                            {item[3].option.every(con => con.current_type !== "No Control") && (

                                <Button variant="outline-primary" className='mt-4' onClick={() => addConsequence('current_control')}>Add Current Control</Button>
                            )}
                        </>}

                        {item[8].step === 3 && <>
                            <RiskAssessment
                                item={item}
                                severity={severity}
                                severityData={severityData}
                                required={true}
                                onChangeSeverity={onChangeSeverity}
                                likelyhood={likelyhood}
                                levelData={levelData}
                                onChangeLikelyhood={onChangeLikelyhood}
                                rowClassName={rowClassName}
                                tableData={tableData}
                                cellClassName={cellClassName}
                                cellStyle={cellStyle}
                                onChangeReAss={onChangeReAss}

                            />
                        </>}

                        {item[8].step === 4 && <>
                            <ProposedRiskManagement
                                item={item}
                                control={controlAdditional}
                                responsibility={responsibility}
                                severity={severity}
                                severityData={severityData}
                                likelyhood={likelyhood}
                                levelData={levelData}
                                tableData={tableData}
                                severityTable={severityTable}
                                likelyhoodTable={likelyhoodTable}
                                riskTable={riskTable}
                                required={required}
                                onControlAddion={onControlAddion}
                                onControlAddionText={onControlAddionText}
                                onDeleteConseq={onDeleteConseq}
                                onResponsePerson={onResponsePerson}
                                onResponseDate={onResponseDate}
                                addAdditionalControl={addAdditionalControl}
                                onChangeSeverity={onChangeSeverity}
                                onChangeLikelyhood={onChangeLikelyhood}
                                cellClassName={cellClassName}
                                cellStyle={cellStyle}
                                rowClassName={rowClassName}
                            />
                        </>}
                    </Modal.Body>

                    <Modal.Footer>
                        {footerTemplate}
                    </Modal.Footer>
                </Modal>
            </>}

        </div >


    )
}

export default Routine