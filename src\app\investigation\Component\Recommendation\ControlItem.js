import React from 'react';
import Card from 'react-bootstrap/Card';
import JobFactors from './JobFactors';
import OrganizationalFactors from './OrganizationalFactors';
import { Row, Col } from 'react-bootstrap';

const ControlItem = ({
    control,
    itemIndex,
    title,
    type,
    startJobFactorIndex,
    startOrganizationalFactorIndex,
}) => {
    return (
        <Card className="mb-3">
            <li className='fw-bold'>{type} {itemIndex + 1} - {control.controlStatement}</li>
            <Card.Body>
                <Row className="d-flex align-items-stretch">
                    {/* Immediate Causes */}
                    {(type !== 'UPC' && type !== 'UMC') && (
                        <Col className="d-flex">
                            <Card className="border-0 shadow-sm mb-3 w-100" style={{ borderRadius: '5px', overflow: 'hidden' }}>
                                <Card.Header className="text-uppercase font-weight-bold py-2" style={{ backgroundColor: '#f8d7da63', borderBottom: '1px solid #f8d7da63', color: '#f95f53' }}>
                                    Immediate Causes
                                </Card.Header>
                                <Card.Body style={{ padding: '10px' }}>
                                    <ol>
                                        <li><strong>{control.immediateCause}</strong></li>
                                        <p>{control.immediateCauseDescription}</p>
                                    </ol>
                                </Card.Body>
                            </Card>
                        </Col>
                    )}


                    {/* Job Factors */}
                    <Col className="d-flex">
                        <Card className="border-0 shadow-sm mb-3 w-100" style={{ borderRadius: '5px', overflow: 'hidden' }}>
                            <Card.Header className="text-uppercase font-weight-bold py-2" style={{ color: '#34b1aa', backgroundColor: '#34b1aa2b', borderBottom: '1px solid #34b1aa2b' }}>
                                RELATED JOB FACTORS
                            </Card.Header>
                            <Card.Body style={{ padding: '10px' }}>
                                <JobFactors
                                    jobFactors={control.jobFactors}
                                    startIndex={startJobFactorIndex}
                                />
                            </Card.Body>
                        </Card>
                    </Col>

                    {/* Organizational Factors */}
                    <Col className="d-flex">
                        <Card className="border-0 shadow-sm mb-3 w-100" style={{ borderRadius: '5px', overflow: 'hidden' }}>
                            <Card.Header className="text-uppercase font-weight-bold py-2" style={{ backgroundColor: '#e29e092b', borderBottom: '1px solid #e29e092b', color: '#e29e09' }}>
                                RELATED ORGANIZATIONAL FACTORS
                            </Card.Header>
                            <Card.Body style={{ padding: '10px' }}>
                                <OrganizationalFactors
                                    organizationalFactors={control.relatedOrganizationalFactors}
                                    startIndex={startOrganizationalFactorIndex}
                                />
                            </Card.Body>
                        </Card>
                    </Col>
                </Row>
            </Card.Body>
        </Card>
    );
};

export default ControlItem;
