import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Spinner } from 'react-bootstrap';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import Select from 'react-select';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import 'primereact/resources/themes/lara-light-indigo/theme.css';
import 'primereact/resources/primereact.min.css';
import API from '../../services/API';
import { INVERTIGATION_RECOMMENDATION_WITH_ID, GET_USER_ROLE_BY_MODE } from '../../constants';

const ActionAssigneeModal = ({ invest, show, handleClose,approveReport }) => {
    const [data, setData] = useState([]);
    const [assignees, setAssignees] = useState([]);
    const [assignedData, setAssignedData] = useState([]);
    const [errors, setErrors] = useState([]); // To track errors in each row
    const [loading, setLoading] = useState(true); // Loader state

    useEffect(() => {
        if (show) {
            fetchData();
            fetchAssignees();
        }
    }, [show]);

    const fetchData = async () => {
        setLoading(true); // Start loader
        try {
            const response = await API.get(INVERTIGATION_RECOMMENDATION_WITH_ID(invest.id));
            const fetchedData = response.data.map(item => ({
                ...item,
                assignedToId: null,
                dueDate: null,
            }));
            setData(fetchedData);
            setAssignedData(fetchedData);
            setErrors(fetchedData.map(() => ({ assignedToId: false, dueDate: false }))); // Initialize errors
        } catch (error) {
            console.error("Error fetching actions data:", error);
        } finally {
            setLoading(false); // Stop loader
        }
    };

    const fetchAssignees = async () => {
        try {
            const response = await API.post(GET_USER_ROLE_BY_MODE, {
                locationOneId: "",
                locationTwoId: "",
                locationThreeId: "",
                locationFourId: "",
                mode: 'incident_action_assignee'
            });
            const assigneeOptions = response.data.map(assignee => ({
                value: assignee.id,
                label: assignee.firstName,
            }));
            setAssignees(assigneeOptions);
        } catch (error) {
            console.error("Error fetching assignees:", error);
        }
    };

    const handleAssigneeChange = (selectedOption, rowIndex) => {
        const updatedData = [...assignedData];
        updatedData[rowIndex].assignedToId = selectedOption ? selectedOption.value : null; // Store only the value
        setAssignedData(updatedData);

        const updatedErrors = [...errors];
        updatedErrors[rowIndex].assignedToId = !selectedOption; // Mark error if no assignedToId selected
        setErrors(updatedErrors);
    };


    const handleDueDateChange = (date, rowIndex) => {
        const updatedData = [...assignedData];
        updatedData[rowIndex].dueDate = date;
        setAssignedData(updatedData);

        const updatedErrors = [...errors];
        updatedErrors[rowIndex].dueDate = !date; // Mark error if no due date selected
        setErrors(updatedErrors);
    };

    const handleSave = () => {
        // Check for any missing values before allowing save
        const newErrors = assignedData.map(item => ({
            assignedToId: !item.assignedToId,
            dueDate: !item.dueDate,
        }));
        setErrors(newErrors);

        const hasErrors = newErrors.some(error => error.assignedToId || error.dueDate);
        if (!hasErrors) {
            const dataToSave = assignedData.map(item => ({
                ...item,
                dueDate: item.dueDate ? item.dueDate.toISOString() : null,
            }));
            console.log('Updated Data:', dataToSave);
            approveReport(dataToSave)
            handleClose();
        }
    };

    const sNoTemplate = (rowData, { rowIndex }) => <span>{rowIndex + 1}</span>;

    const assigneeTemplate = (rowData, { rowIndex }) => {
        const selectedOption = assignees.find(option => option.value === rowData.assignedToId) || null;

        return (
            <Select
                options={assignees}
                value={selectedOption}  // Set the entire object (value and label) for react-select
                onChange={(selectedOption) => handleAssigneeChange(selectedOption, rowIndex)}
                placeholder="Select"
                isClearable
                menuPortalTarget={document.body}  // Render the menu outside the modal
                menuPosition="fixed"              // Position it fixed to avoid layout issues
                styles={{
                    control: (base) => ({
                        ...base,
                        borderColor: errors[rowIndex]?.assignedToId ? 'red' : base.borderColor,
                    }),
                    menuPortal: base => ({ ...base, zIndex: 9999 }) // Ensure the dropdown appears above the modal
                }}
            />
        );
    };


    const dueDateTemplate = (rowData, { rowIndex }) => (
        <DatePicker
            selected={rowData.dueDate}
            onChange={(date) => handleDueDateChange(date, rowIndex)}
            placeholderText="Select Due Date"
            className={`form-control ${errors[rowIndex]?.dueDate ? 'border-danger' : ''}`}
            minDate={new Date()}
            dateFormat="dd-MM-yyyy"  // Display format as DD-MM-YYYY
        />
    );

    return (
        <Modal show={show} onHide={handleClose} centered size="lg">
            <Modal.Header closeButton>
                <Modal.Title>Action Details</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                {loading ? (
                    <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '200px' }}>
                        <Spinner animation="border" variant="primary" />
                    </div>
                ) : (
                    <DataTable value={assignedData} responsiveLayout="scroll" stripedRows>
                        <Column header="S.No" body={sNoTemplate} />
                        <Column field="recommendedActions" header="Recommended Actions" />
                        <Column field="rationaleForRecommendation" header="Rationale for Recommendation" />
                        <Column header="Assignee" body={assigneeTemplate} />
                        <Column header="Due Date" body={dueDateTemplate} />
                    </DataTable>
                )}
            </Modal.Body>
            <Modal.Footer>
                <Button variant="primary" onClick={handleClose}>
                    Close
                </Button>
                <Button variant="secondary" onClick={handleSave}>
                    Submit Report
                </Button>
            </Modal.Footer>
        </Modal>
    );
};

export default ActionAssigneeModal;
