import React from 'react';

const IdentifiedHazards = ({ selectedHazards, onDeleteHaz }) => {
    return (
        <>
            <h6 className='mt-4 mb-3'>Hazards Identified</h6>
            <div className='row'>
                {selectedHazards.map((item, index) => (
                    <div className='col-3 mb-3' key={index}>
                        <div className='d-flex justify-content-between align-items-center p-2' style={{ border: '1px solid #E5E7EB', borderRadius: 8 }}>
                            {!localStorage.getItem('SELECTED_INDUSTRIES') && (
                                <img
                                    src={`https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/hazards/${item.image}`}
                                    style={{ height: 40 }}
                                    alt="sample"
                                />
                            )}

                            <p>{item.name}</p>
                            <i className='pi pi-times' onClick={() => onDeleteHaz(item)}></i>
                        </div>
                    </div>
                ))}
            </div>
        </>
    );
};

export default IdentifiedHazards;
