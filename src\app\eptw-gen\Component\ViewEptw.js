import React from 'react';
import { Container, Row, Col, Card } from 'react-bootstrap';
import moment from 'moment';
import ImageComponent from '../../services/FileDownlodS3';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { useSelector } from 'react-redux';

const ViewEptw = ({ applicationDetails }) => {
    const user = useSelector((state) => state.login.user);
    const groupedControls = applicationDetails.permitRiskControl?.reduce((acc, control, index) => {
        (acc[control.permitType] = acc[control.permitType] || []).push({ ...control, controlIndex: index });
        return acc;
    }, {}) || {};

    const statusData = [
        {
            role: 'Applicant',
            name: applicationDetails.applicant?.firstName || "N/A",
            roleName: `Applicant - ${applicationDetails.applicant?.firstName || "N/A"}`,
            signature: applicationDetails.applicantStatus?.signature || "N/A",
            status: applicationDetails.applicantStatus?.status ? "Approved" : "Pending",
            signedDate: applicationDetails.applicantStatus?.signedDate || "N/A",
            comments: applicationDetails.applicantStatus?.comments || "N/A",
            declaration: "I confirm that all required fields are accurately completed, and I acknowledge responsibility for adhering to the specified safety controls for this work activity."
        },
        applicationDetails.reviewerId && {
            role: 'Reviewer',
            name: applicationDetails.reviewer?.firstName || "N/A",
            roleName: `Reviewer - ${applicationDetails.reviewer?.firstName || "N/A"}`,
            signature: applicationDetails.reviewerStatus?.signature || "N/A",
            status: applicationDetails.reviewerStatus?.status ? "Approved" : "Pending",
            signedDate: applicationDetails.reviewerStatus?.signedDate || "N/A",
            comments: applicationDetails.reviewerStatus?.comments || "N/A",
            declaration: "I have reviewed the application details and verify that the listed controls and prerequisites are suitable and sufficient for safe task execution."
        },
        applicationDetails.assessorId && {
            role: 'Assessor',
            name: applicationDetails.assessor?.firstName || "N/A",
            roleName: `Assessor - ${applicationDetails.assessor?.firstName || "N/A"}`,
            signature: applicationDetails.assessorStatus?.signature || "N/A",
            status: applicationDetails.assessorStatus?.status ? "Approved" : "Pending",
            signedDate: applicationDetails.assessorStatus?.signedDate || "N/A",
            comments: applicationDetails.assessorStatus?.comments || "N/A",
            declaration: "I affirm that I have carefully assessed the risk levels, controls, and work conditions and that all necessary precautions are documented."
        },
        applicationDetails.approverId && {
            role: 'Approver',
            name: applicationDetails.approver?.firstName || "N/A",
            roleName: `Approver - ${applicationDetails.approver?.firstName || "N/A"}`,
            signature: applicationDetails.approverStatus?.signature || "N/A",
            status: applicationDetails.approverStatus?.status ? "Approved" : "Pending",
            signedDate: applicationDetails.approverStatus?.signedDate || "N/A",
            comments: applicationDetails.approverStatus?.comments || "N/A",
            declaration: "I approve this permit with the assurance that all safety measures and controls have been verified and are in place to safely conduct this work."
        },
        applicationDetails.closeoutStatus && {
            role: 'Closeout',
            name: applicationDetails.closeoutStatus.by || "N/A",
            roleName: `Closeout - ${applicationDetails.closeoutStatus.by || "N/A"}`,
            signature: applicationDetails.closeoutStatus?.signature || "N/A",
            status: applicationDetails.closeoutStatus?.status,
            signedDate: applicationDetails.closeoutStatus?.signedDate || "N/A",
            comments: applicationDetails.closeoutStatus?.comments || "N/A",
            declaration: "The task(s) have been completed. The work area(s) have been left in a tidy and safe condition."
        },
        applicationDetails.acknowledgementStatus && {
            role: 'Acknowledger',
            name: applicationDetails.acknowledgementStatus.by || "N/A",
            roleName: `Acknowledger - ${applicationDetails.acknowledgementStatus.by || "N/A"}`,
            signature: applicationDetails.acknowledgementStatus?.signature || "N/A",
            status: applicationDetails.acknowledgementStatus?.status ,
            signedDate: applicationDetails.acknowledgementStatus?.signedDate || "N/A",
            comments: applicationDetails.acknowledgementStatus?.comments || "N/A",
            declaration: "I acknowledge that, to the best of my knowledge, the work area(s) have been left in a tidy and safe condition by the applicant."
        }
    ].filter(Boolean); // Removes undefined or false entries


    const signatureBodyTemplate = (rowData) => (
        rowData.signature !== "N/A"
            ? <ImageComponent fileName={rowData.signature} size="50" name={false} />
            : "N/A"
    );

    const signedDateBodyTemplate = (rowData) => (
        rowData.signedDate !== "N/A"
            ? moment(rowData.signedDate).format('DD-MM-YYYY hh:mm A')
            : "N/A"
    );


    const isInternalUser = user.type === 'Internal'; // Adjust this condition as per your role logic
    const filteredStatusData = isInternalUser
        ? statusData.filter(data => data.role !== 'Reviewer')
        : statusData;


    return (
        <Container fluid className="p-2">
            <Card className="mb-4">
                <Card.Body>
                    <Row className="mb-2">
                        <Col md={4}>
                            <p className="obs-title">Work Type</p>
                            <p className="obs-content">{applicationDetails.permitWorkType || "N/A"}</p>
                        </Col>
                        <Col md={4}>
                            <p className="obs-title">Start Date</p>
                            <p className="obs-content">
                                {applicationDetails.permitStartDate ? moment(applicationDetails.permitStartDate).format('DD-MM-YYYY hh:mm A') : "N/A"}
                            </p>
                        </Col>
                        <Col md={4}>
                            <p className="obs-title">End Date</p>
                            <p className="obs-content">
                                {applicationDetails.permitEndDate ? moment(applicationDetails.permitEndDate).format('DD-MM-YYYY hh:mm A') : "N/A"}
                            </p>
                        </Col>
                    </Row>
                    <Row className="mb-2">
                        <Col md={12}>
                            <p className="obs-title">Work Description</p>
                            <p className="obs-content">{applicationDetails.workDescription || "N/A"}</p>
                        </Col>
                    </Row>
                    <Row className="mb-2">
                        <Col md={4}>
                            <p className="obs-title">Responsible Site / Job Supervisor</p>
                            <p className="obs-content">{applicationDetails.nameOfSiteSupervisor || "N/A"}</p>
                        </Col>

                        <Col md={4}>
                            <p className="obs-title">No. of Workers</p>
                            <p className="obs-content">{applicationDetails.noOfWorkers || "N/A"}</p>
                        </Col>
                    </Row>
                    <Row className="mb-2">
                        <Col md={4}>
                            <p className="obs-title">Supervisor Contact No</p>
                            <p className="obs-content">{applicationDetails.supervisorContactNo || "N/A"}</p>
                        </Col>
                        <Col md={4}>
                            <p className="obs-title">Applicant Contact No</p>
                            <p className="obs-content">{applicationDetails.applicantContactNo || "N/A"}</p>
                        </Col>
                        <Col md={4}>
                            <p className="obs-title">Work Order / Job Number</p>
                            <p className="obs-content">{applicationDetails.workOrderNo || "N/A"}</p>
                        </Col>
                    </Row>
                    <Row className="mb-2">
                        <Col md={12}>
                            <p className="obs-title">Location</p>
                            <p className="obs-content">
                                {[applicationDetails.locationOne, applicationDetails.locationTwo, applicationDetails.locationThree, applicationDetails.locationFour, applicationDetails.locationFive, applicationDetails.locationSix]
                                    .filter(location => location?.name)
                                    .map(location => location.name)
                                    .join(' > ') || "N/A"}
                            </p>
                        </Col>
                    </Row>

                    <Row className="mb-2">
                        <Col md={12}>
                            {/* <p className="obs-title">Uploads</p> */}
                            <div className='col-12 mt-3 mb-4'>
                                {applicationDetails.supportingDocuments && applicationDetails.supportingDocuments.length > 0 && (<label htmlFor="username" className='mb-2'>Uploaded</label>)}
                                <div className="image-preview mt-3">
                                    {applicationDetails.supportingDocuments && applicationDetails.supportingDocuments.length > 0 && (
                                        <div className="row">
                                            {applicationDetails.supportingDocuments.map((file, fileIndex) => (
                                                <div key={fileIndex} className="col-3" style={{ position: 'relative' }}>
                                                    <div className="boxShadow d-flex align-items-center">
                                                        <ImageComponent fileName={file} size={'100'} name={true} />
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                </div>
                            </div>
                        </Col>
                    </Row>
                </Card.Body>
            </Card>

            {Object.entries(groupedControls).map(([permitType, controls]) => (
                <div key={permitType} className="mb-4">
                    <h5 className="mt-4 mb-3 permit-head">{`${permitType}`}</h5>
                    <Row>
                        {controls.map((control) => (
                            <Col md={'4'} key={control.controlIndex} >
                                <div className="border p-3 mb-3 rounded ">
                                    <div className='mb-3'><strong>{control.controlIndex + 1} .</strong>  <span className="obs-content">{control.description}</span></div>
                                    <div>
                                        <p className="obs-title">Type</p>
                                        <p className="obs-content">{control.currentType || "N/A"}</p>
                                    </div>
                                    <div>
                                        <p className="obs-title">Value</p>
                                        <p className="obs-content">{control.value || "N/A"}</p>
                                    </div>
                                    <div>
                                        <p className="obs-title">Remarks</p>
                                        <p className="obs-content">{control.remarks || "N/A"}</p>
                                    </div>
                                    <div className='col-12 mt-3 mb-4'>
                                        {control.evidence && control.evidence.length > 0 && (<label htmlFor="username" className='mb-2'>Uploaded</label>)}
                                        <div className="image-preview mt-3">
                                            {control.evidence && control.evidence.length > 0 && (
                                                <div className="row">
                                                    {control.evidence.map((file, fileIndex) => (
                                                        <div key={fileIndex} className="col-3" style={{ position: 'relative' }}>
                                                            <div className="boxShadow d-flex align-items-center">
                                                                <ImageComponent fileName={file} size={'100'} name={false} />
                                                            </div>
                                                        </div>
                                                    ))}
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </Col>
                        ))}
                    </Row>
                </div>
            ))}

            <Card className="mb-4">
                <Card.Header as="h4">Status Details</Card.Header>
                <Card.Body>
                    <Row  className="mb-3">
                        {filteredStatusData.map((data, index) => (
                            <Col md="6" key={index} className='mb-3'>
                                <p><strong style={{fontSize:'16px'}}>Role/Name: {data.roleName}</strong></p>
                                <p><strong>Declaration:</strong> {data.declaration}</p>
                                <p><strong>Signature:</strong> {data.signature !== "N/A" ? <ImageComponent fileName={data.signature} size="50" name={false} /> : "N/A"}</p>
                                <p><strong>Signed Date:</strong> {data.signedDate !== "N/A" ? moment(data.signedDate).format('DD-MM-YYYY hh:mm A') : "N/A"}</p>
                                <p><strong>Comments:</strong> {data.comments}</p>
                            </Col>


                        ))}
                    </Row>
                </Card.Body>
            </Card>
        </Container>
    );
};

export default ViewEptw;
