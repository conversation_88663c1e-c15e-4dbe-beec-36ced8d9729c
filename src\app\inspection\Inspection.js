// ...existing imports
import React, { useEffect, useState, useCallback } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Button } from 'primereact/button';
import { Dialog } from 'primereact/dialog';
import moment from 'moment';
import { Modal, Form } from 'react-bootstrap';
import Select from 'react-select';
import API from '../services/API';
import { ADMINDROPDOWNS, GET_USER_ROLE_BY_MODE } from '../constants';
import AllFilterLocation from '../investigation/LocationDropDown';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import Swal from 'sweetalert2';
import { FilterMatchMode } from 'primereact/api';
import { Calendar } from 'primereact/calendar';
import { Dropdown } from 'primereact/dropdown';

const InspectionSchedule = ({ inspections, fetchInspectionData }) => {
    // const [inspections, setInspections] = useState([]);
    const [viewDialogVisible, setViewDialogVisible] = useState(false);
    const [selectedInspection, setSelectedInspection] = useState(null);
    const [showAddModal, setShowAddModal] = useState(false);
    const [editMode, setEditMode] = useState(false);
    const [editIndex, setEditIndex] = useState(null);

    const [checklistOptions, setChecklistOptions] = useState([]);
    const [filteredChecklistOptions, setFilteredChecklistOptions] = useState([]);
    const [inspectorOptions, setInspectorOptions] = useState([]);
    const [inspectionCategories, setInspectionCategories] = useState([]);

    const [filters, setFilters] = useState({
        'inspectionCategory': { value: null, matchMode: FilterMatchMode.IN },
        'inspector.firstName': { value: null, matchMode: FilterMatchMode.IN },
        'checklist.name': { value: null, matchMode: FilterMatchMode.CONTAINS },
        'scheduledDate': { value: null, matchMode: FilterMatchMode.DATE_IS },
        timelineStatus: { value: null, matchMode: FilterMatchMode.EQUALS },
    });
    const inspectorFilterOptions = inspectorOptions.map(opt => ({ label: opt.label, value: opt.label }));
    const categoryFilterOptions = inspectionCategories.map(cat => ({ label: cat.label, value: cat.value }));
    const computeTimelineStatus = (row) => {
        const now = moment().startOf('day');
        const start = moment(row.scheduledDate).startOf('day');
        const due = moment(row.dueDate).startOf('day');

        if (now.isBefore(start)) return 'Upcoming';
        if (now.isSameOrAfter(start) && now.isSameOrBefore(due)) return 'Due Now';
        if (now.isAfter(due)) return 'Overdue';
        return 'N/A';
    };
    const enrichedInspections = inspections.map((row) => ({
        ...row,
        timelineStatus: computeTimelineStatus(row),
    }));

    const [newInspection, setNewInspection] = useState({
        name: '',
        scheduledDate: '',
        inspectionCategory: '',
        maskId: '',
        dueDate: '',
        status: '',
        checklistVersion: '',

        inspectorId: '',
        assignedById: '',
        checklistId: '',
        locationOneId: '',
        locationTwoId: '',
        locationThreeId: '',
        locationFourId: '',
        locationFiveId: '',
        locationSixId: '',
    });

    useEffect(() => {
        fetchChecklistOptions();
        fetchInspectorOptions();
        fetchDropdownData('ins_category', setInspectionCategories);
    }, []);

    const fetchChecklistOptions = async () => {
        try {
            const uriString = {
                where: { status: 'Published' },
            };
            const response = await API.get(`/checklists?filter=${encodeURIComponent(JSON.stringify(uriString))}`);
            if (response.status === 200) {
                const options = response.data.map(item => ({
                    label: `${item.name} (v${item.version})`,
                    value: item.id,
                    version: item.version,
                    category: item.category || '',
                }));
                setChecklistOptions(options);
            }
        } catch (error) {
            console.error('Error fetching checklist options:', error);
        }
    };

    const fetchInspectorOptions = async () => {
        try {
            const response = await API.post(GET_USER_ROLE_BY_MODE, {
                locationOneId: "",
                locationTwoId: "",
                locationThreeId: "",
                locationFourId: "",
                mode: 'ins_inspector'
            });

            if (response.status === 200) {
                const options = response.data.map(user => ({
                    label: `${user.firstName}`,
                    value: user.id,
                }));
                setInspectorOptions(options);
            }
        } catch (error) {
            console.error('Error fetching inspector options:', error);
        }
    };

    const fetchDropdownData = useCallback(async (maskId, setState) => {
        try {
            const uriString = {
                where: { maskId },
                include: [{ relation: "dropdownItems" }],
            };
            const url = `${ADMINDROPDOWNS}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
            const response = await API.get(url);
            if (response.status === 200) {
                const data = response.data[0]?.dropdownItems.map(item => ({
                    label: item.name,
                    value: item.name,
                })) || [];
                setState(data);
            }
        } catch (error) {
            console.error(`Error fetching ${maskId} list:`, error);
        }
    }, []);

    const handleFilter = (
        locationOneId,
        locationTwoId,
        locationThreeId,
        locationFourId,
        locationFiveId,
        locationSixId
    ) => {
        setNewInspection((prev) => ({
            ...prev,
            locationOneId,
            locationTwoId,
            locationThreeId,
            locationFourId,
            locationFiveId,
            locationSixId
        }));
    };

    const resetNewInspection = () => {
        setNewInspection({
            name: '',
            scheduledDate: '',
            inspectionCategory: '',
            maskId: '',
            dueDate: '',
            status: '',
            checklistVersion: '',
            inspectorId: '',
            assignedById: '',
            checklistId: '',
            locationOneId: '',
            locationTwoId: '',
            locationThreeId: '',
            locationFourId: '',
            locationFiveId: '',
            locationSixId: '',
        });
        setFilteredChecklistOptions([]);
    };

    const handleSaveInspection = async () => {
        const payload = {
            ...newInspection,
            scheduledDate: newInspection.scheduledDate ? new Date(newInspection.scheduledDate).toISOString() : null,
            dueDate: newInspection.dueDate ? new Date(newInspection.dueDate).toISOString() : null,
        };

        console.log(payload)

        try {
            if (editMode && editIndex !== null) {
                const updateUrl = `/inspections/${newInspection.id}`;
                const response = await API.patch(updateUrl, payload);
                if (response.status === 200) {
                    fetchInspectionData();
                    Swal.fire({
                        icon: 'success',
                        title: 'Updated!',
                        text: 'Inspection updated successfully.',
                    });
                }
            } else {
                const response = await API.post('/inspections', payload);
                if (response.status === 200 || response.status === 201) {
                    fetchInspectionData();
                    Swal.fire({
                        icon: 'success',
                        title: 'Saved!',
                        text: 'Inspection added successfully.',
                    });
                }
            }

            setShowAddModal(false);
            setEditMode(false);
            setEditIndex(null);
            resetNewInspection();
        } catch (error) {
            console.error('Error saving inspection:', error);
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'Failed to save inspection. Please try again.',
            });
        }
    };

    const handleView = (row) => {
        setSelectedInspection(row);
        setViewDialogVisible(true);
    };
    const badgeStyle = {
        display: 'inline-block',
        minWidth: '100px',
        textAlign: 'center',
        padding: '5px 10px',

    };
    const getStatus = (row) => {
        const status = row.timelineStatus;
        if (status === 'Upcoming') return <span style={badgeStyle} className="badge bg-info text-white">Upcoming</span>;
        if (status === 'Due Now') return <span style={badgeStyle} className="badge bg-warning text-white">Due Now</span>;
        if (status === 'Overdue') return <span style={badgeStyle} className="badge bg-danger text-white">Overdue</span>;
        return <span style={badgeStyle} className="badge bg-secondary text-white">N/A</span>;
    };
    

    const actionBodyTemplate = (rowData) => (
        <div className="d-flex gap-2">
            {/* <Button icon="pi pi-eye" className="p-button-text" onClick={() => handleView(rowData)} /> */}
            <Button icon="pi pi-pencil" className="p-button-text p-button-warning" onClick={() => {
                setEditMode(true);
                setEditIndex(inspections.indexOf(rowData));
                setNewInspection({
                    id: rowData.id,
                    name: rowData.name || '',
                    scheduledDate: rowData.scheduledDate ? new Date(rowData.scheduledDate) : '',
                    dueDate: rowData.dueDate ? new Date(rowData.dueDate) : '',
                    inspectionCategory: rowData.inspectionCategory || '',
                    maskId: rowData.maskId || '',
                    status: rowData.status || '',
                    checklistVersion: rowData.checklistVersion || '',
                    checklistId: rowData.checklist?.id || '',
                    inspectorId: rowData.inspector?.id || '',
                    assignedById: rowData.assignedById || '',
                    locationOneId: rowData.locationOneId || '',
                    locationTwoId: rowData.locationTwoId || '',
                    locationThreeId: rowData.locationThreeId || '',
                    locationFourId: rowData.locationFourId || '',
                    locationFiveId: rowData.locationFiveId || '',
                    locationSixId: rowData.locationSixId || '',
                });

                const filtered = checklistOptions.filter(opt =>
                    opt.category.toLowerCase().includes((rowData.inspectionCategory || '').toLowerCase())
                );
                setFilteredChecklistOptions(filtered);
                setShowAddModal(true);
            }} />
        </div>
    );
    const timelineOptions = [
        { label: 'Upcoming', value: 'Upcoming' },
        { label: 'Due Now', value: 'Due Now' },
        { label: 'Overdue', value: 'Overdue' },
    ];
    return (
        <>
            <div className="d-flex justify-content-end align-items-center mb-2">
                <Button label="CSV Download" icon="pi pi-download" className="me-2" />
                <Button label="Add New" icon="pi pi-plus" onClick={() => {
                    resetNewInspection();
                    setShowAddModal(true);
                }} />
            </div>

            <DataTable
                value={enrichedInspections}
                paginator rows={5}
                filters={filters}
                onFilter={(e) => setFilters(e.filters)}
                filterDisplay="menu"
            >
                <Column field="maskId" header="Inspection ID" sortable />

                <Column
                    field="inspectionCategory"
                    header="Category"
                    filter
                    showFilterMatchModes={false}
                    filterElement={(options) => (
                        <Dropdown
                            value={options.value}
                            options={categoryFilterOptions}
                            onChange={(e) => options.filterCallback(e.value)}
                            placeholder="Select Category"
                            showClear
                        />
                    )}
                />

                <Column
                    field="checklist.name"
                    header="Checklist"

                />

                <Column
                    field="inspector.firstName"
                    header="Inspector"
                    filterField="inspector.firstName"
                    filter
                    showFilterMatchModes={false}
                    filterElement={(options) => (
                        <Dropdown
                            value={options.value}
                            options={inspectorFilterOptions}
                            onChange={(e) => options.filterCallback(e.value)}
                            placeholder="Select Inspector"
                            showClear
                        />
                    )}
                />

                <Column
                    field="scheduledDate"
                    header="Scheduled Date"
                    sortable
                    filter
                    dataType="date"
                    body={(row) => moment(row.scheduledDate).format('DD-MM-YYYY')}
                    filterElement={(options) => (
                        <Calendar
                            value={options.value}
                            onChange={(e) => options.filterCallback(e.value)}
                            dateFormat="dd/mm/yy"
                            placeholder="Select date"
                            className="p-column-filter"
                        />
                    )}
                />

                <Column field="dueDate" header="Due Date" body={(row) => moment(row.dueDate).format('DD-MM-YYYY')} />
                <Column
                    field="timelineStatus"
                    header="Timeline"
                    filterField="timelineStatus"     // <-- Important for custom field
                    body={getStatus}
                    filter
                    showFilterMatchModes={false}
                    filterElement={(options) => (
                        <Dropdown
                            value={options.value}
                            options={timelineOptions}
                            onChange={(e) => options.filterCallback(e.value)}
                            placeholder="Select Status"
                            showClear
                        />
                    )}
                />

                <Column header="Actions" body={actionBodyTemplate} />
            </DataTable>


            <Dialog header="Inspection Details" visible={viewDialogVisible} style={{ width: '50vw' }} modal onHide={() => setViewDialogVisible(false)}>
                {selectedInspection && (
                    <div>
                        <p><strong>ID:</strong> {selectedInspection.id}</p>
                        <p><strong>Category:</strong> {selectedInspection.inspectionCategory}</p>
                        <p><strong>Checklist:</strong> {selectedInspection.checklist}</p>
                        <p><strong>Inspector:</strong> {selectedInspection.inspector}</p>
                        <p><strong>Start:</strong> {selectedInspection.scheduledDate}</p>
                        <p><strong>Due:</strong> {selectedInspection.dueDate}</p>
                    </div>
                )}
            </Dialog>

            <Modal show={showAddModal} onHide={() => {
                setShowAddModal(false);
                setEditMode(false);
                setEditIndex(null);
                resetNewInspection();
            }}>
                <Modal.Header closeButton>
                    <Modal.Title>{editMode ? 'Edit Inspection' : 'Add New Inspection'}</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <Form>
                        <Form.Group className="mb-3">
                            <Form.Label>Location</Form.Label>
                            <AllFilterLocation handleFilter={handleFilter} getLocation={newInspection} />
                        </Form.Group>

                        <Form.Group className="mb-3">
                            <Form.Label>Scheduled Date</Form.Label>
                            <DatePicker
                                selected={newInspection.scheduledDate ? new Date(newInspection.scheduledDate) : null}
                                onChange={(date) => setNewInspection(prev => ({ ...prev, scheduledDate: date }))}
                                dateFormat="dd/MM/yyyy"
                                minDate={new Date()}
                                className="form-control"
                                placeholderText="Select scheduled date"
                            />
                        </Form.Group>

                        <Form.Group className="mb-3">
                            <Form.Label>Due Date</Form.Label>
                            <DatePicker
                                selected={newInspection.dueDate ? new Date(newInspection.dueDate) : null}
                                onChange={(date) => setNewInspection(prev => ({ ...prev, dueDate: date }))}
                                dateFormat="dd/MM/yyyy"
                                minDate={newInspection.scheduledDate || new Date()}
                                className="form-control"
                                placeholderText="Select due date"
                            />
                        </Form.Group>

                        <Form.Group className="mb-3">
                            <Form.Label>Inspection Category</Form.Label>
                            <Select
                                name="inspectionCategory"
                                value={inspectionCategories.find(opt => opt.value === newInspection.inspectionCategory) || null}
                                options={inspectionCategories}
                                onChange={(selected) => {
                                    const selectedCategory = selected?.value || '';
                                    setNewInspection(prev => ({
                                        ...prev,
                                        inspectionCategory: selectedCategory,
                                        checklistId: '',
                                        checklistVersion: '',
                                    }));

                                    const filtered = checklistOptions.filter(opt =>
                                        opt.category.toLowerCase().includes(selectedCategory.toLowerCase())
                                    );
                                    setFilteredChecklistOptions(filtered);
                                }}
                                placeholder="Select Category"
                                isClearable
                            />
                        </Form.Group>

                        <Form.Group className="mb-3">
                            <Form.Label>Checklist</Form.Label>
                            <Select
                                name="checklistId"
                                value={filteredChecklistOptions.find(opt => opt.value === newInspection.checklistId) || null}
                                options={filteredChecklistOptions}
                                onChange={(selected) => {
                                    setNewInspection(prev => ({
                                        ...prev,
                                        checklistId: selected?.value || '',
                                        checklistVersion: selected?.version || '',
                                    }));
                                }}
                                placeholder="Select Checklist"
                                isClearable
                                isDisabled={!newInspection.inspectionCategory}
                            />
                        </Form.Group>

                        <Form.Group className="mb-3">
                            <Form.Label>Inspector</Form.Label>
                            <Select
                                name="inspectorId"
                                value={inspectorOptions.find(opt => opt.value === newInspection.inspectorId) || null}
                                options={inspectorOptions}
                                onChange={(selected) => {
                                    setNewInspection(prev => ({
                                        ...prev,
                                        inspectorId: selected?.value || '',
                                    }));
                                }}
                                placeholder="Select Inspector"
                                isClearable
                            />
                        </Form.Group>
                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <Button label="Cancel" className="p-button-secondary" onClick={() => {
                        setShowAddModal(false);
                        setEditMode(false);
                        setEditIndex(null);
                        resetNewInspection();
                    }} />
                    <Button label={editMode ? 'Update' : 'Save'} className="p-button-primary" onClick={handleSaveInspection} />
                </Modal.Footer>
            </Modal>
        </>
    );
};

export default InspectionSchedule;
