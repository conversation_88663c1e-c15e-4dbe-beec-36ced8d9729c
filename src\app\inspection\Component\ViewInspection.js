import React, { useEffect, useState } from 'react';
import { Row, Col, Card } from 'react-bootstrap';
import moment from 'moment';
import API from '../../services/API';
import { USERS_URL } from '../../constants';

function ViewInspection({ reportData }) {

    const [users, setUsers] = useState([])
    const locationDisplay = [
        reportData.locationOne,
        reportData.locationTwo,
        reportData.locationThree,
        reportData.locationFour,
        reportData.locationFive,
        reportData.locationSix
    ]
        .filter(location => location?.name)
        .map(location => location.name)
        .join(' > ') || "N/A";

    useEffect(() => {
        getAllUsers()
    }, [])

    const getName = (id) => {
        const user = users.find(user => user.id === id);
        return user?.firstName || '';
    };

    const getAllUsers = async () => {
        const response = await API.get(USERS_URL);
        setUsers(response.data);
    };
    const renderChecklistGroups = () => {
        const checklistData = reportData.value || {};
        return Object.values(checklistData)
            .filter(group => group.type === 'checklist-group')
            .map((group, groupIndex) => (
                <Card className="mb-3" key={`group-${groupIndex}`}>
                    <Card.Header>
                        <strong>{group.label}</strong>
                        <span className="float-end">Answer: <strong>{group.groupAnswer || 'N/A'}</strong></span>
                    </Card.Header>
                    <Card.Body>
                        {group.groupAnswer === 'Yes' ? (
                            group.questions?.map((q, index) => (
                                <Row className="mb-3" key={`q-${index}`}>
                                    <Col md={12}>
                                        <p className="obs-title">{q.label}</p>
                                        <p className="obs-content">Selected: {q.selected || 'N/A'}</p>
                                        {q.remarks && <p className="obs-subcontent"><strong>Remarks:</strong> {q.remarks}</p>}
                                        {q.actionToBeTaken && <p className="obs-subcontent"><strong>Action:</strong> {q.actionToBeTaken}</p>}
                                        {q.dueDate && <p className="obs-subcontent"><strong>Due:</strong> {moment(q.dueDate).format('DD-MM-YYYY')}</p>}
                                        {q.assignee && <p className="obs-subcontent"><strong>Assignee:</strong> { getName(q.assignee)}</p>}
                                    </Col>
                                </Row>
                            ))
                        ) : (
                            <Row>
                                <Col md={12}>
                                    <p className="obs-subcontent"><strong>Reason:</strong> {group.reason || 'N/A'}</p>
                                </Col>
                            </Row>
                        )}
                    </Card.Body>
                </Card>
            ));
    };

    return (
        <Row>
            <Col>
                <Card className="mb-4">
                    <Card.Body>
                        <Row className="mb-2">
                            <Col md={4}>
                                <p className="obs-title">Inspection Category</p>
                                <p className="obs-content">{reportData.inspectionCategory || "N/A"}</p>
                            </Col>
                            <Col md={4}>
                                <p className="obs-title">Scheduled Date</p>
                                <p className="obs-content">
                                    {reportData.scheduledDate ? moment(reportData.scheduledDate).format('DD-MM-YYYY') : "N/A"}
                                </p>
                            </Col>
                            <Col md={4}>
                                <p className="obs-title">Due Date</p>
                                <p className="obs-content">
                                    {reportData.dueDate ? moment(reportData.dueDate).format('DD-MM-YYYY') : "N/A"}
                                </p>
                            </Col>
                        </Row>

                        <Row className="mb-2">
                            <Col md={4}>
                                <p className="obs-title">Inspector</p>
                                <p className="obs-content">{reportData.inspector?.firstName || "N/A"}</p>
                            </Col>
                            <Col md={4}>
                                <p className="obs-title">Checklist</p>
                                <p className="obs-content">{reportData.checklist?.name || "N/A"}</p>
                            </Col>
                            <Col md={4}>
                                <p className="obs-title">Checklist Version</p>
                                <p className="obs-content">{reportData.checklistVersion || "N/A"}</p>
                            </Col>
                        </Row>

                        <Row className="mb-2">
                            <Col md={12}>
                                <p className="obs-title">Location</p>
                                <p className="obs-content">{locationDisplay}</p>
                            </Col>
                        </Row>

                        <Row className="mb-2">
                            <Col md={4}>
                                <p className="obs-title">Status</p>
                                <p className="obs-content">{reportData.status || "N/A"}</p>
                            </Col>
                            <Col md={4}>
                                <p className="obs-title">Actual Completion Date</p>
                                <p className="obs-content">
                                    {reportData.actualCompletionDate ? moment(reportData.actualCompletionDate).format('DD-MM-YYYY') : "N/A"}
                                </p>
                            </Col>
                            <Col md={4}>
                                <p className="obs-title">Assigned By</p>
                                <p className="obs-content">{reportData.assignedBy?.firstName || "N/A"}</p>
                            </Col>
                        </Row>

                        <Row className="mb-2">
                            <Col md={4}>
                                <p className="obs-title">Checklist ID</p>
                                <p className="obs-content">{reportData.checklist?.customId || "N/A"}</p>
                            </Col>
                            <Col md={4}>
                                <p className="obs-title">Checklist Category</p>
                                <p className="obs-content">{reportData.checklist?.category || "N/A"}</p>
                            </Col>
                            {/* <Col md={4}>
                                <p className="obs-title">Checklist Status</p>
                                <p className="obs-content">{reportData.checklist?.status || "N/A"}</p>
                            </Col> */}
                        </Row>

                        {renderChecklistGroups()}

                    </Card.Body>
                </Card>
            </Col>
        </Row>
    );
}

export default ViewInspection;
