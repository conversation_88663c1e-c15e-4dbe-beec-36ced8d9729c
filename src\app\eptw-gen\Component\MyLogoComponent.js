import React, { useState, useEffect } from 'react';
import axios from 'axios';
import API from '../../services/API';
import { GET_BLOB } from '../../constants';

function MyLogoComponent({ logo }) {
  // logo is presumably your presignedURL
  const [logoDataURL, setLogoDataURL] = useState('');

  useEffect(() => {
    async function fetchLogoBlob() {
      try {
        // 1. POST the presigned URL to your '/get-blob' endpoint
        const response = await API.post(GET_BLOB, { presignedUrl: logo }, {
          responseType: 'blob', // We want the binary data
        });
        
        // 2. Convert Blob to Data URL (base64)
        const reader = new FileReader();
        reader.onloadend = () => {
          setLogoDataURL(reader.result); 
          // reader.result is something like "data:image/png;base64,iVBORw0K..."
        };
        reader.readAsDataURL(response.data);

      } catch (error) {
        console.error('Error fetching logo blob:', error);
      }
    }

    if (logo) {
      fetchLogoBlob();
    }
  }, [logo]);

  return (
    <img
      src={logoDataURL}
      alt="logo"
      style={{ maxWidth: '125px' }}
    />
  );
}

export default MyLogoComponent;
