import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Modal, Button, Form, Card, Row, Col } from 'react-bootstrap';
import SignatureCanvas from 'react-signature-canvas';
import { SUBMIT_PERMIT_ACTION, FILE_URL, GET_USER_ROLE_BY_MODE, SUBMIT_CHECKLIST_INSPECTION } from '../../constants';
import Swal from 'sweetalert2';
import API from '../../services/API';
import Select from 'react-select';
import moment from 'moment';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { DropzoneArea } from 'material-ui-dropzone';
import ImageComponent from '../../services/FileDownlodS3';

function ConductAction({ show, applicationDetails, showItem, closeModal }) {
  /* ─────────────── state ─────────────── */
  const signRef = useRef();
  const [apiStatus, setApiStatus] = useState('');
  const [signs, setSign] = useState('');
  const [signModal, setSignModal] = useState(false);
  const [comments, setComments] = useState('');
  const [showErrors, setShowErrors] = useState(false);
  const [assessor, setAssessor] = useState([]);
  const [assessorId, setAssessorId] = useState('');
  const [checklistData, setChecklistData] = useState([]);
  const [postActions, setPostActions] = useState([]);
  const [showPostActions, setShowPostActions] = useState(false);

  // error maps => 3 buckets: group, checklist questions, post actions
  const [errorMap, setErrorMap] = useState({
    group: {},     // key = `gIdx-answer | gIdx-reason`
    checklist: {}, // key = `g-q-field`
    post: {},      // key = `idx-field`
  });

  /* ─────────────── crew list ─────────────── */
  const getCrewList = useCallback(async (type) => {
    try {
      const response = await API.post(GET_USER_ROLE_BY_MODE, {
        locationOneId: '',
        locationTwoId: '',
        locationThreeId: '',
        locationFourId: '',
        mode: type,
      });
      if (response.status === 200) {
        const data = response.data.map((item) => ({ label: item.firstName, value: item.id }));
        setAssessor(data);
      }
    } catch (error) {
      console.error('Error fetching crew list:', error);
    }
  }, []);

  /* ─────────────── init ─────────────── */
  useEffect(() => {
    if (applicationDetails?.checklist?.value) {
      setChecklistData(JSON.parse(JSON.stringify(applicationDetails.checklist.value)));
    }
  }, [applicationDetails]);

  useEffect(() => {
    getCrewList('ins_action_owner');
  }, [showItem, getCrewList]);

  /* ─────────────── helpers ─────────────── */
  const handlePostActionChange = (index, field, value) => {
    const updated = [...postActions];
    updated[index][field] = value;
    setPostActions(updated);
  };

  const handlePostFileUpload = async (files, index) => {
    const latestFile = files[files.length - 1];
    const formData = new FormData();
    formData.append('file', latestFile);
    try {
      const response = await API.post(FILE_URL, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
      if (response?.status === 200) {
        const uploadedFile = response.data.files[0].originalname;
        const updated = [...postActions];
        updated[index].uploads.push(uploadedFile);
        setPostActions(updated);
      }
    } catch (error) {
      console.error('Post Action file upload error:', error);
    }
  };

  const handleRemovePostImage = (actionIndex, imageIndex) => {
    const updated = [...postActions];
    updated[actionIndex].uploads.splice(imageIndex, 1);
    setPostActions(updated);
  };

  const addNewPostAction = () => {
    setPostActions([...postActions, { actionToBeTaken: '', dueDate: null, uploads: [], assignee: '' }]);
  };

  const handleFileUpload = async (files, groupIndex, questionIndex) => {
    if (!files.length) return;
    const latestFile = files[files.length - 1];
    const formData = new FormData();
    formData.append('file', latestFile);
    try {
      const response = await API.post(FILE_URL, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
      if (response?.status === 200) {
        const uploaded = response.data.files[0].originalname;
        const updated = [...checklistData];
        const uploads = updated[groupIndex].questions[questionIndex].uploads || [];
        updated[groupIndex].questions[questionIndex].uploads = [...uploads, uploaded];
        setChecklistData(updated);
      }
    } catch (e) {
      console.error('File upload error:', e);
    }
  };

  const handleRemoveImage = (gIdx, qIdx, imgIdx) => {
    const updated = [...checklistData];
    const up = updated[gIdx].questions[qIdx].uploads || [];
    up.splice(imgIdx, 1);
    updated[gIdx].questions[qIdx].uploads = up;
    setChecklistData(updated);
  };

  const handleRemovePostAction = (idx) => {
    const updated = [...postActions];
    updated.splice(idx, 1);
    setPostActions(updated);
  };

  /* ─────────────── validation ─────────────── */
  const runValidation = () => {
    const nErr = { group: {}, checklist: {}, post: {} };

    // ▸ group-level
    checklistData.forEach((g, gIdx) => {
      const answerKey = `${gIdx}-answer`;
      const reasonKey = `${gIdx}-reason`;
      if (!g.groupAnswer) {
        nErr.group[answerKey] = 'Select Yes or No';
        return; // skip further checks on this group
      }
      if (g.groupAnswer === 'No' && !g.reason) {
        nErr.group[reasonKey] = 'Reason required';
      }

      // when YES, validate each question
      if (g.groupAnswer === 'Yes') {
        g.questions.forEach((q, qIdx) => {
          const base = `${gIdx}-${qIdx}`;
          if (!q.selected) {
            nErr.checklist[`${base}-sel`] = 'Select an option';
          } else {
            if ((q.selected === 'No' || q.selected === 'N/A') && !q.remarks) {
              nErr.checklist[`${base}-remarks`] = 'Required';
            }
            if (q.selected === 'No') {
              if (!q.actionToBeTaken) nErr.checklist[`${base}-action`] = 'Required';
              if (!q.dueDate) nErr.checklist[`${base}-due`] = 'Required';
              if (!q.assignee) nErr.checklist[`${base}-own`] = 'Required';
            }
          }
        });
      }
    });

    // ▸ post actions
    postActions.forEach((a, idx) => {
      if (!a.actionToBeTaken) nErr.post[`${idx}-action`] = 'Required';
      if (!a.dueDate) nErr.post[`${idx}-due`] = 'Required';
      if (!a.assignee) nErr.post[`${idx}-own`] = 'Required';
    });

    setErrorMap(nErr);
    const hasErrors =
      Object.keys(nErr.group).length +
      Object.keys(nErr.checklist).length +
      Object.keys(nErr.post).length >
      0;
    return !hasErrors;
  };

  /* ─────────────── submit ─────────────── */
  const handleSubmit = async () => {
    // if (!runValidation()) {
    //   Swal.fire('Validation', 'Please fix the highlighted errors.', 'error');
    //   return;
    // }

    try {
      const payload = {
        checklist: checklistData,
        postActions: postActions,
      };

      const response = await API.patch(SUBMIT_CHECKLIST_INSPECTION(showItem.id), payload);

      if (response.status === 204) {
        Swal.fire("Inspection", "Submitted Successfully", "success");
        closeModal();
      }
    } catch (error) {
      console.error("Error:", error);
    }
  };

  /* ─────────────── render ─────────────── */
  return (
    <Modal
      show={show}
      size="lg"
      onHide={() => closeModal(false)}
      aria-labelledby="conduct-action"
    >
      <Modal.Header closeButton>
        {applicationDetails && (
          <div className="w-100">
            <h4>Inspection</h4>
            <div className="d-flex align-items-center">
              <p className="me-2 mb-0">#{applicationDetails.maskId || ''}</p>
              <p className="card-eptw mb-0">{applicationDetails.status}</p>
            </div>
          </div>
        )}
      </Modal.Header>

      <Modal.Body style={{ maxHeight: '70vh', overflowY: 'auto' }}>


        <Card className="mb-4">
          <Card.Body>
            <Row className="mb-2">
              <Col md={4}>
                <p className="obs-title">Inspection Category</p>
                <p className="obs-content">{applicationDetails.inspectionCategory || "N/A"}</p>
              </Col>
              <Col md={4}>
                <p className="obs-title">Scheduled Date</p>
                <p className="obs-content">
                  {applicationDetails.scheduledDate ? moment(applicationDetails.scheduledDate).format('DD-MM-YYYY') : "N/A"}
                </p>
              </Col>
              <Col md={4}>
                <p className="obs-title">Due Date</p>
                <p className="obs-content">
                  {applicationDetails.dueDate ? moment(applicationDetails.dueDate).format('DD-MM-YYYY') : "N/A"}
                </p>
              </Col>
            </Row>

            <Row className="mb-2">
              <Col md={4}>
                <p className="obs-title">Inspector</p>
                <p className="obs-content">{applicationDetails.inspector?.firstName || "N/A"}</p>
              </Col>
              <Col md={4}>
                <p className="obs-title">Checklist</p>
                <p className="obs-content">{applicationDetails.checklist?.name || "N/A"}</p>
              </Col>
              <Col md={4}>
                <p className="obs-title">Checklist Version</p>
                <p className="obs-content">{applicationDetails.checklistVersion || "N/A"}</p>
              </Col>
            </Row>

            <Row className="mb-2">
              <Col md={12}>
                <p className="obs-title">Location</p>
                <p className="obs-content">
                  {[applicationDetails.locationOne, applicationDetails.locationTwo, applicationDetails.locationThree, applicationDetails.locationFour, applicationDetails.locationFive, applicationDetails.locationSix]
                    .filter(location => location?.name)
                    .map(location => location.name)
                    .join(' > ') || "N/A"}
                </p>
              </Col>

            </Row>

            {/* ───── Checklist Groups ───── */}
            {Array.isArray(checklistData) &&
              checklistData.map((group, gIdx) => {
                if (group.type === 'header') {
                  return (
                    <h5 key={gIdx} className="mb-3 text-dark">
                      {group.label}
                    </h5>
                  );
                }
                if (group.type !== 'checklist-group') return null;

                const ansErr = errorMap.group[`${gIdx}-answer`];
                const reasonErr = errorMap.group[`${gIdx}-reason`];

                return (
                  <Card key={gIdx} className="mb-3">
                    <Card.Body>
                      <div className="d-flex justify-content-between align-items-center mb-3">
                        <h6 className="fw-semibold font-size-14 mb-2">{group.label}</h6>
                        <div className="btn-group" role="group">
                          {['Yes', 'No'].map((opt) => {
                            const id = `g-${gIdx}-${opt}`;
                            return (
                              <React.Fragment key={opt}>
                                <input
                                  type="radio"
                                  className="btn-check"
                                  name={`group-answer-${gIdx}`}
                                  id={id}
                                  autoComplete="off"
                                  checked={group.groupAnswer === opt}
                                  onChange={() => {
                                    const updated = [...checklistData];
                                    updated[gIdx].groupAnswer = opt;
                                    if (opt === 'Yes') {
                                      updated[gIdx].reason = '';
                                    } else {
                                      // clear questions if No selected
                                      updated[gIdx].questions.forEach((q) => {
                                        q.selected = '';
                                        q.remarks = '';
                                        q.actionToBeTaken = '';
                                        q.dueDate = null;
                                        q.assignee = '';
                                        q.uploads = [];
                                      });
                                    }
                                    setChecklistData(updated);
                                  }}
                                />
                                <label
                                  className={`btn btn-outline-${opt === 'Yes' ? 'success' : 'danger'}`}
                                  htmlFor={id}
                                >
                                  {opt}
                                </label>
                              </React.Fragment>
                            );
                          })}
                        </div>
                      </div>
                      {ansErr && <div className="text-danger mb-2 small">{ansErr}</div>}

                      {/* Group Questions when YES */}
                      {group.groupAnswer === 'Yes' && (
                        <>
                          {group.questions.map((q, qIdx) => {
                            const sel = q.selected || '';
                            const base = `${gIdx}-${qIdx}`;
                            return (
                              <div className="mb-4" key={qIdx}>
                                <div className="mb-3">{q.label}</div>
                                {/* radio */}
                                <div className="btn-group mb-2" role="group">
                                  {['Yes', 'No', 'N/A'].map((opt, oIdx) => {
                                    const id = `q-${gIdx}-${qIdx}-${opt}`;
                                    return (
                                      <React.Fragment key={opt}>
                                        <input
                                          type="radio"
                                          className="btn-check"
                                          id={id}
                                          name={`q-${gIdx}-${qIdx}`}
                                          autoComplete="off"
                                          checked={sel === opt}
                                          onChange={() => {
                                            const upd = [...checklistData];
                                            upd[gIdx].questions[qIdx].selected = opt;
                                            if (opt !== 'No') {
                                              upd[gIdx].questions[qIdx].actionToBeTaken = '';
                                              upd[gIdx].questions[qIdx].dueDate = null;
                                              upd[gIdx].questions[qIdx].assignee = '';
                                              upd[gIdx].questions[qIdx].uploads = [];
                                            }
                                            upd[gIdx].questions[qIdx].remarks = '';
                                            setChecklistData(upd);
                                          }}
                                        />
                                        <label
                                          className={`btn btn-outline-${opt === 'Yes' ? 'success' : opt === 'No' ? 'danger' : 'secondary'
                                            }`}
                                          htmlFor={id}
                                        >
                                          {opt}
                                        </label>
                                      </React.Fragment>
                                    );
                                  })}
                                  {errorMap.checklist[`${gIdx}-${qIdx}-sel`] && (
                                    <div className="text-danger small">{errorMap.checklist[`${gIdx}-${qIdx}-sel`]}</div>
                                  )}
                                </div>

                                {/* Remarks mandatory */}
                                {['Yes', 'N/A'].includes(sel) && (
                                  <Form.Group className="mb-2">
                                    <Form.Control
                                      placeholder="Remarks (mandatory)..."
                                      value={q.remarks || ''}
                                      onChange={(e) => {
                                        const upd = [...checklistData];
                                        upd[gIdx].questions[qIdx].remarks = e.target.value;
                                        setChecklistData(upd);
                                      }}
                                      isInvalid={!!errorMap.checklist[`${base}-remarks`]}
                                    />
                                    <Form.Control.Feedback type="invalid">
                                      {errorMap.checklist[`${base}-remarks`]}
                                    </Form.Control.Feedback>
                                  </Form.Group>
                                )}

                                {/* Extra fields when answer is NO */}
                                {sel === 'No' && (
                                  <div className="bg-light p-3 rounded border">
                                    {/* remarks */}
                                    <Form.Group className="mb-2">
                                      <Form.Control
                                        placeholder="Remarks (mandatory)..."
                                        value={q.remarks || ''}
                                        onChange={(e) => {
                                          const upd = [...checklistData];
                                          upd[gIdx].questions[qIdx].remarks = e.target.value;
                                          setChecklistData(upd);
                                        }}
                                        isInvalid={!!errorMap.checklist[`${base}-remarks`]}
                                      />
                                      <Form.Control.Feedback type="invalid">
                                        {errorMap.checklist[`${base}-remarks`]}
                                      </Form.Control.Feedback>
                                    </Form.Group>

                                    {/* action */}
                                    <Form.Group className="mb-2">
                                      <Form.Control
                                        placeholder="Actions to be taken *"
                                        value={q.actionToBeTaken || ''}
                                        onChange={(e) => {
                                          const upd = [...checklistData];
                                          upd[gIdx].questions[qIdx].actionToBeTaken = e.target.value;
                                          setChecklistData(upd);
                                        }}
                                        isInvalid={!!errorMap.checklist[`${base}-action`]}
                                      />
                                      <Form.Control.Feedback type="invalid">
                                        {errorMap.checklist[`${base}-action`]}
                                      </Form.Control.Feedback>
                                    </Form.Group>

                                    {/* upload */}
                                    <Form.Group className="mb-2">
                                      <Form.Label>Upload media (optional)</Form.Label>
                                      <DropzoneArea
                                        dropzoneText={'Drag & drop or click'}
                                        filesLimit={1}
                                        maxFileSize={104857600}
                                        showPreviewsInDropzone={false}
                                        showPreviews={false}
                                        dropzoneClass={'dropzoneText d-flex align-items-center justify-content-center'}
                                        onChange={(files) => handleFileUpload(files, gIdx, qIdx)}
                                      />
                                      <div className="row">
                                      {q.uploads?.length > 0 && (
                                        <div className="col-3 mt-2">
                                          {q.uploads.map((img, i) => (
                                            <div key={i} className="m-2 position-relative">
                                              <ImageComponent fileName={img} size={'100'} name={false} />
                                              <i
                                                className="pi pi-trash"
                                                onClick={() => handleRemoveImage(gIdx, qIdx, i)}
                                                style={{ position: 'absolute', top: 5, right: 5, color: 'red', cursor: 'pointer' }}
                                              />
                                            </div>
                                          ))}
                                        </div>
                                      )}
                                      </div>
                                    </Form.Group>

                                    {/* due date */}
                                    <Form.Group className="mb-2">
                                      <Form.Label>Due Date</Form.Label>
                                      <div
                                        className={
                                          errorMap.checklist[`${base}-due`] ? 'border border-danger rounded' : ''
                                        }
                                      >
                                        <DatePicker
                                          selected={q.dueDate ? new Date(q.dueDate) : null}
                                          onChange={(d) => {
                                            const upd = [...checklistData];
                                            upd[gIdx].questions[qIdx].dueDate = d;
                                            setChecklistData(upd);
                                          }}
                                          minDate={new Date()}
                                          placeholderText="Select due date"
                                          dateFormat="dd-MM-yyyy"
                                          className="form-control"
                                        />
                                      </div>
                                      {errorMap.checklist[`${base}-due`] && (
                                        <div className="text-danger small mt-1">
                                          {errorMap.checklist[`${base}-due`]}
                                        </div>
                                      )}
                                    </Form.Group>

                                    {/* assignee */}
                                    <Form.Group className="mb-2">
                                      <Form.Label>Assign Action to *</Form.Label>
                                      <Select
                                        options={assessor}
                                        value={assessor.find((o) => o.value === q.assignee) || null}
                                        onChange={(s) => {
                                          const upd = [...checklistData];
                                          upd[gIdx].questions[qIdx].assignee = s?.value || '';
                                          setChecklistData(upd);
                                        }}
                                        placeholder="Select"
                                        isClearable
                                        className={errorMap.checklist[`${base}-own`] ? 'is-invalid' : ''}
                                      />
                                      {errorMap.checklist[`${base}-own`] && (
                                        <div className="text-danger small mt-1">
                                          {errorMap.checklist[`${base}-own`]}
                                        </div>
                                      )}
                                    </Form.Group>
                                  </div>
                                )}
                              </div>
                            );
                          })}
                        </>
                      )}

                      {/* reason when NO */}
                      {group.groupAnswer === 'No' && (
                        <>
                          <label className="form-label fw-bold">Please specify the reason:</label>
                          <Form.Control
                            type="text"
                            value={group.reason || ''}
                            onChange={(e) => {
                              const upd = [...checklistData];
                              upd[gIdx].reason = e.target.value;
                              setChecklistData(upd);
                            }}
                            placeholder="Enter reason here..."
                            isInvalid={!!reasonErr}
                          />
                          {reasonErr && (
                            <Form.Control.Feedback type="invalid">{reasonErr}</Form.Control.Feedback>
                          )}
                        </>
                      )}
                    </Card.Body>
                  </Card>
                );
              })}

            {/* ───── Post Actions ───── */}
            {!showPostActions && (
              <Button variant="outline-primary" onClick={() => {
                setShowPostActions(true);
                setPostActions([{ actionToBeTaken: '', dueDate: null, uploads: [], assignee: '' }]);
              }}>
                + Add Post Action
              </Button>
            )}

            {showPostActions && (
              <>
                <h5 className="mt-4 mb-5 fw-bold">Post Inspection Actions</h5>

                {postActions.map((pa, idx) => (
                  <Card key={idx} className="mb-4 position-relative">
                    {/* Trash icon */}
                    <i
                      className="pi pi-trash"
                      style={{ position: 'absolute', top: 10, right: 10, color: 'red', cursor: 'pointer', zIndex: 1 }}
                      onClick={() => handleRemovePostAction(idx)}
                    />

                    {/* Number label */}
                    <div className="position-absolute bg-primary text-white rounded-circle d-flex justify-content-center align-items-center"
                      style={{ width: 30, height: 30, top: -10, left: -15, fontWeight: 'bold' }}>
                      {idx + 1}
                    </div>

                    <Card.Body className="pt-4">
                      {/* Action */}
                      <Form.Group className="mb-3">
                        <Form.Label>Action to be Taken *</Form.Label>
                        <Form.Control
                          placeholder="Enter action"
                          value={pa.actionToBeTaken}
                          onChange={(e) => handlePostActionChange(idx, 'actionToBeTaken', e.target.value)}
                          isInvalid={!!errorMap.post[`${idx}-action`]}
                        />
                        <Form.Control.Feedback type="invalid">
                          {errorMap.post[`${idx}-action`]}
                        </Form.Control.Feedback>
                      </Form.Group>

                      {/* Due Date */}
                      <Form.Group className="mb-3">
                        <Form.Label>Due Date *</Form.Label>
                        <div className={errorMap.post[`${idx}-due`] ? 'border border-danger rounded' : ''}>
                          <DatePicker
                            selected={pa.dueDate}
                            onChange={(d) => handlePostActionChange(idx, 'dueDate', d)}
                            minDate={new Date()}
                            placeholderText="Select due date"
                            dateFormat="dd-MM-yyyy"
                            className="form-control"
                          />
                        </div>
                        {errorMap.post[`${idx}-due`] && (
                          <div className="text-danger small mt-1">{errorMap.post[`${idx}-due`]}</div>
                        )}
                      </Form.Group>

                      {/* Upload Evidence */}
                      <Form.Group className="mb-3">
                        <Form.Label>Upload Evidence</Form.Label>
                        <DropzoneArea
                          dropzoneText="Drag or click to upload"
                          filesLimit={1}
                          maxFileSize={104857600}
                          showPreviewsInDropzone={false}
                          showPreviews={false}
                          onChange={(files) => handlePostFileUpload(files, idx)}
                        />
                        <div className="row">
                        {pa.uploads?.length > 0 && (
                          <div className="col-3 mt-2 flex-wrap">
                            {pa.uploads.map((file, i) => (
                              <div key={i} className="m-2 position-relative">
                                <ImageComponent fileName={file} size="100" name />
                                <i
                                  className="pi pi-trash"
                                  onClick={() => handleRemovePostImage(idx, i)}
                                  style={{ position: 'absolute', top: 5, right: 5, color: 'red', cursor: 'pointer' }}
                                />
                              </div>
                            ))}
                          </div>
                        )}
                        </div>
                      </Form.Group>

                      {/* Assign To */}
                      <Form.Group className="mb-1">
                        <Form.Label>Assign To *</Form.Label>
                        <Select
                          options={assessor}
                          value={assessor.find((o) => o.value === pa.assignee) || null}
                          onChange={(s) => handlePostActionChange(idx, 'assignee', s?.value || '')}
                          placeholder="Select"
                          isClearable
                          className={errorMap.post[`${idx}-own`] ? 'is-invalid' : ''}
                        />
                        {errorMap.post[`${idx}-own`] && (
                          <div className="text-danger small mt-1">{errorMap.post[`${idx}-own`]}</div>
                        )}
                      </Form.Group>
                    </Card.Body>
                  </Card>
                ))}

                <Button variant="outline-primary" onClick={addNewPostAction}>
                  + Add Another Post Action
                </Button>
              </>
            )}

          </Card.Body>
        </Card>
      </Modal.Body>

      <Modal.Footer>
        <Button type="button" variant="primary" onClick={handleSubmit}>
          Submit
        </Button>
      </Modal.Footer>
    </Modal>
  );
}

export default ConductAction;
