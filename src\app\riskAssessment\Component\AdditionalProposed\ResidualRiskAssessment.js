import React from 'react';
import Select from 'react-select'; // Import react-select's Select component
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';

const ResidualRiskAssessment = ({ item, severity, severityData, likelyhood, levelData, tableData, severityTable, likelyhoodTable, riskTable, setSeverityTable, setLikelyhoodTable, setRiskTable, onChangeSeverity, onChangeLikelyhood, cellClassName, cellStyle, rowClassName }) => {

    // Transform the severity and likelihood options for react-select
    const severityOptions = severity.map(option => ({
        value: option.value || option,
        label: option.label || option
    }));

    const likelyhoodOptions = likelyhood.map(option => ({
        value: option.value || option,
        label: option.label || option
    }));

    const findMatrixValue = (idValue, columnValue) => {
        // Map the numeric column value to the respective field name
        const columnMap = {
            1: 'rare',
            2: 'unlikely',
            3: 'possible',
            4: 'likely',
            5: 'almostCertain'
        };
    
        // Get the actual column name based on the columnValue passed
        const columnKey = columnMap[columnValue];
    
        // Find the row that matches the given id value (e.g., "2(D)")
        const row = tableData.find(item => item.id.startsWith(`${idValue}(`));
    
        // If row and column exist, return the corresponding value
        if (row && row[columnKey]) {
            return row[columnKey];
        }
    
        return 0; // Return null if no match is found
    };

    return (
        <div>
            <h5 className='mt-4 fw-bold'>Residual Risk Assessment</h5>
            <p>Expected risk based on the implementation of the identified additional controls</p>

            {/* Severity Section */}
            <div className="row mt-4 mb-3 pb-4" style={{ borderBottom: '1px solid #E0E0E0' }}>
                <div className='col-8'>
                    <h6 className='fw-bold'>Severity</h6>
                    <p>Degree of harm or impact that could result from a hazardous event or situation</p>
                </div>
                <div className='col-4'>
                    <Select
                        options={severityOptions}
                        value={severityOptions.find(option => option.value === item[7].severity)}
                        onChange={(e) => onChangeSeverity(e, 'reassessment')}
                        styles={{ container: (base) => ({ ...base, width: '100%' }) }}
                    />
                </div>
                <h6 className='mt-3 pointer' onClick={() => setSeverityTable(!severityTable)}>
                    Understand Severity Levels <i className={`pi ${severityTable ? 'pi-angle-up' : 'pi-angle-down'} ms-1`}></i>
                </h6>
                {severityTable && (
                    <div className='col-12 mt-3'>
                        <div className="card">
                            <DataTable value={severityData} className="table-bordered">
                                <Column field="id" header="Severity Level"></Column>
                                <Column field="severity" header="Descriptor"></Column>
                                <Column field="personnel" header="Personnel"></Column>
                                <Column field="property" header="Equipment / Property"></Column>
                                <Column field="environment" header="Environment"></Column>
                                <Column field="serviceLoss" header="Service Loss"></Column>
                            </DataTable>
                        </div>
                    </div>
                )}
            </div>

            {/* Likelihood Section */}
            <div className="row mt-4 mb-3 pb-4" style={{ borderBottom: '1px solid #E0E0E0' }}>
                <div className='col-8'>
                    <h6 className='fw-bold'>Likelihood</h6>
                    <p>Frequency with which a hazardous event or situation could happen</p>
                </div>
                <div className='col-4'>
                    <Select
                        options={likelyhoodOptions}
                        value={likelyhoodOptions.find(option => option.value === item[7].likelyhood)}
                        onChange={(e) => onChangeLikelyhood(e, 'reassessment')}
                        styles={{ container: (base) => ({ ...base, width: '100%' }) }}
                    />
                </div>
                <h6 className='mt-3 pointer' onClick={() => setLikelyhoodTable(!likelyhoodTable)}>
                    Understand Likelihood Levels <i className={`pi ${likelyhoodTable ? 'pi-angle-up' : 'pi-angle-down'} ms-1`}></i>
                </h6>
                {likelyhoodTable && (
                    <div className='col-12 mt-3'>
                        <div className="card">
                            <DataTable value={levelData} className="table-bordered" rowClassName={rowClassName}>
                                <Column field="level" header="Level"></Column>
                                <Column field="descriptor" header="Descriptor"></Column>
                                <Column field="detailedDescription" header="Detailed Description"></Column>
                            </DataTable>
                        </div>
                    </div>
                )}
            </div>

            {/* Risk Level Section */}
            <div className="row mt-4 mb-3 pb-4">
                <div className='col-8'>
                    <h6 className='fw-bold'>Risk Level</h6>
                </div>
                <div className='col-4'>
                    <div className={`boxShadow p-2 text-center fw-bold ${cellClassName(item[7].severity * item[7].likelyhood)}`}>
                    {findMatrixValue(item[7].severity ,item[7].likelyhood)}
                    </div>
                </div>
                <h6 className='mt-3 pointer' onClick={() => setRiskTable(!riskTable)}>
                    Understand Risk Levels <i className={`pi ${riskTable ? 'pi-angle-up' : 'pi-angle-down'} ms-1`}></i>
                </h6>
                {riskTable && (
                    <div className='col-12 mt-3'>
                        <div className="card">
                            <DataTable value={tableData} className="table-bordered">
                                <Column field="id" header="" bodyClassName="text-center" headerClassName="risk-th" style={{ width: '150px' }}></Column>
                                <Column field="severity" header="" bodyClassName="text-center" headerClassName="risk-th" style={{ width: '150px' }}></Column>
                                <Column field="rare" header="1 Rare" bodyClassName={(data) => `${cellStyle(data, 'rare')} text-center`} headerClassName="risk-th" style={{ width: '150px' }}></Column>
                                <Column field="unlikely" header="2 Unlikely" bodyClassName={(data) => `${cellStyle(data, 'unlikely')} text-center`} headerClassName="risk-th" style={{ width: '150px' }}></Column>
                                <Column field="possible" header="3 Possible" bodyClassName={(data) => `${cellStyle(data, 'possible')} text-center`} headerClassName="risk-th" style={{ width: '150px' }}></Column>
                                <Column field="likely" header="4 Likely" bodyClassName={(data) => `${cellStyle(data, 'likely')} text-center`} headerClassName="risk-th" style={{ width: '150px' }}></Column>
                                <Column field="almostCertain" header="5 Almost Certain" bodyClassName={(data) => `${cellStyle(data, 'almostCertain')} text-center`} headerClassName="risk-th" style={{ width: '150px' }}></Column>
                            </DataTable>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default ResidualRiskAssessment;
