import React, { useEffect, useState } from 'react';
import { Modal, Button, Row, Col } from 'react-bootstrap';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import API from '../../services/API';
import { INCIDENT_RECORD_WITH_ID, INVESTIGATION_RECORD_WITH_ID } from '../../constants';
import EditRecordModal from './EditRecordModal';
import moment from 'moment';

const InvestigationModal = ({ investigationId, type }) => {
    const [investigationRecords, setInvestigationRecords] = useState([]);
    const [editingRecord, setEditingRecord] = useState(null);
    const [isEditModalVisible, setIsEditModalVisible] = useState(false);

    useEffect(() => {
        if (investigationId) {
            getInvestigationRecords();
        }
    }, [investigationId]);

    const getInvestigationRecords = async () => {
        try {
            const response = await API.get(INCIDENT_RECORD_WITH_ID(investigationId));
            if (response.status === 200) {
                setInvestigationRecords(response.data);
            }
        } catch (error) {
            console.error(`Error fetching investigation records:`, error);
        }
    };

    const handleAddRecord = () => {
        setEditingRecord(null); // No record to edit, so add mode
        setIsEditModalVisible(true);
    };

    const handleEditRecord = (rowData) => {
        setEditingRecord(rowData); // Pass the record to edit
        setIsEditModalVisible(true);
    };

    const handleDeleteRecord = async (rowData) => {
        try {
            await API.delete(`${INVESTIGATION_RECORD_WITH_ID(rowData.id)}`);
            getInvestigationRecords();
        } catch (error) {
            console.error('Error deleting record:', error);
        }
    };

    const actionBodyTemplate = (rowData) => {
        return (
            <div className="table-action d-flex justify-content-center">
                <i
                    className="pi pi-pencil me-3"
                    style={{ cursor: 'pointer', color: 'blue' }}
                    onClick={() => handleEditRecord(rowData)}
                ></i>
                <i
                    className="pi pi-trash"
                    style={{ cursor: 'pointer', color: 'red' }}
                    onClick={() => handleDeleteRecord(rowData)}
                ></i>
            </div>
        );
    };

    const renderTableHeader = () => {
        return (
            <div className="d-flex justify-content-between align-items-center">
                <h4 className="mb-0">Investigation Records</h4>
                {(type.actionType === 'conduct_investigation' || type.actionType === 'reconduct_investigation') &&
                    <Button variant="primary" onClick={handleAddRecord}>
                        Add Record
                    </Button>
                }
            </div>
        );
    };

    const createdBodyTemplate = (row) => {
        return moment(row.created).format('DD-MM-YYYY')
    }
    return (
        <>
            <Row>

                <Col>
                    <DataTable value={investigationRecords} header={renderTableHeader()} className="table-bordered">
                        <Column field="title" header="Title"></Column>
                        <Column field="description" header="Description"></Column>
                        <Column field="created" body={createdBodyTemplate} header="Date of Upload"></Column>
                        <Column field="documents" header="Documents"></Column>
                        <Column body={actionBodyTemplate} header="Action"></Column>
                    </DataTable>
                </Col>
            </Row>

            {isEditModalVisible && (
                <EditRecordModal
                    isVisible={isEditModalVisible}
                    onHide={() => setIsEditModalVisible(false)}
                    record={editingRecord}
                    investigationId={investigationId}
                    refreshRecords={getInvestigationRecords}
                />
            )}
        </>
    );
};

export default InvestigationModal;
