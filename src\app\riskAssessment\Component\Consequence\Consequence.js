import React from 'react';
import Select from 'react-select'; // Import react-select
import { InputText } from 'primereact/inputtext';
import { DropzoneArea } from 'material-ui-dropzone';
import ImageComponent from '../../../services/FileDownlodS3';
import { InputTextarea } from 'primereact/inputtextarea';

const Consequence = ({ con, i, impactOn, onImapactOn, onConseqText, onDeleteConseq, handleTaskFileChange, required, type,handleRemoveImage }) => {
    // Convert impactOn options to react-select format

    return (
        <>
            <div className="row mt-4 ">
                <div className="col-3">
                    <p>Impact on</p>
                    <Select
                        options={impactOn}
                        value={impactOn.find(option => option.value === con.current_type)} // Set current value
                        onChange={(e) => onImapactOn(e.value, i, 'consequence')} // Pass selected value
                        className={`d-flex ${(required === false && con.current_type === '') ? 'borderRed' : ''}`}
                        styles={{
                            container: (provided) => ({
                                ...provided,
                                width: '100%'
                            }),
                            control: (provided) => ({
                                ...provided,
                                width: '100%'
                            })
                        }}
                    />
                </div>
                <div className="col-8">
                    <p>Description</p>
                    <InputTextarea
                        style={{ width: '100%' }}
                        value={con.value}
                        autoResize
                        onChange={(e) => onConseqText(e.target.value, i, 'consequence')}
                        className={`${(required === false && con.value === '') ? 'borderRed' : ''}`}
                        
                    />
                </div>
                <div className="col-1 text-center">
                    <i className="pi pi-trash mb-3" onClick={() => onDeleteConseq(i, 'consequence')}></i>
                </div>
            </div>

            <div className="col-12 mt-3">
                <label htmlFor="imageUploads" className="mb-2">{type === 'hazard' ? 'Where possible and available, upload images to illustrate these points. These images will be used to visually communicate the risks and their potential consequences to relevant personnel.' : 'Upload relevant images to visually represent the identified consequences for this sub-activity. These images may also be utilized in other applicable modules to support effective risk communication'}</label>
                <div className="mb-3">
                    <DropzoneArea
                        acceptedFiles={['image/jpeg', 'image/png']}
                        dropzoneText={"Drag 'n' drop some files here, or click to select files"}
                        filesLimit={5}
                        maxFileSize={104857600}
                        onChange={(files) => handleTaskFileChange(files, i, 'consequence')}
                        showPreviewsInDropzone={false}
                        showPreviews={false}
                        dropzoneClass={'dropzoneText d-flex align-items-center justify-content-center '}
                    />
                </div>
            </div>

            <div className="col-12 mt-3">
                <label htmlFor="uploaded" className="mb-2">Uploaded</label>
            </div>
            <div className="col-12 mt-3 mb-3">
                <div className="row">
                    {con.files && con.files.map((item, m) => (
                        <div key={m} className="col-3  " style={{ position: 'relative' }}>
                            <div className="boxShadow d-flex align-items-center justify-content-center" >
                                <ImageComponent fileName={item} size={'100'} name={true}/>
                                <i
                                className="pi pi-trash"
                                onClick={() => handleRemoveImage(m,i,'consequence')}
                                style={{
                                    position: 'absolute',
                                    top: '5px',
                                    right: '5px',
                                    cursor: 'pointer',
                                    color: 'red',
                                }}
                            />
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </>
    );
};

export default Consequence;
