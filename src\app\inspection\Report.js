import React, { useState, useEffect } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { MultiSelect } from 'primereact/multiselect';
import { <PERSON><PERSON> } from 'primereact/button';
import { Dialog } from 'primereact/dialog';
import moment from 'moment';
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";
// import 'primereact/resources/themes/lara-light-indigo/theme.css';
import 'primeicons/primeicons.css';
import ActionTable from './Component/ActionTable';
import { Modal } from 'react-bootstrap';
import { FilterMatchMode } from 'primereact/api';
import ViewInspection from './Component/ViewInspection';

const InspectionReports = ({ reports }) => {
    // const [reports, setReports] = useState([]);
    const [viewDialogVisible, setViewDialogVisible] = useState(false);
    const [selectedReport, setSelectedReport] = useState(null);
    const [actionModal, setActionModal] = useState(false);
    const [current, setCurrent] = useState(null);
    const [maskId, setMaskId] = useState('');
    const [totalAction, setTotalAction] = useState([]);
    const [filters, setFilters] = useState({
        inspectionCategory: { value: null, matchMode: FilterMatchMode.IN },
        'checklist.name': { value: null, matchMode: FilterMatchMode.CONTAINS },
        'inspector.firstName': { value: null, matchMode: FilterMatchMode.IN },
        location: { value: null, matchMode: FilterMatchMode.IN },
        status: { value: null, matchMode: FilterMatchMode.IN },
    });

    useEffect(() => {
        // const dummy = [
        //     {
        //         id: '240426-1',
        //         category: 'Safety',
        //         checklist: 'Daily Site Audit',
        //         checklistVersion: '1.0',
        //         inspector: 'John Doe',
        //         location: 'KRR > Block A',
        //         originalDueDate: '2025-04-28',
        //         actualCompletionDate: '2025-04-29',
        //         outcome: 'Completed with Actions',
        //         actionStatus: '2/3 Closed'
        //     },
        //     {
        //         id: '240425-1',
        //         category: 'Quality',
        //         checklist: 'QA Inspection',
        //         checklistVersion: '2.1',
        //         inspector: 'Jane Smith',
        //         location: 'KTV > Line 1',
        //         originalDueDate: '2025-04-25',
        //         actualCompletionDate: '2025-04-24',
        //         outcome: 'Completed without Actions',
        //         actionStatus: '0/0'
        //     }
        // ];
        // setReports(dummy);
    }, []);

    const categories = [...new Set(reports.map(r => r.category))].map(c => ({ label: c, value: c }));
    const inspectors = [...new Set(reports.map(r => r.inspector))].map(i => ({ label: i, value: i }));
    const locations = [...new Set(reports.map(r => r.location))].map(l => ({ label: l, value: l }));
    const outcomes = [
        { label: 'Completed without Actions', value: 'Completed without Actions' },
        { label: 'Completed with Actions', value: 'Completed with Actions' },
        { label: 'Archived without Completion', value: 'Archived without Completion' }
    ];

    const handleView = (row) => {
        setSelectedReport(row);
        setViewDialogVisible(true);
    };

    const outcomeTemplate = (row) => {
        if (row.status === 'Completed with Actions') {
            return <span className="badge bg-warning">{row.status}</span>;
        }
        if (row.status === 'Completed without Actions') {
            return <span className="badge bg-success">{row.status}</span>;
        }
        if (row.status === 'Archived without Completion') {
            return <span className="badge bg-danger">{row.status}</span>;
        }
        return row.status;
    };

    const locationBodyTemplate = (row) => {
        const parts = [
            row?.locationOne?.name,
            row?.locationTwo?.name,
            row?.locationThree?.name,
            row?.locationFour?.name
        ].filter(Boolean);
        return parts.join(' > ');
    };
    function groupByDescription(data) {

        const filterData = data.filter(item =>
            item.actionType !== 'review_incident' &&
            item.actionType !== 'conduct_inspection' &&
            item.actionType !== 'approve_investigation'
        );


        const groupedData = [];
        const descriptionMap = {};

        filterData.forEach(item => {
            const { objectId, description, actionType, assignedToId, status, trackId } = item;
            if (!descriptionMap[trackId]) {
                descriptionMap[trackId] = {
                    objectId: objectId,
                    firstActionType: actionType,
                    lastActionType: actionType,
                    actionTypes: [actionType],
                    lastAssignedToId: assignedToId,
                    lastStatus: status,
                    data: []
                };
            } else {
                descriptionMap[trackId].lastActionType = actionType;
                descriptionMap[trackId].actionTypes.push(actionType);
                descriptionMap[trackId].lastAssignedToId = assignedToId;
                descriptionMap[trackId].lastStatus = status;

            }
            descriptionMap[trackId].data.push(item);
        });

        // Update lastActionType, lastAssignedToId, and lastStatus in each group
        for (const description in descriptionMap) {
            const group = descriptionMap[description];
            const lastDataObject = group.data[group.data.length - 1];
            group.lastActionType = lastDataObject.actionType;
            group.lastAssignedToId = lastDataObject.assignedToId;
            group.lastStatus = lastDataObject.status;
            groupedData.push(group);
        }

        return groupedData;
    }
    const handleActionLog = (data, actions) => {

        console.log(actions)
        setActionModal(true)
        setCurrent(data)

        setMaskId(data.maskId)
        setTotalAction(actions)
    }
    const nearBodyTemplate = (rowData) => {
        const totalActionData = groupByDescription(rowData.totalActions)

        console.log(totalActionData)
        console.log(rowData.maskId)

        const totalCompleted = totalActionData.filter(item => item.lastActionType === 'verify_task' && item.lastStatus === 'Completed')

        const color = totalActionData.length === totalCompleted.length ? 'greenBox' : totalCompleted.length === 0 ? 'redBox' : 'orangeBox';

        return <a href="#" onClick={(e) => { e.preventDefault(); handleActionLog(rowData, totalActionData) }} className={color} > {totalCompleted.length} / {totalActionData.length}</a>;
    }
    return (
        <>
            <div className="d-flex justify-content-end align-items-center mb-2">

                <div>
                    <Button label="CSV Download" icon="pi pi-download" className="mr-2" />
                </div>
            </div>

            <DataTable
                value={reports}
                paginator
                rows={5}
                filters={filters}
                onFilter={(e) => setFilters(e.filters)}
                filterDisplay="menu"
            >
                <Column field="maskId" header="Inspection ID"
                    body={(row) => (
                        <div className="maskid" style={{ cursor: 'pointer' }} onClick={() => handleView(row)}>
                            {row.maskId}
                        </div>
                    )}
                />
                <Column
                    field="inspectionCategory"
                    header="Inspection Category"
                    filter
                    showFilterMatchModes={false}
                    filterElement={(options) => (
                        <MultiSelect
                            value={options.value}
                            options={categories}
                            onChange={(e) => options.filterCallback(e.value)}
                            placeholder="Any"
                        />
                    )}
                />
                <Column field="checklist.name" header="Inspection Checklist" filter filterPlaceholder="Search Checklist" />
                <Column field="checklistVersion" header="Checklist Version" />
                <Column
                    field="inspector.firstName"
                    header="Assigned Inspector"
                    filterField="inspector.firstName"
                    filter
                    showFilterMatchModes={false}
                    filterElement={(options) => (
                        <MultiSelect
                            value={options.value}
                            options={inspectors}
                            onChange={(e) => options.filterCallback(e.value)}
                            placeholder="Any"
                        />
                    )}
                />
                <Column field="location" body={locationBodyTemplate} header="Location" filter filterElement={(options) => (
                    <MultiSelect value={options.value} options={locations} onChange={(e) => options.filterCallback(e.value)} placeholder="Any" />
                )} />
                <Column field="originalDueDate" header="Original Due Date" sortable body={(row) => moment(row.originalDueDate).format('DD-MM-YYYY')} />
                <Column field="actualCompletionDate" header="Actual Completion Date" sortable body={(row) => moment(row.actualCompletionDate).format('DD-MM-YYYY')} />
                <Column
                    field="status"
                    header="Inspection Outcome"
                    body={outcomeTemplate}
                    filter
                    showFilterMatchModes={false}
                    filterElement={(options) => (
                        <MultiSelect
                            value={options.value}
                            options={outcomes}
                            onChange={(e) => options.filterCallback(e.value)}
                            placeholder="Any"
                        />
                    )}
                />
                <Column field="actionStatus" header="Status of Actions" body={nearBodyTemplate} />
            </DataTable>

            {/* <Dialog header="Inspection Report Preview" visible={viewDialogVisible} style={{ width: '50vw' }} modal onHide={() => setViewDialogVisible(false)}>
                {selectedReport && (
                    <div>
                        <p><strong>ID:</strong> {selectedReport.id}</p>
                        <p><strong>Checklist:</strong> {selectedReport.checklist}</p>
                        <p><strong>Category:</strong> {selectedReport.category}</p>
                        <p><strong>Location:</strong> {selectedReport.location}</p>
                        <p><strong>Inspector:</strong> {selectedReport.inspector}</p>
                        <p><strong>Checklist Version:</strong> {selectedReport.checklistVersion}</p>
                        <p><strong>Original Due Date:</strong> {selectedReport.originalDueDate}</p>
                        <p><strong>Actual Completion Date:</strong> {selectedReport.actualCompletionDate}</p>
                        <p><strong>Outcome:</strong> {selectedReport.outcome}</p>
                        <p><strong>Status of Actions:</strong> {selectedReport.actionStatus}</p>
                    </div>
                )}
            </Dialog> */}
            <Modal
                show={viewDialogVisible}
                size={'lg'}
                onHide={() => setViewDialogVisible(false)}
                aria-labelledby="example-modal-sizes-title-md"
                id="pdf-content"
            >
                <Modal.Header closeButton>
                    {selectedReport && (
                        <div className="row" style={{ width: '100%' }}>
                            <div className="col-9">
                                <div className="row">

                                    <div className="col-12">
                                        <h4>Inspection</h4>
                                        <div className="d-flex align-items-center">
                                            <p className="me-2">#{selectedReport.maskId || ''} </p>
                                            {/* <p className="card-eptw">{applicationDetails.status} </p> */}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                </Modal.Header>
                <Modal.Body>
                    <ViewInspection reportData={selectedReport} />
                </Modal.Body>

            </Modal>
            {actionModal &&
                <Modal
                    show={actionModal}
                    size="lg"
                    onHide={() => setActionModal(false)}
                    aria-labelledby="example-modal-sizes-title-md"
                >
                    <Modal.Body>

                        <ActionTable id={maskId} actions={totalAction} current={current} />
                    </Modal.Body>
                    <Modal.Footer>
                        <Button
                            variant="light"
                            onClick={() => {
                                setActionModal(false);
                            }}
                        >
                            Close
                        </Button>
                    </Modal.Footer>
                </Modal>
            }
        </>
    );
};

export default InspectionReports;
