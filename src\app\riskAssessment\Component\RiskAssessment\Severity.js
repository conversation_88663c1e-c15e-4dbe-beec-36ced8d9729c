import React, { useState } from 'react';
import Select from 'react-select'; // Import react-select's Select component
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';

const Severity = ({ severity, severityData, required, onChangeSeverity, item }) => {

    const [severityTable, setSeverityTable] = useState(false);

    // Create options for react-select
    const severityOptions = severity.map(option => ({
        value: option.value || option, // Use option.value if available, otherwise use option directly
        label: option.label || option // Use option.label if available, otherwise use option directly
    }));

    return (
        <div className="row mt-4 mb-3 pb-4" style={{ borderBottom: '1px solid #E0E0E0' }}>
            <div className='col-8'>
                <h6 className='fw-bold'>Severity</h6>
                <p className='fst-italic'>Degree of harm or impact that could result from a hazardous event or situation</p>
            </div>
            <div className='col-4'>
                <Select
                    className={`d-flex ${(required === false && item[4].severity === '') ? 'borderRed' : ''}`}
                    options={severityOptions} // Pass the transformed options
                    value={severityOptions.find(option => option.value === item[4].severity)} // Set the selected value
                    onChange={(e) => onChangeSeverity(e, 'assessment')} // Call the change handler
                    styles={{
                        container: (provided) => ({ ...provided, width: '100%' }),
                        control: (provided) => ({ ...provided, width: '100%' })  // Ensure control takes full width too
                    }}
                />
            </div>
            <h6 className='mt-3 pointer' onClick={() => setSeverityTable(!severityTable)}>
                Understand Severity Levels <i className={`pi ${severityTable ? 'pi-angle-up' : 'pi-angle-down'} ms-1`}></i>
            </h6>
            {severityTable && (
                <div className='col-12 mt-3'>
                <div className="card">
                    <DataTable value={severityData} className="table-bordered">
                        <Column field="id" header="Severity Level"></Column>
                        <Column field="severity" header="Descriptor"></Column>
                        <Column field="personnel" header="Personnel"></Column>
                        <Column field="property" header="Equipment / Property"></Column>
                        <Column field="environment" header="Environment"></Column>
                        <Column field="serviceLoss" header="Service Loss"></Column>
                    </DataTable>
                </div>
            </div>
            
            )}
        </div>
    );
};

export default Severity;
