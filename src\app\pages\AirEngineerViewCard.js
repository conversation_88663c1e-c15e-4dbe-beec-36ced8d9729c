import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, But<PERSON>, Form } from 'react-bootstrap';
import Box from '@mui/material/Box';
import Stepper from '@mui/material/Stepper';
import Step from '@mui/material/Step';
import StepLabel from '@mui/material/StepLabel';
import StepContent from '@mui/material/StepContent';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import Switch from "react-switch";
import API from "../services/API";

import Select from "react-select";
import { DropzoneArea } from 'material-ui-dropzone';

import { AIR_APPROVE_REPORTS_SECOND_WITH_ID_URL, AIR_COST_REVIEWER_URL, AIR_COST_REVIEWER_WITH_ID_URL, AIR_DUTY_MANAGER_ESTIMATION_WITH_ID_URL, AIR_ENGINEER_WITH_ID_URL, AIR_FINANCE_URL, AIR_SURVEYOR_WITH_ID_URL, GET_USER_ROLE_BY_MODE } from "../constants";
import IncidentStory from "./IncidentStory";
import cogoToast from "cogo-toast";
import InvoiceGenerator from './InvoiceGenerator';
import TotalCalculator from './TotalCalculator';

import TagInput from "./TagInput";

const AirEngineerViewCard = ({ showModal, setShowModal, data, setData }) => {


    const [files, setFiles] = useState([]);
    const [users, setUsers] = useState([]);
    const [damagedEquipmentDetails, setDamagedEquipmentDetails] = useState('');

    const handleCostDetailsChange = (index, value) => {
        const updatedDetails = [...damagedEquipmentDetails];
        updatedDetails[index].costDetails = value;
        setDamagedEquipmentDetails(updatedDetails);
    };

    const handleOperationalChange = (index, value) => {

        console.log(index, value)
        const updatedDetails = damagedEquipmentDetails.map((item, i) => {
            if (i === index) {
                return { ...item, operational: value };
            }
            return item;
        });
        setDamagedEquipmentDetails(updatedDetails);
    };

    const handleFileChangeForEquipment = (index, files) => {
        const updatedDetails = [...damagedEquipmentDetails];
        updatedDetails[index].files = files;
        setDamagedEquipmentDetails(updatedDetails);
    };


    const handleFileChange = (file) => {
        setFiles(file)

    }

    useEffect(() => { console.log('1'); getIRCostEstimator(); }, [])

    const getIRCostEstimator = async () => {
        const response = await API.post(GET_USER_ROLE_BY_MODE, { locationOneId: data.locationOneId || '', locationTwoId: data.locationTwoId || '', locationThreeId: data.locationThreeId, locationFourId: data.locationFourId, mode: 'ir-cost-estimator' });
        if (response.status === 200) {
            setUsers(response.data)
        }
    }


    const [comments, setComments] = useState('');
    const [operational, setOperational] = useState(false);
    const [labourTime, setLabourTime] = useState(0)
    const [technicalFeedback, setTechnicalFeedback] = useState('')
    const [isEstimate, setIsEstimate] = useState(false)
    const [estimatorId, setEstimatorId] = useState('')

    useEffect(() => {
        if (data) {
            setComments(data.engineerComments?.comments || '')
            setLabourTime(data.engineerComments?.labourTime || '')
            setIsEstimate(data.engineerComments?.isEstimate || '')
            setDamagedEquipmentDetails(data.engineerComments?.damagedEquipmentDetails || [])
        }
    }, [data])
    const handleSubmit = async () => {
        try {
            // Patch Request to AIR_WITH_ID_URL
            const response = await API.patch(AIR_ENGINEER_WITH_ID_URL(data.id, data.actionId), {


                engineerComments: {
                    comments: comments,

                    labourTime: labourTime,
                    isEstimate: isEstimate,
                    damagedEquipmentDetails: damagedEquipmentDetails.map(item => ({
                        category: item.category,
                        number: item.number,
                        damageType: item.damageType,
                        costDetails: item.costDetails,
                        operational: item.operational,
                        // Files handling as per your API's requirement
                    })),

                },
                estimatorId: estimatorId

            }

            );

            // If the patch request fails, no need to proceed further
            if (response.status !== 204) {
                console.error('Failed to patch data. Status:', response.status);
                return;  // or handle this error appropriately
            }

            cogoToast.success(`Action for IR ${data.maskId} Completed`)
            setShowModal(false)
            // Proceed to the next step


        } catch (error) {
            console.error('An error occurred:', error);

        }
    };

    return (
        <>
            {data &&
                <div className="row">

                    <div className="col-md-12">
                        {damagedEquipmentDetails && damagedEquipmentDetails.map((i, index) => (
                            <div key={index} className="col-md-12 mb-3">
                                <div className="row">
                                    <div className="col-sm-4">
                                        <label className="col-form-label p-1">Equipment Type</label>
                                        <p className="white-bg-border-radius-10 form-control-plaintext">
                                            {i.category}
                                        </p>
                                    </div>
                                    <div className="col-sm-4">
                                        <label className="col-form-label p-1">Equipment Number</label>
                                        <p className="white-bg-border-radius-10 form-control-plaintext">
                                            {i.number}
                                        </p>
                                    </div>
                                    <div className="col-sm-4">
                                        <label className="col-form-label p-1">Damage Type</label>
                                        <p className="white-bg-border-radius-10 form-control-plaintext">
                                            {i.damageType}
                                        </p>
                                    </div>
                                </div>
                                {/* Additional fields for Cost Details and Photo Upload */}
                                <div className="row">
                                    <div className="col-sm-6">
                                        <label className="col-form-label">Cost Details</label>
                                        <input
                                            type="text"
                                            className="form-control"
                                            placeholder="Enter cost details"
                                            disabled
                                            value={i.costDetails}
                                            onChange={(e) => handleCostDetailsChange(index, e.target.value)}
                                        />
                                    </div>
                                    <div className="col-sm-6">
                                        <label className="col-form-label">Operational</label>
                                        <Switch
                                            disabled
                                            onChange={(checked) => handleOperationalChange(index, checked)}
                                            checked={i.operational}
                                            className="d-block mt-2"
                                        />
                                    </div>
                                </div>
                                {/* <div className="row">
                                    <div className="col-sm-12">
                                        <label className="col-form-label">Upload Photos</label>
                                        <DropzoneArea
                                            acceptedFiles={['image/jpeg', 'image/png']}
                                            dropzoneText={"Drag and drop photos or click"}
                                            filesLimit={5}
                                            maxFileSize={10485760} // 10MB
                                            onChange={(files) => handleFileChangeForEquipment(index, files)}
                                        />
                                    </div>
                                </div> */}
                            </div>
                        ))}
                    </div>

                    <div className={`col-md-12`}>
                        <Box>



                            <div className='row'>
                                <div className='col'>
                                    <div className='form-group'>
                                        <label>Estimated Labour Time (Hours)</label>
                                        <input disabled className="form-control w-25" type="number" min={0} value={labourTime} onChange={(e) => setLabourTime(e.target.value)} />
                                    </div>
                                </div>
                            </div>


                            <div className='row'>
                                <div className='col'>
                                    <div className='form-group'>
                                        <label>Any other comments</label>
                                        <textarea disabled className="form-control" value={comments} onChange={(e) => setComments(e.target.value)}>

                                        </textarea>
                                    </div>
                                </div>
                            </div>

                            <div className="col-sm-6 mb-2">
                                <label className="col-form-label">Is cost to be estiamted?</label>
                                <Switch
                                    onChange={(checked) => setIsEstimate(checked)}
                                    checked={isEstimate}
                                    className="d-block mt-2"
                                    disabled
                                />
                            </div>

                            <div className='row'>
                                <div className='col'>
                                    <div className='form-group'>
                                        {/* <DropzoneArea
                                            acceptedFiles={[
                                                'application/pdf',
                                                'image/jpeg',
                                                'image/png'

                                            ]}
                                            dropzoneText={"Drag and drop files / documents / pictures"}
                                            filesLimit={5}
                                            maxFileSize={104857600}
                                            onChange={handleFileChange}
                                        /> */}
                                    </div>
                                </div>
                            </div>
                          
                          
                        </Box>
                    </div>


                </div>


            }
        </>
    )
}

export default AirEngineerViewCard;