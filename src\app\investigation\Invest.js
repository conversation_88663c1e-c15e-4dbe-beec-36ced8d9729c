import React, { useEffect, useState, useCallback, useRef } from 'react'
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";
// import 'primereact/resources/themes/lara-light-indigo/theme.css';
import 'primeicons/primeicons.css';
import { DropzoneArea } from 'material-ui-dropzone';
import { Dialog } from 'primereact/dialog';
import { TabView, TabPanel } from 'primereact/tabview';
import { Stepper, Step, StepLabel, Typography } from '@mui/material';
import { Calendar } from 'primereact/calendar';
import Accordion from 'react-bootstrap/Accordion';
import { useAccordionButton } from 'react-bootstrap/AccordionButton';
import AccordionContext from 'react-bootstrap/AccordionContext';
import { RadioButton } from 'primereact/radiobutton';
import { Dropdown } from 'primereact/dropdown';
import { Button } from 'primereact/button';
import { MultiSelect } from 'primereact/multiselect';
import { InputText } from 'primereact/inputtext'
import { Checkbox } from 'primereact/checkbox';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { FilterMatchMode, FilterOperator } from 'primereact/api';
import { InputTextarea } from 'primereact/inputtextarea';
import { Form } from 'react-bootstrap';
import Select from 'react-select';
import { GMS1_URL, INCIDENT, GET_USER_ROLE_BY_MODE, HAZARDS_CATEGOTY, GET_ALL_USER, DROPDOWNS, WORK_ACTIVITIES_URL, FILE_URL, INCIDENT_WITH_ID, INVERSTIGATION_TRIGGER, ADMINDROPDOWNS } from '../constants';
import API from '../services/API';
import AllFilterLocation from './LocationDropDown';
import ImageComponent from '../services/FileDownlodS3';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import Swal from 'sweetalert2';
import { Modal, Nav } from 'react-bootstrap';
import moment from 'moment';
import InvestigationModal from './Component/InvestigationModal';
import ViewRecordModal from './Component/ViewRecordModal';
import JoditEditor from 'jodit-react';
import 'jodit/build/jodit.min.css'; // Import Jodit CSS
import ActionTable from './Component/Actions/ActionTable';
import { useSelector } from 'react-redux';
const customSwal2 = Swal.mixin({
    customClass: {
        confirmButton: 'btn btn-primary',

    },
    buttonsStyling: false
})

const initialFormData = {
    "maskId": "",
    "description": "",
    "images": [],
    "title": "",
    "witnessInvolved": "",
    "isCustomLocation": false,
    "customLocation": "",
    "consequences": {
        "comments": "",
        "consequenceItems": [
            {
                "impactOn": "",
                "description": "",
                "consequenceDescription": [

                ]
            }
        ],
        "status": ""
    },
    "precursors": {
        // "whatLedToTheIncident": "",
        // "unusalWorkingCondition": "",
        "workPerformed": "",
        "tasksProcedures": "",
        "rolesAwareness": "",
        "shortcutsDeviations": "",
        "recentChanges": "",
        "deadlinePressure": "",
        "distractionsInterruptions": "",
        "unusualConditions": "",
        "equipmentUsed": "",
        "equipmentMalfunction": "",
        "recentMaintenance": "",
        "similarIncidents": "",
        "status": ""
    },
    "immediateCause": {
        "initialActions": "",
        "medicalAttentionProvided": "",
        "emergencyServicesCalled": "",
        "areaSecured": "",
        "safetyControlsActivated": "",
        "evacuationInitiated": "",
        "hazardousMaterialsHandled": "",
        "communicationWithAuthorities": "",
        "status": ''
    },
    "hazards": [

    ],
    "hazardousSituationOfTheIncident": "",
    "hazardStatus": "",
    "nearTermControlMeasureStatus": "",
    "preventiveControls": {
        "identifiedPreventiveControls": [
            {
                "order": 0,
                "controlStatement": "",
                "isControlImplemented": false,
                "isEffective": false
            }
        ],
        "unIdentifiedPreventiveControls": [
            {
                "order": 0,
                "controlStatement": "",
                "isControlImplemented": false,
                "isEffective": false
            }
        ],
        "controlIdentified": false,
        "controlNotIdentified": false,
        "wasHazardControllable": false,
        "explainHazardControllable": '',
        "status": ""
    },
    "mitigativeControls": {
        "identifiedMitigativeControls": [
            {
                "order": 0,
                "controlStatement": "",
                "isControlImplemented": false,
                "isEffective": false
            }
        ],
        "unIdentifiedMitigativeControls": [
            {
                "order": 0,
                "controlStatement": "",
                "isControlImplemented": false,
                "isEffective": false
            }
        ],
        "controlIdentified": false,
        "controlNotIdentified": false,
        "wasHazardControllable": false,
        "explainHazardControllable": '',
        "status": ""
    },
    "incidentDate": "",
    "incidentStatus": 'incompleted',
    "status": "",
    "locationOneId": "",
    "locationTwoId": "",
    "locationThreeId": "",
    "locationFourId": "",
    "locationFiveId": "",
    "locationSixId": "",
    "incidentType": "",
    "incidentCircumstance": "",
    "incidentCategory": "",
    "surfaceType": "",
    "surfaceCondition": "",
    "pathways": "",
    "lighting": "",
    "weatherCondition": "",
    "reportedById": "",
    "reviewerId": "",
    "personnelId": "",
    "environmentId": "",
    "propertyId": "",
    "operationId": "",
    "personnelInvolved": "",
    "immediateActionsTaken": "",
}
function Invest({ data, onFilterUpdate }) {
    const user = useSelector((state) => state.login.user);
    const editor = useRef(null);
    const [activeTabIndex, setActiveTabIndex] = useState(0);
    const [lead, setLead] = useState([])
    const [leadShow, setLeadShow] = useState(false)
    const [attachShow, setAttachShow] = useState(false)
    const [show, setShow] = useState(false);
    const [viewShow, setViewShow] = useState(false)
    const [risk, setRisk] = useState([])
    const [hazards, setHazards] = useState([])
    const [reviewer, setReviewer] = useState([])
    const [formData, setFormData] = useState(initialFormData);
    const [filters, setFilters] = useState({
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        'project.name': { value: null, matchMode: FilterMatchMode.IN },
        'category.name': { value: null, matchMode: FilterMatchMode.IN },
        priority: { value: null, matchMode: FilterMatchMode.IN },
        'assignee.firstName': { value: null, matchMode: FilterMatchMode.IN },
        nextdate: { value: null, matchMode: FilterMatchMode.IN },
        'type.label': { value: null, matchMode: FilterMatchMode.IN },
        status: { value: null, matchMode: FilterMatchMode.IN },
        captain: { value: null, matchMode: FilterMatchMode.IN },
        department: { value: null, matchMode: FilterMatchMode.IN },
        'reportedBy.firstName': { value: null, matchMode: FilterMatchMode.IN },
        'reviewer.firstName': { value: null, matchMode: FilterMatchMode.IN },
        'investigator.firstName': { value: null, matchMode: FilterMatchMode.IN },

    });
    const [errors, setErrors] = useState({});
    const [leadAssign, setLeadAssign] = useState(null);
    const [investigationId, setInvestigationId] = useState(null)
    const [requiresInvestigation, setRequiresInvestigation] = useState(true);
    const [actionModal, setActionModal] = useState(false);
    const [current, setCurrent] = useState(null);
    const [maskId, setMaskId] = useState('');
    const [totalAction, setTotalAction] = useState([]);
    const [access, setAccess] = useState(false)

    const [activeStage, setActiveStage] = useState(0); // Default stage for title and date
    const stages = ['Precursors', 'Hazardous Conditions', 'Preventive Controls', 'Incident Details', 'Mitigative Controls', 'Consequences'];


    const impactOn = [
        { 'label': 'Personnel', 'value': 'Personnel' },
        { 'label': 'Property', 'value': 'Property' },
        { 'label': 'Environment', 'value': 'Environment' },
        { 'label': 'Service Loss', 'value': 'Service Loss' },
    ]

    useEffect(() => {
        if (data) {
            getAllIncident();
            getCrewList();
        }

    }, [data])

    const getCrewList = useCallback(async () => {
        try {
            const response = await API.post(GET_USER_ROLE_BY_MODE, {
                locationOneId: "",
                locationTwoId: "",
                locationThreeId: "",
                locationFourId: "",
                mode: 'incident_reviewer'
            });

            if (response.status === 200) {
                const data = response.data.map((item) => ({
                    label: item.firstName,
                    value: item.id
                }));
                setReviewer(data);
            }
        } catch (error) {
            console.error("Error fetching crew list:", error);
        }
    }, [])

    const getAllIncident = () => {
        setRisk(data)
        if (user.length !== 0) {
            setAccess(user.roles.some(item => item.maskId === 'incident_reporter'))
        }
    };


    const [incidentType, setIncidentType] = useState([]);
    const [category, setCategory] = useState([]);
    const [surfaceCondition, setSurfaceCondition] = useState([]);
    const [surfaceType, setSurfaceType] = useState([]);
    const [lighting, setLighting] = useState([]);
    const [weatherCondition, setWeatherCondition] = useState([]);
    const [operation, setOperation] = useState([]);
    const [environment, setEnviromnent] = useState([]);
    const [personnel, setPersonnel] = useState([]);
    const [propertyEquipment, setPropertyEquipment] = useState([]);
    const [workActivity, setWorkActivity] = useState([]);

    const fetchDropdownData = useCallback(async (maskId, setState) => {
        try {
            const uriString = {
                where: {
                    maskId: maskId
                },
                include: [
                    { relation: "dropdownItems" },
                ]
            };

            const url = `${ADMINDROPDOWNS}?filter=${encodeURIComponent(
                JSON.stringify(uriString)
            )}`;

            const response = await API.get(url);

            if (response.status === 200) {
                const data = response.data[0].dropdownItems.map((item) => ({
                    label: item.name,
                    value: item.id
                }));
                setState(data);
            }
        } catch (error) {
            console.error(`Error fetching ${maskId} list:`, error);
        }
    }, []);

    useEffect(() => {
        const fetchData = async () => {
            await getHazardList();
            await getWorkActivity();


            await fetchDropdownData('operations', setOperation);
            await fetchDropdownData('property_equipment', setPropertyEquipment);
            await fetchDropdownData('environment', setEnviromnent);
            await fetchDropdownData('personnel', setPersonnel);
        };

        fetchData();
    }, [fetchDropdownData]);

    const getHazardList = async () => {
        const uriString = { include: ["hazards"] };
        const url = `${HAZARDS_CATEGOTY}?filter=${encodeURIComponent(
            JSON.stringify(uriString)
        )}`;
        const response = await API.get(url);
        if (response.status === 200) {
            const data = response.data.filter((item) => item.name !== 'Hazard-Based')
            setHazards(data);
        }
    }
    const getWorkActivity = async () => {

        const response = await API.get(WORK_ACTIVITIES_URL);
        if (response.status === 200) {
            const data = response.data.map((item) => ({
                label: item.name,
                value: item.id
            }));
            setWorkActivity(data);
        }
    }

    const renderHeader = () => {
        const value = filters['global'] ? filters['global'].value : '';

        return (
            <div className='d-flex justify-content-end'>
                {access &&

                    <Button className="btn btn-primary  mb-3 " onClick={() => { setFormData(initialFormData); setShow(true) }}> Add Incident</Button>
                }
            </div>
        );
    };
    const header = renderHeader()


    const handleFilter = (locationOneId, locationTwoId, locationThreeId, locationFourId, locationFiveId, locationSixId) => {
        setFormData(prev => ({ ...prev, locationOneId: locationOneId, locationTwoId: locationTwoId, locationThreeId: locationThreeId, locationFourId: locationFourId, locationFiveId: locationFiveId, locationSixId: locationSixId }));

    };

    const handleSelectChange = (selectedOption, field) => {
        setFormData({
            ...formData,
            [field]: selectedOption ? selectedOption.value : ''
        });
    };

    const handleChange = (e) => {
        const { name, value } = e.target;
        const nameParts = name.split('.');

        if (nameParts.length === 4) {
            // Handling deeply nested fields like preventiveControls or mitigativeControls
            const [parentField, nestedField, index, subfield] = nameParts;
            const updatedItems = formData[parentField][nestedField].map((item, i) =>
                i === parseInt(index)
                    ? { ...item, [subfield]: value }
                    : item
            );
            setFormData({
                ...formData,
                [parentField]: {
                    ...formData[parentField],
                    [nestedField]: updatedItems
                }
            });
        } else if (nameParts.length === 3) {
            // Handling nested fields like consequences.consequenceItems
            const [parentField, index, subfield] = nameParts;
            const updatedItems = formData[parentField].consequenceItems.map((item, i) =>
                i === parseInt(index)
                    ? { ...item, [subfield]: value }
                    : item
            );
            setFormData({
                ...formData,
                [parentField]: {
                    ...formData[parentField],
                    consequenceItems: updatedItems
                }
            });
        } else if (nameParts.length === 2) {
            // Handling fields like precursors.whatLedToTheIncident
            const [parentField, subfield] = nameParts;
            setFormData({
                ...formData,
                [parentField]: {
                    ...formData[parentField],
                    [subfield]: value
                }
            });
        } else {
            // Handling top-level fields
            setFormData({
                ...formData,
                [name]: value
            });
        }
    };



    const handleAddConsequenceItem = () => {
        setFormData({
            ...formData,
            consequences: {
                ...formData.consequences,
                consequenceItems: [
                    ...formData.consequences.consequenceItems,
                    { impactOn: '', description: '' }
                ]
            }
        });
    };

    const handleAddHazard = (hazard) => {
        // Check if hazard already exists in formData.precursors.hazards
        const isAlreadySelected = formData.hazards.some((existingHazard) => existingHazard.id === hazard.id);

        if (isAlreadySelected) {
            // Remove hazard from formData.hazards
            const updatedHazards = formData.hazards.filter((existingHazard) => existingHazard.id !== hazard.id);
            setFormData({
                ...formData,
                hazards: updatedHazards
            });
        } else {
            // Add hazard to formData.hazards
            setFormData({
                ...formData,
                hazards: [...formData.hazards, hazard]
            });
        }
    };

    const handleDeleteHazard = (index) => {
        const updatedHazards = [...formData.hazards];
        updatedHazards.splice(index, 1); // Remove hazard at the specified index
        setFormData({
            ...formData,
            hazards: updatedHazards
        });
    };
    const handleDeleteConsequenceItem = (index) => {
        const updatedConsequenceItems = formData.consequences.consequenceItems.filter((_, i) => i !== index);
        setFormData({
            ...formData,
            consequences: {
                ...formData.consequences,
                consequenceItems: updatedConsequenceItems
            }
        });
    };
    const handleToggleEffectiveness = (parentType, type, index, value) => {
        const updatedControls = formData[parentType][type].map((control, i) =>
            i === index ? { ...control, isEffective: value } : control
        );

        setFormData({
            ...formData,
            [parentType]: {
                ...formData[parentType],
                [type]: updatedControls
            }
        });
    };

    const handleDeleteUnidentifiedPreventiveControl = (index) => {
        const updatedControls = formData.preventiveControls.unIdentifiedPreventiveControls.filter((_, i) => i !== index);
        setFormData({
            ...formData,
            preventiveControls: {
                ...formData.preventiveControls,
                unIdentifiedPreventiveControls: updatedControls
            }
        });
    };
    const handleAddUnidentifiedPreventiveControl = () => {
        const newOrder = formData.preventiveControls.unIdentifiedPreventiveControls.length + 1;
        setFormData({
            ...formData,
            preventiveControls: {
                ...formData.preventiveControls,
                unIdentifiedPreventiveControls: [
                    ...formData.preventiveControls.unIdentifiedPreventiveControls,
                    { order: newOrder, controlStatement: '', isEffective: true }
                ]
            }
        });
    };
    const handleDeleteIdentifiedPreventiveControl = (index) => {
        const updatedControls = formData.preventiveControls.identifiedPreventiveControls.filter((_, i) => i !== index);
        setFormData({
            ...formData,
            preventiveControls: {
                ...formData.preventiveControls,
                identifiedPreventiveControls: updatedControls
            }
        });
    };
    const handleAddIdentifiedPreventiveControl = () => {
        const newOrder = formData.preventiveControls.identifiedPreventiveControls.length + 1;
        setFormData({
            ...formData,
            preventiveControls: {
                ...formData.preventiveControls,
                identifiedPreventiveControls: [
                    ...formData.preventiveControls.identifiedPreventiveControls,
                    { order: newOrder, controlStatement: '', isEffective: true }
                ]
            }
        });
    };

    const handleAddIdentifiedMitigativeControl = () => {
        const newOrder = formData.mitigativeControls.identifiedMitigativeControls.length + 1;
        setFormData({
            ...formData,
            mitigativeControls: {
                ...formData.mitigativeControls,
                identifiedMitigativeControls: [
                    ...formData.mitigativeControls.identifiedMitigativeControls,
                    { order: newOrder, controlStatement: '', isEffective: true }
                ]
            }
        });
    };

    const handleDeleteIdentifiedMitigativeControl = (index) => {
        const updatedControls = formData.mitigativeControls.identifiedMitigativeControls.filter((_, i) => i !== index);
        setFormData({
            ...formData,
            mitigativeControls: {
                ...formData.mitigativeControls,
                identifiedMitigativeControls: updatedControls
            }
        });
    };

    const handleAddUnidentifiedMitigativeControl = () => {
        const newOrder = formData.mitigativeControls.unIdentifiedMitigativeControls.length + 1;
        setFormData({
            ...formData,
            mitigativeControls: {
                ...formData.mitigativeControls,
                unIdentifiedMitigativeControls: [
                    ...formData.mitigativeControls.unIdentifiedMitigativeControls,
                    { order: newOrder, controlStatement: '', isEffective: true }
                ]
            }
        });
    };

    const handleDeleteUnidentifiedMitigativeControl = (index) => {
        const updatedControls = formData.mitigativeControls.unIdentifiedMitigativeControls.filter((_, i) => i !== index);
        setFormData({
            ...formData,
            mitigativeControls: {
                ...formData.mitigativeControls,
                unIdentifiedMitigativeControls: updatedControls
            }
        });
    };
    const handleMainImageUpload = useCallback(async (files) => {
        if (files.length > 0) {
            const latestFile = files[files.length - 1];
            const formData1 = new FormData();
            formData1.append('file', latestFile);
            try {
                const response = await API.post(FILE_URL, formData1, {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                    },
                });

                if (response && response.status === 200) {
                    setFormData(prevState => ({
                        ...prevState,
                        images: [...(prevState.images || []), response.data.files[0].originalname]
                    }));
                }
            } catch (error) {
                console.error("File upload error: ", error);
            }
        }
    }, []);
    const handleRemoveMainImage = (index) => {
        const updatedImages = formData.images.filter((_, i) => i !== index);
        setFormData({
            ...formData,
            images: updatedImages
        });
    };
    const handleDateChange = (date) => {
        setFormData({
            ...formData,
            incidentDate: date, // Update the dueDate in formData
        });
    };


    const handleClose = () => {
        setShow(false);
        setActiveStage(0); // Reset stage when closing
    };

    const handleStageClick = (index) => {
        setActiveStage(index);
    };

    const validateForm1 = () => {
        let errors = {};
        let isValid = true;
        if (activeStage === 0) {
            if (!formData.title) {
                isValid = false;
                errors.title = "Incident Title is required";
            }
            if (!formData.incidentDate) {
                isValid = false;
                errors.incidentDate = "Incident Date is required";
            }
            if (!formData.reviewerId) {
                isValid = false;
                errors.reviewerId = "Reviewer is required";
            }
        }
        return { isValid, errors };
    }
    const handleSaveIncident = async (e) => {
        e.preventDefault();

        // Validate the form before saving
        const { isValid, errors } = validateForm1();

        if (!isValid) {
            setErrors(errors);
            Swal.fire({
                icon: 'error',
                title: 'Validation Error',
                text: 'Please fill out all required fields before saving.',
            });
            return;
        }

        try {

            const response = await API.post(INCIDENT, formData);

            if (response.status === 200) {
                // const createdIncidentId = response.data.id;
                // const allIncidentsResponse = await getAllIncident();

                // if (allIncidentsResponse && allIncidentsResponse.status === 200) {
                //     const allIncidents = allIncidentsResponse.data;

                //     console.log('allIncidents:', allIncidents);
                //     const createdIncident = allIncidents.find(incident => incident.id === createdIncidentId);

                //     if (createdIncident) {

                Swal.fire("Incident", "Submitted Successfully", "success");
                setShow(false)
                // getAllIncident();
                //     setFormData(createdIncident);
                //     setActiveStage(1);
                // } else {
                //     // Fallback to using the response data if the incident isn't found
                //     setFormData(response.data);
                //     setActiveStage(1);
                // }

                // Move to the first stage (Precursors)

                // } else {
                //     // Handle cases where the status isn't 200 or data isn't as expected
                //     Swal.fire("Error Fetching Incidents", "Unable to fetch the list of incidents. Please check the API response.", "error");
                // }
            } else {
                // Handle non-200 status codes
                Swal.fire("Error Creating Incident", `Unexpected status code: ${response.status}`, "error");
            }
        } catch (error) {
            console.error('Error saving incident:', error);
            Swal.fire({
                icon: 'error',
                title: 'Save Error',
                text: 'An error occurred while saving the incident. Please try again.',
            });
        }
    };

    const handleSaveProgress = async () => {
        let dataToSend = {};
        let stageKey = '';

        switch (activeStage) {
            case 1: // Precursors
                dataToSend = {
                    incidentTypeId: formData.incidentTypeId,
                    incidentCategoryId: formData.incidentCategoryId,
                    surfaceTypeId: formData.surfaceTypeId,
                    surfaceConditionId: formData.surfaceConditionId,
                    lightingId: formData.lightingId,
                    weatherConditionId: formData.weatherConditionId,
                    workActivityId: formData.workActivityId,
                    precursors: {
                        ...formData.precursors,
                        status: 'incompleted'
                    }
                };
                stageKey = 'precursors';
                break;
            case 2: // Hazardous Conditions
                dataToSend = {
                    hazards: formData.hazards,
                    hazardStatus: 'incompleted'

                };
                stageKey = 'hazardousConditions';
                break;
            case 3: // Preventive Controls
                dataToSend = {
                    preventiveControls: {
                        ...formData.preventiveControls,
                        status: 'incompleted'
                    }
                };
                stageKey = 'preventiveControls';
                break;
            case 4: // Incident Details
                dataToSend = {
                    incidentStatus: 'incompleted',
                    images: formData.images,
                    description: formData.description
                };
                stageKey = 'incidentDetails';
                break;
            case 5: // Mitigative Controls
                dataToSend = {
                    mitigativeControls: {
                        ...formData.mitigativeControls,
                        status: 'incompleted'
                    }
                };
                stageKey = 'mitigativeControls';
                break;
            case 6: // Consequences
                dataToSend = {
                    consequences: {
                        ...formData.consequences,
                        status: 'incompleted'
                    }
                };
                stageKey = 'consequences';
                break;
            default:
                return;
        }

        try {
            const response = await API.patch(INCIDENT_WITH_ID(formData.id), dataToSend);
            setFormData(prevFormData => ({
                ...prevFormData,
                ...dataToSend
            })); // Update formData with the latest data
            setStageStatus({ ...stageStatus, [stageKey]: 'incomplete' }); // Update stage status
            Swal.fire({
                toast: true,
                icon: 'success',
                title: 'Saved as Draft',
                text: `${stageKey.replace(/([A-Z])/g, ' $1').trim()} saved as draft.`,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
            });
        } catch (error) {
            console.error('Error saving progress:', error);
        }
    };
    const handleSaveFinalize = async () => {
        const { isValid, errors } = validateForm();
        if (!isValid) {
            setErrors(errors);
            Swal.fire({
                icon: 'error',
                title: 'Validation Error',
                text: 'Please fill out all required fields before finalizing.',
            });
            return;
        }
        let dataToSend = {};
        let stageKey = '';

        switch (activeStage) {
            case 1: // Precursors
                dataToSend = {
                    incidentTypeId: formData.incidentTypeId,
                    incidentCategoryId: formData.incidentCategoryId,
                    surfaceTypeId: formData.surfaceTypeId,
                    surfaceConditionId: formData.surfaceConditionId,
                    lightingId: formData.lightingId,
                    weatherConditionId: formData.weatherConditionId,
                    workActivityId: formData.workActivityId,
                    precursors: {
                        ...formData.precursors,
                        status: 'completed'
                    }
                };
                stageKey = 'precursors';
                break;
            case 2: // Hazardous Conditions
                dataToSend = {
                    hazards: formData.hazards,
                    hazardStatus: 'completed'

                };
                stageKey = 'hazardousConditions';
                break;
            case 3: // Preventive Controls
                dataToSend = {
                    preventiveControls: {
                        ...formData.preventiveControls,
                        status: 'completed'
                    }
                };
                stageKey = 'preventiveControls';
                break;
            case 4: // Incident Details
                dataToSend = {
                    incidentStatus: 'completed',
                    images: formData.images,
                    description: formData.description
                };
                stageKey = 'incidentDetails';
                break;
            case 5: // Mitigative Controls
                dataToSend = {
                    mitigativeControls: {
                        ...formData.mitigativeControls,
                        status: 'completed'
                    }
                };
                stageKey = 'mitigativeControls';
                break;
            case 6: // Consequences
                dataToSend = {
                    consequences: {
                        ...formData.consequences,
                        status: 'completed'
                    }
                };
                stageKey = 'consequences';
                break;
            default:
                return;
        }


        try {
            const response = await API.patch(INCIDENT_WITH_ID(formData.id), dataToSend);
            setFormData(prevFormData => ({
                ...prevFormData,
                ...dataToSend
            })); // Update formData with the latest data
            setStageStatus({ ...stageStatus, [stageKey]: 'completed' }); // Update stage status

            Swal.fire({
                toast: true,
                icon: 'success',
                title: 'Finalized',
                text: `${stageKey.replace(/([A-Z])/g, ' $1').trim()} finalized successfully.`,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
            });
        } catch (error) {
            console.error('Error finalizing:', error);
        }
    };

    useEffect(() => {
        setStageStatus({
            precursors: formData.precursors.status || '',
            hazardousConditions: formData.hazardStatus || '',
            preventiveControls: formData.preventiveControls.status || '',
            incidentDetails: formData.incidentStatus || '',
            mitigativeControls: formData.mitigativeControls.status || '',
            consequences: formData.consequences.status || '',
        });
    }, [formData]);

    const [stageStatus, setStageStatus] = useState({
        precursors: '',
        hazardousConditions: '',
        preventiveControls: '',
        incidentDetails: '',
        mitigativeControls: '',
        consequences: ''
    });

    const validateForm = () => {
        let errors = {};
        let isValid = true;



        if (activeStage === 6) {
            formData.consequences.consequenceItems.forEach((item, index) => {
                if (!item.impactOn) {
                    isValid = false;
                    errors[`consequences.${index}.impactOn`] = "Impact On is required";
                }
                if (!item.description) {
                    isValid = false;
                    errors[`consequences.${index}.description`] = "Description is required";
                }
            });
        }

        return { isValid, errors };
    };
    const getStatusClass = (status) => {


        if (status === 'completed') return 'completed-stage'; // Green color
        if (status === 'incompleted') return 'incomplete-stage'; // Yellow color
        return 'not-started-stage'; // Default (no color or gray color)
    };
    const stageKeyMap = {
        'Precursors': 'precursors',
        'Hazardous Conditions': 'hazardousConditions',
        'Preventive Controls': 'preventiveControls',
        'Incident Details': 'incidentDetails',
        'Mitigative Controls': 'mitigativeControls',
        'Consequences': 'consequences'
    };

    const editIncident = (row) => {
        setFormData(row)
        setActiveStage(1);
        setShow(true)
    }

    const triggerInverstication = async (row) => {
        setInvestigationId(row.id)

        try {
            const response = await API.post(GET_USER_ROLE_BY_MODE, {
                locationOneId: "",
                locationTwoId: "",
                locationThreeId: "",
                locationFourId: "",
                mode: 'incident_lead_investigator'
            });

            if (response.status === 200) {
                const data = response.data.map((item) => ({
                    label: item.firstName,
                    value: item.id
                }));
                setLead(data);
                setLeadShow(true)
            }
        } catch (error) {
            console.error("Error fetching crew list:", error);
        }


    }


    const actionBodyTemplate = (row) => {

        return (
            <div className="table-action d-flex">
                {/* <i className="pi pi-eye" onClick={() => viewRisk(row)}></i> */}

                {row.status === 'Reviewed' &&
                    <p className=" me-2 text-center text-secondary text-decoration-underline" style={{ cursor: 'pointer' }} onClick={() => triggerInverstication(row)}>Assign Investigator</p>
                }



            </div>
        );
    }

    const dateBodyTemplate = (row) => {
        return moment(row.incidentDate).format('DD-MM-YYYY')
    }

    const assignInverstigator = async () => {

        console.log(leadAssign)
        console.log(requiresInvestigation)

        await API.patch(INVERSTIGATION_TRIGGER(investigationId), { "isInvestigationRequired": requiresInvestigation, "investigatorId": leadAssign });

        setLeadShow(false)

    }
    const [record, setRecord] = useState([])
    const openViewModal = (row) => {
        setRecord(row)
        setViewShow(true)
    }
    const maskBodyTemplate = (row) => {
        return <div className='maskid' onClick={() => openViewModal(row)}>{row.maskId}</div>
    }

    const locationBodyTemplate = (row) => {
        if (row.isCustomLocation) {
            return row.customLocation;
        } else {
            return (
                <>

                    {row?.locationOne?.name && (
                        <>
                            {row.locationOne.name}
                            {row.locationTwo?.name && ' > '}
                        </>
                    )}
                    {row?.locationTwo?.name && (
                        <>
                            {row.locationTwo.name}
                            {row.locationThree?.name && ' > '}
                        </>
                    )}
                    {row?.locationThree?.name && (
                        <>
                            {row.locationThree.name}
                            {row.locationFour?.name && ' > '}
                        </>
                    )}
                    {row?.locationFour?.name && row.locationFour.name}
                </>
            );
        }
    };


    const handleCheckboxChange = (e) => {
        const { checked } = e.target;
        setFormData((prevData) => ({
            ...prevData,
            isCustomLocation: checked
        }));
    }
    const handleFilterChange = (filteredData) => {


        // Update the count of filtered data in the parent component
        if (onFilterUpdate) {
            onFilterUpdate(filteredData.length);
        }
    };
    function groupByDescription(data) {

        const filterData = data.filter(item =>
            item.actionType !== 'review_incident' &&
            item.actionType !== 'conduct_investigation' &&
            item.actionType !== 'approve_investigation'
        );


        const groupedData = [];
        const descriptionMap = {};

        filterData.forEach(item => {
            const { objectId, description, actionType, assignedToId, status, trackId } = item;
            if (!descriptionMap[trackId]) {
                descriptionMap[trackId] = {
                    objectId: objectId,
                    firstActionType: actionType,
                    lastActionType: actionType,
                    actionTypes: [actionType],
                    lastAssignedToId: assignedToId,
                    lastStatus: status,
                    data: []
                };
            } else {
                descriptionMap[trackId].lastActionType = actionType;
                descriptionMap[trackId].actionTypes.push(actionType);
                descriptionMap[trackId].lastAssignedToId = assignedToId;
                descriptionMap[trackId].lastStatus = status;

            }
            descriptionMap[trackId].data.push(item);
        });

        // Update lastActionType, lastAssignedToId, and lastStatus in each group
        for (const description in descriptionMap) {
            const group = descriptionMap[description];
            const lastDataObject = group.data[group.data.length - 1];
            group.lastActionType = lastDataObject.actionType;
            group.lastAssignedToId = lastDataObject.assignedToId;
            group.lastStatus = lastDataObject.status;
            groupedData.push(group);
        }

        return groupedData;
    }

    const handleActionLog = (data, actions) => {

        console.log(actions)
        setActionModal(true)
        setCurrent(data)

        setMaskId(data.maskId)
        setTotalAction(actions)
    }
    const nearBodyTemplate = (rowData) => {
        const totalActionData = groupByDescription(rowData.totalActions)

        console.log(totalActionData)
        console.log(rowData.maskId)

        const totalCompleted = totalActionData.filter(item => item.lastActionType === 'verify_task' && item.lastStatus === 'Completed')

        const color = totalActionData.length === totalCompleted.length ? 'greenBox' : totalCompleted.length === 0 ? 'redBox' : 'orangeBox';

        return <a href="#" onClick={(e) => { e.preventDefault(); handleActionLog(rowData, totalActionData) }} className={color} > {totalCompleted.length} / {totalActionData.length}</a>;
    }


    // Extract distinct values for each field
    const reporterOptions = [
        ...new Set(risk?.map(item => item.reportedBy?.firstName).filter(Boolean))
    ].map(name => ({ label: name, value: name }));

    const reviewerOptions = [
        ...new Set(risk?.map(item => item.reviewer?.firstName).filter(Boolean))
    ].map(name => ({ label: name, value: name }));

    const investigatorOptions = [
        ...new Set(risk?.map(item => item.investigator?.firstName).filter(Boolean))
    ].map(name => ({ label: name, value: name }));

    const reviewerFilterTemplate = (options) => {
        return (
            <MultiSelect
                value={options.value}
                options={reviewerOptions}
                onChange={(e) => options.filterCallback(e.value)}
                placeholder="Select Reviewer(s)"
                display="chip"
                className="p-column-filter"
                style={{ minWidth: '12rem' }}
            />
        );
    };

    const investigatorFilterTemplate = (options) => {
        return (
            <MultiSelect
                value={options.value}
                options={investigatorOptions}
                onChange={(e) => options.filterCallback(e.value)}
                placeholder="Select Investigator(s)"
                display="chip"
                className="p-column-filter"
                style={{ minWidth: '12rem' }}
            />
        );
    };
    const reporterFilterTemplate = (options) => {
        return (
            <MultiSelect
                value={options.value}                 // current selected values
                options={reporterOptions}             // all possible reporters
                onChange={(e) => options.filterCallback(e.value)}
                placeholder="Select Reporter(s)"
                display="chip"                        // optional, to show selected names as chips
                className="p-column-filter"
                style={{ minWidth: '12rem' }}
            />
        );
    };
    // Example: "Open", "Closed", "Resolved", ...
    const statusOptions = [
        ...new Set(risk?.map(item => item.status).filter(Boolean))
    ].map(status => ({ label: status, value: status }));

    const statusFilterTemplate = (options) => {
        return (
            <MultiSelect
                value={options.value}                 // current selected statuses from filters
                options={statusOptions}               // unique statuses from your data
                onChange={(e) => options.filterCallback(e.value)}
                placeholder="Select Status(es)"
                display="chip"                        // shows selected items as 'chips'
                className="p-column-filter"
                style={{ minWidth: '12rem' }}
            />
        );
    };

    return (
        <>
            <div>
                <div className="row  ">
                    <div className="col-12">

                        <div className="card">
                            <div className="card-body p-0">
                                <div className="row">
                                    <div className="col-12">
                                        <div>
                                            <>
                                                <DataTable value={risk} paginator onValueChange={handleFilterChange} rows={10} header={header} filters={filters} onFilter={(e) => setFilters(e.filters)} paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                                                    rowsPerPageOptions={[10, 25, 50]}
                                                    emptyMessage="No Data found." tableStyle={{ minWidth: '50rem' }}>
                                                    <Column field="maskId" header="Incident ID" body={maskBodyTemplate} sortable ></Column>
                                                    <Column
                                                        field="status"
                                                        header="Status"
                                                        filter
                                                        showFilterMatchModes={false}
                                                        filterMatchMode="in"
                                                        filterElement={statusFilterTemplate}
                                                    />

                                                    <Column field="title" header="Incident Title" sortable ></Column>
                                                    <Column field="" header="Severity" sortable ></Column>
                                                    <Column
                                                        field="incidentDate"
                                                        header="Date of Incident"
                                                        body={dateBodyTemplate}   // Your custom format using moment
                                                        sortable
                                                        dataType="date"          // Ensures proper date sorting
                                                    />

                                                    <Column field="" header="Location" body={locationBodyTemplate} ></Column>
                                                    <Column
                                                        field="reportedBy.firstName"
                                                        header="Reporter"
                                                        filter
                                                        showFilterMatchModes={false}
                                                        filterMatchMode="in"            // important for multi-value matching
                                                        filterField="reportedBy.firstName"  // ensure field is correct
                                                        filterElement={reporterFilterTemplate}
                                                    />

                                                    <Column
                                                        field="reviewer.firstName"
                                                        header="Reviewer"
                                                        filter
                                                        showFilterMatchModes={false}
                                                        filterMatchMode="in"
                                                        filterField="reviewer.firstName"
                                                        filterElement={reviewerFilterTemplate}
                                                    />

                                                    <Column
                                                        field="investigator.firstName"
                                                        header="Investigator"
                                                        filter
                                                        showFilterMatchModes={false}
                                                        filterMatchMode="in"
                                                        filterField="investigator.firstName"
                                                        filterElement={investigatorFilterTemplate}
                                                    />
                                                    <Column field="" header="Near-term Control Measure Status" body={nearBodyTemplate}  ></Column>
                                                    <Column header="Action" body={actionBodyTemplate} ></Column>
                                                </DataTable>
                                            </>

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>


            {show &&
                <Modal show={show} onHide={handleClose} size="lg">
                    <Modal.Header closeButton>
                        <Modal.Title>{activeStage === 0 ? 'Add Incident' : 'Data Collection for Incident Investigation'}</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                        {activeStage === 0 && (
                            <Form>
                                {/* Incident Title */}
                                <div className="col-12 mb-2">
                                    <div className="d-flex flex-column col-12">
                                        <label htmlFor="incidentTitle" className="mb-2">Incident Title</label>
                                        <InputText
                                            className="d-flex"
                                            id="incidentTitle"
                                            name="title"
                                            placeholder=''
                                            value={formData.title}
                                            onChange={handleChange}
                                        />
                                        {errors.title && <small className="p-error">{errors.title}</small>}
                                    </div>
                                </div>
                                <div className='col-4 mb-2'>
                                    <Form.Group controlId="Incident Date" className="mb-3">
                                        <Form.Label>Incdient Date & Time </Form.Label>

                                        <DatePicker
                                            selected={formData.incidentDate}
                                            onChange={handleDateChange}
                                            name="incidentDate"
                                            showTimeSelect
                                            dateFormat="dd-MM-yyyy HH:mm"
                                            className="form-control"
                                            maxDate={new Date()}
                                            timeIntervals={15} // Set time intervals to 15 minutes
                                            timeCaption="Time"
                                        />

                                        {errors.incidentDate && <small className="p-error">{errors.incidentDate}</small>}

                                    </Form.Group>
                                </div>
                                <div className='row mb-4'>
                                    <div className="col-12 mb-2">
                                        <div className="d-flex flex-column col-12">
                                            <label htmlFor="incidentImages" className="fw-bold mb-2">Location</label>



                                            {formData.isCustomLocation === false &&
                                                <AllFilterLocation handleFilter={handleFilter} getLocation={formData} />
                                            }

                                            <div className="form-check mb-3">
                                                <input
                                                    type="checkbox"
                                                    className="form-check-input"
                                                    id="customLocationCheckbox"
                                                    checked={formData.isCustomLocation}
                                                    onChange={handleCheckboxChange}
                                                />
                                                <label className="form-check-label" htmlFor="customLocationCheckbox">
                                                    Use Custom Location
                                                </label>
                                            </div>

                                            {formData.isCustomLocation && (
                                                <div className="col-12">
                                                    <label htmlFor="customLocation" className="mb-2">Custom Location</label>
                                                    <InputText
                                                        className="d-flex"
                                                        id="customLocation"
                                                        name="customLocation"
                                                        style={{ width: '100%' }}
                                                        placeholder='Enter custom location and limited to 75 letters'
                                                        maxLength={'75'}
                                                        value={formData.customLocation || ''}
                                                        onChange={handleChange}
                                                    />
                                                    {errors.customLocation && <small className="p-error">{errors.customLocation}</small>}
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </div>
                                <div className="col-12 mb-2">
                                    <label htmlFor="incidentDescription" className="mb-2">Description of the incident</label>


                                    <InputTextarea
                                        className="d-flex"
                                        id="incidentDescription"
                                        name="description"
                                        style={{ width: '100%' }}
                                        placeholder='Provide a brief factual description of the incident. Do not include reasons or assumptions about what caused it.'
                                        rows={3}
                                        value={formData.description}
                                        onChange={handleChange}
                                        autoResize
                                    />
                                    {errors.description && <small className="p-error">{errors.description}</small>}
                                </div>
                                <div className="col-12 mt-3 mb-2">
                                    <label htmlFor="incidentImages" className="mb-2">Evidences</label>
                                    <div className="mb-3">
                                        <DropzoneArea

                                            dropzoneText={"Attach supporting images / documents as necessary to provide more information about the incident at this point of time"}
                                            filesLimit={5}
                                            maxFileSize={104857600}
                                            showPreviewsInDropzone={false}
                                            showPreviews={false}
                                            dropzoneClass={'dropzoneText d-flex align-items-center justify-content-center'}
                                            onChange={(files) => handleMainImageUpload(files)}
                                        />
                                    </div>
                                </div>
                                <div className='col-12 mt-3 mb-4'>
                                    <label htmlFor="username" className='mb-2'>Uploaded</label>
                                    <div className="image-preview mt-3 ">
                                        {formData.images && formData.images.length > 0 && (
                                            <div className="row">
                                                {formData.images.map((item, index) => (
                                                    <div key={index} className="col-3  " style={{ position: 'relative' }}>
                                                        <div className="boxShadow d-flex align-items-center" >
                                                            <ImageComponent fileName={item} size={'100'} name={true} />
                                                            <i
                                                                className="pi pi-trash"
                                                                onClick={() => handleRemoveMainImage(index)}
                                                                style={{
                                                                    position: 'absolute',
                                                                    top: '5px',
                                                                    right: '5px',
                                                                    cursor: 'pointer',
                                                                    color: 'red',
                                                                }}
                                                            />
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        )}
                                    </div>
                                </div>

                                <div className='row mb-4'>
                                    <label htmlFor="username" className='mb-2 fw-bold'>Immediate apparent consequences of the incident</label>
                                    <div className="col-3 mb-2">
                                        <label htmlFor="incidentType" className="mb-2">Personnel</label>
                                        <Select
                                            options={personnel}
                                            value={personnel.find(option => option.value === formData.personnelId)}
                                            onChange={(selectedOption) => handleSelectChange(selectedOption, 'personnelId')}
                                            placeholder="Select"
                                            isClearable
                                        />
                                    </div>
                                    <div className="col-3 mb-2">
                                        <label htmlFor="incidentType" className="mb-2">Environment</label>
                                        <Select
                                            options={environment}
                                            value={environment.find(option => option.value === formData.environmentId)}
                                            onChange={(selectedOption) => handleSelectChange(selectedOption, 'environmentId')}
                                            placeholder="Select"
                                            isClearable
                                        />
                                    </div>
                                    <div className="col-3 mb-2">
                                        <label htmlFor="incidentType" className="mb-2">Property / Equipment</label>
                                        <Select
                                            options={propertyEquipment}
                                            value={propertyEquipment.find(option => option.value === formData.propertyEquipmentId)}
                                            onChange={(selectedOption) => handleSelectChange(selectedOption, 'propertyId')}
                                            placeholder="Select"
                                            isClearable
                                        />
                                    </div>
                                    <div className="col-3 mb-2">
                                        <label htmlFor="incidentType" className="mb-2">Operations</label>
                                        <Select
                                            options={operation}
                                            value={operation.find(option => option.value === formData.operationId)}
                                            onChange={(selectedOption) => handleSelectChange(selectedOption, 'operationId')}
                                            placeholder="Select"
                                            isClearable
                                        />
                                    </div>
                                </div>

                                <div className="col-12 mb-2">
                                    <label htmlFor="incidentDescription" className="mb-2">Personnel Involved</label>

                                    <InputTextarea
                                        className="d-flex"
                                        id="incidentDescription"
                                        name="personnelInvolved"
                                        style={{ width: '100%' }}
                                        placeholder='List the names, roles, and any witnesses involved or affected by the incident.'
                                        rows={3}
                                        value={formData.personnelInvolved}
                                        onChange={handleChange}
                                        autoResize
                                    />
                                    {errors.description && <small className="p-error">{errors.personnelInvolved}</small>}
                                </div>


                                <div className="col-12 mb-2">
                                    <label htmlFor="incidentDescription" className="mb-2">Witnesses</label>

                                    <InputTextarea
                                        className="d-flex"
                                        id="incidentDescription"
                                        name="witnessInvolved"
                                        style={{ width: '100%' }}
                                        placeholder='List the names, roles, and any witnesses involved or affected by the incident.'
                                        rows={3}
                                        value={formData.witnessInvolved}
                                        onChange={handleChange}
                                        autoResize
                                    />
                                    {errors.description && <small className="p-error">{errors.personnelInvolved}</small>}
                                </div>

                                <div className="col-12 mb-2">
                                    <label htmlFor="incidentDescription" className="mb-2">Immediate Actions Taken</label>


                                    <InputTextarea
                                        className="d-flex"
                                        id="incidentDescription"
                                        name="immediateActionsTaken"
                                        style={{ width: '100%' }}
                                        placeholder='Describe any actions taken immediately after the incident to control or manage the situation.
'
                                        rows={3}
                                        value={formData.immediateActionsTaken}
                                        onChange={handleChange}
                                        autoResize
                                    />
                                    {errors.description && <small className="p-error">{errors.immediateActionsTaken}</small>}
                                </div>

                                <div className="col-4 mb-2">
                                    <label htmlFor="incidentType" className="mb-2">Select Reviewer</label>
                                    <Select
                                        options={reviewer}
                                        value={reviewer.find(option => option.value === formData.reviewerId)}
                                        onChange={(selectedOption) => handleSelectChange(selectedOption, 'reviewerId')}
                                        placeholder="Select"
                                        isClearable
                                    />
                                    {errors.reviewerId && <small className="p-error">{errors.reviewerId}</small>}
                                </div>
                                <div className="col-12 mb-2" style={{ textAlign: 'right' }}>

                                    <Button variant="primary" onClick={handleSaveIncident} className="mt-3">
                                        Submit Preliminary Incident Report
                                    </Button>
                                </div>
                            </Form>
                        )}


                    </Modal.Body>

                </Modal>

            }

            {leadShow &&
                <Modal show={leadShow} onHide={() => setLeadShow(false)} size="md">
                    <Modal.Header closeButton>
                        <Modal.Title>Select Investigation team Leader</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                        {/* <div className="row mb-2 justify-content-center">
                            <div className="col-4 mb-2 d-flex align-items-center">
                                <input
                                    type="checkbox"
                                    id="requiresInvestigation"
                                    checked={requiresInvestigation}
                                    onChange={(e) => setRequiresInvestigation(e.target.checked)}
                                    className="me-2"
                                />
                                <label htmlFor="requiresInvestigation" className="mb-0">Requires Investigation</label>
                            </div>
                        </div> */}
                        {requiresInvestigation && (
                            <div className="row mb-2 justify-content-center">
                                <div className="col-4 mb-2">
                                    <label htmlFor="incidentType" className="mb-2">Investigator</label>
                                    <Select
                                        options={lead}
                                        value={lead.find(option => option.value === leadAssign)}
                                        onChange={(selectedOption) => setLeadAssign(selectedOption.value)}
                                        placeholder="Select"
                                        isClearable
                                    />
                                </div>
                            </div>
                        )}

                    </Modal.Body>
                    <Modal.Footer>
                        <Button variant="primary" onClick={assignInverstigator}>
                            Assign
                        </Button>
                    </Modal.Footer>
                </Modal>
            }
            {attachShow &&
                <InvestigationModal
                    isVisible={attachShow}
                    onHide={() => setAttachShow(false)}
                    investigationId={investigationId}
                />
            }
            {viewShow &&
                <ViewRecordModal
                    isVisible={viewShow}
                    onHide={() => setViewShow(false)}
                    record={record}
                />
            }
            {actionModal &&
                <Modal
                    show={actionModal}
                    size="lg"
                    onHide={() => setActionModal(false)}
                    aria-labelledby="example-modal-sizes-title-md"
                >
                    <Modal.Body>

                        <ActionTable id={maskId} actions={totalAction} current={current} />
                    </Modal.Body>
                    <Modal.Footer>
                        <Button
                            variant="light"
                            onClick={() => {
                                setActionModal(false);
                            }}
                        >
                            Close
                        </Button>
                    </Modal.Footer>
                </Modal>
            }
        </>
    )
}

export default Invest