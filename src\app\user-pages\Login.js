import React, { useState, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Form } from 'react-bootstrap';
import { Auth } from 'aws-amplify';
import { useHistory, useLocation } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { loginActions } from '../store/login-slice';
import { USER_LOGIN_DETAILS, FILE_DOWNLOAD } from '../constants';
import axios from 'axios';

const baseUrl = window.location.origin;
console.log(baseUrl);

const Login = () => {
  const email = useRef();
  const password = useRef();
  const history = useHistory();
  const location = useLocation();
  const [isLoading, setIsLoading] = useState(false);
  const [details, setDetails] = useState({});
  const [logo, setLogo] = useState('');
  const dispatch = useDispatch();

  useEffect(() => {
    fetch(USER_LOGIN_DETAILS, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
    })
      .then(response => response.json())
      .then(data => {
        console.log(data);
        setDetails(data);

        localStorage.setItem('COGNITO_USER_DOMAIN', data.COGNITO_USER_DOMAIN);
        localStorage.setItem('COGNITO_USER_APP_CLIENT_ID', data.COGNITO_USER_APP_CLIENT_ID);
        localStorage.setItem('SELECTED_INDUSTRIES', data.SELECTED_INDUSTRIES);
        if (data.LOGO) {
          localStorage.setItem('logo', data.LOGO);
          fetchLogo(data.LOGO);
        }
      });
  }, []);

  const fetchLogo = async (logoFilename) => {
    try {
      const response = await axios.get(FILE_DOWNLOAD(logoFilename), {
        headers: {
          'Content-Type': 'application/json'
        },
      });
      const data = response.data;
      setLogo(data); // Assuming the API returns an object with a `url` field
    } catch (error) {
      console.error('Error fetching logo:', error);
    }
  };

  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const code = searchParams.get('code');
    if (code && details.COGNITO_USER_DOMAIN && details.COGNITO_USER_APP_CLIENT_ID) {
      fetch(`${details.COGNITO_USER_DOMAIN}/oauth2/token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: `grant_type=authorization_code&client_id=${details.COGNITO_USER_APP_CLIENT_ID}&code=${code}&redirect_uri=${encodeURIComponent(baseUrl + '/login')}`
      })
        .then(response => response.json())
        .then(data => {
          console.log(data);
          const access_token = data.access_token;
          const refresh_token = data.refresh_token;
          localStorage.setItem('access_token', access_token);
          localStorage.setItem('refresh_token', refresh_token);
          dispatch(loginActions.setLogin());
          history.push('/dashboard');
        })
        .catch(error => console.error('Error:', error));
    }
  }, [details, location.search, history, dispatch]);

  const handleLoginWithAzure = async (e) => {
    e.preventDefault();
    setIsLoading(true);

    const redirectUrl = `${details.COGNITO_USER_DOMAIN}/oauth2/authorize?client_id=${details.COGNITO_USER_APP_CLIENT_ID}&response_type=code&scope=email+openid+phone&redirect_uri=${encodeURIComponent(baseUrl + '/login')}`;
    console.log(redirectUrl);
    window.location.replace(redirectUrl);
    setIsLoading(false);
  };

  const getToken = async () => {
    var data = await Auth.currentSession();
    return data.idToken.jwtToken;
  };

  return (
    <div>
      <div className="row  auth px-0 ">
        <div className='col-6 bg-white' style={{ position: 'relative' }}>
          <div className='bg-background-cover'></div>
        </div>
        <div className='col-6 d-flex align-items-center bg-white'>
          <div className='col-12 p-0'>
            <div className="text-left ">
              <div className="brand-logo">
                <img src={logo} alt="logo" />
              </div>
              <h2 className="fw-bold">AcuiZen Work Hub</h2>
              <h6 className="font-weight-light">Sign in to continue.</h6>
              <Form className="pt-3">
                <div className="mb-2">
                  <button onClick={(e) => handleLoginWithAzure(e)} type="button" className={isLoading ? "btn btn-block btn-secondary disabled" : "btn btn-block btn-secondary"}>
                    {details.LOGIN_BUTTON_TEXT}
                  </button>
                </div>
              </Form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
