import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import API from '../services/API';
import { FilterMatchMode, FilterOperator } from 'primereact/api';
import { DataTable } from 'primereact/datatable';
import { MultiSelect } from 'primereact/multiselect';
import { Modal } from 'react-bootstrap';
import { format } from 'date-fns';
import Swal from 'sweetalert2';
import moment from 'moment';
import TaskComponent from './Components/TaskComponent';
import TaskApproveComponent from './Components/TaskApproveComponent';
import { Tag } from 'primereact/tag';
import { sortNumbers } from '../services/NumberSort';
import { sortDate } from '../services/SortDate';
import { ACTION_URL, AIR_WITH_ID_URL, ACTION_OTT_SUBMIT, OTT_TASKS_WITH_ID, STATIC_URL, RA_ACTION_WITH_ID, GET_USER_ROLE_BY_MODE, ASSIGNED_ACTION_URL, OTT_WITH_ID, DROPDOWNS } from '../constants';
import { Column } from 'primereact/column';
const Action = ({ action, onRefresh, onFilterUpdate }) => {
    const signRef = useRef();
    const [selectedReviewer, setSelectedReviewer] = useState('');
    const [actions, setActions] = useState([]);
    const [incidentData, setIncidentData] = useState({});
    const [modalState, setModalState] = useState({ type: null, isOpen: false, actionId: null });
    const [filters, setFilters] = useState({
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        'applicationDetails.maskId': { value: null, matchMode: FilterMatchMode.IN },
        'applicationDetails.status': { value: null, matchMode: FilterMatchMode.IN },
        'applicationDetails.project.name': { value: null, matchMode: FilterMatchMode.IN },
        'applicationDetails.priority': { value: null, matchMode: FilterMatchMode.IN },
        'applicationDetails.creator.firstName': { value: null, matchMode: FilterMatchMode.IN },
        'timelineCategory': { value: null, matchMode: FilterMatchMode.IN },
        createdDate: { operator: FilterOperator.AND, constraints: [{ value: null, matchMode: FilterMatchMode.DATE_IS }] },
        'actionSubmittedBy.firstName': { value: null, matchMode: FilterMatchMode.IN },
        timeline: { value: null, matchMode: FilterMatchMode.IN },
    });

    const [startDate, setStartDate] = useState(null);
    const [endDate, setEndDate] = useState(null);
    const [maskId, setMaskId] = useState([]);
    const [names, setNames] = useState([]);
    const [project, setProject] = useState([]);
    const [data, setData] = useState([]);
    const [showModal, setShowModal] = useState(false);
    const [showItem, setShowItem] = useState([]);
    const [applicationDetails, setApplicationDetails] = useState([]);
    const [reviewerComment, setReviewerComment] = useState('');
    const [assignee, setAssignee] = useState([]);
    const overallStatus = useMemo(() => [
        { value: 'Yet to Start', name: 'Yet to Start' },
        { value: 'Planning', name: 'Planning' },
        { value: 'In Progress: On Track', name: 'In Progress: On Track' },
        { value: 'At Risk', name: 'At Risk' },
        { value: 'On Hold', name: 'On Hold' },
        { value: 'Under Review', name: 'Under Review' },
        { value: 'Testing / QA', name: 'Testing / QA' },
        { value: 'Ready for Deployment', name: 'Ready for Deployment' },
    ], []);

    const timelineOptions = useMemo(() => [
        { label: 'Overdue', value: 'Overdue', color: 'red' },
        { label: 'Due Soon', value: 'Due Soon', color: 'orange' },
        { label: 'Upcoming', value: 'Upcoming', color: 'green' },
    ], []);
    const handleFilterChange = (filteredData) => {


        // Update the count of filtered data in the parent component
        if (onFilterUpdate) {
            onFilterUpdate(filteredData.length);
        }
    };
    useEffect(() => {
        if (action) {
            const computedActions = action.map(item => ({
                ...item,
                timelineCategory: getTimelineCategory(item.applicationDetails.dueDate),
            }));
            setActions(computedActions);
            const obs = computedActions.map(item => ({
                name: item.applicationDetails.creator?.firstName || '',
                value: item.applicationDetails.creator?.firstName || '',
            }));
            setAssignee(obs.filter((ele, ind) => ind === obs.findIndex(elem => elem.value === ele.value && elem.name === ele.name)));
        }
        fetchDropdownData('project', setProject);
    }, [action]);

    const fetchDropdownData = useCallback(async (maskId, setState) => {
        try {
            const uriString = {
                where: { maskId: maskId },
                include: [{ relation: "dropdownItems" }],
            };
            const url = `${DROPDOWNS}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
            const response = await API.get(url);

            if (response.status === 200) {
                const data = response.data[0].dropdownItems.map((item) => ({
                    label: item.name,
                    value: item.name,
                }));
                setState(data);
            }
        } catch (error) {
            console.error(`Error fetching ${maskId} list:`, error);
        }
    }, []);

    const handleReviewerComment = useCallback((e) => {
        setReviewerComment(e);
    }, []);

    const getActions = useCallback(async () => {
        const uriString = { include: [{ relation: "submittedBy" }] };
        const url = `${ASSIGNED_ACTION_URL('OTT')}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
        const response = await API.get(url);
        if (response.status === 200) {
            const computedActions = response.data.map(item => ({
                ...item,
                timelineCategory: getTimelineCategory(item.applicationDetails.dueDate),
            }));
            setActions(computedActions);
        }
    }, []);

    const openActionCard = async (action) => {
        const uriString = {
            include: [
                { relation: "creator" },
                { relation: "assignee" },
                { relation: "ottTasks" },
                { relation: "reviewer" },
                { relation: "project" },
                { relation: "category" },
            ],
        };

        const url = `${OTT_WITH_ID(action.applicationId)}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
        const response = await API.get(url);
        if (response.status === 200) {
            setApplicationDetails(response.data);
            setData(response.data.ottTasks);
        }

        setShowItem(action);
        setShowModal(true);
    };

    const renderHeader = useCallback(() => {
        const value = filters['global'] ? filters['global'].value : '';
        return (
            <div className='d-flex justify-content-end'>
                {/* You can add a global search input here */}
            </div>
        );
    }, [filters]);

    const header = renderHeader();

    const idBodyTemplate = (row) => {
        return <div className='maskid' onClick={() => openActionCard(row)}>{row.maskId}</div>;
    };

    const getTimelineCategory = (dueDate) => {
        const today = moment().startOf('day');
        const dueDateMoment = moment(dueDate).startOf('day');

        if (dueDateMoment.isSame(today)) {
            return 'Due Soon';
        } else if (dueDateMoment.isBefore(today)) {
            return 'Overdue';
        } else {
            return 'Upcoming';
        }
    };

    const timelineBodyTemplate = (row) => {
        const category = row.timelineCategory;
        const color = category === 'Overdue' ? 'red' : category === 'Due Soon' ? 'orange' : 'green';
        return <Tag value={category} style={{ backgroundColor: color, color: 'white' }} />;
    };

    const timelineFilterTemplate = (options) => {
        return (
            <MultiSelect
                value={options.value}
                options={timelineOptions}
                onChange={(e) => options.filterCallback(e.value)}
                itemTemplate={(option) => (
                    <div className="flex align-items-center gap-2">
                        <Tag value={option.label} style={{ backgroundColor: option.color, color: 'white' }} />
                    </div>
                )}
                placeholder="Any"
                className="p-column-filter"
            />
        );
    };

    const submitActionReviewer = useCallback(async () => {
        if (!selectedReviewer) {
            Swal.fire("Please select a reviewer!", "", "error");
            return;
        }

        try {
            const response1 = await API.patch(ACTION_OTT_SUBMIT(showItem.id), { reviewerId: selectedReviewer });

            if (response1.status === 204) {
                Swal.fire("Task Assigned to Reviewer!", "", "success").then(() => {
                    setShowModal(false);
                    getActions();
                });
            } else {
                Swal.fire("Failed to assign task. Please try again.", "", "error");
            }
        } catch (error) {
            console.error("Error during task submission:", error);
            Swal.fire("An unexpected error occurred. Please try again.", "", "error");
        }
    }, [selectedReviewer, showItem, getActions]);

    const submitActionApprove = useCallback(async (type) => {
        try {
            // Check if reviewerComment is empty
            if (!reviewerComment || reviewerComment.trim() === "") {
                Swal.fire("Reviewer comment cannot be empty!", "", "warning");
                return;
            }

            // Check if there is data
            if (data && data.length !== 0) {
                // Check if all activities are checked before approval
                if (type === 'approve' && data.some(item => !item.checked)) {
                    Swal.fire("All Activities must be checked before approval!", "", "warning");
                    return;
                }

                // Only proceed with the API call if data is not empty
                const patchPromises = data.map(element =>
                    API.patch(OTT_TASKS_WITH_ID(element.id), {
                        checked: element.checked,
                        reviewerComments: element.reviewerComments,
                    })
                );

                const responses = await Promise.all(patchPromises);
                const allSuccessful = responses.every(response => response.status === 204);

                if (!allSuccessful) {
                    Swal.fire("Some tasks failed to update!", "", "error");
                    return;
                }
            }

            // If all API calls are successful or data is empty, proceed to update the status
            const status = type === 'approve' ? "Completed" : "Returned";

            const response1 = await API.patch(ACTION_OTT_SUBMIT(showItem.id), {
                status,
                reviewerComments: reviewerComment,
            });

            if (response1.status === 204) {
                Swal.fire(type === 'approve' ? "Task Completed" : "Task Returned to Assignee", "", "success").then(() => {
                    setShowModal(false);
                    onRefresh();
                });
            } else {
                Swal.fire("Failed to update the task status!", "", "error");
            }

        } catch (error) {
            console.error("Error during task submission:", error);
            Swal.fire("Please Try Again!", "", "error");
        }
    }, [data, reviewerComment, showItem, onRefresh]);



    const dueBodyTemplate = (data) => {
        try {
            // Safely check if dueDate exists and is a valid date
            if (data.applicationDetails?.dueDate) {
                const date = new Date(data.applicationDetails.dueDate);
                if (!isNaN(date)) {
                    return format(date, 'dd-MM-yyyy'); // Format the valid date
                }
            }
            return "No Due Date"; // Fallback message for invalid or missing dueDate
        } catch (error) {
            console.error("Error formatting dueDate:", error);
            return "Error"; // Fallback message for unexpected errors
        }
    };
    
    const statusBodyTemplate = (value) => value.applicationDetails.status;
    const actionBodyTemplate = (value) => value.actionToBeTaken === 'Perform Task' ? 'Update Status' : value.actionToBeTaken;

    const sendOverallStatus = useCallback((value) => {
        setApplicationDetails((prevState) => ({
            ...prevState,
            status: value,
        }));
    }, []);

    const sendAssigneeComments = useCallback((value) => {
        setApplicationDetails((prevState) => ({
            ...prevState,
            assigneeComments: value,
        }));
    }, [])
    const completedPercentage = useCallback((value) => {
        setApplicationDetails((prevState) => ({
            ...prevState,
            estimatedPercentage: parseInt(value),
        }));
    }, []);

    const representativesItemTemplate = (option) => {
        return (
            <div className="flex align-items-center gap-2">
                <span>{option.value}</span>
            </div>
        );
    };

    const statusFilterTemplate = (options) => {
        return (
            <MultiSelect
                value={options.value}
                options={overallStatus}
                itemTemplate={representativesItemTemplate}
                onChange={(e) => options.filterCallback(e.value)}
                optionLabel="name"
                placeholder="Any"
                className="p-column-filter"
            />
        );
    };

    const projectFilterTemplate = (options) => {
        return (
            <MultiSelect
                value={options.value}
                options={project}
                itemTemplate={representativesItemTemplate}
                onChange={(e) => options.filterCallback(e.value)}
                optionLabel="value"
                placeholder="Any"
                className="p-column-filter"
            />
        );
    };

    const assigneeFilterTemplate = (options) => {
        return (
            <MultiSelect
                value={options.value}
                options={assignee}
                itemTemplate={representativesItemTemplate}
                onChange={(e) => options.filterCallback(e.value)}
                optionLabel="value"
                placeholder="Any"
                className="p-column-filter"
            />
        );
    };

    return (
        <>
            <DataTable value={actions} paginator onValueChange={handleFilterChange} rows={10} header={header} filters={filters}
                emptyMessage="No Data found." tableStyle={{ minWidth: '50rem' }}
                rowClassName={rowData => rowData.applicationDetails.status === 'At Risk' ? 'red-background' : ''}
            >
                <Column field="timelineCategory" header="Timeline" body={timelineBodyTemplate} showFilterMatchModes={false} filter filterElement={timelineFilterTemplate} />
                <Column field="maskId" header="Task ID" body={idBodyTemplate} sortable />
                <Column field='actionToBeTaken' header="Task" body={actionBodyTemplate} />
                <Column field='applicationDetails.taskName' header="Task Name" />
                <Column field="applicationDetails.project.name" header="Project" filter filterElement={projectFilterTemplate} showFilterMatchModes={false} />
                <Column field="applicationDetails.creator.firstName" header="Assignor" filter filterElement={assigneeFilterTemplate} showFilterMatchModes={false} />
                <Column field="applicationDetails.priority" header="Priority" filter showFilterMatchModes={false} />
                <Column field="applicationDetails.dueDate" header="Due Date" body={dueBodyTemplate} sortable sortFunction={sortDate} />
                <Column field="applicationDetails.status" header="Status" filter filterElement={statusFilterTemplate} showFilterMatchModes={false} />
                <Column field="applicationDetails.estimatedPercentage" header="% Done" sortable sortFunction={sortNumbers} />
            </DataTable>

            {showModal && (
                showItem.actionType === 'perform_task' || showItem.actionType === 'reperform_task' ?
                    <TaskComponent
                        showModal={showModal}
                        type={showItem.actionType}
                        applicationDetails={applicationDetails}
                        data={data}
                        setFormData={(data) => setData(data)}
                        handleSelectChange={(e) => setSelectedReviewer(e.value)}
                        setShowModal={setShowModal}
                        sendOverallStatus={sendOverallStatus}
                        sendCompletedPercentage={completedPercentage}
                        submitActionReviewer={submitActionReviewer}
                        sendAssigneeComments={sendAssigneeComments}
                    />
                    :
                    showItem.actionType === 'verify_task' &&
                    <TaskApproveComponent
                        showModal={showModal}
                        applicationDetails={applicationDetails}
                        data={data}
                        handleReviewerComment={handleReviewerComment}
                        setFormData={(data) => setData(data)}
                        setShowModal={setShowModal}
                        submitActionApprove={submitActionApprove}
                    />
            )}
        </>
    );
};

export default Action;

