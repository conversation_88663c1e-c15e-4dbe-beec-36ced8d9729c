import React, { useState, useEffect } from "react";
import { Tabs, CircularProgress } from '@mui/material';
import MTab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import PropTypes from 'prop-types';
import { useSelector } from "react-redux";
import API from "../services/API";
import Swal from "sweetalert2";
import Actions from "./Actions";
import RiskAssessment from "./RiskAssessment";
import HighRiskHazard from "./HighRiskHazard";
import AppSwitch from "../pages/AppSwitch";
import { ASSIGNED_ACTION_URL, RISKASSESSMENT_LIST, TOOLBOXTALK_LIST } from "../constants";
import { Modal, Button, ButtonGroup } from "react-bootstrap";
import Routine from "./Routine";
import Hazard from "./HazardBased";
import ExistingOperational from "./ExistingOperational";
import PlannedOperational from "./PlannedOperational";
import ToolBoxTalk from "./ToolBoxTalk";
import ToolBoxFlag from "./ToolBoxFlag";
function CustomTabPanel(props) {
  const { children, value, tabValue, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== tabValue}
      id={`incident-tabpanel-${tabValue}`}
      aria-labelledby={`incident-tab-${tabValue}`}
      {...other}
    >
      {value === tabValue && (
        <Box>
          {children}
        </Box>
      )}
    </div>
  );
}

CustomTabPanel.propTypes = {
  children: PropTypes.node,
  value: PropTypes.string.isRequired,
  tabValue: PropTypes.string.isRequired,
};

const customFontStyle = {
  fontFamily: 'Lato, sans-serif',
  display: "flex",
  alignItems: 'center',
  justifyContent: 'center'
};

const Dashboard = () => {
  const user = useSelector((state) => state.login.user);
  const [actions, setActions] = useState([]);
  const [risk, setRisk] = useState([]);
  const [highRisk, setHighRisk] = useState([]);
  const [value, setValue] = useState('DASHBOARD');
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState(''); // To differentiate routine/non-routine
  const [riskData, setRiskData] = useState(null); // S
  const [domain, setDomain] = useState([])
  const [access, setAccess] = useState(false)
  const [loading, setLoading] = useState({
    actions: true,
    risk: true,
    highRisk: true
  });
  const [error, setError] = useState(null);
  const [actionCount, setActionCount] = useState(0);
  const [riskCount, setRiskCount] = useState(0);
  const [highRiskCount, setHighRiskCount] = useState(0);

  useEffect(() => {
    fetchActions();
    fetchRisk();
    fetchHighRisk();

    if (user.length !== 0) {
      setAccess(user.roles.some(item => item.maskId === 'ra_leader'))
    }

  }, []);

  const fetchActions = async () => {
    try {
      const uriString = { include: [{ relation: "submittedBy" }] };
      const url = `${ASSIGNED_ACTION_URL('RA')}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
      const response = await API.get(url);
      if (response.status === 200) {
        setActions(response.data);
        setActionCount(response.data.length);
      }
    } catch (err) {
      setError('Failed to fetch actions.');
      console.error(err);
    } finally {
      setLoading(prev => ({ ...prev, actions: false }));
    }
  };

  const fetchRisk = async () => {
    try {
      const uriString = {
        where: {
          $and: [
            { $or: [{ status: 'Pending' }, { status: 'Draft' }, { status: 'Published' }] },
            { $or: [{ type: 'Routine' }, { type: 'Non Routine' }, { type: 'High-Risk Hazard' }] }
          ]
        },
        include: [
          { relation: "department" },
          { relation: "teamLeader" },
          { relation: "workActivity" },
          {
            relation: "raTeamMembers",
            scope: {
              include: [{ relation: "user" }]
            }
          }
        ]
      };
      const url = `${RISKASSESSMENT_LIST}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
      const response = await API.get(url);
      if (response.status === 200) {
        setRisk(response.data);
        setRiskCount(response.data.length);
      }
    } catch (err) {
      setError('Failed to fetch risk assessments.');
      console.error(err);
    } finally {
      setLoading(prev => ({ ...prev, risk: false }));
    }
  };
  const handleCloseModal = () => {

    Swal.fire({
      title: 'Are you sure?',
      text: 'You have made edits to this form. Are you sure you want to exit without saving the changes?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, close it!',
      cancelButtonText: 'No, keep it open',
    }).then((result) => {
      if (result.isConfirmed) {
        setShowModal(false);
        setRiskData(null); // Close the modal if the user confirms
        Swal.fire('Closed!', '', 'success');
      } else {
        Swal.fire('Cancelled', '', 'info');
      }
    });
    // setShowModal(false);
    // setRiskData(null);
  };
  const fetchHighRisk = async () => {
    try {
      const uriString = {

        include: [
          { relation: "locationOne" },
          { relation: "locationTwo" },
          { relation: "locationThree" },
          { relation: "locationFour" },
          { relation: "locationFive" },
          { relation: "locationSix" },
          { relation: "locationSix" },
          {
            relation: "toolboxSignStatuses",
            scope: {
              include: [{ relation: "signedBy" }]
            }
          },
          { relation: "conductedBy" },
          {
            relation: "riskAssessment",
            scope: {
              include: [{ relation: "workActivity" }]
            }
          },
        ]
      };
      const url = `${TOOLBOXTALK_LIST}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
      const response = await API.get(url);
      if (response.status === 200) {
        setHighRisk(response.data);
        setHighRiskCount(response.data.length);
      }
    } catch (err) {
      setError('Failed to fetch high-risk hazards.');
      console.error(err);
    } finally {
      setLoading(prev => ({ ...prev, highRisk: false }));
    }
  };

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  // Handler to update the counts when filters are applied inside components
  const handleFilterUpdate = (count, type) => {
    switch (type) {
      case 'actions':
        setActionCount(count);
        break;
      case 'risk':
        setRiskCount(count);
        break;
      case 'highRisk':
        setHighRiskCount(count);
        break;
      default:
        break;
    }
  };
  const openAssessment = (type) => {
    setModalType(type); // 'routine' or 'nonroutine'
    setRiskData(null);  // Clear any previous data
    setShowModal(true); // Open the modal
    setDomain('new')
  };
  return (
    <>
      <AppSwitch value={{ label: 'Integrated Risk Management', value: 'ra' }} />

      {access && <div className="d-flex justify-content-end">
        <Button className="btn btn-primary  mb-3 " onClick={() => openAssessment('routine')}>
          Add New Risk Assessment

        </Button>
      </div>

      }

      <Tabs value={value} onChange={handleChange} aria-label="incident report table" className="risk">
        <MTab
          label={
            <Typography variant="body1" style={customFontStyle}>
              My Actions <span className='headerCount'>{actionCount}</span>
            </Typography>
          }
          value="MY ACTIONS"
        />
        <MTab
          label={
            <Typography variant="body1" style={customFontStyle}>
              Risk Register <span className='headerCount'>{riskCount}</span>
            </Typography>
          }
          value="DASHBOARD"
        />
        {/* <MTab
          label={
            <Typography variant="body1" style={customFontStyle}>
              Critical High Risk Activity <span className='headerCount'>{highRiskCount}</span>
            </Typography>
          }
          value="ROUTINE"
        /> */}
        <MTab
          label={
            <Typography variant="body1" style={customFontStyle}>
              Existing Operational Controls Register <span className='headerCount'>{0}</span>
            </Typography>
          }
          value="EXISTING"
        />

        <MTab
          label={
            <Typography variant="body1" style={customFontStyle}>
              Planned Operational Controls Register  <span className='headerCount'>{0}</span>
            </Typography>
          }
          value="PLANNED"
        />


        <MTab
          label={
            <Typography variant="body1" style={customFontStyle}>
              Tool Box Talk Records<span className='headerCount'>{highRiskCount}</span>
            </Typography>
          }
          value="TALK"
        />
        <MTab
          label={
            <Typography variant="body1" style={customFontStyle}>
              Tool Box Talk Flags<span className='headerCount'>{0}</span>
            </Typography>
          }
          value="FLAG"
        />
      </Tabs>

      {error && <p style={{ color: 'red' }}>{error}</p>}

      <CustomTabPanel value={value} tabValue="MY ACTIONS">
        {loading.actions ? (
          <Box display="flex" justifyContent="center">
            <CircularProgress />
          </Box>
        ) : (
          <Actions data={actions} onFilterUpdate={(count) => handleFilterUpdate(count, 'actions')} />
        )}
      </CustomTabPanel>

      <CustomTabPanel value={value} tabValue="DASHBOARD">
        {loading.risk ? (
          <Box display="flex" justifyContent="center">
            <CircularProgress />
          </Box>
        ) : (
          <RiskAssessment data={risk} onFilterUpdate={(count) => handleFilterUpdate(count, 'risk')} />
        )}
      </CustomTabPanel>

      <CustomTabPanel value={value} tabValue="EXISTING">
        {loading.highRisk ? (
          <Box display="flex" justifyContent="center">
            <CircularProgress />
          </Box>
        ) : (
          <ExistingOperational />
        )}
      </CustomTabPanel>
      <CustomTabPanel value={value} tabValue="PLANNED">
        {loading.highRisk ? (
          <Box display="flex" justifyContent="center">
            <CircularProgress />
          </Box>
        ) : (
          <PlannedOperational />
        )}
      </CustomTabPanel>
      <CustomTabPanel value={value} tabValue="TALK">
        {loading.highRisk ? (
          <Box display="flex" justifyContent="center">
            <CircularProgress />
          </Box>
        ) : (
          <ToolBoxTalk  data={highRisk} onFilterUpdate={(count) => handleFilterUpdate(count, 'highRisk')}/>
        )}
      </CustomTabPanel>
      <CustomTabPanel value={value} tabValue="FLAG">
        {loading.highRisk ? (
          <Box display="flex" justifyContent="center">
            <CircularProgress />
          </Box>
        ) : (
          <ToolBoxFlag />
        )}
      </CustomTabPanel>


      {showModal &&
        <Modal size="lg" show={showModal} onHide={handleCloseModal}>
          <Modal.Header closeButton>
            <div className="d-flex w-100 flex-column justify-content-between ">
              <Modal.Title className="fw-bold">
                {modalType === 'edit'
                  ? 'Edit Risk'
                  : modalType === 'view'
                    ? 'View Risk'
                    : `Conduct Risk Assessment`
                }
              </Modal.Title>
              <p className="mt-2">As the Risk Assessment (RA) Team Leader, you are responsible for leading a qualified team of RA Team Members in conducting a thorough risk assessment. This process includes identifying potential hazards associated with work  routine or non-routine work activities or high-risk scenarios, evaluating existing controls, and proposing additional controls to minimize potential consequences.  Select the appropriate type of activity and proceed based on the given instructions.
              </p>
            </div>

          </Modal.Header>
          <Modal.Body>
            <div className="ps-4">
              <ButtonGroup className="mb-3">
                <Button
                  variant={modalType === 'routine' ? 'primary' : 'outline-primary'}
                  onClick={() => setModalType('routine')}
                >
                  Routine
                </Button>
                <Button
                  variant={modalType === 'nonroutine' ? 'primary' : 'outline-primary'}
                  onClick={() => setModalType('nonroutine')}
                >
                  Non-Routine
                </Button>
                <Button
                  variant={modalType === 'hazard' ? 'primary' : 'outline-primary'}
                  onClick={() => setModalType('hazard')}
                >
                  High-Risk Scenarios
                </Button>

              </ButtonGroup>
            </div>

            {modalType === 'hazard' ?
              <Hazard data={riskData} type={modalType} domain={domain} />
              :
              <Routine data={riskData} type={modalType} domain={domain} />
            }

          </Modal.Body>

        </Modal>
      }
    </>
  );
};

export default Dashboard;
