import React, { useState, useRef, useEffect, useCallback } from 'react'
import { Modal, Button, Form, Card, Row, Col, Container } from 'react-bootstrap';
import SignatureCanvas from 'react-signature-canvas'
import { SUBMIT_PERMIT_ACTION, FILE_URL, GET_USER_ROLE_BY_MODE } from '../../../constants';
import Swal from 'sweetalert2';
import API from '../../../services/API';
import Select from 'react-select';
import ViewEptw from '../ViewEptw';
function Actions({ show, applicationDetails, showItem, closeModal }) {

    const signRef = useRef()

    const [apiStatus, setApiStatus] = useState('')
    const [signs, setSign] = useState('')
    const [signModal, setSignModal] = useState(false)
    const [comments, setComments] = useState('')
    const [showErrors, setShowErrors] = useState(false);
    const [assessor, setAssessor] = useState([])
    const [assessorId, setAssessorId] = useState('')

    useEffect(() => {
        if (showItem.actionType === 'Review') {
            getCrewList('eptwAssessor')
        } else if (showItem.actionType === 'Assess') {
            getCrewList('eptwApprover')
        }


    }, [showItem])

    const getCrewList = useCallback(async (type) => {
        try {
            const response = await API.post(GET_USER_ROLE_BY_MODE, {
                locationOneId: "",
                locationTwoId: "",
                locationThreeId: "",
                locationFourId: "",
                mode: type
            });

            if (response.status === 200) {
                const data = response.data.map((item) => ({
                    label: item.firstName,
                    value: item.id
                }));
                setAssessor(data);
            }
        } catch (error) {
            console.error("Error fetching crew list:", error);
        }
    }, [])
    const isValid = () => {
        if (apiStatus === 'Approve') {
            return showItem.actionType === 'Approve' ? comments !== '' && signs !== '' : signs !== '' && assessorId !== '';
        }
        if (apiStatus === 'Return') {
            return comments !== '';
        }
        return false;
    };
    const dataURItoFile = (dataURI, filename) => {
        var byteString = atob(dataURI.split(",")[1]);
        // separate out the mime component
        var mimeString = dataURI.split(",")[0].split(":")[1].split(";")[0];
        // write the bytes of the string to an ArrayBuffer
        var ab = new ArrayBuffer(byteString.length);
        var dw = new DataView(ab);
        for (var i = 0; i < byteString.length; i++) {
            dw.setUint8(i, byteString.charCodeAt(i));
        }

        // write the ArrayBuffer to a blob, and you're done
        return new File([ab], filename, { type: mimeString });
    };
    const uploadSignature = async () => {
        const filename = `${new Date().getTime()}_captin_sign.png`;
        const formData1 = new FormData();
        const signatureFile = dataURItoFile(signs, filename);
        formData1.append('file', signatureFile);

        try {
            const response = await API.post(FILE_URL, formData1, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                }
            });

            if (response && response.status === 200) {
                // Return the uploaded file name
                return response.data.files[0].originalname;
            } else {
                throw new Error("File upload failed.");
            }
        } catch (error) {
            console.error("File upload error:", error);
            throw error;
        }
    };
    const handleSubmit = async () => {

        setShowErrors(true);
        if (isValid()) {
            try {
                let payload;

                if (apiStatus === "Return") {
                    // If apiStatus is "Return", only include comments and status
                    payload = {
                        comments: comments,
                        status: "Returned"
                    };
                } else {
                    // If apiStatus is not "Return", upload signature and set other fields
                    const signatureFileName = await uploadSignature();

                    payload = {
                        comments: comments,
                        ...(showItem.actionType === 'Review'
                            ? { reviewerStatus: { signature: signatureFileName }, assessorId: assessorId }
                            : showItem.actionType === 'Assess'
                                ? { assessorStatus: { signature: signatureFileName }, approverId: assessorId }
                                : { approverStatus: { signature: signatureFileName } })
                    };
                }
                const response = await API.patch(SUBMIT_PERMIT_ACTION(showItem.id), payload);

                if (response.status === 204) {
                    Swal.fire("Permit", "Submitted Successfully", "success");
                    closeModal();
                    // window.location.reload(); 
                }
            } catch (error) {
                console.error("Error:", error);
            }
        } else {
            Swal.fire({
                icon: 'error',
                title: 'Validation',
                text: `Please fill all the required fields`
            });
        }
    };
    const handleApplicantChange = (selectedOption) => {
        setAssessorId(selectedOption ? selectedOption.value : "");
    };

    return (<>

        <Modal
            show={show}
            size={'lg'}
            onHide={() => closeModal(false)}
            aria-labelledby="example-modal-sizes-title-md"
            id="pdf-content"
        >
            <Modal.Header closeButton>
                {applicationDetails && (
                    <div className="row" style={{ width: '100%' }}>
                        <div className="col-9">
                            <div className="row">

                                <div className="col-12">
                                    <h4>Permit to Work</h4>
                                    <div className="d-flex align-items-center">
                                        <p className="me-2">#{applicationDetails.maskId || ''} </p>
                                        <p className="card-eptw">{applicationDetails.status} </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </Modal.Header>
            <Modal.Body>

                <ViewEptw applicationDetails={applicationDetails} />

                <h5 className='p-3 fw-bold'>{showItem.actionToBeTaken}</h5>
                <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                    <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>
                        <Card.Body >
                            <Row className="justify-content-center">
                                <Col xs={8} sm={8} md={8} className="d-flex  text-center">
                                    <Container
                                        fluid
                                        className="col-5 p-2"
                                        style={{ border: '1px solid #dee2e6', borderRadius: 10, color: '#000000' }}
                                        onClick={() => setApiStatus('Approve')}
                                    >
                                        <Row>
                                            <Col xs={4} sm={4} md={4}>
                                                <div style={apiStatus === 'Approve' ? { width: 24, height: 24, borderRadius: 12, background: 'green' } : { width: 24, height: 24, borderRadius: 12, background: 'lightgray' }} >
                                                    {apiStatus === 'Approve' && (
                                                        <span className="material-icons" style={{ color: 'white' }}>
                                                            done
                                                        </span>
                                                    )}
                                                </div>
                                            </Col>
                                            <Col xs={6} sm={6} md={6} style={apiStatus === 'Approve' ? { color: 'green' } : {}}>
                                                Approve
                                            </Col>
                                        </Row>
                                    </Container>
                                    <Container
                                        fluid
                                        className="col-5 p-2"
                                        style={{ border: '1px solid #dee2e6', borderRadius: 10, color: '#000000' }}
                                        onClick={() => setApiStatus('Return')}
                                    >
                                        <Row>
                                            <Col xs={4} sm={4} md={4}>
                                                <div style={apiStatus === 'Return' ? { width: 24, height: 24, borderRadius: 12, background: 'red' } : { width: 24, height: 24, borderRadius: 12, background: 'lightgray' }} >
                                                    {apiStatus === 'Return' && (
                                                        <span className="material-icons" style={{ color: 'white' }}>
                                                            done
                                                        </span>
                                                    )}
                                                </div>
                                            </Col>
                                            <Col xs={8} sm={8} md={8} style={apiStatus === 'Return' ? { color: 'red' } : {}}>
                                                Return
                                            </Col>
                                        </Row>
                                    </Container>
                                </Col>
                            </Row>
                            {showErrors && !apiStatus && (
                                <p className="text-danger mt-2">Please select Approve or Return.</p>
                            )}
                        </Card.Body>
                    </Card>
                </Col>

                <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                    <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>
                        <Card.Body>
                            <Row className="justify-content-center">
                                <Col xs={8} sm={8} md={8} className="d-flex text-center">
                                    <textarea
                                        rows="4"
                                        cols="50"
                                        className="form-control"
                                        placeholder="Enter your comments here..."
                                        onChange={(e) => setComments(e.target.value)}
                                    />
                                </Col>
                            </Row>
                            {showErrors && comments === '' && (
                                <p className="text-danger mt-2">Comments are required.</p>
                            )}
                        </Card.Body>
                    </Card>
                </Col>




                {apiStatus === 'Approve' && (<>
                    {showItem.actionType !== 'Approve' &&
                        <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                            <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>
                                <Card.Body>
                                    <Form.Group className="mb-3">
                                        <Form.Label>{showItem.actionType === 'Review' ? 'Assessor' : 'Approver'}</Form.Label>
                                        <Select
                                            options={assessor}
                                            value={assessor.find(option => option.value === assessorId)}
                                            onChange={handleApplicantChange}
                                            placeholder={showItem.actionType === 'Review' ? 'Select Assessor' : 'Select Approver'}
                                            isClearable
                                        />

                                    </Form.Group>
                                    {showErrors && assessorId === '' && (
                                        <p className="text-danger mt-2">{showItem.actionType === 'Review' ? 'Assessor' : 'Approver'}  required.</p>
                                    )}

                                </Card.Body>
                            </Card>
                        </Col>
                    }
                    <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                        <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>
                            <Card.Body>
                                <Row>
                                    <Col xs={12} sm={12} md={12} className="d-flex text-justify">
                                        <label style={{ textAlign: 'justify' }}>
                                            I acknowledge this permit application.
                                        </label>
                                    </Col>
                                    <Col xs={12} sm={12} md={12} className="d-flex justify-content-center p-2" onClick={() => setSignModal(true)}>
                                        <span className="material-icons" style={{ fontSize: 60 }}>
                                            draw
                                        </span>
                                    </Col>
                                    <div className="d-flex justify-content-center">
                                        {signs ? (
                                            <img src={signs} height={100} style={{ minWidth: 150 }} />
                                        ) : (
                                            showErrors && (
                                                <p className="text-danger mt-2">Signature is required for approval.</p>
                                            )
                                        )}
                                    </div>
                                </Row>
                            </Card.Body>
                        </Card>
                    </Col>
                </>
                )}
            </Modal.Body>
            <Modal.Footer>
                <Button
                    type="button" // Explicitly set the type to button
                    severity="primary"
                    onClick={handleSubmit} // Ensure handleSubmit is correctly referenced
                    disabled={!isValid()} // Ensure isValid() returns true as expected
                >
                    {apiStatus === 'Return' ? 'Return to Applicant' : 'Submit'}
                </Button>
            </Modal.Footer>
        </Modal>

        <Modal
            show={signModal}
            onHide={() => { setSignModal(false) }}
            aria-labelledby="contained-modal-title-vcenter"
            centered
            backdrop={'static'}
        >
            <Modal.Header closeButton={false}>
                <Modal.Title id="contained-modal-title-vcenter">
                    Sign
                </Modal.Title>
            </Modal.Header>
            <Modal.Body style={{ background: '#f5f5f5', width: '100%' }}>
                <SignatureCanvas
                    ref={signRef}
                    penColor="#1F3BB3"
                    backgroundColor="white"
                    canvasProps={{
                        className: "sigCanvas",
                        style: {
                            width: '100%', // Ensures the canvas takes up the full width
                            background: '#fff',
                            boxShadow: "0px 0px 10px 3px rgb(189 189 189)",
                            height: '100px'
                        },
                    }}
                />
            </Modal.Body>
            <Modal.Footer>
                <Button onClick={() => { setSign(signRef.current.getTrimmedCanvas().toDataURL("image/png")); setSignModal(false) }}>confirm</Button>
                <Button onClick={() => signRef.current.clear()}>Clear</Button>
                <Button onClick={() => { setSignModal(false) }}>Close</Button>
            </Modal.Footer>
        </Modal>
    </>)
}

export default Actions