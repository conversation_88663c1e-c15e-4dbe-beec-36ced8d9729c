import React, { useState } from 'react';
import Select from 'react-select'; // Import react-select's Select component
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';

const Likelihood = ({ likelyhood, levelData, required, onChangeLikelyhood, item, rowClassName }) => {
    const [likelyhoodTable, setLikelyhoodTable] = useState(false);

    // Transform the likelyhood options to be compatible with react-select
    const likelyhoodOptions = likelyhood.map(option => ({
        value: option.value || option, // Use option.value if available, otherwise use option directly
        label: option.label || option // Use option.label if available, otherwise use option directly
    }));

    return (
        <div className="row mt-4 mb-3 pb-4" style={{ borderBottom: '1px solid #E0E0E0' }}>
            <div className='col-8'>
                <h6 className='fw-bold'>Likelihood</h6>
                <p className='fst-italic'>Frequency with which a hazardous event or situation could happen</p>
            </div>
            <div className='col-4'>
                <Select
                    className={`d-flex ${(required === false && item[4].likelyhood === '') ? 'borderRed' : ''}`}
                    options={likelyhoodOptions} // Pass the transformed options
                    value={likelyhoodOptions.find(option => option.value === item[4].likelyhood)} // Set the selected value
                    onChange={(e) => onChangeLikelyhood(e, 'assessment')} // Call the change handler
                    styles={{
                        container: (provided) => ({ ...provided, width: '100%' }),
                        control: (provided) => ({ ...provided, width: '100%' })  // Ensure control takes full width too
                    }}
                />
            </div>
            <h6 className='mt-3 pointer' onClick={() => setLikelyhoodTable(!likelyhoodTable)}>
                Understand Likelihood Levels <i className={`pi ${likelyhoodTable ? 'pi-angle-up' : 'pi-angle-down'} ms-1`}></i>
            </h6>
            {likelyhoodTable && (
                <div className='col-12 mt-3'>
                    <div className="card">
                        <DataTable value={levelData} className="table-bordered" rowClassName={rowClassName}>
                            <Column field="level" header="Level"></Column>
                            <Column field="descriptor" header="Descriptor"></Column>
                            <Column field="detailedDescription" header="Detailed Description"></Column>
                        </DataTable>
                    </div>
                </div>
            )}
        </div>
    );
};

export default Likelihood;
