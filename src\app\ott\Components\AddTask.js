import React, { useEffect, useState, useCallback } from 'react';
import { Form, Card, Accordion } from 'react-bootstrap';
import { Button } from 'primereact/button';
import { DropzoneArea } from 'material-ui-dropzone';
import Select from 'react-select';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import 'primereact/resources/themes/saga-blue/theme.css';
import 'primereact/resources/primereact.min.css';
import 'primeicons/primeicons.css';
import API from '../../services/API';
import { DROPDOWNS, GET_USER_ROLE_BY_MODE, FILE_URL, ADMINDROPDOWNS } from '../../constants';
import ImageComponent from '../../services/FileDownlodS3';

const AddTask = ({ initialData, onSave }) => {
    const [assignee, setAssignee] = useState([]);
    const [project, setProject] = useState([]);
    const [category, setCategory] = useState([]);
    const [originalTasks, setOriginalTasks] = useState([]); // New state to store the original tasks
    const [formData, setFormData] = useState({
        ott: { taskName: '', taskDescription: '', dueDate: '', img: [], priority: 'Medium', assigneeId: '', projectId: '', categoryId: '' },
        tasks: []
    });

    useEffect(() => {
        getCrewList();
        getProjectList();
        getCategoryList();
    }, []);

    useEffect(() => {
        if (initialData) {
            const tasks = initialData.ottTasks && initialData.ottTasks.length > 0 ? initialData.ottTasks : [];
            setFormData({
                ott: {
                    taskName: initialData.taskName || '',
                    taskDescription: initialData.taskDescription || '',
                    priority: initialData.priority || 'Medium',
                    assigneeId: initialData.assigneeId || '',
                    projectId: initialData.projectId || '',
                    dueDate: initialData.dueDate ? new Date(initialData.dueDate) : ''
                },
                tasks: tasks
            });
            setOriginalTasks(tasks); // Store the original tasks
        }
    }, [initialData]);

    const getCrewList = useCallback(async () => {
        try {
            const response = await API.post(GET_USER_ROLE_BY_MODE, {
                locationOneId: "",
                locationTwoId: "",
                locationThreeId: "",
                locationFourId: "",
                mode: 'ott_assignee'
            });

            if (response.status === 200) {
                const data = response.data.map((item) => ({
                    label: item.firstName,
                    value: item.id
                }));
                setAssignee(data);
            }
        } catch (error) {
            console.error("Error fetching crew list:", error);
        }
    }, []);

    const getProjectList = useCallback(async () => {
        try {
            const uriString = {
                where: {
                    maskId: 'project'
                },
                include: [
                    { relation: "dropdownItems" },
                ]
            };

            const url = `${ADMINDROPDOWNS}?filter=${encodeURIComponent(
                JSON.stringify(uriString)
            )}`;

            const response = await API.get(url);

            if (response.status === 200) {
                const data = response.data[0].dropdownItems.map((item) => ({
                    label: item.name,
                    value: item.id
                }));
                setProject(data);
            }
        } catch (error) {
            console.error("Error fetching project list:", error);
        }
    }, []);

    const getCategoryList = useCallback(async () => {
        try {
            const uriString = {
                where: {
                    maskId: 'category'
                },
                include: [
                    { relation: "dropdownItems" },
                ]
            };

            const url = `${ADMINDROPDOWNS}?filter=${encodeURIComponent(
                JSON.stringify(uriString)
            )}`;

            const response = await API.get(url);

            if (response.status === 200) {
                const data = response.data[0].dropdownItems.map((item) => ({
                    label: item.name,
                    value: item.id
                }));
                setCategory(data);
            }
        } catch (error) {
            console.error("Error fetching project list:", error);
        }
    }, []);

    const handleInputChange = useCallback((e) => {
        const { name, value } = e.target;
        setFormData(prevState => ({
            ...prevState,
            ott: { ...prevState.ott, [name]: value }
        }));
    }, []);

    const handleMainImageUpload = useCallback(async (files) => {
        if (files.length > 0) {
            const latestFile = files[files.length - 1];
            const formData1 = new FormData();
            formData1.append('file', latestFile);

            try {
                const response = await API.post(FILE_URL, formData1, {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                    },
                });

                if (response && response.status === 200) {
                    setFormData(prevState => ({
                        ...prevState,
                        ott: { ...prevState.ott, img: [...(prevState.ott.img || []), response.data.files[0].originalname] }
                    }));
                }
            } catch (error) {
                console.error("File upload error: ", error);
            }
        }
    }, []);


    const handleSelectChange = useCallback((selectedOption, { name }) => {
        setFormData(prevState => ({
            ...prevState,
            ott: { ...prevState.ott, [name]: selectedOption ? selectedOption.value : '' }
        }));
    }, []);

    const handleTaskChange = useCallback((index, e) => {
        const { name, value } = e.target;
        setFormData(prevState => {
            const updatedTasks = prevState.tasks.map((task, i) =>
                i === index ? { ...task, [name]: value } : task
            );
            return { ...prevState, tasks: updatedTasks };
        });
    }, []);

    const handleImageUpload = useCallback(async (index, files) => {
        if (files.length > 0) {
            const latestFile = files[files.length - 1];
            const formData1 = new FormData();
            formData1.append('file', latestFile);

            try {
                const response = await API.post(FILE_URL, formData1, {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                    },
                });

                if (response && response.status === 200) {
                    setFormData((prevState) => {
                        const updatedTasks = prevState.tasks.map((task, i) =>
                            i === index
                                ? { ...task, img: [...(task.img || []), response.data.files[0].originalname] }
                                : task
                        );
                        return { ...prevState, tasks: updatedTasks };
                    });
                }
            } catch (error) {
                console.error("File upload error: ", error);
            }
        }
    }, []);



    const removeTask = useCallback(async (index) => {
        const task = formData.tasks[index];

        if (task.id) {
            try {
                setFormData(prevState => {
                    const updatedTasks = prevState.tasks.filter((_, i) => i !== index);
                    return { ...prevState, tasks: updatedTasks };
                });
            } catch (error) {
                console.error('Error deleting task:', error);
                alert('Error deleting task');
            }
        } else {
            setFormData(prevState => {
                const updatedTasks = prevState.tasks.filter((_, i) => i !== index);
                return { ...prevState, tasks: updatedTasks };
            });
        }
    }, [formData.tasks]);

    const addTask = useCallback(() => {
        setFormData(prevState => ({
            ...prevState,
            tasks: [...prevState.tasks, { name: '', img: [] }]
        }));
    }, []);

    const handleDateChange = useCallback((date) => {
        setFormData(prevState => ({
            ...prevState,
            ott: { ...prevState.ott, dueDate: date }
        }));
    }, []);

    const handleSubmit = (e) => {
        e.preventDefault();

        const { taskName, assigneeId, dueDate } = formData.ott;


        if (!taskName || !assigneeId || !dueDate) {
            alert("Please fill in all required fields");
            return;
        }

        if (initialData) {
            if (formData.tasks.length > 0) {
                const tasksAreValid = formData.tasks.length > 0 && formData.tasks.every(task => task.name.trim());
                if (!tasksAreValid) {
                    alert(" Ensure all tasks have a name.");
                    return;
                }
            }
        }

        // Determine new, deleted, and modified tasks
        const newTasks = formData.tasks.filter(task => !task.id);
        const deletedTasks = originalTasks.filter(originalTask => !formData.tasks.some(task => task.id === originalTask.id));
        const modifiedTasks = formData.tasks.filter(task => {
            const originalTask = originalTasks.find(original => original.id === task.id);
            return originalTask && JSON.stringify(originalTask) !== JSON.stringify(task);
        });

        console.log("New Tasks: ", newTasks);
        console.log("Deleted Tasks: ", deletedTasks);
        console.log("Modified Tasks: ", modifiedTasks);
        if (initialData) {
            // Editing an existing task
            console.log("Editing an existing task");
            onSave({ ...formData, newTasks, deletedTasks, modifiedTasks, action: 'edit' });
        } else {
            // Adding a new task
            console.log("Adding a new task");
            onSave({ ...formData, newTasks, deletedTasks, modifiedTasks, action: 'add' });
        }

        // onSave({ ...formData, newTasks, deletedTasks, modifiedTasks });
    };

    const handleRemoveImage = (activityIndex, imgIndex) => {
        setFormData((prevState) => {
            const updatedTasks = [...prevState.tasks];
            const updatedImages = [...updatedTasks[activityIndex].img];
            updatedImages.splice(imgIndex, 1);
            updatedTasks[activityIndex] = {
                ...updatedTasks[activityIndex],
                img: updatedImages
            };
            return {
                ...prevState,
                tasks: updatedTasks
            };
        });
    };

    const handleRemoveMainImage = (imgIndex) => {
        setFormData((prevState) => {
            // Create a copy of the ott object from the previous state
            const updatedOtt = { ...prevState.ott };

            // Create a copy of the img array
            const updatedImages = [...updatedOtt.img];

            // Remove the image at the specified index
            updatedImages.splice(imgIndex, 1);

            // Update the ott object with the modified img array
            updatedOtt.img = updatedImages;

            // Return the new state with the updated ott object
            return {
                ...prevState,
                ott: updatedOtt,
            };
        });
    };


    return (
        <Form onSubmit={handleSubmit}>
            <Form.Group controlId="assigneeId" className="mb-3">
                <Form.Label>Category <span className="text-danger">*</span></Form.Label>
                <Select
                    name="categoryId"
                    value={category.find(option => option.value === formData.ott.categoryId) || ''}
                    onChange={handleSelectChange}
                    options={category}
                    isClearable
                    required
                />
            </Form.Group>
            <Form.Group controlId="assigneeId" className="mb-3">
                <Form.Label>Project <span className="text-danger">*</span></Form.Label>
                <Select
                    name="projectId"
                    value={project.find(option => option.value === formData.ott.projectId) || ''}
                    onChange={handleSelectChange}
                    options={project}
                    isClearable
                    required
                />
            </Form.Group>
            <Form.Group controlId="taskName" className="mb-3">
                <Form.Label>Task Name <span className="text-danger">*</span></Form.Label>
                <Form.Control
                    type="text"
                    name="taskName"
                    value={formData.ott.taskName}
                    onChange={handleInputChange}
                    required
                />
            </Form.Group>

            <Form.Group controlId="assigneeId" className="mb-3">
                <Form.Label>Assignee <span className="text-danger">*</span></Form.Label>
                <Select
                    name="assigneeId"
                    value={assignee.find(option => option.value === formData.ott.assigneeId) || ''}
                    onChange={handleSelectChange}
                    options={assignee}
                    isClearable
                    required
                />
            </Form.Group>

            <Form.Group controlId="taskDescription" className="mb-3">
                <Form.Label>Task Description (Optional)</Form.Label>
                <Form.Control
                    as="textarea"
                    name="taskDescription"
                    value={formData.ott.taskDescription || ''}
                    onChange={handleInputChange}
                />
            </Form.Group>

            <Form.Group controlId={`subtaskImages`} className="mb-3">
                <Form.Label>Images</Form.Label>
                <DropzoneArea
                    acceptedFiles={[]}
                    dropzoneText={"Drag 'n' drop some files here, or click to select files (Optional)"}
                    onChange={(files) => handleMainImageUpload(files)}
                    filesLimit={5}
                    maxFileSize={104857600} // 100 MB
                    showPreviewsInDropzone={false}
                    showPreviews={false}
                    dropzoneClass={'dropzoneText d-flex align-items-center justify-content-center'}
                />

                <div className="image-preview mt-3">
                    {formData.ott.img && formData.ott.img.length > 0 && (
                        <div className="d-flex">
                            {formData.ott.img.map((item, m) => (
                                <div key={m} className="col-3" style={{ position: 'relative' }}>
                                    <div className="boxShadow d-flex align-items-center justify-content-center">
                                        <ImageComponent fileName={item} size={'100'} name={true} />
                                        <i
                                            className="pi pi-trash"
                                            onClick={() => handleRemoveMainImage(m)}
                                            style={{
                                                position: 'absolute',
                                                top: '5px',
                                                right: '5px',
                                                cursor: 'pointer',
                                                color: 'red',
                                            }}
                                        />
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            </Form.Group>

            <Form.Group controlId="dueDate" className="mb-3">
                <Form.Label>Due Date <span className="text-danger">*</span></Form.Label>
                <DatePicker
                    selected={formData.ott.dueDate}
                    onChange={handleDateChange}
                    minDate={new Date()}
                    dateFormat="dd-MM-yyyy"
                    className="form-control"
                    required
                />
            </Form.Group>

            <Form.Group controlId="ottPriority" className="mb-3">
                <Form.Label>Priority</Form.Label>
                <div className='d-flex'>
                    {['High', 'Medium', 'Low'].map((level, index) => (
                        <Form.Check
                            key={index}
                            inline
                            type="radio"
                            label={level}
                            name="priority"
                            value={level}
                            checked={formData.ott.priority === level}
                            onChange={handleInputChange}
                            style={{ color: level === 'High' ? 'red' : level === 'Medium' ? 'orange' : 'green' }}
                        />
                    ))}
                </div>
            </Form.Group>

            <Card>
                <strong>Activities / Dependencies</strong>
                <Card.Body>
                    <Accordion>
                        {formData.tasks.map((task, index) => (
                            <Accordion.Item eventKey={index.toString()} key={index}>
                                <Accordion.Header>Activity {index + 1}</Accordion.Header>
                                <Accordion.Body>
                                    <div className="mb-3">
                                        <Form.Label>SubActivity Name</Form.Label>
                                        <div className="d-flex align-items-center mb-3">
                                            <Form.Control
                                                type="text"
                                                name="name"
                                                value={task.name}
                                                onChange={(e) => handleTaskChange(index, e)}
                                                className="me-3"
                                                required
                                            />
                                            <Button
                                                icon="pi pi-trash"
                                                className="p-button-danger p-button-rounded p-2"
                                                onClick={() => removeTask(index)}
                                            />
                                        </div>
                                    </div>

                                    <Form.Group controlId={`subtaskImages${index}`} className="mb-3">
                                        <Form.Label>Images</Form.Label>
                                        <DropzoneArea
                                            acceptedFiles={['image/*']}
                                            dropzoneText={"Drag 'n' drop some files here, or click to select files (Optional)"}
                                            onChange={(files) => handleImageUpload(index, files)}
                                            filesLimit={5}
                                            maxFileSize={104857600}
                                            showPreviewsInDropzone={false}
                                            showPreviews={false}
                                            dropzoneClass={'dropzoneText d-flex align-items-center justify-content-center '}
                                        />
                                        <div className="image-preview mt-3">
                                            {task.img && task.img.length > 0 && (
                                                <div className="d-flex">
                                                    {task.img.map((item, m) => (
                                                        <div key={m} className="col-3" style={{ position: 'relative' }}>
                                                            <div className="boxShadow d-flex align-items-center justify-content-center">
                                                                <ImageComponent fileName={item} size={'100'} name={true} />
                                                                <i
                                                                    className="pi pi-trash"
                                                                    onClick={() => handleRemoveImage(index, m)}
                                                                    style={{
                                                                        position: 'absolute',
                                                                        top: '5px',
                                                                        right: '5px',
                                                                        cursor: 'pointer',
                                                                        color: 'red',
                                                                    }}
                                                                />
                                                            </div>
                                                        </div>
                                                    ))}
                                                </div>
                                            )}
                                        </div>
                                    </Form.Group>


                                </Accordion.Body>
                            </Accordion.Item>
                        ))}
                    </Accordion>
                    <Button
                        icon="pi pi-plus"
                        label="Add Activity"
                        className="p-button-primary mt-3"
                        onClick={(e) => {
                            e.preventDefault();
                            addTask();
                        }}
                    />
                </Card.Body>
            </Card>

            <Button
                label="Save"
                className="p-button-success mt-3"
                type="submit"
            />
        </Form>
    );
};

export default AddTask;
