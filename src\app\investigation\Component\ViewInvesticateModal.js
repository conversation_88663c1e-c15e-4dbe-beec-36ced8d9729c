import React, { useState, useEffect } from 'react';
import { Modal, Button, Row, Col, Card } from 'react-bootstrap';
import ViewDetails from './ViewDetails';
import moment from 'moment';
import ViewInvestigation from './ViewInvestigation';

const ViewInvestigateModal = ({ isVisible, onHide, record }) => {

    console.log(record)
    const [formData, setFormData] = useState(record);

    useEffect(() => {
        if (record) {
            setFormData(record);
        }
    }, [record]);



    return (
        <Modal show={isVisible} onHide={onHide} size="lg">
            <Modal.Header closeButton>
                <Modal.Title>Investigation Review Report (View Only)</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                {/* <i className='d-flex mb-3'>This is a record of the incident based on initial submission and review and prior to any assigned investigation. No further edits are possible on this report.</i> */}
                
                {/* <ViewDetails data={formData} disable={true} type={'view'} details={true}/> */}
                <ViewInvestigation applicationDetails={record.incident} data={record} type={''}/>
            </Modal.Body >
            <Modal.Footer>
               <i>This is a view-only screen. Updates can only be made by those assigned actions on this incident, through their respective  “My Actions” tab.
               </i>
            </Modal.Footer>
        </Modal>
    );
};

export default ViewInvestigateModal;
