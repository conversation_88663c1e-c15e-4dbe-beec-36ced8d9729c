import { InputTextarea } from 'primereact/inputtextarea';
import React from 'react';
import { Row, Col, Form } from 'react-bootstrap';
import Select from 'react-select';

const ImmediateCauseSection = ({
  control,
  controlIndex,
  section,
  handleChange,
  handleImmediateCauseChange,
  incImmediateCauseOptions,
}) => {
  return (
    <>
      {/* Immediate Cause */}
      <Row>
        {(section !== 'unidentifiedPreventiveControls' && section !== 'unidentifiedMitigativeControls') && (
          <Col md="12" className="mb-3">
            <Form.Group>
              <Form.Label className="fw-bold">Immediate Cause</Form.Label>
              <p className="mb-3 fst-italic">
                An immediate cause is the direct action, inaction, or condition that led to the failure of a control that
                was identified. It focuses on what actually happened rather than why it happened. It’s a straightforward,
                factual description of the circumstance or condition that resulted in the lack of implementation or failure
                of the control.
              </p>
            </Form.Group>
          </Col>
        )}
        <Col md="12" className="mb-3 d-flex flex-column justify-content-between">
          {(section === 'unidentifiedPreventiveControls' || section === 'unidentifiedMitigativeControls') ? (
            <Form.Label className="fw-bold">
              Why was this control not considered or included during the planning or risk assessment process?
            </Form.Label>
          ) : (
            <Form.Label className="fw-bold">
              What was the Immediate Cause for the non-implementation or ineffectiveness of  this control?
            </Form.Label>
          )}
          <InputTextarea
            autoResize
            style={{ width: '100%',minHeight:100 }}
            value={control.immediateCauseDescription || ''}
            onChange={(e) => handleChange(section, controlIndex, 'immediateCauseDescription', e.target.value)}
          />
        </Col>

        {/* Conditional Rendering: Display this section only if it's not unidentified */}
        {(section !== 'unidentifiedPreventiveControls' && section !== 'unidentifiedMitigativeControls') && (
          <Col md="4" className="mb-3">
            <Form.Group>
              <Form.Label className="fw-bold">How would you categorize this immediate cause?</Form.Label>
              <Select
                value={incImmediateCauseOptions.find((option) => option.label === control.immediateCause)}
                onChange={(selectedOption) => handleImmediateCauseChange(section, controlIndex, selectedOption)}
                options={incImmediateCauseOptions}
              />
            </Form.Group>
          </Col>
        )}
      </Row>
      <hr />
      <Row>
        <Col md="12" className="mb-3">
          <Form.Group>
            <Form.Label className="fw-bold">Identification of underlying causes</Form.Label>
            <p>Based on your investigation, please list all relevant Job Factors and related Organizational Factors that contributed to the failure of controls leading to the identified immediate cause. Include multiple factors as needed, and ensure each is explained in detail. When a factor is considered significant, reference the investigation records and provide supporting details to justify the team’s reasoning. </p>
          </Form.Group>
        </Col>
      </Row>


      <hr />
    </>
  );
};

export default ImmediateCauseSection;
