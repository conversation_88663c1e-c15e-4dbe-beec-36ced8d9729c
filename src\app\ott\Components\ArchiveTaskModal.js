import React, { useState } from 'react';
import { Modal, Button, Form } from 'react-bootstrap';

function ArchiveTaskModal({ show, handleClose, handleSave }) {
  const [archiveComments, setArchiveComments] = useState('');
  const [isTaskIsCompletedForArchive, setIsTaskIsCompletedForArchive] = useState(false);

  const onSave = () => {
    // Call the save handler with the form data
    handleSave({ archiveComments, isTaskIsCompletedForArchive });
    handleClose(); // Close the modal after saving
  };

  return (
    <Modal show={show} onHide={handleClose} dialogClassName="side-modal">
      <Modal.Header closeButton>
        <Modal.Title>Archive Task</Modal.Title>
      </Modal.Header>
      <Modal.Body style={{ backgroundColor: '#f8f9fa' }}>
        <Form>
          <Form.Group controlId="formArchiveComments">
            <Form.Label>Archive Comments</Form.Label>
            <Form.Control
              type="text"
              placeholder="Enter comments"
              value={archiveComments}
              onChange={(e) => setArchiveComments(e.target.value)}
            />
          </Form.Group>

          <Form.Group controlId="formIsTaskIsCompletedForArchive">
            <Form.Check
              type="checkbox"
              label="Is Task Completed for Archive"
              checked={isTaskIsCompletedForArchive}
              onChange={(e) => setIsTaskIsCompletedForArchive(e.target.checked)}
            />
          </Form.Group>
        </Form>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={handleClose}>
          Close
        </Button>
        <Button variant="primary" onClick={onSave}>
          Save Changes
        </Button>
      </Modal.Footer>
    </Modal>
  );
}

export default ArchiveTaskModal;
