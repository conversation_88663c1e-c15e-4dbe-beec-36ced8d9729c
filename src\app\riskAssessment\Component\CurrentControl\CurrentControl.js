import React from 'react';
import Select from 'react-select';
import { InputText } from 'primereact/inputtext';
import { DropzoneArea } from 'material-ui-dropzone';
import ImageComponent from '../../../services/FileDownlodS3';
import { InputTextarea } from 'primereact/inputtextarea';
import { Checkbox } from 'primereact/checkbox';

const CurrentControl = ({ con, i, control, controlType, onImapactOn, onConseqText, onDeleteConseq,onConseqRequired, handleTaskFileChange, onMethodOn, required, type, handleRemoveMainImage }) => {
    return (
        <>
            <div className="row mt-4">
                <div className="col-3">
                    <p>Method of Control</p>
                    <Select
                        options={control}
                        value={control.find(option => option.value === con.current_type)} // Set current value
                        onChange={(e) => onImapactOn(e.value, i, 'current_control')} // Handle onChange
                        className={`d-flex ${(required === false && con.current_type === '') ? 'borderRed' : ''}`}
                        styles={{
                            container: (provided) => ({
                                ...provided,
                                width: '100%'
                            }),
                            control: (provided) => ({
                                ...provided,
                                width: '100%'
                            })
                        }}
                    />
                </div>
                {con.current_type !== "No Control" && (<>
                    <div className="col-3">
                        <p>Purpose of Control</p>
                        <Select
                            options={controlType}
                            value={controlType.find(option => option.value === con.method)} // Set current value
                            onChange={(e) => onMethodOn(e.value, i, 'current_control')} // Handle onChange
                            className={`d-flex ${(required === false && con.current_type === '') ? 'borderRed' : ''}`}
                            styles={{
                                container: (provided) => ({
                                    ...provided,
                                    width: '100%'
                                }),
                                control: (provided) => ({
                                    ...provided,
                                    width: '100%'
                                })
                            }}
                        />
                    </div>
                    <div className="col-5">
                        <p>Provide a brief description of the control that is implemented  </p>
                        <InputTextarea
                            style={{ width: '100%' }}
                            value={con.value}

                            onChange={(e) => onConseqText(e.target.value, i, 'current_control')}
                            className={`${(required === false && con.value === '') ? 'borderRed' : ''}`}
                            autoResize
                        />
                    </div>
                    <div className="col-1 text-center">
                        <i className="pi pi-trash mb-3" onClick={() => onDeleteConseq(i, 'current_control')}></i>
                    </div>
                </>)}
            </div>
            <div className='row mt-4 mb-4'>

                <div className="d-flex flex-wrap justify-content-end gap-3">
                    <div className="d-flex align-items-center">
                        <Checkbox
                            inputId="ingredient1"
                            name="pizza"
                            value="required"
                            onChange={()=>onConseqRequired(con.required,i,'current_control','required')}
                            checked={con.required}
                            style={{ marginRight: '8px' }} // Option A: space on the checkbox
                        />
                        <label
                            htmlFor="ingredient1"
                            style={{ marginLeft: '8px' }}   // Option B: space on the label
                        >
                            Required
                        </label>
                    </div>

                    <div className="d-flex align-items-center">
                        <Checkbox
                            inputId="ingredient1"
                            name="pizza"
                            value="required"
                            onChange={()=>onConseqRequired(con.validity,i,'current_control','validity')}
                            checked={con.validity}
                            style={{ marginRight: '8px' }} // Option A: space on the checkbox
                        />
                        <label
                            htmlFor="ingredient1"
                            style={{ marginLeft: '8px' }}   // Option B: space on the label
                        >
                            Validity
                        </label>
                    </div>
                </div>
            </div>
            {con.current_type !== "No Control" && (<>

                <div className="col-12 mt-3">
                    <label htmlFor="imageUploads" className="mb-2">{type === 'hazard' ? 'Where applicable, upload images that illustrate the controls to be implemented for this Critical High Risk Activity. These images will help enhance understanding and communication of the controls to personnel involved in managing the associated risks' : 'Upload relevant images to visually represent the current controls identified here. These images will be used in other modules as appropriate.'}</label>
                    <div className="mb-3">
                        <DropzoneArea
                            acceptedFiles={['image/jpeg', 'image/png']}
                            dropzoneText={"Drag 'n' drop some files here, or click to select files"}
                            filesLimit={5}
                            maxFileSize={104857600}
                            onChange={(files) => handleTaskFileChange(files, i, 'current_control')}
                            showPreviewsInDropzone={false}
                            showPreviews={false}
                            dropzoneClass={'dropzoneText d-flex align-items-center justify-content-center '}
                        />
                    </div>
                </div>

                <div className="col-12 mt-3">
                    <label htmlFor="uploaded" className="mb-2">Uploaded</label>
                </div>
                <div className="col-12 mt-3 mb-3">
                    <div className="row">
                        {con.files && con.files.map((item, m) => (
                            <div key={m} className="col-3  " style={{ position: 'relative' }}>
                                <div className="boxShadow d-flex align-items-center justify-content-center" >
                                    <ImageComponent fileName={item} size={'100'} name={true} />
                                    <i
                                        className="pi pi-trash"
                                        onClick={() => handleRemoveMainImage(m, i, 'current_control')}
                                        style={{
                                            position: 'absolute',
                                            top: '5px',
                                            right: '5px',
                                            cursor: 'pointer',
                                            color: 'red',
                                        }}
                                    />
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </>)}
        </>
    );
};

export default CurrentControl;
