import React, { useState, useEffect } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { MultiSelect } from 'primereact/multiselect';
import { Dialog } from 'primereact/dialog';
import moment from 'moment';
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";
// import 'primereact/resources/themes/lara-light-indigo/theme.css';
import 'primeicons/primeicons.css';
const ClosedActions = () => {
    const [closedActions, setClosedActions] = useState([]);
    const [viewDialogVisible, setViewDialogVisible] = useState(false);
    const [selectedAction, setSelectedAction] = useState(null);

    useEffect(() => {
        const dummy = [
            {
                id: 'FindingID-201',
                category: 'Environment',
                checklist: 'Waste Management Audit',
                owner: 'Rahul K.',
                location: 'KCH > Storage Yard',
                dueDate: '2025-04-25',
                actualCompletionDate: '2025-04-24'
            },
            {
                id: 'FindingID-202',
                category: 'Safety',
                checklist: 'Site PPE Audit',
                owner: '<PERSON>sha <PERSON>.',
                location: 'KTV > Site A',
                dueDate: '2025-04-23',
                actualCompletionDate: '2025-04-25'
            }
        ];
        setClosedActions(dummy);
    }, []);

    const categories = [...new Set(closedActions.map(c => c.category))].map(c => ({ label: c, value: c }));
    const owners = [...new Set(closedActions.map(c => c.owner))].map(o => ({ label: o, value: o }));
    const locations = [...new Set(closedActions.map(c => c.location))].map(l => ({ label: l, value: l }));

    const getTimelinessStatus = (row) => {
        const due = moment(row.dueDate);
        const actual = moment(row.actualCompletionDate);

        if (actual.isSameOrBefore(due)) {
            return <span className="badge bg-success">Closed within time</span>;
        } else {
            return <span className="badge bg-danger">Closed with delay</span>;
        }
    };

    const idTemplate = (row) => (
        <span className="text-primary" style={{ cursor: 'pointer' }} onClick={() => handleView(row)}>
            {row.id}
        </span>
    );

    const handleView = (row) => {
        setSelectedAction(row);
        setViewDialogVisible(true);
    };

    return (
        <>
            <div className="d-flex justify-content-between align-items-center mb-2">
                <h5>Closed Actions</h5>
                <div>
                    <button className="btn btn-outline-secondary">
                        <i className="pi pi-download"></i> CSV Download
                    </button>
                </div>
            </div>

            <DataTable value={closedActions} paginator rows={5}>
                <Column field="id" header="Finding ID" body={idTemplate} />
                <Column field="category" header="Inspection Category" filter filterElement={(options) => (
                    <MultiSelect value={options.value} options={categories} onChange={(e) => options.filterCallback(e.value)} placeholder="Any" />
                )} />
                <Column field="checklist" header="Inspection Checklist" filter filterPlaceholder="Search Checklist" />
                <Column field="owner" header="Assigned Action Owner" filter filterElement={(options) => (
                    <MultiSelect value={options.value} options={owners} onChange={(e) => options.filterCallback(e.value)} placeholder="Any" />
                )} />
                <Column field="location" header="Location" filter filterElement={(options) => (
                    <MultiSelect value={options.value} options={locations} onChange={(e) => options.filterCallback(e.value)} placeholder="Any" />
                )} />
                <Column field="dueDate" header="Due Date" sortable body={(row) => moment(row.dueDate).format('DD-MM-YYYY')} />
                <Column field="actualCompletionDate" header="Actual Completion Date" sortable body={(row) => moment(row.actualCompletionDate).format('DD-MM-YYYY')} />
                <Column header="Timeliness of Closure" body={getTimelinessStatus} />
            </DataTable>

            <Dialog header="Closed Action Details" visible={viewDialogVisible} style={{ width: '40vw' }} modal onHide={() => setViewDialogVisible(false)}>
                {selectedAction && (
                    <div>
                        <p><strong>ID:</strong> {selectedAction.id}</p>
                        <p><strong>Category:</strong> {selectedAction.category}</p>
                        <p><strong>Checklist:</strong> {selectedAction.checklist}</p>
                        <p><strong>Owner:</strong> {selectedAction.owner}</p>
                        <p><strong>Location:</strong> {selectedAction.location}</p>
                        <p><strong>Due Date:</strong> {moment(selectedAction.dueDate).format('DD-MM-YYYY')}</p>
                        <p><strong>Completion Date:</strong> {moment(selectedAction.actualCompletionDate).format('DD-MM-YYYY')}</p>
                    </div>
                )}
            </Dialog>
        </>
    );
};

export default ClosedActions;
