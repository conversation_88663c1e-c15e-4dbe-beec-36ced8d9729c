import React from 'react';
import { RadioButton } from 'primereact/radiobutton';

const TaskItem = ({ item, index, openDialog, subActivity, deleteTask, onDragStart, onDrop, onDragOver, type, cellClassName }) => {
    return (
        <div
            className='d-flex align-items-center mb-3'
            onDrop={(e) => onDrop(e, index)}
            onDragOver={(e) => onDragOver(e)}
        >
            {/* Numbered Circle */}
            <div className='d-flex justify-content-center align-items-center me-3' style={{ width: '40px' }}>
                <span className='number-circle'>
                    {index + 1}
                </span>
            </div>

            {/* Main content box with draggable and stages */}
            <div className={`subBox p-3 flex-grow-1 d-flex align-items-center justify-content-between me-3 ${cellClassName(item[4].severity * item[4].likelyhood)}`}>
                {/* Left section with drag handle and name */}

                <div className='d-flex align-items-center col-5' >
                    {/* Drag icon with draggable functionality */}
                    {!type &&
                        <span
                            className="drag-handle"
                            style={{ cursor: 'move' }}
                            draggable
                            onDragStart={(e) => onDragStart(e, index)}
                        >
                            <svg fill="none" height="30" viewBox="0 0 24 24" width="30" xmlns="http://www.w3.org/2000/svg"><g fill="rgb(0,0,0)"><path d="m8.5 7c1.10457 0 2-.89543 2-2s-.89543-2-2-2-2 .89543-2 2 .89543 2 2 2z" /><path d="m8.5 14c1.10457 0 2-.8954 2-2s-.89543-2-2-2-2 .8954-2 2 .89543 2 2 2z" /><path d="m10.5 19c0 1.1046-.89543 2-2 2s-2-.8954-2-2 .89543-2 2-2 2 .8954 2 2z" /><path d="m15.5 7c1.1046 0 2-.89543 2-2s-.8954-2-2-2-2 .89543-2 2 .8954 2 2 2z" /><path d="m17.5 12c0 1.1046-.8954 2-2 2s-2-.8954-2-2 .8954-2 2-2 2 .8954 2 2z" /><path d="m15.5 21c1.1046 0 2-.8954 2-2s-.8954-2-2-2-2 .8954-2 2 .8954 2 2 2z" /></g></svg>
                        </span>
                    }
                    <h6 className='m-0 ps-2 pointer text-decoration-underline text-secondary' onClick={() => openDialog(item, index)}>
                        {item[0].name.length > 45 ? `${item[0].name.slice(0, 45)} ....` : item[0].name}
                    </h6>
                </div>


                {/* Right section with stages */}
                <div className='d-flex align-items-center justify-content-start col-7' >
                    {item[9].level.map((level, l) => {
                        // Map the stage in item[9].level to its corresponding key in item[11].value
                        const statusMap = {
                            "Hazards Identification": "hazardsIdentification",
                            "Consequences": "consequences",
                            "Current Controls": "currentControls",
                            "Risk Estimation": "riskEstimation",
                            "Additional Controls": "additionalControls"
                        };

                        // Get the status for the current level
                        const statusKey = statusMap[level];
                        const stageStatus = item[11].value[statusKey];

                        // Determine the class based on the status
                        const statusClass = stageStatus === "completed"
                            ? 'completed'
                            : stageStatus === "inprogress"
                                ? 'in-progress'
                                : ''; // Default (empty) if no status

                        // Determine the color class for the radio button based on status
                        const radioButtonColorClass = stageStatus === "completed"
                            ? 'radio-green'
                            : stageStatus === "inprogress"
                                ? 'radio-yellow'
                                : ''; // No color for other statuses

                        return (
                            <div key={l} className="d-flex align-items-center me-3">
                                {/* Check if the current stage is completed or in progress */}
                                <div className={`custom-radio ${radioButtonColorClass}`}>
                                    <RadioButton checked={stageStatus === "completed"} />
                                </div>
                                <span className={`ms-2 ${statusClass}`}>
                                    {level}
                                </span>
                            </div>
                        );
                    })}
                </div>

            </div>

            {/* Delete Icon */}
            {!type &&
                <div className='d-flex justify-content-center align-items-center trash-icon-container' style={{ width: '40px', height: '40px' }}>
                    <i className="pi pi-trash" onClick={(e) => deleteTask(e, index)}></i>
                </div>
            }
        </div>

    );
};

export default TaskItem;
