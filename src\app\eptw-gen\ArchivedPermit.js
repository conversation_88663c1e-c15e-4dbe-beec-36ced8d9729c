import React, { useEffect, useState, useCallback, useRef } from 'react'
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";
// import 'primereact/resources/themes/lara-light-indigo/theme.css';
import 'primeicons/primeicons.css';
import { DropzoneArea } from 'material-ui-dropzone';
import { Dialog } from 'primereact/dialog';
import { TabView, TabPanel } from 'primereact/tabview';
import { Stepper, Step, StepLabel, Typography } from '@mui/material';
import { Calendar } from 'primereact/calendar';
import Accordion from 'react-bootstrap/Accordion';
import { useAccordionButton } from 'react-bootstrap/AccordionButton';
import AccordionContext from 'react-bootstrap/AccordionContext';
import { RadioButton } from 'primereact/radiobutton';
import { Dropdown } from 'primereact/dropdown';
import { Button } from 'primereact/button';
import { MultiSelect } from 'primereact/multiselect';
import { InputText } from 'primereact/inputtext'
import { Checkbox } from 'primereact/checkbox';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { FilterMatchMode, FilterOperator } from 'primereact/api';
import { InputTextarea } from 'primereact/inputtextarea';
import { Form } from 'react-bootstrap';
import Select from 'react-select';
import { GMS1_URL, INCIDENT, GET_USER_ROLE_BY_MODE, FILE_DOWNLOAD, HAZARDS_CATEGOTY, GET_ALL_USER, DROPDOWNS, WORK_ACTIVITIES_URL, FILE_URL, INCIDENT_WITH_ID, INVERSTIGATION_TRIGGER, ADMINDROPDOWNS, PERMIT_REPORTS, CHANGE_PERMIT_STATUS } from '../constants';
import API from '../services/API';
import AllFilterLocation from '../investigation/LocationDropDown';
import ImageComponent from '../services/FileDownlodS3';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import Swal from 'sweetalert2';
import { Modal, Nav } from 'react-bootstrap';
import moment from 'moment';
import SignatureCanvas from "react-signature-canvas";
import JoditEditor from 'jodit-react';
import 'jodit/build/jodit.min.css'; // Import Jodit CSS
import { useSelector } from 'react-redux';
import ViewEptw from './Component/ViewEptw';
import PermitModal from './Component/PermitModal';
import { Button as Button1 } from 'primereact/button';
import axios from 'axios';

import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import pdfMake from 'pdfmake/build/pdfmake';
import pdfFonts from 'pdfmake/build/vfs_fonts';
import PDFExport from './Component/generateEptwPdf';
import MyLogoComponent from './Component/MyLogoComponent';
pdfMake.vfs = pdfFonts.pdfMake.vfs;
const customSwal2 = Swal.mixin({
    customClass: {
        confirmButton: 'btn btn-primary',

    },
    buttonsStyling: false
})
const applicantOptions = [
    { value: 'applicant1', label: 'Applicant 1' },
    { value: 'applicant2', label: 'Applicant 2' },
    { value: 'applicant3', label: 'Applicant 3' }
];
const initialFormData = {
    "uploads": [],
    "supportingDocuments": [],
    "permitStartDate": "",
    "permitEndDate": "",
    "workDescription": "",
    "nameOfSiteSupervisor": "",
    "applicantContactNo": "",
    "supervisorContactNo": "",
    "workOrderNo": "",
    "noOfWorkers": 0,
    "permitWorkType": "",
    "permitType": "",
    "permitTag": "",
    "status": "",
    "permitRiskControl": [],
    // "applicantId": "",
    "assessorId": "",
    "approverId": "",
    "locationFiveId": "",
    "locationFourId": "",
    "locationOneId": "",
    "locationSixId": "",
    "locationThreeId": "",
    "locationTwoId": "",
    "reviewerId": "",
    "applicantStatus": {
        "signature": ""
    },
}
const ArchivedPermit = ({
    data = [],
    totalRecords = 0,
    currentPage = 1,
    rows = 10,
    onPageChange,
    onFilterUpdate,
    from }) => {
    const signRef = useRef()
    const user = useSelector((state) => state.login.user);

    const [userRoles, setUserRoles] = useState([]);
    const editor = useRef(null);
    const [activeTabIndex, setActiveTabIndex] = useState(0);
    const [lead, setLead] = useState([])
    const [leadShow, setLeadShow] = useState(false)
    const [attachShow, setAttachShow] = useState(false)
    const [show, setShow] = useState(false);
    const [viewShow, setViewShow] = useState(false)
    const [risk, setRisk] = useState([])
    const [hazards, setHazards] = useState([])
    const [reviewer, setReviewer] = useState([])
    const [formData, setFormData] = useState(initialFormData);
    const [filters, setFilters] = useState({
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        'applicant.firstName': { value: null, matchMode: FilterMatchMode.IN },
        'assessor.firstName': { value: null, matchMode: FilterMatchMode.IN },
        'approver.firstName': { value: null, matchMode: FilterMatchMode.IN },
        'reviewer.firstName': { value: null, matchMode: FilterMatchMode.IN },
        status: { value: null, matchMode: FilterMatchMode.IN },
        'assignee.firstName': { value: null, matchMode: FilterMatchMode.IN },
        nextdate: { value: null, matchMode: FilterMatchMode.IN },
        permitType: { value: null, matchMode: FilterMatchMode.CUSTOM },
        status: { value: null, matchMode: FilterMatchMode.IN },
        captain: { value: null, matchMode: FilterMatchMode.IN },
        department: { value: null, matchMode: FilterMatchMode.IN },
    });
    const [errors, setErrors] = useState({});
    const [leadAssign, setLeadAssign] = useState(null);
    const [investigationId, setInvestigationId] = useState(null)
    const [requiresInvestigation, setRequiresInvestigation] = useState(false);
    const [actionModal, setActionModal] = useState(false);
    const [current, setCurrent] = useState(null);
    const [maskId, setMaskId] = useState('');
    const [totalAction, setTotalAction] = useState([]);
    const [permitTypeOptions, setPermitTypeOptions] = useState([]);
    const [activeStage, setActiveStage] = useState(0); // Default stage for title and date
    const stages = ['Precursors', 'Hazardous Conditions', 'Preventive Controls', 'Incident Details', 'Mitigative Controls', 'Consequences'];
    const [remarks, setRemarks] = useState('');
    const [statusToUpdate, setStatusToUpdate] = useState('');
    const [remarksShow, setRemarksShow] = useState(false)
    const [error, setError] = useState('');
    const [status, setStatus] = useState([])
    const [applicant, setApplicant] = useState([])
    const [assessor, setAssessor] = useState([])
    const [approver, setApprover] = useState([])
    const [type, setType] = useState([])
    const [logo, setLogo] = useState('')

    const workTypeOptions = [
        { value: 'High-Risk Hazard', label: 'High-Risk Hazard' },
        { value: 'Routine', label: 'Routine' }
    ];

    const first = (currentPage - 1) * rows;

    // Called by PrimeReact DataTable when user clicks next/previous, changes rows, etc.
    const handlePage = (event) => {
        // event.first is the 0-based index of the first row
        // event.rows is the new rows per page
        // Convert 0-based index to a 1-based page
        const newPage = Math.floor(event.first / event.rows) + 1;
        const newRows = event.rows;
        onPageChange(newPage, newRows); // notify parent
    };

    useEffect(() => {
        if (data) {
            getAllIncident();
            // getCrewList();
        }
        const rolesToCheck = ['eptwApplicant', 'eptwAssessor', 'eptwReviewer', 'eptwApprover'];
        const matchedRoles = getUserRoles(user.roles, rolesToCheck);
        console.log(matchedRoles)
        setUserRoles(matchedRoles); // Update the state with matched roles
    }, [data])


    const getUserRoles = (userRoles, rolesToCheck) => {
        // Filter roles that match the rolesToCheck
        return userRoles.filter(role => rolesToCheck.includes(role.maskId));
    };



    const getAllIncident = () => {
        setRisk(data)
        getFetchLogo()

        const obs = data.map(item => {
            return { name: item.status || '', value: item.status || '' }
        })
        setStatus(obs.filter((ele, ind) => ind === obs.findIndex(elem => elem.value === ele.value && elem.name === ele.name)))

        const obs1 = data.map(item => {
            return { name: item.applicant?.firstName || '', value: item.applicant?.firstName || '' }
        })
        setApplicant(obs1.filter((ele, ind) => ind === obs1.findIndex(elem => elem.value === ele.value && elem.name === ele.name)))
        const obs2 = data.map(item => {
            return { name: item.assessor?.firstName || '', value: item.assessor?.firstName || '' }
        })
        setAssessor(obs2.filter((ele, ind) => ind === obs2.findIndex(elem => elem.value === ele.value && elem.name === ele.name)))

        const obs3 = data.map(item => {
            return { name: item.approver?.firstName || '', value: item.approver?.firstName || '' }
        })
        setApprover(obs3.filter((ele, ind) => ind === obs3.findIndex(elem => elem.value === ele.value && elem.name === ele.name)))

        const obs4 = data.map(item => {
            return { name: item.reviewer?.firstName || '', value: item.reviewer?.firstName || '' }
        })
        setReviewer(obs4.filter((ele, ind) => ind === obs4.findIndex(elem => elem.value === ele.value && elem.name === ele.name)))

        const obs5 = data.map(item => {
            return { name: item.permitType[0] || '', value: item.permitType[0] || '' }
        })
        setType(obs5.filter((ele, ind) => ind === obs5.findIndex(elem => elem.value === ele.value && elem.name === ele.name)))
    };

    useEffect(() => {
        if (formData.permitWorkType === 'High-Risk Hazard') {
            fetchPermitTypeOptions('high-risk-hazard-list');
        } else if (formData.permitWorkType === 'Routine') {
            fetchPermitTypeOptions('routine-list');
        }
    }, [formData.permitWorkType]);


    const fetchPermitTypeOptions = async (endpoint) => {
        try {
            const response = await API.get(`/${endpoint}`);
            const options = response.data.map((item) => ({
                value: item.hazardName,
                label: item.hazardName,
                controls: item.controls
            }));
            setPermitTypeOptions(options);
        } catch (error) {
            console.error("Error fetching permit type options:", error);
        }
    };



    const renderHeader = () => {
        const value = filters['global'] ? filters['global'].value : '';

        return (

            <div className='d-flex justify-content-end'>


            </div>
        );
    };
    const header = renderHeader()

    const handleClose = () => setShow(false);


    const dateBodyTemplate = (row) => {
        return moment(row.created).format('DD-MM-YYYY')
    }

    const [record, setRecord] = useState([])
    const openViewModal = (row) => {

        console.log(row)
        setRecord(row)
        setViewShow(true)
    }
    const maskBodyTemplate = (row) => {
        return <div className='maskid' onClick={() => openViewModal(row)}>{row.maskId}</div>
    }

    const locationBodyTemplate = (row) => {
        if (row.isCustomLocation) {
            return row.customLocation;
        } else {
            return (
                <>

                    {row?.locationOne?.name && (
                        <>
                            {row.locationOne.name}
                            {row.locationTwo?.name && ' > '}
                        </>
                    )}
                    {row?.locationTwo?.name && (
                        <>
                            {row.locationTwo.name}
                            {row.locationThree?.name && ' > '}
                        </>
                    )}
                    {row?.locationThree?.name && (
                        <>
                            {row.locationThree.name}
                            {row.locationFour?.name && ' > '}
                        </>
                    )}
                    {row?.locationFour?.name && row.locationFour.name}
                </>
            );
        }
    };

    const handleFilterChange = (filteredData) => {


        // Update the count of filtered data in the parent component
        if (onFilterUpdate) {
            onFilterUpdate(filteredData.length);
        }
    };
    const dataURItoFile = (dataURI, filename) => {
        var byteString = atob(dataURI.split(",")[1]);
        // separate out the mime component
        var mimeString = dataURI.split(",")[0].split(":")[1].split(";")[0];
        // write the bytes of the string to an ArrayBuffer
        var ab = new ArrayBuffer(byteString.length);
        var dw = new DataView(ab);
        for (var i = 0; i < byteString.length; i++) {
            dw.setUint8(i, byteString.charCodeAt(i));
        }

        // write the ArrayBuffer to a blob, and you're done
        return new File([ab], filename, { type: mimeString });
    };


    const handleSubmit = async () => {
        // Validate remarks
        if (!remarks.trim()) {
            setError('Remarks are required.');
            return;
        }

        // Placeholder for the API call
        console.log("Calling API with status:", statusToUpdate, "and remarks:", remarks);
        const payload = {
            remarks: remarks,
            status: statusToUpdate
        }
        const response = await API.patch(CHANGE_PERMIT_STATUS(record.id), payload);

        if (response.status === 204) {
            Swal.fire('Success', "Updated Successfully", "success")

            window.location.reload();
        }
        // After API call, close the modal
        setRemarksShow(false);
        setRemarks('');
    };
    const handleButtonClick = (status) => {
        setStatusToUpdate(status); // Set the status that will be sent to the API
        setRemarksShow(true); // Open the modal for entering remarks
    };

    const representativesItemTemplate = (option) => {
        return (
            <div className="flex align-items-center gap-2">

                <span>{option.value}</span>
            </div>
        );
    };
    const statusFilterTemplate = (options) => {

        return (
            <React.Fragment>
                <div className="mb-3 font-bold">Type</div>
                <MultiSelect value={options.value} options={status} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }
    const applicantFilterTemplate = (options) => {

        return (
            <React.Fragment>
                <div className="mb-3 font-bold">Type</div>
                <MultiSelect value={options.value} options={applicant} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }
    const assessorFilterTemplate = (options) => {

        return (
            <React.Fragment>
                <div className="mb-3 font-bold">Type</div>
                <MultiSelect value={options.value} options={assessor} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }
    const approverFilterTemplate = (options) => {

        return (
            <React.Fragment>
                <div className="mb-3 font-bold">Type</div>
                <MultiSelect value={options.value} options={approver} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }
    const reviewerFilterTemplate = (options) => {

        return (
            <React.Fragment>
                <div className="mb-3 font-bold">Type</div>
                <MultiSelect value={options.value} options={reviewer} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }

    const typeFilterTemplate = (options) => {
        return (
            <MultiSelect
                value={options.value}
                options={type}
                itemTemplate={representativesItemTemplate}
                onChange={(e) => {
                    options.filterCallback(e.value);
                }}
                optionLabel="name"
                placeholder="Select Permit Types"
                className="p-column-filter"
            />
        );
    };

    const getFetchLogo = async () => {
        try {
            const response = await axios.get(FILE_DOWNLOAD(localStorage.getItem('logo')), {
                headers: {
                    'Content-Type': 'application/json'
                },
            });
            const data = response.data;
            const logoUrl = data // Assuming the API returns an object with a `url` field
            setLogo(logoUrl);

        } catch (error) {
            console.error('Error fetching logo:', error);
        }
    }

    const startDateBodyTemplate = (rowData) => {
        return <span>{moment(rowData.permitStartDate).format('DD-MM-YYYY hh:mm A')}</span>;
    };

    // Custom template for End Date
    const endDateBodyTemplate = (rowData) => {
        return <span>{moment(rowData.permitEndDate).format('DD-MM-YYYY hh:mm A')}</span>;
    };

    const typeBodyTemplate = (row) => {
        return row.permitType?.join(", ") || "N/A"; // Convert array to a string
    };

    const permitTypeFilter = (value, filter) => {
        if (!filter || filter.length === 0) return true; // If no filter, show all
        if (!value) return false; // If no value, do not match
        return filter.some((f) => value.includes(f)); // Check if any filter value is in the data
    };
    const generatePdf = () => {


        const input = document.getElementById('pdf-content');

        // Hide elements temporarily
        const downloadButton = document.querySelector('.btn-download'); // Adjust selector if needed
        const modalFooter = document.querySelector('.modal-footer');

        if (downloadButton) downloadButton.style.display = 'none';
        if (modalFooter) modalFooter.style.display = 'none';

        const pdf = new jsPDF('p', 'mm', 'a4');
        const pdfWidth = pdf.internal.pageSize.getWidth();
        const pdfHeight = pdf.internal.pageSize.getHeight();
        const marginBottom = 10; // margin at the bottom of each page

        html2canvas(input).then((canvas) => {
            const imgData = canvas.toDataURL('image/png');
            const imgProps = pdf.getImageProperties(imgData);
            const imgHeight = (imgProps.height * pdfWidth) / imgProps.width;

            let heightLeft = imgHeight;
            let position = 0;

            pdf.addImage(imgData, 'PNG', 0, position, pdfWidth, imgHeight);
            heightLeft -= pdfHeight;

            while (heightLeft >= 0) {
                position = heightLeft - imgHeight + marginBottom;
                pdf.addPage();
                pdf.addImage(imgData, 'PNG', 0, position, pdfWidth, imgHeight);
                heightLeft -= pdfHeight;
            }

            // Restore elements' visibility
            if (downloadButton) downloadButton.style.display = '';
            if (modalFooter) modalFooter.style.display = '';

            pdf.save(`${record.maskId + '-' + new Date()}.pdf`);
        });
    };

    const handleValueChange = (filteredData) => {
        if (onFilterUpdate) {
          onFilterUpdate(filteredData.length);
        }
      };
    return (
        <>
            <div>
                <div className="row  ">
                    <div className="col-12">
                        <div className="card">
                            <div className="card-body p-0">
                                <div className="row">
                                    <div className="col-12">
                                        <div>
                                            <>
                                                <DataTable
                                                       value={risk}
                                                       lazy // indicates we do server-side pagination
                                                       paginator
                                                       totalRecords={totalRecords}
                                                       first={first}
                                                       rows={rows}
                                                       onPage={handlePage}
                                                    //    onValueChange={handleValueChange}
                                                       paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                                                       rowsPerPageOptions={[10, 25, 50]}
                                                       emptyMessage="No archived permits found."
                                                       tableStyle={{ minWidth: '50rem' }}
                                                    >

                                                    <Column field="maskId" header="ID" body={maskBodyTemplate} sortable ></Column>
                                                    <Column field="status" header="Status" filterElement={statusFilterTemplate} showFilterMatchModes={false} filter ></Column>
                                                    <Column
                                                        field="permitType"
                                                        header="Permit"
                                                        body={typeBodyTemplate}
                                                        filter
                                                        filterMatchMode="custom"
                                                        filterFunction={permitTypeFilter}
                                                        filterElement={typeFilterTemplate}
                                                        showFilterMatchModes={false}
                                                    />

                                                    <Column field="permitStartDate" header="Start Date" body={startDateBodyTemplate}></Column>
                                                    <Column field="permitEndDate" header="End Date" body={endDateBodyTemplate}></Column>
                                                    <Column field="created" header="Submitted Date" body={dateBodyTemplate} sortable ></Column>
                                                    <Column field="" header="Location" body={locationBodyTemplate} ></Column>
                                                    <Column field="applicant.firstName" header="Applicant" filterElement={applicantFilterTemplate} showFilterMatchModes={false} filter  ></Column>
                                                    <Column field="reviewer.firstName" header="Reviewer" filterElement={reviewerFilterTemplate} showFilterMatchModes={false} filter  ></Column>
                                                    <Column field="assessor.firstName" header="Assessor" filterElement={assessorFilterTemplate} showFilterMatchModes={false} filter  ></Column>
                                                    <Column field="approver.firstName" header="Approver" filterElement={approverFilterTemplate} showFilterMatchModes={false} filter  ></Column>


                                                </DataTable>
                                            </>

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>

            {show &&
                <PermitModal show={show} handleClose={handleClose} initialFormData={initialFormData} isEditMode={false} type={'permit'} />
            }


            {viewShow &&
                <Modal show={viewShow} onHide={() => setViewShow(false)} size="lg" id="pdf-content">
                    <Modal.Header closeButton>
                        {record && (
                            <div className="row" style={{ width: '100%' }}>
                                <div className="col-9">
                                    <div className="row">
                                        <div className="col-3" style={{ borderRight: '1px solid #D1D5DB' }}>
                                            {/* <img src={logo} className="me-3" alt="logo" style={{ maxWidth: '125px' }} /> */}
                                            <MyLogoComponent logo={logo} />
                                        </div>
                                        <div className="col-9">
                                            <h4>Permit to Work</h4>
                                            <div className="d-flex align-items-center">
                                                <p className="me-2">#{record.maskId || ''} </p>
                                                <p className="card-eptw">{record.status} </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className="col-3 d-flex justify-content-end align-items-center">
                                    {(record.status === "Active" || record.status === "Closed" || record.status === 'Acknowledged & Closed') && (
                                        <Button1 type="button" className='btn-download' label="Download Permit" outlined icon="pi pi-download"
                                            onClick={generatePdf}

                                        />
                                    )}
                                </div>

                            </div>
                        )}
                    </Modal.Header>
                    <Modal.Body>
                        <ViewEptw applicationDetails={record} />
                    </Modal.Body>
                    <Modal.Footer>

                        {!['Withdrawn', 'Closed', 'Suspended'].includes(record.status) && <>
                            {userRoles.some(role => ['eptwApplicant'].includes(role.maskId)) && record.status === 'Active' && record.applicantId === user.id && (
                                <Button severity="success" label="Closeout" onClick={() => handleButtonClick('Closed')} />
                            )}

                            {userRoles.some(role => ['eptwReviewer', 'eptwAssessor', 'eptwApprover'].includes(role.maskId)) &&
                                (record.approverId === user.id || record.assessorId === user.id || record.reviewerId === user.id) &&
                                record.status === 'Active' && (
                                    <Button severity="warning" label="Suspend" onClick={() => handleButtonClick('Suspended')} />
                                )
                            }


                            {userRoles.some(role => ['eptwApplicant'].includes(role.maskId)) && record.applicantId === user.id && (
                                <Button severity="danger" label="Withdraw" onClick={() => handleButtonClick('Withdrawn')} />
                            )}
                        </>}

                        <Button severity="secondary" label="Close" onClick={() => setViewShow(false)} />

                    </Modal.Footer>
                </Modal>
            }

            <Modal show={remarksShow} onHide={() => setRemarksShow(false)}>
                <Modal.Header closeButton>
                    <Modal.Title>Provide Remarks</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <Form.Group controlId="remarksTextarea">
                        <Form.Label>Please provide remarks for this action:</Form.Label>
                        <Form.Control
                            as="textarea"
                            rows={4}
                            value={remarks}
                            onChange={(e) => {
                                setRemarks(e.target.value);
                                setError(''); // Clear error when user starts typing
                            }}
                            placeholder="Enter your remarks here"
                            isInvalid={!!error}
                        />
                        <Form.Control.Feedback type="invalid">
                            {error}
                        </Form.Control.Feedback>
                    </Form.Group>
                </Modal.Body>
                <Modal.Footer>
                    <Button label="Submit" className="p-button-primary" onClick={handleSubmit} />
                    <Button label="Cancel" className="p-button-secondary" onClick={() => setRemarksShow(false)} />
                </Modal.Footer>
            </Modal>
        </>
    )
}

export default ArchivedPermit