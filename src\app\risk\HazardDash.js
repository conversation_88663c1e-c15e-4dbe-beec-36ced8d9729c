import React, { Component, useState, useEffect, useRef } from 'react'
import { Col, Row, Nav, Tabs, Tab } from 'react-bootstrap';
import Tabsss from '@mui/material/Tabs';
import Tabss from '@mui/material/Tab';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import { RISKASSESSMENT_LIST, RISK_WITH_ID_URL } from "../constants";
import API from "../services/API";
import HazardBased from './HazardBased';
import HazardAmend from './HazardAmend';
import * as Icon from 'feather-icons-react';
import { useSelector } from "react-redux";
import Swal from "sweetalert2";
import { Dropdown } from 'react-bootstrap';
import Editable from "react-bootstrap-editable";
import { Form, Modal, Button } from "react-bootstrap";
import { useHistory } from 'react-router-dom/cjs/react-router-dom.min';

function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      className='col-10'
      role="tabpanel"
      hidden={value !== index}
      id={`vertical-tabpanel-${index}`}
      aria-labelledby={`vertical-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3, color: '#101828' }}>
          <Typography>{children}</Typography>
        </Box>
      )}
    </div>
  );
}

function a11yProps(index) {
  return {
    id: `vertical-tab-${index}`,
    'aria-controls': `vertical-tabpanel-${index}`,
  };
}

const customSwal2 = Swal.mixin({
  customClass: {
    confirmButton: 'btn btn-primary',

  },
  buttonsStyling: false
})

const customSwal = Swal.mixin({
  customClass: {
    confirmButton: 'btn btn-danger',
    cancelButton: 'btn btn-light'
  },
  buttonsStyling: false
})
const HazardDash = () => {
  const user = useSelector((state) => state.login.user)
  const history = useHistory()
  const title = useRef();
  const itemName = useRef();
  const [access, setAccess] = useState(false)
  const [value, setValue] = useState(0);
  const [risk, setRisk] = useState([]);
  const [hazardId, setHazardId] = useState('')
  const [details, setDetails] = useState([])
  const [showModal, setShowModal] = useState(false)
  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  useEffect(() => {
    getPermit()

  }, [])
  const getPermit = async () => {
    const uriString = { include: ["user"] };

    const url = `${RISKASSESSMENT_LIST}?filter=${encodeURIComponent(
      JSON.stringify(uriString)
    )}`;
    const response = await API.get(url);
    if (response.status === 200) {

      response.data = response.data.filter(item =>
        item.type.label === 'Hazard-Based'
      )



      setRisk(response.data.reverse())
      console.log(response.data.reverse())
    }

    if (user.length !== 0) {
      setAccess(user.roles.some(item => item.name === 'RA Team Leader'))
    }

  }

  const onDelete = async (id) => {

    customSwal.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      reverseButtons: true,

      confirmButtonText: 'Delete'
    }).then(async (result) => {
      if (result.isConfirmed) {
        const response = await API.delete(RISK_WITH_ID_URL(id));
        if (response.status === 204) {

          customSwal2.fire(
            'Deleted!',
            '',
            'success'
          )


        }
        getPermit();
      }
    })

  }
  const viewHazard = (item) => {
    setHazardId(item.id)
    setDetails(item)
  }
  const handleCardUpdate = async () => {
    const response = await fetch(RISK_WITH_ID_URL(hazardId), {
      method: "PATCH",
      body: JSON.stringify({
        hazard_name: itemName.current.value
      }),
      headers: { "Content-type": "application/json; charset=UTF-8" },
    });

    if (response.ok) {
      
      customSwal2.fire("Title Updated!", "", "success")
      getPermit();
      setShowModal(false)
    }

  }
  const onCreateHazard = async () => {
    const response = await fetch(RISKASSESSMENT_LIST, {
      method: "POST",
      body: JSON.stringify({
        meetid: "",
        title: "",
        hazard_name: title.current.value,
        activity: "",
        type: { label: "Hazard-Based", value: "Hazard-Based" },
        member: [],
        department: '',
        task: [],
        date: "",
        status: "1",
        teamMemberInvolved: [],
        additionalDates: [],
        captain: user.firstName,
        userId: user.id,

      }),
      headers: { "Content-type": "application/json; charset=UTF-8" },
    });

    if (response.ok) {
      getPermit();
    } else {


    }

  }
  return (
    <>
      <div className="row">
        <div className="col-md-12 grid-margin stretch-card">
          <div className="card">
            <div className="card-body">

              <h4 className="card-title">HIGH-RISK HAZARD CONTROL MEASURES </h4>
              <p className="mb-4">Identify consequences and controls for hazards that are considered as critical by SAGT. This will form as the input for communication to impacted personnel. These controls will also be included in permits that may be needed prior to performing work (Permit-to-Work).</p>

              <div className='row'>
                <div className='col-3'>
                  <div className="card boxShadow">
                    <div className="card-body p-0">

                      <Form.Group className="form-group p-3 mb-0">
                        <div className="input-group">
                          <Form.Control type="text" ref={title} className="form-control mb-0" placeholder="Enter new item to list" aria-label="item" aria-describedby="basic-addon2" />
                          <div className="input-group-append">
                            <button className="btn btn-primary btn-icon mb-0" type="button" onClick={() => onCreateHazard()} ><i className="mdi mdi-plus-circle"></i></button>
                          </div>
                        </div>
                      </Form.Group>
                      <div className="h-250">
                        {
                          risk.map((item) => {
                            return (
                              <div className={`list d-flex justify-content-between align-items-center p-3 border-bottom cursor-pointer ${(hazardId === item.id ? "active" : '')}`} onClick={() => viewHazard(item)}>

                                <div className="content">
                                  <p className="list-name m-0">{item.hazard_name}</p>
                                </div>
                                <div className="options">
                                  <Dropdown variant="p-0">
                                    <Dropdown.Toggle variant="dropdown-toggle p-0 no-caret">
                                      <i className="ti-more-alt"></i>
                                    </Dropdown.Toggle>
                                    <Dropdown.Menu>
                                      <Dropdown.Item onClick={() => setShowModal(true)}><div><i className="typcn typcn-cog me-2"></i> Edit</div></Dropdown.Item>
                                      {access ?
                                        <Dropdown.Item onClick={() => onDelete(item.id)}><div><i className="mdi mdi-delete me-2"></i> Delete</div></Dropdown.Item>
                                        :
                                        <Dropdown.Item onClick={() => customSwal2.fire(
                                          'You Cant Access',
                                          '',
                                          'success'
                                        )}><div><i className="mdi mdi-delete me-2"></i> Delete</div></Dropdown.Item>
                                      }
                                    </Dropdown.Menu>
                                  </Dropdown>
                                </div>
                              </div>
                            )
                          })
                        }


                      </div>





                    </div>

                  </div>
                </div>
                <div className='col-9'>
                  {hazardId !== '' && <>
                    {details.task.length === 0 ?
                      <HazardBased id={hazardId} />
                      :
                      <HazardAmend id={hazardId} name={details.hazard_name} />
                    }
                  </>
                  }
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Modal
        size="md"
        show={showModal}
        onHide={() => setShowModal(false)}
        aria-labelledby="example-modal-sizes-title-lg"
      >
        <Modal.Header closeButton>
          <Modal.Title>Change Title</Modal.Title>
        </Modal.Header>

        <Modal.Body>
          <form className="forms-sample">

            <div className="form-group">
              <label htmlFor="title">Title</label>
              <input type="text" ref={itemName} defaultValue={details.hazard_name} className="form-control p-input" id="title" placeholder="Item Name" />
            </div>

          </form>
        </Modal.Body>

        <Modal.Footer>

          <Button variant="light" onClick={() => setShowModal(false)}>Cancel</Button>
          <Button variant="primary" onClick={handleCardUpdate}>Update Changes</Button>
        </Modal.Footer>
      </Modal>
    </>
  )

}

export default HazardDash
