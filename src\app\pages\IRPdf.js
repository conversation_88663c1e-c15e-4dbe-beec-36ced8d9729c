import { logoBase64 } from "./Base64";
import moment from 'moment'
async function convertImageToBase64(url) {
    const response = await fetch(url);
    const blob = await response.blob();
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => resolve(reader.result);
        reader.onerror = reject;
        reader.readAsDataURL(blob);
    });
}

async function convertAllImages(imageUrls) {
    const base64Images = [];
    for (const url of imageUrls) {
        const base64Image = await convertImageToBase64(url);
        base64Images.push(base64Image);
    }
    return base64Images;
}

const isAnyoneInjured = (person, personnelImpacted) => person.some(person => person && person.injured === true) || personnelImpacted.some(person => person && person.injured === true);

const transformDataForPdfMake = (persons) => {
    const filteredPersons = persons.filter(person => person && person.selectedEmp && person.selectedEmp.name);
    const externalPersons = persons.filter(person => person && !person.internal)
    const transformedData = [];

    // Process filteredPersons
    filteredPersons.forEach(person => {
        transformedData.push({
            name: person.selectedEmp.name,
            employeeId: person.selectedEmp.uniqueId,
            designation: person.selectedEmp.designation,
            injuries: person.injured ? person.injuryParts.join(', ') : "None",
            injuryDetails: person.injuryDetails.map(parts => parts && parts.name).join(', ') || "None",
            ppe: person.isPPE ? person.ppes.map(parts => parts && parts.name).join(', ') : "None"
        });
    });

    // Process externalPersons
    externalPersons.forEach(person => {
        transformedData.push({
            name: person.name,
            employeeId: person.empId,
            designation: person.designation,
            injuries: person.injured ? person.injuryParts.join(', ') : "None",
            injuryDetails: person.injuryDetails.map(parts => parts && parts.name).join(', ') || "None",
            ppe: person.isPPE ? person.ppes.map(parts => parts && parts.name).join(', ') : "None"
        });
    });

    return transformedData;
};

const transformDataForWitness = (persons) => {
    const filteredPersons = persons.filter(person => person && person.selectedEmp && person.selectedEmp.name);
    const externalPersons = persons.filter(person => person && !person.internal)

    const transformedData = [];

    filteredPersons.forEach(person => {
        transformedData.push({
            name: person.selectedEmp.name,
            employeeId: person.selectedEmp.uniqueId,
            designation: person.selectedEmp.designation,
            comments: ""
        });
    });

    // Process externalPersons
    externalPersons.forEach(person => {
        transformedData.push({
            name: person.name,
            employeeId: person.empId,
            designation: person.designation,
            comments: person.comments
        });
    });

    return transformedData;
}

const createPdfMakeWitnessTableBody = (transformedData) => {

    const tableBody = [];

    // Header Row
    tableBody.push([

        {
            text: 'Employee Details',
            border: [false, true, false, true],
            bold: true,
            fillColor: '#dadada',
            margin: [0, 5, 0, 5],
            textTransform: 'uppercase',
        },

        {
            text: 'Comments',
            border: [false, true, false, true],
            bold: true,
            fillColor: '#dadada',
            margin: [0, 5, 0, 5],
            textTransform: 'uppercase',
        },

    ]);

    // Data Rows
    transformedData.forEach(item => {
        tableBody.push([
            {
                border: [true, true, true, true],
                text: `${item.name} (${item.designation}) - ${item.employeeId}`,

                alignment: 'left',

                margin: [0, 5, 0, 5],
            },
            {
                border: [true, true, true, true],
                text: item.comments,

                alignment: 'left',

                margin: [0, 5, 0, 5],
            },
        ]);
    });

    return tableBody;


}

const createPdfMakeTableBody = (transformedData) => {
    const tableBody = [];

    // Header Row
    tableBody.push([
        {
            text: 'Employee Details',
            border: [false, true, false, true],
            bold: true,
            fillColor: '#dadada',
            margin: [0, 5, 0, 5],
            textTransform: 'uppercase',
        },

        {
            text: 'Injuries',
            border: [false, true, false, true],
            bold: true,
            fillColor: '#dadada',
            margin: [0, 5, 0, 5],
            textTransform: 'uppercase',
        },
        {
            text: 'Injury Details',
            border: [false, true, false, true],
            bold: true,
            fillColor: '#dadada',
            margin: [0, 5, 0, 5],
            textTransform: 'uppercase',
        },
        {
            text: 'PPE',
            border: [false, true, false, true],
            bold: true,
            fillColor: '#dadada',
            margin: [0, 5, 0, 5],
            textTransform: 'uppercase',
        }
    ]);

    // Data Rows
    transformedData.forEach(item => {
        tableBody.push([
            {
                border: [true, true, true, true],
                text: `${item.name} (${item.designation}) - ${item.employeeId}`,

                alignment: 'left',

                margin: [0, 5, 0, 5],
            },
            {
                border: [true, true, true, true],
                text: item.injuries,

                alignment: 'left',

                margin: [0, 5, 0, 5],
            }, {
                border: [true, true, true, true],
                text: item.injuryDetails,

                alignment: 'left',

                margin: [0, 5, 0, 5],
            },
            {
                border: [true, true, true, true],
                text: item.ppe,

                alignment: 'left',

                margin: [0, 5, 0, 5],
            }
        ]);
    });

    return tableBody;
};


export const IRPdf = async (data) => {
    const equipmentData = data.damagedEquipmentNumber.filter(i => i).map(item => ({
        type: item.category,
        number: item.number,
        damage: item.damageType
    }));

    const equipmentTableBody = [
        [
            {
                text: 'Equipment Type',
                bold: true,
                fillColor: '#dadada',
                border: [false, true, false, true],
                margin: [0, 5, 0, 5],
                textTransform: 'uppercase',
            },
            {
                text: 'Equipment Number',
                border: [false, true, false, true],
                bold: true,
                fillColor: '#dadada',
                margin: [0, 5, 0, 5],
                textTransform: 'uppercase',
            },
            {
                text: 'Damage Type',
                border: [false, true, false, true],
                bold: true,
                fillColor: '#dadada',
                margin: [0, 5, 0, 5],
                textTransform: 'uppercase',
            }
        ],
        ...equipmentData.map(item => ([
            {
                border: [true, true, true, true],
                text: item.type,

                alignment: 'left',

                margin: [0, 5, 0, 5],
            },
            {
                border: [true, true, true, true],
                text: item.number,

                alignment: 'left',

                margin: [0, 5, 0, 5],
            },
            {
                border: [true, true, true, true],
                text: item.damage,

                alignment: 'left',

                margin: [0, 5, 0, 5],
            }

        ]))

    ]

    const transformedPersonInvolved = transformDataForPdfMake(data.personInvolved)
    const personInvolvedTableBody = createPdfMakeTableBody(transformedPersonInvolved)

    const transformedPersonnelInvolved = transformDataForPdfMake(data.personnelImpacted)
    const personnelInvolvedTableBody = createPdfMakeTableBody(transformedPersonnelInvolved)

    const transformedWitness = transformDataForWitness(data.witnessInvolved)
    const witnessBody = createPdfMakeWitnessTableBody(transformedWitness)

    const base64Images = await convertAllImages(data.evidence.map(i => i.src));
    const singleImageRows = base64Images.map(image => [{ image: image, width: 500 }]);
    const pairedImagesRows = [];
    for (let i = 0; i < base64Images.length; i += 2) {
        const imagesInRow = base64Images.slice(i, i + 2);
        // Create a row for each pair of images, or a single image for the last row if odd number of images
        const row = imagesInRow.map(image => ({ image: image, width: 250 }));
        // If there's only one image in the row, push an empty cell to maintain the table structure
        if (imagesInRow.length === 1) {
            row.push({ text: '', width: 250 });
        }
        pairedImagesRows.push(row);
    }

    return {
        content: [
            {
                width: '100%',
                alignment: 'center',
                text: `INCIDENT REPORT ${data.edited ? `(${data.edited})` : ''}`,
                bold: true,
                margin: [0, 0, 0, 10],
                fontSize: 15,
            },
            {

                columns: [

                    {
                        image: logoBase64,
                        width: 100,
                    },
                    [
                        {
                            text: data.maskId,
                            color: data.incidentRating ? data.incidentRating.color === 'yellow' ? '#ffdf00' : data.incidentRating.color : 'black',
                            width: '*',
                            fontSize: 24,
                            bold: true,
                            alignment: 'right',
                            margin: [0, 0, 0, 15],
                        },
                        {
                            stack: [
                                {
                                    columns: [
                                        {
                                            text: 'Type',
                                            color: '#aaaaab',
                                            bold: true,
                                            width: '*',
                                            fontSize: 12,
                                            alignment: 'right',
                                        },
                                        {
                                            text: data.incidentTypeName ? data.incidentTypeName.name : '-',
                                            bold: true,
                                            color: '#333333',
                                            fontSize: 12,
                                            alignment: 'right',
                                            width: 100,
                                        },
                                    ],
                                },
                                {
                                    columns: [
                                        {
                                            text: 'Reported Status',
                                            color: '#aaaaab',
                                            bold: true,
                                            width: '*',
                                            fontSize: 12,
                                            alignment: 'right',
                                        },
                                        {
                                            text: data.isReported === 'Yes' ? 'Reported' : data.isReported === 'No' ? 'Unreported' : data.isReported,
                                            bold: true,
                                            color: '#333333',
                                            fontSize: 12,
                                            alignment: 'right',
                                            width: 100,
                                        },
                                    ],
                                },
                                {
                                    columns: [
                                        {
                                            text: data.edited ? 'Updated On' : 'Circulated On',
                                            color: '#aaaaab',
                                            bold: true,
                                            width: '*',
                                            fontSize: 12,
                                            alignment: 'right',
                                        },
                                        {
                                            text: data.edited ? moment(data.latestEdited).format('DD/MM/YYYY HH:mm') : data.investigationNotificationTime ? data.investigationNotificationTime : '-',
                                            bold: true,
                                            color: '#333333',
                                            fontSize: 12,
                                            alignment: 'right',
                                            width: 100,
                                        },


                                    ],
                                },
                                {
                                    columns: [
                                        {
                                            text: 'Rating',
                                            color: '#aaaaab',
                                            bold: true,
                                            fontSize: 12,
                                            alignment: 'right',
                                            width: '*',
                                        },
                                        {
                                            text: data.incidentRating && data.incidentRating.item ? data.incidentRating.item : '-',
                                            bold: true,
                                            fontSize: 14,
                                            alignment: 'right',
                                            color: data.incidentRating ? data.incidentRating.color === 'yellow' ? '#ffdf00' : data.incidentRating.color : 'black',
                                            width: 100,
                                        },
                                    ],
                                },
                            ],
                        },
                    ],
                ],
            },
            '\n',

            {
                width: '100%',
                alignment: 'center',
                text: data.shortDescription ? data.shortDescription : '-',
                bold: true,
                margin: [0, 10, 0, 10],
                fontSize: 15,
            },

            {
                width: '100%',
                alignment: 'justify',
                text: data.description ? data.description : '-',
                margin: [0, 0, 0, 5],
                fontSize: 12,
            },

            '\n',

            {
                layout: {
                    defaultBorder: false,
                    hLineWidth: function (i, node) {
                        return 1;
                    },
                    vLineWidth: function (i, node) {
                        return 1;
                    },
                    hLineColor: function (i, node) {
                        return '#eaeaea';
                    },
                    vLineColor: function (i, node) {
                        return '#eaeaea';
                    },
                    hLineStyle: function (i, node) {
                        // if (i === 0 || i === node.table.body.length) {
                        return null;
                        //}
                    },
                    // vLineStyle: function (i, node) { return {dash: { length: 10, space: 4 }}; },
                    paddingLeft: function (i, node) {
                        return 10;
                    },
                    paddingRight: function (i, node) {
                        return 10;
                    },
                    paddingTop: function (i, node) {
                        return 3;
                    },
                    paddingBottom: function (i, node) {
                        return 3;
                    },
                    fillColor: function (rowIndex, node, columnIndex) {
                        return '#fff';
                    },
                },
                table: {

                    widths: ['*', '*', '*', '*'],
                    body: [
                        [
                            {
                                border: [false, true, false, true],
                                text: 'Location',
                                bold: true,
                                alignment: 'left',
                                fillColor: '#dadada',
                                margin: [0, 5, 0, 5],
                            },
                            {
                                text: (data.locationThree && data.locationFour) ? `${data.locationThree.name} > ${data.locationFour.name}` : '-',
                                border: [false, true, false, true],
                                alignment: 'left',
                                margin: [0, 5, 0, 5],
                            },
                            {
                                border: [false, true, false, true],
                                text: 'Date / TIme',
                                bold: true,
                                alignment: 'left',
                                fillColor: '#dadada',
                                margin: [0, 5, 0, 5],
                            },
                            {
                                text: data.incidentDate ? data.incidentDate : '-',
                                border: [false, true, true, true],
                                alignment: 'left',
                                margin: [0, 5, 0, 5],
                            }

                        ],
                        [
                            {
                                text: 'Working Group',
                                border: [false, false, false, true],
                                alignment: 'left',
                                fillColor: '#dadada',
                                bold: true,
                                margin: [0, 5, 0, 5],
                            },
                            {
                                text: data.workingGroup ? data.workingGroup.name : '-',
                                border: [false, false, false, true],

                                alignment: 'left',
                                margin: [0, 5, 0, 5],
                            },
                            {
                                border: [false, true, false, true],
                                text: 'Weather Condition',
                                bold: true,
                                alignment: 'left',
                                fillColor: '#dadada',
                                margin: [0, 5, 0, 5],
                            },
                            {
                                text: data.weatherCondition ? data.weatherCondition.name : '-',
                                border: [false, true, true, true],
                                alignment: 'left',
                                margin: [0, 5, 0, 5],
                            }
                        ],
                        [
                            {
                                text: 'Surface Type & Condition',
                                border: [false, false, false, true],
                                alignment: 'left',
                                fillColor: '#dadada',
                                bold: true,
                                margin: [0, 5, 0, 5],
                            },
                            {
                                text: (data.surfaceType && data.surfaceCondition) ? `${data.surfaceType.name} > ${data.surfaceCondition.name}` : '-',
                                border: [false, false, false, true],

                                alignment: 'left',
                                margin: [0, 5, 0, 5],
                            },
                            {
                                border: [false, true, false, true],
                                text: 'Lighting',
                                bold: true,
                                alignment: 'left',
                                fillColor: '#dadada',
                                margin: [0, 5, 0, 5],
                            },
                            {
                                text: data.lighting ? data.lighting.name : '-',
                                border: [false, true, true, true],
                                alignment: 'left',
                                margin: [0, 5, 0, 5],
                            }
                        ],
                        [
                            {
                                text: 'Work Activity',
                                border: [false, false, false, true],
                                alignment: 'left',
                                fillColor: '#dadada',
                                bold: true,
                                margin: [0, 5, 0, 5],
                            },
                            {
                                text: data.workActivity ? data.workActivity.name : '-',
                                border: [false, false, false, true],

                                alignment: 'left',
                                margin: [0, 5, 0, 5],
                            },
                            {
                                border: [false, true, false, true],
                                text: 'Department',
                                bold: true,
                                alignment: 'left',
                                fillColor: '#dadada',
                                margin: [0, 5, 0, 5],
                            },
                            {
                                text: data.workActivityDepartment ? data.workActivityDepartment.name : '-',
                                border: [false, true, true, true],
                                alignment: 'left',
                                margin: [0, 5, 0, 5],
                            }
                        ]

                    ],
                },
            },
            '\n\n',
            {
                layout: {
                    defaultBorder: false,
                    hLineWidth: function (i, node) {
                        return 1;
                    },
                    vLineWidth: function (i, node) {
                        return 1;
                    },
                    hLineColor: function (i, node) {
                        return '#eaeaea';
                    },
                    vLineColor: function (i, node) {
                        return '#eaeaea';
                    },
                    hLineStyle: function (i, node) {
                        // if (i === 0 || i === node.table.body.length) {
                        return null;
                        //}
                    },
                    // vLineStyle: function (i, node) { return {dash: { length: 10, space: 4 }}; },
                    paddingLeft: function (i, node) {
                        return 10;
                    },
                    paddingRight: function (i, node) {
                        return 10;
                    },
                    paddingTop: function (i, node) {
                        return 3;
                    },
                    paddingBottom: function (i, node) {
                        return 3;
                    },
                    fillColor: function (rowIndex, node, columnIndex) {
                        return '#fff';
                    },
                },
                table: {

                    widths: ['*', '*', '*'],
                    body: equipmentTableBody,
                },
            },
            '\n\n',
            {
                layout: {
                    defaultBorder: false,
                    hLineWidth: function (i, node) {
                        return 1;
                    },
                    vLineWidth: function (i, node) {
                        return 1;
                    },
                    hLineColor: function (i, node) {
                        return '#eaeaea';
                    },
                    vLineColor: function (i, node) {
                        return '#eaeaea';
                    },
                    hLineStyle: function (i, node) {
                        // if (i === 0 || i === node.table.body.length) {
                        return null;
                        //}
                    },
                    // vLineStyle: function (i, node) { return {dash: { length: 10, space: 4 }}; },
                    paddingLeft: function (i, node) {
                        return 10;
                    },
                    paddingRight: function (i, node) {
                        return 10;
                    },
                    paddingTop: function (i, node) {
                        return 3;
                    },
                    paddingBottom: function (i, node) {
                        return 3;
                    },
                    fillColor: function (rowIndex, node, columnIndex) {
                        return '#fff';
                    },
                },
                table: {

                    widths: [100, '*'],
                    body: [


                        [
                            {
                                border: [false, true, true, true],
                                text: 'Vessel Details',
                                bold: true,
                                alignment: 'left',
                                fillColor: '#dadada',
                                margin: [0, 5, 0, 5],
                            },
                            {
                                text: data.vesselDetails ? data.vesselDetails : '-',
                                border: [false, true, true, true],
                                alignment: 'left',
                                margin: [0, 5, 0, 5],
                            },

                        ],
                        [
                            {
                                border: [false, true, true, true],
                                text: 'Any Other Type of Damage',
                                bold: true,
                                alignment: 'left',
                                fillColor: '#dadada',
                                margin: [0, 5, 0, 5],
                            },
                            {
                                text: data.damageType ? data.damageType : '-',
                                border: [false, true, true, true],
                                alignment: 'left',
                                margin: [0, 5, 0, 5],
                            },

                        ],
                        [
                            {
                                border: [false, true, true, true],
                                text: 'Trucks Involved',
                                bold: true,
                                alignment: 'left',
                                fillColor: '#dadada',
                                margin: [0, 5, 0, 5],
                            },
                            {
                                text: data.truckDetails ? data.truckDetails : '-',
                                border: [false, true, true, true],
                                alignment: 'left',
                                margin: [0, 5, 0, 5],
                            },

                        ],
                        [
                            {
                                border: [false, true, true, true],
                                text: 'how/Why the Incident Occur',
                                bold: true,
                                alignment: 'left',
                                fillColor: '#dadada',
                                margin: [0, 5, 0, 5],
                            },
                            {
                                text: data.moreDetails ? data.moreDetails : '-',
                                border: [false, true, true, true],
                                alignment: 'left',
                                margin: [0, 5, 0, 5],
                            },

                        ],
                        [
                            {
                                border: [false, true, true, true],
                                text: 'Immediate Actions Taken',
                                bold: true,
                                alignment: 'left',
                                fillColor: '#dadada',
                                margin: [0, 5, 0, 5],
                            },
                            {
                                text: data.actionsTaken ? data.actionsTaken : '-',
                                border: [false, true, true, true],
                                alignment: 'left',
                                margin: [0, 5, 0, 5],
                            },

                        ]




                    ],
                },
            },
            '\n\n',
            {
                text: 'Person(s) Involved',
                style: 'notesTitle',
            },
            {
                layout: {
                    defaultBorder: false,
                    hLineWidth: function (i, node) {
                        return 1;
                    },
                    vLineWidth: function (i, node) {
                        return 1;
                    },
                    hLineColor: function (i, node) {
                        return '#eaeaea';
                    },
                    vLineColor: function (i, node) {
                        return '#eaeaea';
                    },
                    hLineStyle: function (i, node) {
                        // if (i === 0 || i === node.table.body.length) {
                        return null;
                        //}
                    },
                    // vLineStyle: function (i, node) { return {dash: { length: 10, space: 4 }}; },
                    paddingLeft: function (i, node) {
                        return 10;
                    },
                    paddingRight: function (i, node) {
                        return 10;
                    },
                    paddingTop: function (i, node) {
                        return 3;
                    },
                    paddingBottom: function (i, node) {
                        return 3;
                    },
                    fillColor: function (rowIndex, node, columnIndex) {
                        return '#fff';
                    },
                },
                table: {

                    widths: ['*', '*', '*', '*'],
                    body: personInvolvedTableBody,
                },
            },

            {
                text: 'Personnel Injured',
                style: 'notesTitle',
            },
            {
                layout: {
                    defaultBorder: false,
                    hLineWidth: function (i, node) {
                        return 1;
                    },
                    vLineWidth: function (i, node) {
                        return 1;
                    },
                    hLineColor: function (i, node) {
                        return '#eaeaea';
                    },
                    vLineColor: function (i, node) {
                        return '#eaeaea';
                    },
                    hLineStyle: function (i, node) {
                        // if (i === 0 || i === node.table.body.length) {
                        return null;
                        //}
                    },
                    // vLineStyle: function (i, node) { return {dash: { length: 10, space: 4 }}; },
                    paddingLeft: function (i, node) {
                        return 10;
                    },
                    paddingRight: function (i, node) {
                        return 10;
                    },
                    paddingTop: function (i, node) {
                        return 3;
                    },
                    paddingBottom: function (i, node) {
                        return 3;
                    },
                    fillColor: function (rowIndex, node, columnIndex) {
                        return '#fff';
                    },
                },
                table: {

                    widths: ['*', '*', '*', '*'],
                    body: personnelInvolvedTableBody
                },
            },
            {
                text: 'Witness Involved',
                style: 'notesTitle',
            },
            {
                layout: {
                    defaultBorder: false,
                    hLineWidth: function (i, node) {
                        return 1;
                    },
                    vLineWidth: function (i, node) {
                        return 1;
                    },
                    hLineColor: function (i, node) {
                        return '#eaeaea';
                    },
                    vLineColor: function (i, node) {
                        return '#eaeaea';
                    },
                    hLineStyle: function (i, node) {
                        // if (i === 0 || i === node.table.body.length) {
                        return null;
                        //}
                    },
                    // vLineStyle: function (i, node) { return {dash: { length: 10, space: 4 }}; },
                    paddingLeft: function (i, node) {
                        return 10;
                    },
                    paddingRight: function (i, node) {
                        return 10;
                    },
                    paddingTop: function (i, node) {
                        return 3;
                    },
                    paddingBottom: function (i, node) {
                        return 3;
                    },
                    fillColor: function (rowIndex, node, columnIndex) {
                        return '#fff';
                    },
                },
                table: {

                    widths: ['*', '*'],
                    body: witnessBody,
                },
            },



            {
                text: 'Other Details',
                style: 'notesTitle',
            },
            {
                layout: {
                    defaultBorder: false,
                    hLineWidth: function (i, node) {
                        return 1;
                    },
                    vLineWidth: function (i, node) {
                        return 1;
                    },
                    hLineColor: function (i, node) {
                        return '#eaeaea';
                    },
                    vLineColor: function (i, node) {
                        return '#eaeaea';
                    },
                    hLineStyle: function (i, node) {
                        // if (i === 0 || i === node.table.body.length) {
                        return null;
                        //}
                    },
                    // vLineStyle: function (i, node) { return {dash: { length: 10, space: 4 }}; },
                    paddingLeft: function (i, node) {
                        return 10;
                    },
                    paddingRight: function (i, node) {
                        return 10;
                    },
                    paddingTop: function (i, node) {
                        return 3;
                    },
                    paddingBottom: function (i, node) {
                        return 3;
                    },
                    fillColor: function (rowIndex, node, columnIndex) {
                        return '#fff';
                    },
                },
                table: {

                    widths: ['15%', '35%', '15%', '35%'],
                    body: [
                        [
                            {
                                border: [false, true, false, true],
                                text: 'Reporter',
                                bold: true,

                                fillColor: '#dadada',
                                margin: [0, 5, 0, 5],
                            },
                            {
                                text: data.reporter ? `${data.reporter.email} (${data.reporter.firstName})` : '-',
                                border: [false, true, false, true],

                                margin: [0, 5, 0, 5],
                            },
                            {
                                border: [false, true, false, true],
                                text: 'Reviewer',
                                bold: true,

                                fillColor: '#dadada',
                                margin: [0, 5, 0, 5],
                            },
                            {
                                text: data.reviewer ? `${data.reviewer.email} (${data.reviewer.firstName})` : '',
                                border: [false, true, true, true],

                                margin: [0, 5, 0, 5],
                            }

                        ],




                    ],
                },
            },
            {
                text: 'Notification Information',
                style: 'notesTitle',
            },
            {
                layout: {
                    defaultBorder: false,
                    hLineWidth: function (i, node) {
                        return 1;
                    },
                    vLineWidth: function (i, node) {
                        return 1;
                    },
                    hLineColor: function (i, node) {
                        return '#eaeaea';
                    },
                    vLineColor: function (i, node) {
                        return '#eaeaea';
                    },
                    hLineStyle: function (i, node) {
                        // if (i === 0 || i === node.table.body.length) {
                        return null;
                        //}
                    },
                    // vLineStyle: function (i, node) { return {dash: { length: 10, space: 4 }}; },
                    paddingLeft: function (i, node) {
                        return 10;
                    },
                    paddingRight: function (i, node) {
                        return 10;
                    },
                    paddingTop: function (i, node) {
                        return 3;
                    },
                    paddingBottom: function (i, node) {
                        return 3;
                    },
                    fillColor: function (rowIndex, node, columnIndex) {
                        return '#fff';
                    },
                },
                table: {

                    widths: ['25%', '25%', '25%', '25%'],
                    body: [
                        [
                            {
                                border: [false, true, false, true],
                                text: 'Medical Officer',
                                bold: true,

                                fillColor: '#dadada',
                                margin: [0, 5, 0, 5],
                            },
                            {
                                text: isAnyoneInjured(data.personInvolved, data.personnelImpacted) ? data.medicalNotificationTime ? data.medicalNotificationTime : '-' : '-',
                                border: [false, true, false, true],

                                margin: [0, 5, 0, 5],
                            },
                            {
                                border: [false, true, false, true],
                                text: 'Duty Engineer Manager',
                                bold: true,

                                fillColor: '#dadada',
                                margin: [0, 5, 0, 5],
                            },
                            {
                                text: (data.damagedEquipmentNumber && data.damagedEquipmentNumber.length > 0) ? data.propertyNotificationTime ? data.propertyNotificationTime : '-' : '-',
                                border: [false, true, true, true],

                                margin: [0, 5, 0, 5],
                            }

                        ],

                        [
                            {
                                border: [false, true, false, true],
                                text: 'Custodian',
                                bold: true,

                                fillColor: '#dadada',
                                margin: [0, 5, 0, 5],
                            },
                            {
                                text: data.custodianNotificationTime ? data.custodianNotificationTime : '',
                                border: [false, true, false, true],

                                margin: [0, 5, 0, 5],
                            },
                            {
                                border: [false, true, false, true],
                                text: 'Insurance',
                                bold: true,

                                fillColor: '#dadada',
                                margin: [0, 5, 0, 5],
                            },
                            {
                                text: data.insuranceNotificationTime ? data.insuranceNotificationTime : '-',
                                border: [false, true, true, true],

                                margin: [0, 5, 0, 5],
                            }

                        ],

                        [
                            {
                                border: [false, true, false, true],
                                text: 'Investigation Team',
                                bold: true,

                                fillColor: '#dadada',
                                margin: [0, 5, 0, 5],
                            },
                            {
                                text: data.investigationNotificationTime ? data.investigationNotificationTime : '-',
                                border: [false, true, false, true],

                                margin: [0, 5, 0, 5],
                            },
                            {
                                border: [false, true, false, true],
                                text: 'Third Party',
                                bold: true,

                                fillColor: '#dadada',
                                margin: [0, 5, 0, 5],
                            },
                            {
                                text: data.thirdPartyNotificationTime ? data.thirdPartyNotificationTime : '-',
                                border: [false, true, true, true],

                                margin: [0, 5, 0, 5],
                            }

                        ],




                    ],
                },
            },
            {
                text: 'Investigation Team Members',
                style: 'notesTitle',
            },
            {
                layout: {
                    defaultBorder: false,
                    hLineWidth: function (i, node) {
                        return 1;
                    },
                    vLineWidth: function (i, node) {
                        return 1;
                    },
                    hLineColor: function (i, node) {
                        return '#eaeaea';
                    },
                    vLineColor: function (i, node) {
                        return '#eaeaea';
                    },
                    hLineStyle: function (i, node) {
                        // if (i === 0 || i === node.table.body.length) {
                        return null;
                        //}
                    },
                    // vLineStyle: function (i, node) { return {dash: { length: 10, space: 4 }}; },
                    paddingLeft: function (i, node) {
                        return 10;
                    },
                    paddingRight: function (i, node) {
                        return 10;
                    },
                    paddingTop: function (i, node) {
                        return 3;
                    },
                    paddingBottom: function (i, node) {
                        return 3;
                    },
                    fillColor: function (rowIndex, node, columnIndex) {
                        return '#fff';
                    },
                },
                table: {

                    widths: ['*'],
                    body: [
                        [

                            {
                                text: data.investigation ? data.investigation.investigationTeam ? data.investigation.investigationTeam.length > 0 ? data.investigation.investigationTeam.map(member => member.label).join(', ') : 'No members are alloted' : 'No members are alloted' : 'No members are alloted',
                                border: [false, true, false, true],

                                margin: [0, 5, 0, 5],
                            },


                        ],


                    ],
                },
            },
            { text: '', pageBreak: 'after' },
            {
                text: 'Evidence Photos',
                style: 'notesTitle',
            },
            {
                table: {
                    widths: ['50%', '50%'],
                    body:
                        base64Images.length > 0
                            ? pairedImagesRows
                            : [[{ text: 'No evidence images are submitted', colSpan: 2, alignment: 'center' }]]
                    ,
                },
                layout: 'noBorders', // This will remove the borders from the table
            },


        ],
        styles: {
            notesTitle: {
                fontSize: 12,
                bold: true,
                margin: [0, 20, 0, 3],
            },
            notesText: {
                fontSize: 10,
            },
        },
        defaultStyle: {
            columnGap: 20,
            //font: 'Quicksand',
        },
    }


}
