// @ts-nocheck
import React, { useState, useRef, useEffect } from 'react';
import { useHistory } from 'react-router-dom'
import { FILE_URL, DOCUMENT_URL, DOWNLOAD_DOCS_URL, GET_USER_ROLE_BY_MODE,DOCUMENT_WITH_ID, DOCUMENTS_WITH_ID_URL, DOCUMENT_CATEGORY, DOCUMENT_CATEGORY_WITH_ID } from '../constants';
import { Modal, Button, Form } from 'react-bootstrap';
import cogoToast from 'cogo-toast';
import Loader from '../shared/Loader';
import Swal from 'sweetalert2';
import { useSelector } from 'react-redux'
import Select from "react-select";
// @ts-ignore
import { Link } from 'react-router-dom';
import API from '../services/API';
// import S3 from "react-aws-s3";
import { singlePopup, deletePopup } from "./../notifications/Swal";
import MaterialTable from "material-table";
import { ThemeProvider, createTheme } from "@mui/material";
import { Buffer } from "buffer";
import CateListBox from '../form-elements/CateListBox';

Buffer.from("anything", "base64");
window.Buffer = window.Buffer || require("buffer").Buffer;

const defaultMaterialTheme = createTheme();
const tableStyle = {
  borderRadius: '0',
  boxShadow: 'none',
};
const customSwal = Swal.mixin({
  customClass: {
    confirmButton: 'btn btn-danger',
    cancelButton: 'btn btn-light'
  },
  buttonsStyling: false
})

const customSwal2 = Swal.mixin({
  customClass: {
    confirmButton: 'btn btn-primary',

  },
  buttonsStyling: false
})

const customSwal3 = Swal.mixin({
  customClass: {
    confirmButton: 'btn btn-primary',
    cancelButton: 'btn btn-light'
  },
  buttonsStyling: false
})
const config = {
  bucketName: "sagt",
  region: "ap-southeast-1",
  accessKeyId: process.env.REACT_APP_AWS_ACCESS_KEY,
  secretAccessKey: process.env.REACT_APP_AWS_SECRET_KEY,
};


const Document = (props) => {
  const user = useSelector((state) => state.login.user);
  const initial = user.roles.some(item => item.name=== 'Initiator')
  const creatorAccess = user.roles.some(item => item.id === 'Creator')
  console.log(initial)
  const [mdShow, setMdShow] = useState(false)
  const [smShow, setSmShow] = useState(false)
  const [smDocShow, setSmDocShow] = useState(false)
  const [initialShow, setIntialShow] = useState(false)
  const [cateShow, setCateShow] = useState(false)
  const [title, setTitle] = useState('')
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState([]);
  const history = useHistory();
  const [selectedFile, setSelectedFile] = useState();
  const [File, setFile] = useState('');
  const [creator, setCreator] = useState([])
  const [reviewer, setReviewer] = useState([])
  const [approver, setApprover] = useState([])
  const [selectedCreator, setSelectedCreator] = useState([])
  const [selectedReviewer, setSelectedReviewer] = useState([])
  const [selectedApprover, setSelectedApprover] = useState([])
  const [category, setCategory] = useState([])
  const [selectedCategory, setSelectedCategory] = useState([])
  const [tier1, setTier1] = useState([])
  const [selectedTier1, setSelectedTier1] = useState('')
  const dcate = useRef();
  const dName = useRef();
  const dCategory = useRef();
  const dDesc = useRef();
  const dRef = useRef();
  const dVersion = useRef();
  const edName = useRef();
  const edCategory = useRef();
  const edDesc = useRef();
  const edRef = useRef();
  const edVersion = useRef();
  const dTitle = useRef();
  const dpurpose = useRef();
  useEffect(() => {
    getChecklist();
    getUserList();
    getCategory();
  }, []);

  const getCategory = async () => {
    const response = await API.get(DOCUMENT_CATEGORY);
    if (response.status === 200) {

      let data = [];
      response.data.map((item) => {

        // if (item.id !== user.id) {
        data.push({ label: item.name, value: item.id });
        // }
      });

      setCategory(data)


      setTier1(response.data)

    }

  }
  const getUserList = async () => {
    const response = await API.post(GET_USER_ROLE_BY_MODE, {
      locationOneId: "",
      locationTwoId: "",
      locationThreeId: "",
      locationFourId: "",
      mode: 'document-creator'
    });
    if (response.status === 200) {
      let data = [];
      response.data.map((item) => {

        // if (item.id !== user.id) {
        data.push({ label: item.firstName, value: item.id });
        // }
      });

      setCreator(data)

    }
  }
  const getChecklist = async () => {

    const response = await API.get(DOCUMENT_URL);
    if (response.status === 200) {
      setData(response.data);
    }
  };
  const handleApprover = async (e) => {
    setSelectedApprover(e)
  }
  const handleReviewer = async (e) => {
    setSelectedReviewer(e)
    const response = await API.post(GET_USER_ROLE_BY_MODE, {
      locationOneId: "",
      locationTwoId: "",
      locationThreeId: "",
      locationFourId: "",
      mode: 'document-approver'
    });
    if (response.status === 200) {
      let data = [];
      response.data.map((item) => {

        data.push({ label: item.firstName, value: item.id });

      });

      setApprover(data)

    }
  }
  const handleCreator = async (e) => {
    setSelectedCreator(e)
    setSelectedReviewer([])
    const response = await API.post(GET_USER_ROLE_BY_MODE, {
      locationOneId: "",
      locationTwoId: "",
      locationThreeId: "",
      locationFourId: "",
      mode: 'document-reviewer'
    });
    if (response.status === 200) {
      let data = [];
      response.data.map((item) => {

        if (item.id !== user.id) {
          if (item.id !== e.value) {
            data.push({ label: item.firstName, value: item.id });
          }
        }
      });

      setReviewer(data)

    }
  }
  const deleteStep = (item) => {
    deletePopup.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      reverseButtons: true,

      confirmButtonText: 'Delete'
    }).then(async (result) => {
      if (result.isConfirmed) {
        //   deleteChecklist(id);

        const response = await fetch(DOCUMENTS_WITH_ID_URL(item.id), {
          method: 'DELETE',
          headers: {
            "Content-type": "application/json; charset=UTF-8"
          }
        })
        if (response.ok) {
          singlePopup.fire(
            'Deleted!',
            '',
            'success'
          );
          getChecklist();
        }
      }
    })
  }
  const columns = [


    {
      field: 'name',
      title: 'Name',
      sort: true,

    },
    {
      field: 'cate.name',
      title: 'Category',
      sort: true,

    },



    {
      field: 'intial.firstName',
      title: 'Initiated By',
      sort: true,

    },
    {
      field: 'creator.firstName',
      title: 'Developed By',
      sort: true,

    },
    {
      field: 'reviewer.firstName',
      title: 'Reviewer',
      sort: true,

    },
    {
      field: 'approver.firstName',
      title: 'Approver',
      sort: true,

    },
    {
      field: 'status',
      title: 'Status',
      sort: true,
      render: (row) => {
        if (row.status === '1') {
          return (
            <>Sent To Author</>
          )
        } else if (row.status === '2') {
          return (
            <>Sent To Reviewer</>
          )
        } else if (row.status === '3') {
          return (
            <>Sent To Approver</>
          )
        } else if (row.status === '4') {
          return (
            <>Completed</>
          )
        }

      }
    },
    {
      field: 'createdAt',
      title: 'Created Date',
      sort: true,

    },




  ];

  const fileChangeHandler = (event) => {
    setSelectedFile(event.target.files[0]);
  }

  // const fileEditChangeHandler = async (event) => {
  //   const ReactS3Client = new S3(config);
  //   const filename = new Date().getTime() + event.target.files[0].name

  //   await ReactS3Client.uploadFile(event.target.files[0], 'uploads/documents/' + filename)
  //     .then((data) => console.log(data))
  //     .catch((err) => console.error(err));
  //   setFile(filename)
  // }
  const createDocumentHandler = async () => {

    // console.log(selectedCategory.id)
    // @ts-ignore
    setIsLoading(true)


    const response = await fetch(
      DOCUMENT_URL,
      {
        method: 'POST',
        body: JSON.stringify({
          name: dName.current.value,
          description: dDesc.current.value,
          initiatorId: user.id,
          creatorId: selectedCreator.value,
          reviewerId: selectedReviewer.value,
          approverId: selectedApprover.value,
          status: '1',
          documentCategoryId: selectedCategory.value,
          docStatus:'1'

        }),
        headers: { "Content-type": "application/json; charset=UTF-8" }
      })

    if (response.ok) {

      customSwal2.fire(
        'Document Created!',
        '',
        'success'
      )
    } else {
      //show error
      customSwal2.fire(
        'Please Try Again!',
        '',
        'error'
      )
      setIsLoading(false)
    }


    setIsLoading(false)
    getChecklist();
    setIntialShow(false)
    // dName.current.value = '';
    // dCategory.current.value = '';
    // dDesc.current.value = '';
    // dRef.current.value = '';
    // dVersion.current.value = '';

  }

  const updateDocumentHandler = async () => {
    setIsLoading(true)




    const response = await fetch(
      DOCUMENT_WITH_ID(title.id),
      {
        method: 'PATCH',
        body: JSON.stringify({
          name: edName.current.value,
          description: edDesc.current.value,
          reviewerId: selectedReviewer.value,
          approverId: selectedApprover.value,
          status: '1',
          documentCategoryId: selectedCategory.value
       
        }),
        headers: { "Content-type": "application/json; charset=UTF-8" }
      })

    if (response.ok) {

      customSwal2.fire(
        'Document Updated!',
        '',
        'success'
      )
      setIsLoading(false)
    } else {
      //show error
      customSwal2.fire(
        'Please Try Again!',
        '',
        'error'
      )
      setIsLoading(false)
    }
    setIsLoading(false)
    // } else {
    //   //Show Error
    //   customSwal2.fire(
    //     'Please Try Again!',
    //     '',
    //     'error'
    //   )
    //   setIsLoading(false)
    // }
    getChecklist();
    // edName.current.value = '';
    // edCategory.current.value = '';
    // edDesc.current.value = '';
    // edRef.current.value = '';
    // edVersion.current.value = '';
    setSmShow(false)
  }
  const fetchData = async (mode) => {
    const response = await API.post(GET_USER_ROLE_BY_MODE, {
      locationOneId: "",
      locationTwoId: "",
      locationThreeId: "",
      locationFourId: "",
      mode: mode,
    });

    if (response.status === 200) {
      let data = [];

      response.data.forEach((item) => {
        // if (mode === 'document-reviewer' && (item.id === user.id )) {
        //   return;
        // }

        data.push({ label: item.firstName, value: item.id });
      });

      return data;
    }

    return [];
  };
  const getAllUserType = async () => {

    const approverData = await fetchData('document-approver');
    setApprover(approverData);

    const reviewerData = await fetchData('document-reviewer');
    setReviewer(reviewerData);
  }

  const renderField = (field, index) => {


    if (field.toolType === 'WEB_LINK') {
      return (
        <div className='card mb-3'>
          <div className='card-body boxShadow'>
            <a href={field.title} >{field.title}</a>
          </div>
        </div>
      )


    } else if (field.toolType === 'IMAGE') {
      return (
        <div className='card mb-3'>
          <div className='card-body boxShadow'>
            <img src={'https://sagt.s3-ap-southeast-1.amazonaws.com/uploads/form_image/' + field.title} style={{ maxWidth: '100%' }} />
          </div>
        </div>
      )
    } else if (field.toolType === 'YOUTUBE') {
      return (
        <div className='card mb-3'>
          <div className='card-body boxShadow'>
            <iframe sandbox="allow-same-origin allow-forms allow-popups allow-scripts allow-presentation" src={field.title} allowfullscreen=""></iframe>
          </div>
        </div>
      )
    } else if (field.toolType === 'VIDEO') {
      return (
        <div className='card mb-3'>
          <div className='card-body boxShadow'>

            <video controls style={{ maxWidth: '100%' }}>
              <source src={'https://sagt.s3-ap-southeast-1.amazonaws.com/uploads/form_video/' + field.title} type="video/mp4" />
              Your browser does not support video play.
            </video>
          </div>
        </div>
      )
    } else if (field.toolType === "PARAGRAPH") {
      return (
        <div className='card mb-3'>
          <div className='card-body boxShadow'>
            <p dangerouslySetInnerHTML={{ __html: field.content }} />

          </div>
        </div>
      )
    } else if (field.toolType === "AUDIO") {
      return (
        <div className='card mb-3'>
          <div className='card-body boxShadow'>
            <div className="col-12 text-center">
              <audio controls style={{ maxWidth: '100%' }}>
                <source src={'https://sagt.s3-ap-southeast-1.amazonaws.com/uploads/form_audio/' + field.title} />
              </audio>
            </div>

          </div>
        </div>
      )
    } else if (field.toolType === "PDF") {
      return (
        <div className='card mb-3'>
          <div className='card-body boxShadow'>
            <div className="col-12 text-center">
              <a href={'https://sagt.s3-ap-southeast-1.amazonaws.com/uploads/pdf_uploads/' + field.title} target="_blank"><i className="fa fa-file-pdf-o fa-5x"></i><p style={{ textDecoration: 'none' }}>click to view</p></a></div>

          </div>
        </div>
      )
    } else if (field.toolType === "EMBEDCODE") {
      return (
        <div className='card mb-3'>
          <div className='card-body boxShadow'>
            <div className="col-12 text-center">
              <div dangerouslySetInnerHTML={{ __html: field.title }} style={{ width: '100%' }} /></div>

          </div>
        </div>
      )
    }
    else if (field.toolType === "MCQ") {
      return (
        <div className='card mb-3'>
          <div className='card-body boxShadow'>
            <p
              dangerouslySetInnerHTML={{ __html: field.title }}
            />
            {field.radios.map(item => {
              return (
                <div className="form-check">
                  <input


                    className="form-check-input"
                    type="checkbox"
                    id="isRequired"
                  />
                  <label className="form-check-label" htmlFor="isRequired">
                    {item.value}
                  </label>
                </div>
              )
            })}

          </div>
        </div>
      )
    } else if (field.toolType === "TEXT_INPUT") {
      return (
        <div className='card mb-3'>
          <div className='card-body boxShadow'>
            <p>{field.title}</p>
            <input


              className="form-control"
              type="text"
              id="isRequired"
            />
          </div>
        </div>
      )
    } else if (field.toolType === "IMAGE_INPUT") {
      return (
        <div className='card mb-3'>
          <div className='card-body boxShadow'>
            <p>{field.title}</p>

          </div>
        </div>
      )
    } else if (field.toolType === "VIDEO_INPUT") {
      return (
        <div className='card mb-3'>
          <div className='card-body boxShadow'>
            <p>{field.title}</p>

          </div>
        </div>
      )
    } else if (field.toolType === "AUDIO_INPUT") {
      return (
        <div className='card mb-3'>
          <div className='card-body boxShadow'>
            <p>{field.title}</p>

          </div>
        </div>
      )
    } else if (field.toolType === "OPTION_INPUT") {
      return (
        <div className='card mb-3'>
          <div className='card-body boxShadow'>
            <p
              dangerouslySetInnerHTML={{ __html: field.title }}
            />
            {field.radios.map(item => {
              return (
                <div className="form-check">
                  <input


                    className="form-check-input"
                    type="checkbox"
                    id="isRequired"
                  />
                  <label className="form-check-label" htmlFor="isRequired">
                    {item.value}
                  </label>
                </div>
              )
            })}

          </div>
        </div>
      )
    } else if (field.toolType === "SIGN_INPUT") {
      return (
        <div className='card mb-3'>
          <div className='card-body boxShadow'>
            <p>{field.title}</p>

          </div>
        </div>
      )
    } else if (field.toolType === "CHECK_INPUT") {
      return (
        <div className='card mb-3'>
          <div className='card-body boxShadow'>
            <p
              dangerouslySetInnerHTML={{ __html: field.title }}
            />
            {field.radios.map(item => {
              return (
                <div className="form-check">
                  <input


                    className="form-check-input"
                    type="checkbox"
                    id="isRequired"
                  />
                  <label className="form-check-label" htmlFor="isRequired">
                    {item.value}
                  </label>
                </div>
              )
            })}

          </div>
        </div>
      )
    } else if (field.toolType === "MULTIMEDIA") {
      return (
        <></>
      )
    }
  }
  const tableActions = [

    {
      icon: 'article',
      tooltip: 'View ',
      onClick: (event, rowData) => {

        // openCurate(rowData)
        setTitle(rowData)
        setSmDocShow(true)

        // history.push('/document/curate/edit', { id: rowData.id, type: 'document' })



      },
    },
    // {
    //   icon: "modeEdit",
    //   tooltip: "Edit ",
    //   onClick: (event, rowData) => {
    //     if (initial) {
    //       setSmShow(true);
    //       getAllUserType()
    //       setTitle(rowData);
    //       setSelectedApprover({ value: rowData.approverId, label: rowData.approver.firstName });
    //       setSelectedReviewer({ value: rowData.reviewerId  , label: rowData.reviewer.firstName });
    //       setSelectedCreator({ value: rowData.creatorId, label: rowData.creator.firstName });
    //       setSelectedCategory({value:rowData.cate.id,label:rowData.cate.name})

    //     } else {
    //       customSwal2.fire(
    //         'You Cant Access',
    //         '',
    //         'success'
    //       )
    //     }
    //   },
    // },
    {

      icon: "delete",
      tooltip: "Delete ",
      onClick: (event, rowData) => {
        if (initial) {
          deleteStep(rowData)
        } else {
          customSwal2.fire(
            'You Cant Access',
            '',
            'success'
          )
        }

      },
    },
  ]

  const handleCloneItem = async (mode, id) => {

  }

  const handleActivityItem = async (mode, id) => {

  }
  const handleDeleteItem = async (mode, id) => {

    deletePopup.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      reverseButtons: true,

      confirmButtonText: 'Delete'
    }).then(async (result) => {
      if (result.isConfirmed) {
        //   deleteChecklist(id);

        const response = await API.delete(DOCUMENT_CATEGORY_WITH_ID(id))

        if (response.status === 204) {

          setTier1(prev => prev.filter(i => i.id !== id))
          setSelectedTier1({ id: '' });


          singlePopup.fire(
            'Deleted!',
            '',
            'success'
          );
        }

      }
    })

  }
  const createTier1 = async (value) => {
    const response = await API.post(DOCUMENT_CATEGORY, {

      name: value


    })
    if (response.status === 200) {
      const createdTier1 = response.data;
      setTier1((prev) => [...prev, createdTier1]);
      cogoToast.info('Created!', { position: 'top-right' })
    }
  }
  const handleTier1Select = (id) => {
    setSelectedTier1(tier1.find(i => i.id === id))
  }

  const handleCategory = (e) => {
    setSelectedCategory(e)
  }


  return (
    <>
      <div>
        <div className="row">
          <div className="col-12">
            <div className="card">
              <div className="card-body">

                <h4 className="card-title">Documents</h4>
                <div className="row">
                  <div className="col-12">
                    <div>
                      <div className='d-flex justify-content-between'>
                        {initial && <button type="button" className="btn btn-primary btn-rounded mb-3 " onClick={(e) => { e.preventDefault(); setIntialShow(true); }}><i className="mdi mdi-account-plus mr-2" /> Initiate Document</button>}
                       
                      </div>
                  
                      <ThemeProvider theme={defaultMaterialTheme}>
                        <MaterialTable
                          columns={columns}
                          data={data}
                          title=""
                          style={tableStyle}
                          actions={tableActions}
                          options={{
                            actionsColumnIndex: -1,
                          }}
                        />
                      </ThemeProvider>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Modal
        show={initialShow}
        onHide={() => setIntialShow(false)}
        aria-labelledby="example-modal-sizes-title-md"
      >
        <Modal.Body>
          <form className="forms">
            <div className="form-group">
              <label htmlFor="user_name">Select Category</label>
              <Select
                labelKey="label"
                id="user_description"
                onChange={handleCategory}
                options={category}
                placeholder="Type..."
              />
            </div>
            <div className="form-group">
              <label htmlFor="document_name" >Title</label>
              <Form.Control type="text" ref={dName} id="document_name" placeholder="Enter Title" />
            </div>
            <div className="form-group">
              <label htmlFor="document_category" >Purpose</label>
              <Form.Control as={'textarea'} ref={dDesc} id="document_category" placeholder="Enter Purpose" />
            </div>
            <div className="form-group">
              <label htmlFor="user_name">Assign Author</label>
              <Select
                labelKey="label"
                id="user_description"
                onChange={handleCreator}
                options={creator}
                placeholder="Type..."
              />
            </div>
            <div className="form-group">
              <label htmlFor="user_name">Assign Reviewer(s)</label>
              <Select
                labelKey="label"
                id="user_description"
                onChange={handleReviewer}
                options={reviewer}
                // isMulti={true}
                placeholder="Type..."
              />
            </div>
            <div className="form-group">
              <label htmlFor="user_name">Assign Approver</label>
              <Select
                labelKey="label"
                id="user_description"
                onChange={handleApprover}
                options={approver}
                placeholder="Type..."
              />
            </div>
          </form>

        </Modal.Body>
        <Modal.Footer>
          <Button variant="light" onClick={() => setIntialShow(false)}>Cancel</Button>
          <Button variant="primary" onClick={createDocumentHandler}>Create</Button>
        </Modal.Footer>
      </Modal>
      {/* <Modal
        show={mdShow}
        onHide={() => setMdShow(false)}
        aria-labelledby="example-modal-sizes-title-md"
      >

        <Modal.Body>
          <form className="forms">
            <div className="form-group">
              <label htmlFor="document_name" >Document Name</label>
              <Form.Control type="text" ref={dName} id="document_name" placeholder="Enter Document Name" />
            </div>

            <div className="form-group">
              <label htmlFor="document_category" >Document Category</label>
              <Form.Control type="text" ref={dCategory} id="document_category" placeholder="Enter Document Category" />
            </div>

            <div className="form-group">
              <label htmlFor="document_description" >Description</label>
              <Form.Control type="text" ref={dDesc} id="document_description" placeholder="Enter Document Description" />
            </div>

            <div className="form-group">
              <label htmlFor="document_ref" >Reference</label>
              <Form.Control type="text" ref={dRef} id="document_ref" placeholder="Enter Document Reference" />
            </div>

            <div className="form-group">
              <label htmlFor="document_version" >Version No</label>
              <Form.Control type="text" ref={dVersion} id="document_version" placeholder="Enter Version No" />
            </div>

            <div className="form-group">

              <Form.Control type="file" id="document_file" onChange={fileChangeHandler} placeholder="Choose File" />
            </div>

          </form>
        </Modal.Body>

        <Modal.Footer className="flex-wrap">

          <Button variant="light" onClick={() => setMdShow(false)}>Cancel</Button>
          <Button variant="primary" onClick={createDocumentHandler}>Create</Button>


        </Modal.Footer>
      </Modal> */}
      <Modal
        show={smShow}
        onHide={() => setSmShow(false)}
        aria-labelledby="example-modal-sizes-title-md"
      >

        <Modal.Body>
          <form className="forms">
            <div className="form-group">
              <label htmlFor="user_name">Select Category</label>
              <Select
                labelKey="label"
                id="user_description"
                onChange={handleCategory}
                options={category}
                value={selectedCategory}
                placeholder="Type..."
              />
            </div>
            <div className="form-group">
              <label htmlFor="document_name" >Document Name</label>
              <Form.Control type="text" ref={edName} id="document_name" placeholder="Enter Document Name" defaultValue={title.name} />
            </div>
            {/* 
            <div className="form-group">
              <label htmlFor="document_category" >Document Category</label>
              <Form.Control type="text" ref={edCategory} id="document_category" placeholder="Enter Document Category" defaultValue={title.category} />
            </div> */}

            <div className="form-group">
              <label htmlFor="document_description" >Description</label>
              <Form.Control type="text" ref={edDesc} id="document_description" placeholder="Enter Document Description" defaultValue={title.description} />
            </div>

            {/* <div className="form-group">
              <label htmlFor="document_ref" >Reference</label>
              <Form.Control type="text" ref={edRef} id="document_ref" placeholder="Enter Document Reference" defaultValue={title.uniqueid} />
            </div>

            <div className="form-group">
              <label htmlFor="document_version" >Version No</label>
              <Form.Control type="text" ref={edVersion} id="document_version" placeholder="Enter Version No" defaultValue={title.version} />
            </div>

            <div className="form-group">

              <Form.Control type="file" id="document_file" onChange={fileEditChangeHandler} placeholder="Choose File" />
              <a href={'https://sagt.s3-ap-southeast-1.amazonaws.com/uploads/documents/' + File} rel="noreferrer" target='_blank'>{File.length > 35 ? File.substr(0, 35) + '…' : File}</a>
            </div> */}
            {/* <div className="form-group" >
              <label htmlFor="user_name">Assign Author</label>
              <Select
                labelKey="label"
                id="user_description"
                onChange={handleCreator}
                options={creator}
                value={selectedCreator}
                placeholder="Type..."
                
              />
            </div> */}
            <div className="form-group">
              <label htmlFor="user_name">Assign Reviewer(s)</label>
              <Select
                labelKey="label"
                id="user_description"
                onChange={handleReviewer}
                options={reviewer}
                value={selectedReviewer}
               
                placeholder="Type..."
              />
            </div>
            <div className="form-group">
              <label htmlFor="user_name">Assign Approver</label>
              <Select
                labelKey="label"
                id="user_description"
                onChange={handleApprover}
                value={selectedApprover}
                options={approver}
                placeholder="Type..."
              />
            </div>

          </form>
        </Modal.Body>

        <Modal.Footer className="flex-wrap">

          <Button variant="light" onClick={() => setSmShow(false)}>Cancel</Button>
          <Button variant="primary" onClick={updateDocumentHandler}>Update</Button>


        </Modal.Footer>
      </Modal>

      <Modal
        show={smDocShow}
        onHide={() => setSmDocShow(false)}
        aria-labelledby="example-modal-sizes-title-md"
      >
        <Modal.Header>
          Document
        </Modal.Header>

        <Modal.Body>
          <div className="row">
            <div className={`animate-col-md-12`}>
              {title !== '' ?
                title.value ?
                  JSON.parse(title.value).map((field, index) => {
                    return renderField(field, index)
                  })
                  : '' : ''
              }
            </div>
          </div>

        </Modal.Body>

        <Modal.Footer className="flex-wrap">

          <Button variant="light" onClick={() => setSmDocShow(false)}>Cancel</Button>



        </Modal.Footer>
      </Modal>

      <Modal
        show={cateShow}
        onHide={() => setCateShow(false)}
        aria-labelledby="example-modal-sizes-title-md"
      >
        <Modal.Header>
          Adding Category
        </Modal.Header>

        <Modal.Body>
          <div className="row">
            <div className="col-lg-12 p-0">


              {/* <h4 className="card-title">Category Management</h4> */}

              <>
                <div className="row">
                  <div className="col p-1 ps-3">
                    <CateListBox handleActivity={handleActivityItem} handleClone={handleCloneItem} handleDeleteItem={handleDeleteItem} changeTitle={(id, value) => setTier1(prev => prev.map(i => { return i.id === id ? { ...i, name: value } : i }))} title={'Category'} onHandleCreateItem={createTier1} lists={tier1} selected={true} handleSelect={handleTier1Select} selectedItem={selectedTier1} mode='tier1' />
                  </div>



                </div>
              </>






            </div>
          </div>


        </Modal.Body>

        <Modal.Footer className="flex-wrap">

          <Button variant="light" onClick={() => setCateShow(false)}>Cancel</Button>



        </Modal.Footer>
      </Modal>
    </>
  )
}


export default Document;
