import React, { useState } from 'react';
import ProposedAdditionalControls from './ProposedAdditionalControls';
import ResidualRiskAssessment from './ResidualRiskAssessment';

const ProposedRiskManagement = ({
    item, control, responsibility, severity, severityData, likelyhood, levelData, tableData, required,
    onControlAddion, onControlAddionText, onDeleteConseq, onResponsePerson, onResponseDate, addAdditionalControl,
    onChangeSeverity, onChangeLikelyhood, cellClassName, cellStyle, rowClassName
}) => {

    const [severityTable, setSeverityTable] = useState(false);
    const [likelyhoodTable, setLikelyhoodTable] = useState(false);
    const [riskTable, setRiskTable] = useState(false);
    return (
        <div>
            <ProposedAdditionalControls
                item={item}
                control={control}
                responsibility={responsibility}
                onControlAddion={onControlAddion}
                onControlAddionText={onControlAddionText}
                onDeleteConseq={onDeleteConseq}
                onResponsePerson={onResponsePerson}
                onResponseDate={onResponseDate}
                addAdditionalControl={addAdditionalControl}
                required ={required}
            />

            <ResidualRiskAssessment
                item={item}
                severity={severity}
                severityData={severityData}
                likelyhood={likelyhood}
                levelData={levelData}
                tableData={tableData}
                severityTable={severityTable}
                likelyhoodTable={likelyhoodTable}
                riskTable={riskTable}
                setSeverityTable={setSeverityTable}
                setLikelyhoodTable={setLikelyhoodTable}
                setRiskTable={setRiskTable}
                onChangeSeverity={onChangeSeverity}
                onChangeLikelyhood={onChangeLikelyhood}
                cellClassName={cellClassName}
                cellStyle={cellStyle}
                rowClassName={rowClassName}
                required ={required}
            />
        </div>
    );
};

export default ProposedRiskManagement;
