import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, Nav, Tab, <PERSON>ton, ButtonGroup } from "react-bootstrap";
import MaterialTable from "material-table";
import { RISKASSESSMENT_LIST, RISK_DELETE_WITH_ID_URL, RISK_WITH_ID_URL } from "../constants";
import { ThemeProvider, createTheme } from "@mui/material";
import { useHistory, useLocation } from "react-router-dom/cjs/react-router-dom";
import { useSelector } from "react-redux";
import API from "../services/API";
import moment from "moment";
import Swal from "sweetalert2";
import { FilterMatchMode, FilterOperator } from 'primereact/api';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { InputText } from 'primereact/inputtext';
import { MultiSelect } from 'primereact/multiselect';
import { Calendar } from 'primereact/calendar';
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";
import { Dropdown, Form } from 'react-bootstrap';
import { convertToLocalTime } from "../services/ConvertLocalTime";
import Routine from "./Routine";
import Hazard from "./HazardBased";
import EditRoutine from "./EditRoutine";

const RiskAssessment = ({ data, onFilterUpdate }) => {
    const user = useSelector((state) => state.login.user)
    console.log(user)
    const history = useHistory();
    const location = useLocation()

    const [risk, setRisk] = useState([])
    const [users, setUsers] = useState([])
    const [depart, setDepart] = useState([])
    const [overdue, setOverdue] = useState([])
    const [additional, setAdditional] = useState([])
    const [aOverdue, setAOverdue] = useState([])
    const [access, setAccess] = useState(false)
    const [showModal, setShowModal] = useState(false);
    const [viewModal, setViewModal] = useState(false);
    const [modalType, setModalType] = useState(''); // To differentiate routine/non-routine
    const [riskData, setRiskData] = useState(null); // S
    const [domain, setDomain] = useState([])
    const [filters, setFilters] = useState({
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        meetid: { value: null, matchMode: FilterMatchMode.IN },
        activity: { value: null, matchMode: FilterMatchMode.IN },
        date: { value: null, matchMode: FilterMatchMode.IN },
        nextdate: { value: null, matchMode: FilterMatchMode.IN },
        'type': { value: null, matchMode: FilterMatchMode.IN },
        status: { value: null, matchMode: FilterMatchMode.IN },
        'teamLeader.firstName': { value: null, matchMode: FilterMatchMode.IN },
        department: { value: null, matchMode: FilterMatchMode.IN },
    });
    // const [hazard,setHazard] =useState([])
    const renderHeader = () => {
        const value = filters['global'] ? filters['global'].value : '';

        return (
            <div className='d-flex justify-content-end'>
                {access && <></>
                    // <Button className="btn btn-primary  mb-3 " onClick={() => openAssessment('routine')}>
                    //     Add New Risk Assessment

                    // </Button>
                    // <div className="nav-item nav-profile">

                    //     <Dropdown>
                    //         <Dropdown.Toggle className="btn btn-primary  mb-3 ">
                    //             Add New
                    //         </Dropdown.Toggle>
                    //         <Dropdown.Menu >
                    //             <Dropdown.Item href="#" className="dropdown-item preview-item p-2" onClick={() => openAssessment('routine')}>
                    //                 <div className="d-flex align-items-center">
                    //                     <span >Routine</span>
                    //                 </div>
                    //             </Dropdown.Item>
                    //             <Dropdown.Item href="#" className="dropdown-item preview-item p-2" onClick={() => openAssessment('nonroutine')}>
                    //                 <div className="d-flex align-items-center">
                    //                     <span>NonRoutine</span>
                    //                 </div>
                    //             </Dropdown.Item>
                    //             {/* <Dropdown.Item href="#" className="dropdown-item preview-item p-2" onClick={() => openAssessment('hazard')}>
                    //                 <div className="d-flex align-items-center">
                    //                     <span>Hazard-Based</span>
                    //                 </div>
                    //             </Dropdown.Item> */}
                    //         </Dropdown.Menu>
                    //     </Dropdown>
                    // </div>
                }
            </div>
        );
    };

    const header = renderHeader();

    useEffect(() => {
        getPermit();

    }, [data])
    const customSwal = Swal.mixin({
        customClass: {
            confirmButton: 'btn btn-danger',
            cancelButton: 'btn btn-light'
        },
        buttonsStyling: false
    })
    const customSwal2 = Swal.mixin({
        customClass: {
            confirmButton: 'btn btn-primary',

        },
        buttonsStyling: false
    })

    const getPermit = async () => {

        setRisk(data)
        const teamLeaderNames = data
            .map(item => ({
                name: item.teamLeader.firstName,
                value: item.teamLeader.firstName // or use item.teamLeader.id if you prefer the ID
            }))
            .filter((obj, index, self) =>
                index === self.findIndex((t) => t.name === obj.name)
            );


        setUsers(teamLeaderNames)


        if (user.length !== 0) {
            setAccess(user.roles.some(item => item.maskId === 'ra_leader'))
        }

    }

    // const openAssessment = (type) => {
    //     if (type === 'hazard') {
    //         history.push('/hazardnew', { type })
    //     } else {
    //         history.push('/routinenew', { type, domain: 'new' })
    //     }


    // }

    const openAssessment = (type) => {
        setModalType(type); // 'routine' or 'nonroutine'
        setRiskData(null);  // Clear any previous data
        setShowModal(true); // Open the modal
        setDomain('new')
    };


    const viewRisk = (data) => {

        setModalType(data.type === 'Routine' ? 'routine' : data.type === 'High-Risk Hazard' ? 'hazard' : 'nonroutine'); // 'routine' or 'nonroutine'
        setRiskData(data);  // Clear any previous data
        setViewModal(true); // Open the modal
        setDomain('view')
        // if (data.type === 'High-Risk Hazard') {
        //     history.push('/hazardnew', { data, type: 'view' })
        // } else {
        //     history.push('/routineedit', { data, type: 'view' })
        // }
    }
    const editRisk = (data) => {
        setModalType(data.type === 'Routine' ? 'routine' : data.type === 'High-Risk Hazard' ? 'hazard' : 'nonroutine'); // 'routine' or 'nonroutine'
        setRiskData(data);  // Clear any previous data
        setShowModal(true); // Open the modal
        setDomain('edit')

        // let id = data.id
        // if (data.type === 'High-Risk Hazard') {
        //     if (data.status === 'Draft') {
        //         history.push('/risk-assessment/hazarddraft', { id })
        //     } else {
        //         history.push('/hazardnew', { data, type: 'edit' })
        //     }

        // } else {
        //     if (data.status === 'Draft') {
        //         history.push('/routineedit', { data, type: 'draft' })
        //     } else {
        //         if (data.type === 'Routine') {
        //             history.push('/routinenew', { type: 'routine', domain: 'edit', data })
        //         } else {
        //             history.push('/routinenew', { type: 'nonroutine', domain: 'edit', data })
        //         }
        //     }

        // }
    }

    const onDelete = async (id) => {

        customSwal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            reverseButtons: true,

            confirmButtonText: 'Delete'
        }).then(async (result) => {
            if (result.isConfirmed) {
                const response = await API.delete(RISK_DELETE_WITH_ID_URL(id));
                if (response.status === 204) {

                    customSwal2.fire(
                        'Deleted!',
                        '',
                        'success'
                    )


                }
                getPermit();
            }
        })

    }



    const actionBodyTemplate = (row) => {
        return (
            <div className="table-action d-flex">
                {/* <i className="pi pi-eye" onClick={() => viewRisk(row)}></i> */}
                {access && <>
                    <i className="pi pi-pencil" onClick={() => editRisk(row)}></i>
                    <i className="pi pi-trash text-danger" onClick={() => onDelete(row.id)}></i>
                </>}
            </div>
        );
    }

    const typeFilterTemplate = (options) => {
        return (
            <>
                <div className="mb-3 fw-bold">Type</div>
                <MultiSelect
                    value={options.value}
                    options={[
                        { name: 'Routine', value: 'Routine' },
                        { name: 'Non Routine', value: 'Non Routine' },
                        { name: 'High-Risk Scenarios', value: 'High-Risk Hazard' } // ✅ FIXED
                    ]}
                    itemTemplate={(option) => <span>{option.name}</span>}
                    onChange={(e) => options.filterCallback(e.value)}
                    optionLabel="name"
                    placeholder="Any"
                    className="p-column-filter"
                />
            </>
        );
    };

    const leaderFilterTemplate = (options) => {

        return (
            <React.Fragment>
                <div className="mb-3 font-bold">Leader</div>
                <MultiSelect value={options.value} options={users} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }
    const statusFilterTemplate = (options) => {

        return (
            <React.Fragment>
                <div className="mb-3 font-bold">Status</div>
                <MultiSelect value={options.value} options={[{ name: 'Pending', value: 'Pending' }, { name: 'Published', value: 'Published' }, { name: 'Draft', value: 'Draft' }]} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }
    const representativesItemTemplate = (option) => {
        return (
            <div className="flex align-items-center gap-2">

                <span>{option.value}</span>
            </div>
        );
    };

    const reviewBodyTemplate = (data) => {
        if (data.nextReviewDate) {
            return convertToLocalTime(data.nextReviewDate)
        }

    }
    const createdBodyTemplate = (data) => {
        return convertToLocalTime(data.created)
    }

    const publishBodyTemplate = (data) => {
        if (data.publishedDate) {
            return convertToLocalTime(data.publishedDate)
        }

    }
    const workBodyTemplate = (data) => {
        if (data.type === 'Routine') {
            return data.workActivity?.name
        } else if (data.type === 'Non Routine') {
            return data.nonRoutineWorkActivity
        } else if (data.type === 'High-Risk Hazard') {
            return data.hazardName
        }
        return 'N/A'
    }
    const departBodyTemplate = (data) => {
        if (data.type === 'Routine') {
            return data.department?.name
        } else if (data.type === 'Non Routine') {
            return data.nonRoutineDepartment
        }
        return 'N/A'
    }
    const maskBodyTemplate = (row) => {
        return <div className='maskid' onClick={() => viewRisk(row)}>{row.maskId}</div>
    }

    const handleCloseModal = () => {

        Swal.fire({
            title: 'Are you sure?',
            text: 'You have made edits to this form. Are you sure you want to exit without saving the changes?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, close it!',
            cancelButtonText: 'No, keep it open',
        }).then((result) => {
            if (result.isConfirmed) {
                setShowModal(false);
                setRiskData(null); // Close the modal if the user confirms
                Swal.fire('Closed!', '', 'success');
            } else {
                Swal.fire('Cancelled', '', 'info');
            }
        });
        // setShowModal(false);
        // setRiskData(null);
    };

    const handleSwitchType = (isRoutine) => {
        if (isRoutine) {
            setModalType('routine');
        } else {
            setModalType('nonroutine');
        }
    };
    const handleFilterChange = (filteredData) => {


        // Update the count of filtered data in the parent component
        if (onFilterUpdate) {
            onFilterUpdate(filteredData.length);
        }
    };

    const typeBodyTemplate = (row) => {
        if (row.type === 'High-Risk Hazard') {
            return 'High-Risk Scenarios '
        }
        return row.type

    }
    return (
        <>
            <div>
                <div className="row">
                    <div className="col-12">



                        <div className="card">
                            <div className="card-body p-0">
                                <div className="row">
                                    <div className="col-12">
                                        <div className="p-4">
                                            <p> The Risk Register catalogs all activities and scenarios requiring risk assessments, including routine operations, non-routine tasks, and high-risk scenarios. Entries progress through three states:</p>
                                            <ul>
                                                <li>Draft: Editable by RA Team Leaders (click the ID to refine).</li>
                                                <li>Pending: Published for team affirmation—visible to all RA Team Members but locked for edits.</li>
                                                <li>Published: Finalized and accessible to all assigned platform users.</li>
                                            </ul>

                                            <p>RA Team Members can view Draft/Pending entries for transparency, while Published risk assessments include a Download button (top-right) for easy sharing and compliance.</p>
                                        </div>
                                        <div>


                                            <>

                                                <DataTable value={risk} paginator rows={10} onValueChange={handleFilterChange} header={header} filters={filters} onFilter={(e) => setFilters(e.filters)} paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                                                    rowsPerPageOptions={[10, 25, 50]}
                                                    emptyMessage="No Data found." tableStyle={{ minWidth: '50rem' }}>

                                                    <Column field="maskId" header="ID" sortable body={maskBodyTemplate}></Column>

                                                    <Column field='workActivity.name' body={workBodyTemplate} header="Process/Activity/High-Risk Scenarios" ></Column>
                                                    <Column field='department.name' body={departBodyTemplate} header="Operational Risk Area" ></Column>
                                                    <Column field="type" header="Type" body={typeBodyTemplate} filterElement={typeFilterTemplate} showFilterMatchModes={false} filter filterPlaceholder="Search" ></Column>
                                                    <Column field="created" body={createdBodyTemplate} header="Initiated Date"></Column>
                                                    <Column field="publishedDate" body={publishBodyTemplate} header="Published / Amended Date"  ></Column>

                                                    <Column field="nextReviewDate" body={reviewBodyTemplate} header="Next Review Date" filterPlaceholder="Search" ></Column>

                                                    <Column field="status" header="Status" filterElement={statusFilterTemplate} showFilterMatchModes={false} filter filterPlaceholder="Search" ></Column>

                                                    <Column field="teamLeader.firstName" header="RA Leader" filterElement={leaderFilterTemplate} showFilterMatchModes={false} filter filterPlaceholder="Search" ></Column>
                                                    {access &&
                                                        <Column header="Action" body={actionBodyTemplate} ></Column>
                                                    }

                                                </DataTable>
                                            </>

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            {showModal &&
                <Modal size="lg" show={showModal} onHide={handleCloseModal}>
                    <Modal.Header closeButton>
                        <div className="d-flex w-100 flex-column justify-content-between ">
                            <Modal.Title className="fw-bold">
                                {modalType === 'edit'
                                    ? 'Edit Risk'
                                    : modalType === 'view'
                                        ? 'View Risk'
                                        : `Conduct Risk Assessment`
                                }
                            </Modal.Title>
                            <p className="mt-2">As the Risk Assessment (RA) Team Leader, you are responsible for leading a qualified team of RA Team Members in conducting a thorough risk assessment. This process includes identifying potential hazards associated with work activities, evaluating existing controls, and proposing additional controls to minimize potential consequences.
                            </p>
                        </div>

                    </Modal.Header>
                    <Modal.Body>
                        <div className="ps-4">
                            <ButtonGroup className="mb-3">
                                <Button
                                    variant={modalType === 'routine' ? 'primary' : 'outline-primary'}
                                    onClick={() => setModalType('routine')}
                                >
                                    Routine
                                </Button>
                                <Button
                                    variant={modalType === 'nonroutine' ? 'primary' : 'outline-primary'}
                                    onClick={() => setModalType('nonroutine')}
                                >
                                    Non-Routine
                                </Button>
                                <Button
                                    variant={modalType === 'hazard' ? 'primary' : 'outline-primary'}
                                    onClick={() => setModalType('hazard')}
                                >
                                    High-Risk Scenarios
                                </Button>

                            </ButtonGroup>
                        </div>

                        {modalType === 'hazard' ?
                            <Hazard data={riskData} type={modalType} domain={domain} />
                            :
                            <Routine data={riskData} type={modalType} domain={domain} />
                        }

                    </Modal.Body>

                </Modal>
            }

            {viewModal &&

                <Modal size="lg" show={viewModal} onHide={() => { setRiskData(null); setViewModal(false) }}>
                    <Modal.Header closeButton>
                        <Modal.Title>

                            {`${modalType === 'routine' ? "Routine Work" : modalType === 'nonroutine' ? "Non Routine" : "Hazard Based"} RiskAssessment`}

                        </Modal.Title>

                    </Modal.Header>
                    <Modal.Body>
                        <EditRoutine data={riskData} type={modalType} domain={domain} />
                    </Modal.Body>

                </Modal>
            }
        </>
    );
};

export default RiskAssessment;
