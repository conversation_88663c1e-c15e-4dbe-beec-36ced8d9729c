import React, { useState, useEffect, useRef } from "react";
import API from "../services/API";
import { DYNAMIC_TITLES_URL, API_URL } from "../constants";
import Select from 'react-select';

const AllFilterLocation = (props) => {
    const BASE_URL = `${API_URL}/`;

    const [locationOne, setLocationOne] = useState([]);
    const [locationTwo, setLocationTwo] = useState([]);
    const [locationThree, setLocationThree] = useState([]);
    const [locationFour, setLocationFour] = useState([]);
    const [locationFive, setLocationFive] = useState([]);
    const [locationSix, setLocationSix] = useState([]);

    const [selectedLocationOne, setSelectedLocationOne] = useState('');
    const [selectedLocationTwo, setSelectedLocationTwo] = useState('');
    const [selectedLocationThree, setSelectedLocationThree] = useState('');
    const [selectedLocationFour, setSelectedLocationFour] = useState('');
    const [selectedLocationFive, setSelectedLocationFive] = useState('');
    const [selectedLocationSix, setSelectedLocationSix] = useState('');

    const prevSelectedLocationOne = useRef('');
    const prevSelectedLocationTwo = useRef('');
    const prevSelectedLocationThree = useRef('');
    const prevSelectedLocationFour = useRef('');
    const prevSelectedLocationFive = useRef('');
    const prevSelectedLocationSix = useRef('');

    // useEffect(() => {
    //     if (
    //         props.getLocation &&
    //         Object.keys(props.getLocation).length > 0 &&
    //         (
    //             selectedLocationOne !== props.getLocation.locationOneId ||
    //             selectedLocationTwo !== props.getLocation.locationTwoId ||
    //             selectedLocationThree !== props.getLocation.locationThreeId ||
    //             selectedLocationFour !== props.getLocation.locationFourId ||
    //             selectedLocationFive !== props.getLocation.locationFiveId ||
    //             selectedLocationSix !== props.getLocation.locationSixId
    //         )
    //     ) {
    //         setSelectedLocationOne(props.getLocation.locationOneId);
    //         setSelectedLocationTwo(props.getLocation.locationTwoId);
    //         setSelectedLocationThree(props.getLocation.locationThreeId);
    //         setSelectedLocationFour(props.getLocation.locationFourId);
    //         setSelectedLocationFive(props.getLocation.locationFiveId);
    //         setSelectedLocationSix(props.getLocation.locationSixId);
    //     }
    // }, [props.getLocation]);

    useEffect(() => {
        const populateLocations = async () => {
            if (!props.getLocation || Object.keys(props.getLocation).length === 0) return;
    
            const { locationOneId, locationTwoId, locationThreeId, locationFourId, locationFiveId, locationSixId } = props.getLocation;
    
            if (locationOneId) {
                setSelectedLocationOne(locationOneId);
                const twoRes = await API.get(`${BASE_URL}location-ones/${locationOneId}/location-twos`);
                if (twoRes.status === 200) setLocationTwo(twoRes.data);
            }
    
            if (locationTwoId) {
                setSelectedLocationTwo(locationTwoId);
                const threeRes = await API.get(`${BASE_URL}location-twos/${locationTwoId}/location-threes`);
                if (threeRes.status === 200) setLocationThree(threeRes.data);
            }
    
            if (locationThreeId) {
                setSelectedLocationThree(locationThreeId);
                const fourRes = await API.get(`${BASE_URL}location-threes/${locationThreeId}/location-fours`);
                if (fourRes.status === 200) setLocationFour(fourRes.data);
            }
    
            if (locationFourId) {
                setSelectedLocationFour(locationFourId);
                const fiveRes = await API.get(`${BASE_URL}location-fours/${locationFourId}/location-fives`);
                if (fiveRes.status === 200) setLocationFive(fiveRes.data);
            }
    
            if (locationFiveId) {
                setSelectedLocationFive(locationFiveId);
                const sixRes = await API.get(`${BASE_URL}location-fives/${locationFiveId}/location-sixes`);
                if (sixRes.status === 200) setLocationSix(sixRes.data);
            }
    
            if (locationSixId) {
                setSelectedLocationSix(locationSixId);
            }
        };
    
        populateLocations();
    }, [props.getLocation]);
    

    useEffect(() => {
        const fetchLocationOne = async () => {
            const response = await API.get(`${BASE_URL}location-ones`);
            if (response.status === 200) {
                setLocationOne(response.data);
            }
        };
        fetchLocationOne();
    }, []);

    useEffect(() => {
        if (!selectedLocationOne || selectedLocationOne === prevSelectedLocationOne.current) return;
        prevSelectedLocationOne.current = selectedLocationOne;

        const fetchLocationTwo = async () => {
            const response = await API.get(`${BASE_URL}location-ones/${selectedLocationOne}/location-twos`);
            if (response.status === 200) {
                setLocationTwo(response.data);
                // Reset lower-level selections
                setSelectedLocationTwo('');
                setSelectedLocationThree('');
                setSelectedLocationFour('');
                setSelectedLocationFive('');
                setSelectedLocationSix('');
                setLocationThree([]);
                setLocationFour([]);
                setLocationFive([]);
                setLocationSix([]);
            }
        };
        fetchLocationTwo();
        // Call handleFilter with updated selections
        props.handleFilter(selectedLocationOne, '', '', '', '', '');
    }, [selectedLocationOne]);

    useEffect(() => {
        if (!selectedLocationTwo || selectedLocationTwo === prevSelectedLocationTwo.current) return;
        prevSelectedLocationTwo.current = selectedLocationTwo;

        const fetchLocationThree = async () => {
            const response = await API.get(`${BASE_URL}location-twos/${selectedLocationTwo}/location-threes`);
            if (response.status === 200) {
                setLocationThree(response.data);
                // Reset lower-level selections
                setSelectedLocationThree('');
                setSelectedLocationFour('');
                setSelectedLocationFive('');
                setSelectedLocationSix('');
                setLocationFour([]);
                setLocationFive([]);
                setLocationSix([]);
            }
        };
        fetchLocationThree();
        // Call handleFilter with updated selections
        props.handleFilter(selectedLocationOne, selectedLocationTwo, '', '', '', '');
    }, [selectedLocationTwo]);

    useEffect(() => {
        if (!selectedLocationThree || selectedLocationThree === prevSelectedLocationThree.current) return;
        prevSelectedLocationThree.current = selectedLocationThree;

        const fetchLocationFour = async () => {
            const response = await API.get(`${BASE_URL}location-threes/${selectedLocationThree}/location-fours`);
            if (response.status === 200) {
                setLocationFour(response.data);
                // Reset lower-level selections
                setSelectedLocationFour('');
                setSelectedLocationFive('');
                setSelectedLocationSix('');
                setLocationFive([]);
                setLocationSix([]);
            }
        };
        fetchLocationFour();
        // Call handleFilter with updated selections
        props.handleFilter(selectedLocationOne, selectedLocationTwo, selectedLocationThree, '', '', '');
    }, [selectedLocationThree]);

    useEffect(() => {
        if (!selectedLocationFour || selectedLocationFour === prevSelectedLocationFour.current) return;
        prevSelectedLocationFour.current = selectedLocationFour;

        const fetchLocationFive = async () => {
            const response = await API.get(`${BASE_URL}location-fours/${selectedLocationFour}/location-fives`);
            if (response.status === 200) {
                setLocationFive(response.data);
                // Reset lower-level selections
                setSelectedLocationFive('');
                setSelectedLocationSix('');
                setLocationSix([]);
            }
        };
        fetchLocationFive();
        // Call handleFilter with updated selections
        props.handleFilter(selectedLocationOne, selectedLocationTwo, selectedLocationThree, selectedLocationFour, '', '');
    }, [selectedLocationFour]);

    useEffect(() => {
        if (!selectedLocationFive || selectedLocationFive === prevSelectedLocationFive.current) return;
        prevSelectedLocationFive.current = selectedLocationFive;

        const fetchLocationSix = async () => {
            const response = await API.get(`${BASE_URL}location-fives/${selectedLocationFive}/location-sixes`);
            if (response.status === 200) {
                setLocationSix(response.data);
                // Reset lower-level selections
                setSelectedLocationSix('');
            }
        };
        fetchLocationSix();
        // Call handleFilter with updated selections
        props.handleFilter(selectedLocationOne, selectedLocationTwo, selectedLocationThree, selectedLocationFour, selectedLocationFive, '');
    }, [selectedLocationFive]);

    useEffect(() => {
        if (!selectedLocationSix || selectedLocationSix === prevSelectedLocationSix.current) return;
        prevSelectedLocationSix.current = selectedLocationSix;

        // Call handleFilter with updated selections
        props.handleFilter(selectedLocationOne, selectedLocationTwo, selectedLocationThree, selectedLocationFour, selectedLocationFive, selectedLocationSix);
    }, [selectedLocationSix]);

    const [title, setTitles] = useState({
        tier1: 'Tier I',
        tier2: 'Tier II',
        tier3: 'Tier III',
        tier4: 'Tier IV',
        tier5: 'Tier V',
        tier6: 'Tier VI',
    });

    useEffect(() => {
        getLocationConfigs();
    }, []);

    const getLocationConfigs = async () => {
        try {
            const response = await API.get(DYNAMIC_TITLES_URL);

            if (response.status === 200 && response.data && response.data.length > 0) {
                const titles = response.data;
                const locationsObject = titles.reduce((obj, item) => {
                    obj[item.title] = item.altTitle;
                    return obj;
                }, {});
                setTitles({
                    tier1: locationsObject.LocationOne || 'Tier I',
                    tier2: locationsObject.LocationTwo || 'Tier II',
                    tier3: locationsObject.LocationThree || 'Tier III',
                    tier4: locationsObject.LocationFour || 'Tier IV',
                    tier5: locationsObject.LocationFive || 'Tier V',
                    tier6: locationsObject.LocationSix || 'Tier VI',
                });
            } else {
                setTitles({
                    tier1: 'Tier I',
                    tier2: 'Tier II',
                    tier3: 'Tier III',
                    tier4: 'Tier IV',
                    tier5: 'Tier V',
                    tier6: 'Tier VI',
                });
            }
        } catch (error) {
            console.error("Error fetching location configs:", error);
            setTitles({
                tier1: 'Tier I',
                tier2: 'Tier II',
                tier3: 'Tier III',
                tier4: 'Tier IV',
                tier5: 'Tier V',
                tier6: 'Tier VI',
            });
        }
    };

    const formatOptions = (locations) => {
        return locations.map(location => ({
            value: location.id,
            label: location.name,
        }));
    };
    const customStyles = {
        menu: (provided) => ({
          ...provided,
          zIndex: 9999,         // Set a high z-index here
        }),
        // If you use menuPortalTarget, use the key "menuPortal"
        // menuPortal: (provided) => ({
        //   ...provided,
        //   zIndex: 9999,
        // }),
      };
    return (
        <div className="row">
            <div className={`col ${!selectedLocationOne ? 'col-md-4' : ''}`}>
                <div className='mb-4 d-flex flex-column'>
                    <label className='mb-2 font-sm'>{title.tier1}</label>
                    <Select
                        options={formatOptions(locationOne)}
                        value={formatOptions(locationOne).find(option => option.value === selectedLocationOne)}
                        onChange={(selectedOption) => setSelectedLocationOne(selectedOption ? selectedOption.value : '')}
                        placeholder="Select"
                        styles={customStyles}
                        isClearable
                    />
                </div>
            </div>

            {selectedLocationOne && locationTwo.length > 0 && (
                <div className="col">
                    <div className='mb-4 d-flex flex-column'>
                        <label className='mb-2 font-sm'>{title.tier2}</label>
                        <Select
                            options={formatOptions(locationTwo)}
                            value={formatOptions(locationTwo).find(option => option.value === selectedLocationTwo)}
                            onChange={(selectedOption) => setSelectedLocationTwo(selectedOption ? selectedOption.value : '')}
                            placeholder="Select"
                            styles={customStyles}
                            isClearable
                        />
                    </div>
                </div>
            )}

            {selectedLocationTwo && locationThree.length > 0 && (
                <div className="col">
                    <div className='mb-4 d-flex flex-column'>
                        <label className='mb-2 font-sm'>{title.tier3}</label>
                        <Select
                            options={formatOptions(locationThree)}
                            value={formatOptions(locationThree).find(option => option.value === selectedLocationThree)}
                            onChange={(selectedOption) => setSelectedLocationThree(selectedOption ? selectedOption.value : '')}
                            placeholder="Select"
                            styles={customStyles}
                            isClearable
                        />
                    </div>
                </div>
            )}

            {selectedLocationThree && locationFour.length > 0 && (
                <div className="col">
                    <div className='mb-4 d-flex flex-column'>
                        <label className='mb-2 font-sm'>{title.tier4}</label>
                        <Select
                            options={formatOptions(locationFour)}
                            value={formatOptions(locationFour).find(option => option.value === selectedLocationFour)}
                            onChange={(selectedOption) => setSelectedLocationFour(selectedOption ? selectedOption.value : '')}
                            placeholder="Select"
                            styles={customStyles}
                            isClearable
                        />
                    </div>
                </div>
            )}

            {selectedLocationFour && locationFive.length > 0 && (
                <div className="col">
                    <div className='mb-4 d-flex flex-column'>
                        <label className='mb-2 font-sm'>{title.tier5}</label>
                        <Select
                            options={formatOptions(locationFive)}
                            value={formatOptions(locationFive).find(option => option.value === selectedLocationFive)}
                            onChange={(selectedOption) => setSelectedLocationFive(selectedOption ? selectedOption.value : '')}
                            placeholder="Select"
                            styles={customStyles}
                            isClearable
                        />
                    </div>
                </div>
            )}

            {selectedLocationFive && locationSix.length > 0 && (
                <div className="col">
                    <div className='mb-4 d-flex flex-column'>
                        <label className='mb-2 font-sm'>{title.tier6}</label>
                        <Select
                            options={formatOptions(locationSix)}
                            value={formatOptions(locationSix).find(option => option.value === selectedLocationSix)}
                            onChange={(selectedOption) => setSelectedLocationSix(selectedOption ? selectedOption.value : '')}
                            placeholder="Select"
                            styles={customStyles}
                            isClearable
                        />
                    </div>
                </div>
            )}
        </div>
    );
};

export default AllFilterLocation;
