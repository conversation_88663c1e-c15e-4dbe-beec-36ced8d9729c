import React, { useState, useEffect } from 'react';
import { Modal, Button, Form } from 'react-bootstrap';
import { DropzoneArea } from 'material-ui-dropzone';
import API from '../../services/API';
import { INCIDENT_RECORD_WITH_ID, FILE_URL,INVESTIGATION_RECORD_WITH_ID } from '../../constants';
import ImageComponent from '../../services/FileDownlodS3';

const EditRecordModal = ({ isVisible, onHide, record, investigationId, refreshRecords }) => {
    const [title, setTitle] = useState('');
    const [description, setDescription] = useState('');
    const [uploadedFiles, setUploadedFiles] = useState([]);

    useEffect(() => {
        if (record) {
            setTitle(record.title);
            setDescription(record.description);
            // Assuming the uploadedFiles are not fetched from API, set to empty or existing files
            setUploadedFiles(record.documents || []); // Set to existing files if available
        } else {
            clearForm();
        }
    }, [record]);

    const handleFileUpload = async (files) => {
        if (files.length > 0) {
            const latestFile = files[files.length - 1];
            const formData1 = new FormData();
            formData1.append('file', latestFile);

            try {
                const response = await API.post(FILE_URL, formData1, {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                    },
                });

                if (response && response.status === 200) {
                    // Append the new file to the existing array of uploaded files
                    setUploadedFiles((prevFiles) => [...prevFiles, response.data.files[0].originalname]);
                }
            } catch (error) {
                console.error("File upload error: ", error);
            }
        }
    };

    const handleSaveChanges = async () => {
        const recordData = {
            title,
            description,
            documents: uploadedFiles,
        };

        try {
            if (record) {
                // Update existing record
                await API.patch(`${INVESTIGATION_RECORD_WITH_ID(record.id)}`, recordData);
            } else {
                // Add new record
                await API.post(INCIDENT_RECORD_WITH_ID(investigationId), recordData);
            }

            // Refresh the records after saving changes
            refreshRecords();
            onHide();
        } catch (error) {
            console.error(`Error saving investigation record:`, error);
        }
    };

    const clearForm = () => {
        setTitle('');
        setDescription('');
        setUploadedFiles([]);
    };

    const handleRemoveImage = (index) => {
        setUploadedFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
    };

    return (
        <Modal show={isVisible} onHide={onHide} size="md">
            <Modal.Header closeButton>
                <Modal.Title>{record ? 'Edit Investigation Record' : 'Add Investigation Record'}</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <Form>
                    <Form.Group className="mb-3" controlId="formTitle">
                        <Form.Label>Title</Form.Label>
                        <Form.Control
                            type="text"
                            placeholder="Enter title"
                            value={title}
                            onChange={(e) => setTitle(e.target.value)}
                        />
                    </Form.Group>
                    <Form.Group className="mb-3" controlId="formDescription">
                        <Form.Label>Brief Description</Form.Label>
                        <Form.Control
                            type="text"
                            placeholder="Enter description"
                            value={description}
                            onChange={(e) => setDescription(e.target.value)}
                        />
                    </Form.Group>
                    <Form.Group className="mb-3" controlId="formUpload">
                        <Form.Label>Upload Incident Images</Form.Label>
                        <DropzoneArea
                            dropzoneText={"Drag 'n' drop some files here, or click to select files"}
                            filesLimit={1}
                            maxFileSize={104857600} // 100MB limit
                            showPreviewsInDropzone={false}
                            showPreviews={false}
                            dropzoneClass={'dropzoneText d-flex align-items-center justify-content-center'}
                            onChange={handleFileUpload}
                        />
                    </Form.Group>
                    <div className="image-preview mt-3">
                        {uploadedFiles && uploadedFiles.length > 0 && (
                            <div className="d-flex">
                                {uploadedFiles.map((item, index) => (
                                    <div key={index} className="m-2" style={{ position: 'relative' }}>
                                        <div className="boxShadow d-flex align-items-center">
                                            <ImageComponent fileName={item} size={'100'} name={true}/>
                                            <i
                                                className="pi pi-trash"
                                                onClick={() => handleRemoveImage(index)}
                                                style={{
                                                    position: 'absolute',
                                                    top: '5px',
                                                    right: '5px',
                                                    cursor: 'pointer',
                                                    color: 'red',
                                                }}
                                            />
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>
                </Form>
            </Modal.Body>
            <Modal.Footer>
                <Button variant="secondary" onClick={onHide}>
                    Close
                </Button>
                <Button variant="primary" onClick={handleSaveChanges}>
                    {record ? 'Save Changes' : 'Add Record'}
                </Button>
            </Modal.Footer>
        </Modal>
    );
};

export default EditRecordModal;
