import React, { useEffect, useState } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { MultiSelect } from 'primereact/multiselect';
import { Dialog } from 'primereact/dialog';
import moment from 'moment';
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";
// import 'primereact/resources/themes/lara-light-indigo/theme.css';
import 'primeicons/primeicons.css';

const OpenActions = () => {
    const [actions, setActions] = useState([]);
    const [viewDialogVisible, setViewDialogVisible] = useState(false);
    const [selectedAction, setSelectedAction] = useState(null);

    useEffect(() => {
        const dummy = [
            {
                id: 'FindingID-101',
                category: 'Compliance',
                checklist: 'Post-Incident Review',
                version: '2.0',
                owner: 'Venkatesh R.',
                location: 'KTV > Boiler Area',
                dueDate: '2025-04-27'
            },
            {
                id: 'FindingID-102',
                category: 'Safety',
                checklist: 'Equipment Audit',
                version: '1.3',
                owner: 'Ananya N.',
                location: 'KRR > Zone C',
                dueDate: '2025-04-24'
            }
        ];
        setActions(dummy);
    }, []);

    const categories = [...new Set(actions.map(a => a.category))].map(c => ({ label: c, value: c }));
    const owners = [...new Set(actions.map(a => a.owner))].map(o => ({ label: o, value: o }));
    const locations = [...new Set(actions.map(a => a.location))].map(l => ({ label: l, value: l }));

    const getStatus = (row) => {
        const today = moment();
        const due = moment(row.dueDate);
        if (today.isBefore(due)) return <span className="badge bg-info">Upcoming</span>;
        if (today.isSame(due, 'day')) return <span className="badge bg-warning">Due Now</span>;
        if (today.isAfter(due)) return <span className="badge bg-danger">Overdue</span>;
        return null;
    };

    const handleView = (row) => {
        setSelectedAction(row);
        setViewDialogVisible(true);
    };

    const idTemplate = (row) => (
        <span className="text-primary" style={{ cursor: 'pointer' }} onClick={() => handleView(row)}>
            {row.id}
        </span>
    );

    return (
        <>
            <div className="d-flex justify-content-between align-items-center mb-2">
                <h5>Open Actions</h5>
                <div>
                    <button className="btn btn-outline-secondary">
                        <i className="pi pi-download"></i> CSV Download
                    </button>
                </div>
            </div>

            <DataTable value={actions} paginator rows={5}>
                <Column field="id" header="Finding ID" body={idTemplate} />
                <Column field="category" header="Inspection Category" filter filterElement={(options) => (
                    <MultiSelect value={options.value} options={categories} onChange={(e) => options.filterCallback(e.value)} placeholder="Any" />
                )} />
                <Column field="checklist" header="Inspection Checklist" filter filterPlaceholder="Search Checklist" />
                <Column field="version" header="Checklist Version" />
                <Column field="owner" header="Assigned Action Owner" filter filterElement={(options) => (
                    <MultiSelect value={options.value} options={owners} onChange={(e) => options.filterCallback(e.value)} placeholder="Any" />
                )} />
                <Column field="location" header="Location" filter filterElement={(options) => (
                    <MultiSelect value={options.value} options={locations} onChange={(e) => options.filterCallback(e.value)} placeholder="Any" />
                )} />
                <Column field="dueDate" header="Due Date" sortable body={(row) => moment(row.dueDate).format('DD-MM-YYYY')} />
                <Column header="Status" body={getStatus} />
            </DataTable>

            <Dialog header="Open Action Details" visible={viewDialogVisible} style={{ width: '40vw' }} modal onHide={() => setViewDialogVisible(false)}>
                {selectedAction && (
                    <div>
                        <p><strong>ID:</strong> {selectedAction.id}</p>
                        <p><strong>Category:</strong> {selectedAction.category}</p>
                        <p><strong>Checklist:</strong> {selectedAction.checklist}</p>
                        <p><strong>Version:</strong> {selectedAction.version}</p>
                        <p><strong>Owner:</strong> {selectedAction.owner}</p>
                        <p><strong>Location:</strong> {selectedAction.location}</p>
                        <p><strong>Due Date:</strong> {moment(selectedAction.dueDate).format('DD-MM-YYYY')}</p>
                    </div>
                )}
            </Dialog>
        </>
    );
};

export default OpenActions;
