import React, { useState, useRef, useEffect } from 'react';
import { <PERSON><PERSON>, But<PERSON>, Form } from 'react-bootstrap';
import SignatureCanvas from 'react-signature-canvas';
import API from '../../services/API';
import { RISK_UPDATE_WITH_ID_URL, FILE_URL } from '../../constants';
import ImageComponent from '../../services/FileDownlodS3';
import { useSelector } from 'react-redux';
const RiskUpdate = ({ show, onChangeModel, id, onSubmitUpdate }) => {
    const user = useSelector((state) => state.login.user);
    const [formValues, setFormValues] = useState({
        reasonForReview: '',
        changes: '',
        reasonForChanges: '',
        initiatedBy: '',
        approvedBy: '',
        reference: '',
        signature: {},
        attachment: []
    });
    const [formErrors, setFormErrors] = useState({});
    const [amend, setAmend] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const sign1 = useRef(null);
    const dataURItoFile = (dataURI, filename) => {
        var byteString = atob(dataURI.split(",")[1]);
        // separate out the mime component
        var mimeString = dataURI.split(",")[0].split(":")[1].split(";")[0];
        // write the bytes of the string to an ArrayBuffer
        var ab = new ArrayBuffer(byteString.length);
        var dw = new DataView(ab);
        for (var i = 0; i < byteString.length; i++) {
            dw.setUint8(i, byteString.charCodeAt(i));
        }

        // write the ArrayBuffer to a blob, and you're done
        return new File([ab], filename, { type: mimeString });
    };
    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormValues({
            ...formValues,
            [name]: value,
        });

        // Clear the error message for the current field
        if (formErrors[name]) {
            setFormErrors({
                ...formErrors,
                [name]: '',
            });
        }
    };

    const validateForm = () => {
        const errors = {};
        if (!formValues.reasonForReview) errors.reasonForReview = 'Reason for review is required';
        if (!formValues.changes) errors.changes = 'Changes are required';
        if (!formValues.reasonForChanges) errors.reasonForChanges = 'Reason for change is required';
        if (!formValues.initiatedBy) errors.initiatedBy = 'Initiated by is required';
        if (!formValues.approvedBy) errors.approvedBy = 'Approved by is required';
        if (!formValues.reference) errors.reference = 'Reference is required';
        if (sign1.current.isEmpty()) errors.signature = 'Signature is required';
        if (amend.length === 0) errors.attachment = 'Attachment is required';
        return errors;
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        const errors = validateForm();

        if (Object.keys(errors).length === 0) {
            try {
                // Handle file upload if the signature is not empty
                const filename = new Date().getTime() + "captin_sign.png";
                let signatureResponse;

                if (!sign1.current.isEmpty()) {
                    const formData1 = new FormData();
                    formData1.append('file', dataURItoFile(sign1.current.getTrimmedCanvas().toDataURL("image/png"), filename));

                    signatureResponse = await API.post(FILE_URL, formData1, {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                        }
                    });

                    // Log the response to see if file upload was successful
                    console.log("Signature upload response: ", signatureResponse);
                }
                let data = ''
                // If the file upload was successful, update the form values
                if (signatureResponse && signatureResponse.status === 200) {
                    setFormValues(prevState => {
                        const newFormValues = {
                            ...prevState,
                            signature: { name: user.firstName, sign: signatureResponse.data.files[0].originalname }
                        };
                        // Log the updated form values
                        data = newFormValues
                        console.log("Updated form values: ", newFormValues);
                        return newFormValues;
                    });
                }




                const response1 = await API.post(RISK_UPDATE_WITH_ID_URL(id), data);

                // Log the API response
                console.log("API response: ", response1);

                if (response1.status === 200) {
                    console.log("Form successfully submitted and updated.");

                    onSubmitUpdate()
                }

                setIsLoading(true);

                // Simulate async operation
                setTimeout(() => {
                    setIsLoading(false);
                    onChangeModel(false);
                    resetForm();
                }, 1000);
            } catch (error) {
                console.error("File upload or API error: ", error);
            }
        } else {
            setFormErrors(errors);
        }
    };


    const resetForm = () => {
        setFormValues({
            reasonForReview: '',
            changes: '',
            reasonForChanges: '',
            initiatedBy: '',
            approvedBy: '',
            reference: '',
        });
        setFormErrors({});
        sign1.current.clear();
    };

    const amendUploads = async (e) => {
        // Handle file upload logic here
        const formData1 = new FormData();
        formData1.append('file', e.target.files[0]);

        try {
            const response = await API.post(FILE_URL, formData1, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                }
            });

            if (response && response.status === 200) {


                setAmend(prevAmend => [...prevAmend, response.data.files[0].originalname]);


            }
        } catch (error) {
            console.error("File upload error: ", error);
        }

    };

    useEffect(() => {

        setFormValues(prevState => ({
            ...prevState,
            attachment: amend // update the value of the new field
        }));

    }, [amend])

    const onDeleteAmendFiles = (index) => {
        const newAmend = [...amend];

        // Remove the element at the specified index
        newAmend.splice(index, 1);

        // Update the state with the modified array
        setAmend(newAmend);


        // Handle file delete logic here
    };

    return (
        <Modal
            show={show}
            size="lg"
            onHide={() => onChangeModel(false)}
            aria-labelledby="example-modal-sizes-title-md"
        >
            <Modal.Header closeButton></Modal.Header>
            <Modal.Body>
                <form className="forms row" onSubmit={handleSubmit}>
                    <div className="form-group col-6">
                        <label htmlFor="reasonForReview">Reason For Review</label>
                        <textarea
                            className="form-control"
                            name="reasonForReview"
                            value={formValues.reasonForReview}
                            onChange={handleInputChange}
                        ></textarea>
                        {formErrors.reasonForReview && <p className="text-danger">{formErrors.reasonForReview}</p>}
                    </div>
                    <div className="form-group col-6">
                        <label htmlFor="changes">Changes</label>
                        <textarea
                            className="form-control"
                            name="changes"
                            value={formValues.changes}
                            onChange={handleInputChange}
                        ></textarea>
                        {formErrors.changes && <p className="text-danger">{formErrors.changes}</p>}
                    </div>
                    <div className="form-group col-6">
                        <label htmlFor="reasonForChanges">Reason for Changes</label>
                        <textarea
                            className="form-control"
                            name="reasonForChanges"
                            value={formValues.reasonForChanges}
                            onChange={handleInputChange}
                        ></textarea>
                        {formErrors.reasonForChanges && <p className="text-danger">{formErrors.reasonForChanges}</p>}
                    </div>
                    <div className="form-group col-6">
                        <label htmlFor="initiatedBy">Initiated By</label>
                        <textarea
                            className="form-control"
                            name="initiatedBy"
                            value={formValues.initiatedBy}
                            onChange={handleInputChange}
                        ></textarea>
                        {formErrors.initiatedBy && <p className="text-danger">{formErrors.initiatedBy}</p>}
                    </div>
                    <div className="form-group col-6">
                        <label htmlFor="approvedBy">Approved By</label>
                        <textarea
                            className="form-control"
                            name="approvedBy"
                            value={formValues.approvedBy}
                            onChange={handleInputChange}
                        ></textarea>
                        {formErrors.approvedBy && <p className="text-danger">{formErrors.approvedBy}</p>}
                    </div>
                    <div className="form-group col-6">
                        <label htmlFor="reference">Reference</label>
                        <textarea
                            className="form-control"
                            name="reference"
                            value={formValues.reference}
                            onChange={handleInputChange}
                        ></textarea>
                        {formErrors.reference && <p className="text-danger">{formErrors.reference}</p>}
                    </div>
                    <div className="form-group col-12">
                        <label>Attachment If any</label>
                        <div className="form-group col-12">
                            <div
                                className="row mt-3"
                                style={{
                                    padding: 10,
                                    border: '1px dashed',
                                    borderRadius: 10,
                                }}
                            >
                                {amend.map((file, index) => (
                                    <div
                                        key={index}
                                        className="col-3 d-flex align-items-center justify-content-center"
                                        style={{
                                            position: 'relative',
                                        }}
                                    >
                                        <ImageComponent fileName={file} size={'100'} name={true}/>
                                        <i
                                            className="mdi mdi-delete"
                                            style={{
                                                position: 'absolute',
                                                top: '-8px',
                                                right: 22,
                                                color: 'red',
                                            }}
                                            onClick={() => onDeleteAmendFiles(index)}
                                        ></i>
                                    </div>
                                ))}
                            </div>
                        </div>
                        <label
                            className="d-flex justify-content-center mt-4"
                            htmlFor="files"
                        >
                            <i
                                style={{
                                    fontSize: 55,
                                    padding: 10,
                                    border: '1px dashed',
                                    borderRadius: 10,
                                }}
                                className="mdi mdi-cloud-upload"
                            ></i>
                        </label>
                        {formErrors.attachment && <p className="text-danger">{formErrors.attachment}</p>}
                        <Form.Control
                            id="files"
                            type="file"
                            style={{ display: 'none' }}
                            onChange={(e) => amendUploads(e)}
                        />
                    </div>
                    

                    <div className="" style={{ textAlign: 'center' }}>
                        <p>
                            I confirm that the risk assessment has been reviewed as above and
                            changes (if any) have been made as shown here. This change has
                            also been authorized by the relevant authority.
                        </p>
                    </div>
                    <div className="col-12" style={{ textAlign: 'center' }}>
                        <SignatureCanvas
                            penColor="#1F3BB3"
                            canvasProps={{
                                width: 500,
                                height: 200,
                                className: 'sigCanvas',
                                style: { boxShadow: '0px 0px 10px 3px rgb(189 189 189)' },
                            }}
                            ref={sign1}
                        />
                        {formErrors.signature && <p className="text-danger">{formErrors.signature}</p>}
                        <div className="col-12">
                            <button
                                className="btn btn-secondary"
                                onClick={(e) => {
                                    e.preventDefault();
                                    sign1.current.clear();
                                }}
                            >
                                Clear
                            </button>
                        </div>
                    </div>
                    <div className="col-12 d-flex justify-content-end" style={{ textAlign: 'left' }}>
                        <Button variant="primary" type="submit" disabled={isLoading}>
                            {isLoading ? 'Updating...' : 'Update'}
                        </Button>
                    </div>
                </form>
            </Modal.Body>
        </Modal>
    );
};

export default RiskUpdate;
