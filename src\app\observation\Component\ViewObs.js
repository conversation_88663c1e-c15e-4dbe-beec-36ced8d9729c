import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, Button, Form } from 'react-bootstrap';
import moment from "moment";
import GalleryPage from "../../apps/Gallery";
import API from "../../services/API";
import { STATIC_URL, USERS_URL } from "../../constants";
import ImageComponent from "../../services/FileDownlodS3";



const ViewObs = ({ reportData, showReportModal, setShowReportModal }) => {
    console.log()

    useEffect(() => {
        if (reportData && reportData.observationActions)
            getAllUsers();
    }, [reportData])
    let k = 0;
    const [actions, setActions] = useState([])
    const [users, setUsers] = useState([])
    const [lastActionOwnerObject, setlastActionOwnerObject] = useState(null)
    // let conditionAct = '';
    // try {
    //     const parsedRemarks = JSON.parse(reportData.remarks);
    //     conditionAct = parsedRemarks.condition_act ? `(${parsedRemarks.condition_act})` : '';
    // } catch (error) {
    //     console.error('Error parsing remarks:', error);
    // }
    // useEffect(() => { getAllUsers() }, [])

    // const getAllActions = async () => {
    //     if (reportData.actions && reportData.actions.length > 0) {
    //         const filteredAndFormattedData = reportData.actions
    //             .filter(item => item.actionType === 'reject' || item.actionType === 'approve' || item.actionType === 'reviewer')
    //             .map(item => ({
    //                 ...item,
    //                 evidence: item.uploads.map(upload => ({
    //                     src: `${STATIC_URL}/${upload}`,
    //                     width: 4,
    //                     height: 3
    //                 })),
    //                 createdDate: moment(item.createdDate).format('Do MMM YYYY hh:mm:ss A') // Including createdDate as requested
    //             }));

    //         console.log(filteredAndFormattedData, 'action')
    //         setActions(filteredAndFormattedData)
    //     }


    // }



    const getAllUsers = async () => {
        const response = await API.get(USERS_URL);
        setUsers(response.data)
    }

    function getName(id) {
        const user = users.find(user => user.id === id)
        return id ? user?.firstName : ''
    }

    function getCompany(id) {
        const user = users.find(user => user.id === id)
        console.log(user);
        if (user.type === 'External') {
            return id ? user.company : ''
        } else {
            return id ? 'STT GDC' : ''
        }
    }




    const getClosedDate = (item) => {

        return moment(item.createdDate).format('Do MMM YYYY, hh:mm:ss a')

    }

    // useEffect(() => {
    //     if (reportData) {
    //         lastActionOwner(reportData.actions)
    //     }


    // }, [lastActionOwnerObject])
    // const lastActionOwner = (dataArray) => {
    //     const actionOwnerObject = dataArray.find(item => item.actionType === "action_owner");


    //     setlastActionOwnerObject(actionOwnerObject)


    // }

    // const generatePdf = () => {
    //     const input = document.getElementById('pdf-content');
    //     const pdf = new jsPDF('p', 'mm', 'a4');
    //     const pdfWidth = pdf.internal.pageSize.getWidth();
    //     const pdfHeight = pdf.internal.pageSize.getHeight();
    //     const marginBottom = 10; // margin at the bottom of each page

    //     html2canvas(input).then((canvas) => {
    //         const imgData = canvas.toDataURL('image/png');
    //         const imgProps = pdf.getImageProperties(imgData);
    //         const imgHeight = (imgProps.height * pdfWidth) / imgProps.width;

    //         let heightLeft = imgHeight;
    //         let position = 0;

    //         pdf.addImage(imgData, 'PNG', 0, position, pdfWidth, imgHeight);
    //         heightLeft -= pdfHeight;

    //         while (heightLeft >= 0) {
    //             position = heightLeft - imgHeight + marginBottom;
    //             pdf.addPage();
    //             pdf.addImage(imgData, 'PNG', 0, position, pdfWidth, imgHeight);
    //             heightLeft -= pdfHeight;
    //         }

    //         pdf.save('permit-report.pdf');
    //     });
    // }
    return (
        <>




            {
                reportData && (<>

                    <div className="observation-report">
                        <div className=" p-4 pb-0">





                            <div className="row mt-4 mb-4">
                                {reportData.isQR && <div className="col-12">
                                    <p className="obs-title">Description</p>
                                    <p className="obs-dec obs-head-color">

                                        {reportData.describeActionTaken || reportData.describeAtRiskObservation || reportData.describeSafekObservation || "No description available"}

                                    </p>
                                </div>}

                                {!reportData.isQR && <div className="col-12">
                                    <p className="obs-title">Description</p>
                                    <p className="obs-dec obs-head-color">

                                        {reportData.description}

                                    </p>
                                </div>}

                            </div>

                            <div className="row mb-3">


                                <div className="col-md-6">
                                    <p className="obs-title">Category</p>
                                    <p className="obs-content">{reportData.observationCategory}</p>
                                </div>
                                <div className="col-md-6">
                                    <p className="obs-title">Type</p>
                                    <p className="obs-content">{reportData.observationType || ''} - {reportData.observationActOrCondition
                                    }</p>
                                </div>

                            </div>

                            <div className="col-md-12">
                                <p className="obs-title"> Images</p>
                                <div className="row">
                                    {reportData.uploads && reportData.uploads.length > 0 && (
                                        reportData.uploads.map((file, fileIndex) => (
                                            <div key={fileIndex} className="col-3" style={{ position: 'relative' }}>
                                                <div className="boxShadow d-flex align-items-center">
                                                    <ImageComponent fileName={file} size={'100'} name={true} />
                                                </div>
                                            </div>
                                        ))
                                    )}
                                </div>

                            </div>
                        </div>

                        <div className="obs-section p-4">
                            <div className="row mb-3">
                                <div className="col-md-6">
                                    <p className="obs-title">Reported Date</p>
                                    <p className="obs-content">{reportData && moment(reportData.created).format('Do MMM YYYY, hh:mm:ss a')}</p>
                                </div>
                                {/* {reportData.type !== 'Safe' ?
                                            reportData.rectifiedStatus !== 'Yes' ?
                                                <div className="col-md-6">
                                                    <p className="obs-title">Due Date</p>
                                                    <p className="obs-content">
                                                        {reportData && reportData.dueDate
                                                            ? moment(reportData.dueDate, ["DD-MM-YYYY", "DD/MM/YYYY"]).format('Do MMM YYYY')
                                                            : '-'}
                                                    </p>
                                                </div> : '' : ''} */}
                            </div>

                            {reportData.isQR ?
                                <div className="row">
                                    <div className="col-md-6">
                                        <p className="obs-title">Role</p>
                                        <p className="obs-content">{reportData.qrRole}</p>
                                    </div>

                                    <div className="col-md-6">
                                        <p className="obs-title">Hazard Category</p>
                                        <p className="obs-content"> {reportData.hazardCategory?.name}</p>
                                    </div>
                                    <div className="col-md-6">
                                        <p className="obs-title">Hazard Description</p>
                                        <p className="obs-content"> {reportData.hazardDescription?.name}</p>
                                    </div>
                                </div>
                                :
                                <div className="row">
                                    <div className="col-md-6">
                                        <p className="obs-title">Reporter</p>
                                        <p className="obs-content">{reportData.reporterId && reportData.reporter.firstName}</p>
                                    </div>

                                    {/* <div className="col-md-6">
                                        <p className="obs-title">Reporter Organization</p>
                                        <p className="obs-content"> {reportData.submittedId && getCompany(reportData.submittedId)}</p>
                                    </div> */}
                                </div>
                            }

                        </div>

                        <div className="obs-section p-4">
                            <div className="row mb-3">
                                <div className="col-md-6">
                                    <p className="obs-title">Location</p>
                                    <p className="obs-content"> {[reportData.locationOne, reportData.locationTwo, reportData.locationThree, reportData.locationFour, reportData.locationFive, reportData.locationSix]
                                        .filter(location => location?.name)
                                        .map(location => location.name)
                                        .join(' > ') || "N/A"}</p>

                                </div>


                            </div>

                            {/* <div className="row mb-3">
                                <div className="col-md-6">
                                    <p className="obs-title">Project/DC name</p>
                                    <p className="obs-content">{reportData.locationFour && reportData.locationFour.name}</p>
                                </div>

                                <div className="col-md-6">
                                    <p className="obs-title">Level</p>
                                    <p className="obs-content">{reportData.locationFive && reportData.locationFive.name}</p>
                                </div>
                            </div> */}

                            {/* <div className="row mb-3">
                                <div className="col-md-6">
                                    <p className="obs-title">Zone</p>
                                    <p className="obs-content">{reportData.locationSix && reportData.locationSix.name}</p>
                                </div>

                                <div className="col-md-6">
                                    <p className="obs-title">Work Activity</p>
                                    <p className="obs-content">{reportData.workActivity?.name || ''}</p>
                                </div>
                            </div> */}

                        </div>



                        {
                            reportData.observationType === 'Unsafe' ?
                                <div className="obs-section p-4">
                                    {console.log(reportData.observationActions)}


                                    {reportData.rectifiedOnSpot === false &&


                                        <>
                                            {reportData.observationActions &&

                                                reportData.observationActions.map((action, i) => {


                                                    if (action.actionType === 'take_action' || action.actionType === "reperform_action") {
                                                        const actionOwnerObject = reportData.observationActions.slice().reverse().find(item => item.actionType === "take_action");
                                                        console.log(actionOwnerObject)

                                                        k = k + 1;

                                                        return (
                                                            <div className="obs-section p-4">
                                                                <div className="row mb-3">
                                                                    <div className="col-md-12">
                                                                        <div className="row">

                                                                            {k === 1 ?
                                                                                <>
                                                                                    <div className="col-6">
                                                                                        <p className="obs-title"> Assigned Action {reportData.maskId} - A{k} </p>
                                                                                        <p className="obs-content">{action.actionToBeTaken}</p>
                                                                                    </div>
                                                                                    <div className="col-md-6">
                                                                                        <p className="obs-title">Due Date</p>
                                                                                        <p className="obs-content">
                                                                                            {action
                                                                                                ? moment(action.dueDate, [
                                                                                                    "DD-MM-YYYY",
                                                                                                    "DD/MM/YYYY",
                                                                                                    moment.ISO_8601,
                                                                                                ]).isValid()
                                                                                                    ? moment(action.dueDate, [
                                                                                                        "DD-MM-YYYY",
                                                                                                        "DD/MM/YYYY",
                                                                                                        moment.ISO_8601,
                                                                                                    ]).format("Do MMM YYYY")
                                                                                                    : "-"
                                                                                                : "-"}
                                                                                        </p>
                                                                                    </div>
                                                                                    <div className="col-6">
                                                                                        {action.status === 'Initiated' && <>

                                                                                            <p className="obs-title"> Action Assignee</p>
                                                                                            <p className="obs-content">{action.assignedToId &&
                                                                                                getName(action.assignedToId[0]
                                                                                                )}</p>

                                                                                        </>}
                                                                                    </div>


                                                                                </>
                                                                                :
                                                                                <>
                                                                                    <div className="col-md-12">
                                                                                        <p className="obs-title"> Action Verifier Comments & Reassigned Action {reportData.maskId} - A{k} </p>

                                                                                        <p className="obs-content">{action.comments}</p>
                                                                                    </div>
                                                                                    {/* <div className="col-md-6">
                                                                                        <p className="obs-title">Due Date</p>
                                                                                        <p className="obs-content">
                                                                                            {action
                                                                                                ? moment(action.dueDate, [
                                                                                                    "DD-MM-YYYY",
                                                                                                    "DD/MM/YYYY",
                                                                                                    moment.ISO_8601,
                                                                                                ]).isValid()
                                                                                                    ? moment(action.dueDate, [
                                                                                                        "DD-MM-YYYY",
                                                                                                        "DD/MM/YYYY",
                                                                                                        moment.ISO_8601,
                                                                                                    ]).format("Do MMM YYYY")
                                                                                                    : "-"
                                                                                                : "-"}
                                                                                        </p>
                                                                                    </div> */}

                                                                                    {/* <p className="obs-title">Action To Be Taken</p>
                                                                                    <p className="obs-content">{action.actionToBeTaken}</p> */}
                                                                                    <div className="col-12">
                                                                                        {action.status === 'Initiated' && <>

                                                                                            <p className="obs-title"> Action Assignee</p>
                                                                                            <p className="obs-content">{action.assignedToId &&
                                                                                                getName(action.assignedToId[0]
                                                                                                )}</p>

                                                                                        </>}
                                                                                    </div>


                                                                                </>
                                                                            }

                                                                            {/* {actionOwnerObject ?
                                                                                        action.id === actionOwnerObject.id &&

                                                                                        <div className="col-md-6">
                                                                                            <p className="obs-title">Due Date</p>
                                                                                            <p className="obs-content">
                                                                                                {reportData && reportData.dueDate
                                                                                                    ? moment(reportData.dueDate, ["DD-MM-YYYY", "DD/MM/YYYY"]).format('Do MMM YYYY')
                                                                                                    : '-'}
                                                                                            </p>
                                                                                        </div>
                                                                                        : ''
                                                                                    } */}
                                                                        </div>
                                                                    </div>
                                                                </div>


                                                                {action.status === 'Completed' && <>
                                                                    <div className="row mb-3">
                                                                        <div className="col-md-12">
                                                                            <p className="obs-title">Action Taken </p>
                                                                            <p className="obs-content">{action.actionTaken}</p>
                                                                        </div>
                                                                    </div>

                                                                    <div className="row mb-3">
                                                                        <div className="col-md-6">
                                                                            <p className="obs-title">Action Taken By</p>
                                                                            <p className="obs-content">{action.assignedToId &&
                                                                                getName(action.assignedToId[0]
                                                                                )}</p>
                                                                        </div>
                                                                        <div className="col-md-6">
                                                                            <p className="obs-title">Date</p>
                                                                            <p className="obs-content">{getClosedDate(action)}</p>
                                                                        </div>
                                                                    </div>

                                                                    <div className="col-md-12">
                                                                        {action.uploads && <>
                                                                            <p className="obs-title">Evidence</p>
                                                                            <div className="d-flex">


                                                                                {action.uploads.map((item, index) => {

                                                                                    return (
                                                                                        <div

                                                                                            className="col-3"
                                                                                            style={{ position: 'relative' }}
                                                                                        >
                                                                                            <div className="boxShadow d-flex align-items-center">
                                                                                                <ImageComponent
                                                                                                    fileName={item}
                                                                                                    size={'100'}
                                                                                                    name={true}
                                                                                                />

                                                                                            </div>
                                                                                        </div>
                                                                                    )

                                                                                })}
                                                                            </div>

                                                                        </>
                                                                        }

                                                                    </div>
                                                                </>
                                                                }

                                                            </div>
                                                        )


                                                    } else if (action.actionType === 'verify_action') {

                                                        return (
                                                            <div className="obs-section p-4">

                                                                {action.status === 'Initiated' ?

                                                                    <div className="row mb-3">
                                                                        <div className="col-md-6">
                                                                            <p className="obs-title">Action Verifier - A{k}</p>
                                                                            <p className="obs-content">{getName(action.assignedToId[0])}</p>
                                                                        </div>
                                                                       
                                                                    </div>
                                                                    : action.status === 'Completed' ? <>

                                                                        <div className="row mb-3">
                                                                            <div className="col-md-6">
                                                                                <p className="obs-title">Action Verified By</p>
                                                                                <p className="obs-content">{reportData.reviewerId && getName(reportData.reviewerId)}</p>
                                                                            </div>
                                                                            <div className="col-md-6">
                                                                                <p className="obs-title">Date</p>
                                                                                <p className="obs-content">{moment(action.createdDate).format('Do MMM YYYY, hh:mm:ss a')}</p>
                                                                            </div>
                                                                        </div>
                                                                       
                                                                    </> : ''}

                                                            </div>
                                                        )
                                                    }
                                                   
                                                    else if (action.actionType === 'review') {
                                                        return (
                                                            <div className="obs-section p-4">


                                                                <div className="row mb-3">
                                                                    <div className="col-md-6">
                                                                        <p className="obs-title">{action.status === 'Initiated' ? "Action Reviewer" : "Action Reviewed By"}</p>
                                                                        <p className="obs-content">{action.assignedToId && getName(action.assignedToId[0])}</p>
                                                                    </div>
                                                                    <div className="col-md-6">
                                                                        <p className="obs-title">Date</p>
                                                                        <p className="obs-content">{moment(action.createdDate).format('Do MMM YYYY, hh:mm:ss a')}</p>
                                                                    </div>
                                                                </div>
                                                               

                                                            </div>
                                                        )
                                                    }

                                                })}




                                        </>}





                                </div>
                                : ''}
                        {reportData.rectifiedOnSpot && (<>
                            <div className="obs-section p-4">
                                <div className="col-md-12">



                                    <div className="row mb-3">
                                        <div className="col-md-12">
                                            <p className="obs-title">Action Taken </p>
                                            <p className="obs-content">{reportData.actionTaken}</p>
                                        </div>
                                    </div>

                                    <div className="row">
                                        <div className="col-12">

                                            {reportData.evidence && <>
                                                <p className="obs-title">Evidence</p>
                                                <div className="row">


                                                    {reportData.evidence.map((item, index) => {

                                                        return (
                                                            <div

                                                                className="col-3"
                                                                style={{ position: 'relative' }}
                                                            >
                                                                <div className="boxShadow d-flex align-items-center">
                                                                    <ImageComponent
                                                                        fileName={item}
                                                                        size={'100'}
                                                                        name={true}
                                                                    />

                                                                </div>
                                                            </div>
                                                        )

                                                    })}
                                                </div>

                                            </>
                                            }
                                        </div>
                                    </div>



                                </div>
                            </div>
                        </>)
                        }
                    </div>





                </>)
            }


        </>
    )
}

export default ViewObs;