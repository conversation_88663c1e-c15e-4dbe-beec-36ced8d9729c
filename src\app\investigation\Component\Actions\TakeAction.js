import React, { useEffect, useState } from 'react'
import { ProgressBar, Form, Col, Row, Card, Button, Modal } from 'react-bootstrap';
import moment from 'moment';
import ViewDetails from '../ViewDetails';
import { InputTextarea } from 'primereact/inputtextarea'
import { DropzoneArea } from 'material-ui-dropzone';
import ImageComponent from '../../../services/FileDownlodS3';
import API from '../../../services/API';
import { GET_USER_BY_ROLES_URL, FILE_URL } from '../../../constants';
import Select from 'react-select';
import ViewInvestigation from '../ViewInvestigation';
import { Accordion, AccordionTab } from 'primereact/accordion';

const TakeAction = ({ show, type, data, applicationDetails, handleClose, reportToApprove, submitActionType }) => {
    const [reviewer, setReviewer] = useState([])
    const [reviewSelect, setReviewerSelect] = useState(null)
    const [actionToBeTaken, setActionToBeTaken] = useState(type.actionToBeTaken || '');
    const [actionTaken, setActionTaken] = useState('');
    const [actionComments, setActionCommends] = useState('');
    const [files, setFiles] = useState([]);
    const [errors, setErrors] = useState({});


    useEffect(() => {
        if (show) {
            fetchReviewer();
        }
    }, [show]);

    const fetchReviewer = async () => {
        try {
            const response = await API.post(GET_USER_BY_ROLES_URL, {
                locationOneId: "",
                locationTwoId: "",
                locationThreeId: "",
                locationFourId: "",
                mode: 'Incident_action_reviewer'
            });
            const assigneeOptions = response.data.map(assignee => ({
                value: assignee.id,
                label: assignee.firstName,
            }));
            setReviewer(assigneeOptions);
        } catch (error) {
            console.error("Error fetching assignees:", error);
        }
    };
    const handleFileChange = async (value) => {
        if (value.length > 0) {
            const latestFile = value[value.length - 1];
            const formData1 = new FormData();
            formData1.append('file', latestFile);

            try {
                const response = await API.post(FILE_URL, formData1, {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                    }
                });

                if (response && response.status === 200) {
                    // Add the uploaded file to the existing files array
                    setFiles((prevFiles) => [...prevFiles, response.data.files[0].originalname]); // Assuming response.data contains file information
                }
            } catch (error) {
                console.error("File upload error: ", error);
            }
        }
    };
    const handleRemoveMainImage = (index) => {
        setFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
    };
    const validateForm = () => {
        const newErrors = {};

        if (!actionToBeTaken) newErrors.actionToBeTaken = 'Action to be taken is required.';
        if (!actionTaken) newErrors.actionTaken = 'Action taken is required.';
        if (files.length === 0) newErrors.files = 'Please upload at least one file.';
        if (!reviewSelect) newErrors.reviewer = 'Please select a reviewer.';

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };
    const submitToApprove = () => {

        if (validateForm()) {
            // Submit form data
            const formData = {
                actionToBeTaken,
                actionTaken,
                uploads: files,
                reviewerId: reviewSelect.value,
            };
            console.log('Submitting:', formData);

            reportToApprove(formData)
            // Call API here with formData
        }
    };

    const submitActionApprove = (actionType) => {
        if (actionType === 'Returned' && !actionComments.trim()) {
            // If actionType is 'return' and comments are empty, show an error
            setErrors((prev) => ({ ...prev, actionComments: 'Comments are required to return the task.' }));
            return; // Prevent submission
        }

        setErrors({});

        // Proceed with your submission logic
        const payload = {
            actionType,
            actionComments,
            // other data to submit
        };


        submitActionType(payload)
        // Call the API or submit the data
        console.log('Submitting:', payload);
    }

    return (
        <Modal
            show={show}
            size="lg"
            onHide={() => handleClose()}
            aria-labelledby="example-modal-sizes-title-md"
            backdrop="static"
        >
            <Modal.Header closeButton>
                <Modal.Title style={{ width: '100%' }}>
                    <div className='row'>
                        <div className='col-9'>
                            {type.prefix === 'NTCM-TASK' || type.prefix === 'PICM-TASK' ? (
                                type.actionType === 'perform_task' || type.actionType === 'reperform_task' ? (
                                    `${type.prefix === 'NTCM-TASK' ? 'Implement Near Term Control Measure' : 'Implement Post Investigation Control Measure'} - ${type.maskId}`
                                ) : (
                                    'Verify Action'
                                )
                            ) : (
                                ''
                            )}
                        </div>
                        <div className='col-3'>
                            DueDate : {moment(type.dueDate).format('DD-MM-YYYY')}
                        </div>
                    </div>
                </Modal.Title>

            </Modal.Header>

            <Modal.Body>
                {(type.actionType === 'perform_task' || type.actionType === 'reperform_task') &&
                    <i className='d-flex mb-3'>You have been assigned action to undertake the below mentioned actions for the reported incident. Please take the action within the stipulated time-frame and upload evidence of action taken for review and subsequent closure. You can view full details about the incident as-reported from the “All Incidents” tab in this application by looking up this Incident Number. </i>
                }
                {/* {type.prefix === 'PICM-TASK' ?
                    <ViewInvestigation type={type} data={data} applicationDetails={applicationDetails} />
                    :
                    <ViewDetails types={type} data={applicationDetails} disable={true} type={'view'} details={true} />
                } */}
                {/* Accordion to toggle between ViewInvestigation and ViewDetails */}
                <Accordion activeIndex={0}>
                    <AccordionTab header={type.prefix === 'PICM-TASK' ? 'Investigation Details' : 'Incident Details'}>
                        {type.prefix === 'PICM-TASK' ? (
                            <ViewInvestigation type={type} data={data} applicationDetails={applicationDetails} />
                        ) : (
                            <ViewDetails types={type} data={applicationDetails} disable={true} type="view" details={true} />
                        )}
                    </AccordionTab>
                </Accordion>
                <hr />

                <h4 className='fw-bold mb-3'>Near Term Control Measure to be Implemented</h4>

                <div className="row mb-4">
                    {(type.actionType === 'perform_task' || type.actionType === 'reperform_task') && (<>
                        <div className="col-12 mb-4">
                            <p className="obs-title">Action To be Taken</p>
                            {type.actionType === 'reperform_task' ?
                                <p className="obs-content mb-3">{type.description || ''}</p>
                                :
                                <p className="obs-content mb-3">{type.actionToBeTaken || ''}</p>
                            }
                        </div>
                        {type.actionType === 'reperform_task' &&
                            <div className="col-12 mb-4">
                                <p className="obs-title">Reviewer Comments</p>
                                <p className="obs-content mb-3">{type.comments || ''}</p>
                            </div>
                        }

                        <div className="col-12 mb-4">
                            <p className="obs-title">Describe the actions taken</p>
                            <InputTextarea
                                style={{ width: '100%' }}
                                rows={2}
                                autoResize
                                value={actionTaken}
                                onChange={(e) => {
                                    setActionTaken(e.target.value);
                                    setErrors((prev) => ({ ...prev, actionTaken: '' }));
                                }}
                            />
                            {errors.actionTaken && <span className="text-danger">{errors.actionTaken}</span>}
                        </div>

                        <div className="col-12 mt-3">
                            <label htmlFor="imageUploads" className="mb-2">Attach necessary evidence to support the actions taken</label>
                            <DropzoneArea
                                acceptedFiles={['image/jpeg', 'image/png']}
                                dropzoneText={"Drag 'n' drop some files here, or click to select files"}
                                filesLimit={5}
                                maxFileSize={104857600}
                                onChange={handleFileChange}
                                showPreviewsInDropzone={false}
                                showPreviews={false}
                                dropzoneClass="dropzoneText d-flex align-items-center justify-content-center"
                            />
                            {errors.files && <span className="text-danger">{errors.files}</span>}
                        </div>

                        <div className="col-12 mt-3 mb-3">
                            <div className="row">
                                {files && files.map((item, m) => (
                                    <div key={m} className="col-3" style={{ position: 'relative' }}>
                                        <div className="boxShadow d-flex align-items-center justify-content-center">
                                            <ImageComponent fileName={item} size="100" name={true} />
                                            <i
                                                className="pi pi-trash"
                                                onClick={() => handleRemoveMainImage(m)}
                                                style={{
                                                    position: 'absolute',
                                                    top: '5px',
                                                    right: '5px',
                                                    cursor: 'pointer',
                                                    color: 'red',
                                                }}
                                            />
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>

                        <div className="col-6 mt-3">
                            <label htmlFor="reviewer" className="mb-2">Select designated Action Reviewer</label>
                            <Select
                                options={reviewer}
                                value={reviewSelect}
                                onChange={(option) => {
                                    setReviewerSelect(option);
                                    setErrors((prev) => ({ ...prev, reviewer: '' }));
                                }}
                                placeholder="Select"
                                isClearable
                                menuPortalTarget={document.body}
                                menuPosition="fixed"
                                styles={{
                                    menuPortal: (base) => ({ ...base, zIndex: 9999 }),
                                }}
                            />
                            {errors.reviewer && <span className="text-danger">{errors.reviewer}</span>}
                        </div>
                    </>)}
                    {type.actionType === 'verify_task' && <>
                        <div className="col-md-12">
                            <p className="obs-title">Action To be taken</p>
                            <p className="obs-content mb-3">{type.actionToBeTaken || ''}</p>
                        </div>

                        <div className="col-md-12">
                            <p className="obs-title">Action taken</p>
                            <p className="obs-content mb-3">{type.actionTaken || ''}</p>
                        </div>
                        <div className="col-12 mt-3 mb-3">
                            <label htmlFor="imageUploads" className="obs-title mb-2">Evidences</label>
                            <div className="row">
                                {type.uploads && type.uploads.map((item, m) => (
                                    <div key={m} className="col-3" style={{ position: 'relative' }}>
                                        <div className="boxShadow d-flex align-items-center justify-content-center">
                                            <ImageComponent fileName={item} size="100" name={true} />

                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>


                        <div className="col-12 mb-4">
                            <p className="obs-title">Comments</p>
                            <InputTextarea
                                style={{ width: '100%' }}
                                rows={2}
                                autoResize
                                value={actionComments}
                                onChange={(e) => {
                                    setActionCommends(e.target.value);
                                    setErrors((prev) => ({ ...prev, actionComments: '' }));
                                }}
                            />
                            {errors.actionComments && <span className="text-danger">{errors.actionComments}</span>}
                        </div>
                    </>}
                </div>



            </Modal.Body>

            <Modal.Footer className="flex-wrap">
                {type.actionType === 'perform_task' || type.actionType === 'reperform_task' ? (<>

                    <Button className="btn btn-primary" onClick={() => submitToApprove()}>
                        submit to Action Reviewer
                    </Button>

                </>) : type.actionType === 'verify_task' ? (
                    <>

                        <Button className="btn btn-primary" onClick={() => submitActionApprove('Returned')}>
                            Return To Assignee
                        </Button>
                        <Button className="btn btn-secondary" onClick={() => submitActionApprove('Completed')}>
                            Approve
                        </Button>
                    </>
                ) : null}

            </Modal.Footer>

        </Modal>
    )
}

export default TakeAction