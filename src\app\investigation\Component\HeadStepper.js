import React from 'react';

const HeadStepper = ({ stages, stageKeyMap, stageStatus, activeStage, handleStageClick, getStatusClass }) => {
  return (
    <>
      <div className="stepper-container mt-4">

        {stages.map((stage, index) => {
          const stageKey = stageKeyMap[stage];
          const statusClass = getStatusClass(stageStatus[stageKey]);

          return (
            <div key={index} className="stepper-step" onClick={() => handleStageClick(index + 1)}>
              <div className={`circle ${statusClass} `}>
                {index + 1}
              </div>
              {index < stages.length - 1 && <div className="line"></div>}
              <div className={`step-title ${activeStage === index + 1 ? 'active' : ''}`}>{stage}</div>
            </div>
          );
        })}
      </div>
      <div className='color mb-3 mt-3 d-flex justify-content-end'>
        <div className="d-flex align-items-center me-4">
          <div className="instruction-circle" style={{ backgroundColor: '#28a745' }}></div>
          <span className="ms-2">Finalized</span>
        </div>
        <div className="d-flex align-items-center me-4">
          <div className="instruction-circle" style={{ backgroundColor: '#ffa500' }}></div>
          <span className="ms-2">Drafted</span>
        </div>
        <div className="d-flex align-items-center">
          <div className="instruction-circle" style={{ backgroundColor: '#808080' }}></div>
          <span className="ms-2">No information entered</span>
        </div>
      </div>
    </>
  );
};

export default HeadStepper;
