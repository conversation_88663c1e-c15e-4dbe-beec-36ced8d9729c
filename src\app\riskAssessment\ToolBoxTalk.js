import React, { useState } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Dropdown } from 'primereact/dropdown';
import { InputText } from 'primereact/inputtext';
import { format } from 'date-fns';
import { Modal } from 'react-bootstrap';
import MyLogoComponent from '../eptw-gen/Component/MyLogoComponent';
import ImageComponent from '../services/FileDownlodS3';
import ViewToolboxTalk from './Component/ViewToolboxTalk';

export default function ToolBoxTalk({ data }) {
  const [filters, setFilters] = useState({});
  const [globalFilter, setGlobalFilter] = useState('');
  const [riskData, setRiskData] = useState('')
  const [showModal, setShowModal] = useState(false)

  const yesNoOptions = [
    { label: 'YES', value: 'YES' },
    { label: 'NO', value: 'NO' }
  ];

  const conductedByOptions = [
    { label: 'Supervisor', value: 'Supervisor' },
    { label: 'Safety Officer', value: 'Safety Officer' },
    { label: 'Manager', value: 'Manager' }
  ];

  const viewRisk = (data) => {

    setRiskData(data);  // Clear any previous data
    setShowModal(true)
  }

  const tbtIdTemplate = (rowData) => {
    return (
      <div className='maskid' onClick={() => viewRisk(rowData)}>
        {rowData.maskId}
      </div>
    );
  };

  const locationBodyTemplate = (row) => {
    if (row.isCustomLocation) {
      return row.customLocation;
    } else {
      return (
        <>

          {row?.locationOne?.name && (
            <>
              {row.locationOne.name}
              {row.locationTwo?.name && ' > '}
            </>
          )}
          {row?.locationTwo?.name && (
            <>
              {row.locationTwo.name}
              {row.locationThree?.name && ' > '}
            </>
          )}
          {row?.locationThree?.name && (
            <>
              {row.locationThree.name}
              {row.locationFour?.name && ' > '}
            </>
          )}
          {row?.locationFour?.name && row.locationFour.name}
        </>
      );
    }
  };
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return format(date, 'dd-MM-yyyy hh:mm a'); // e.g., 29-03-2025 08:05 AM
  };


  const yesNoBodyTemplate = (value) => {
    return value ? 'YES' : 'NO';
  };
  return (
    <div className="card p-3">
      {/* Description Section */}
      <div className="mb-4">
        <p>
          This table provides a <strong>centralized digital record</strong> of all safety briefings (Tool Box Talks) conducted on the AcuiZen platform.
          Click on the <strong>TBT ID</strong> field to view detailed records, including attachments, filled out checklists and other actions.
        </p>
      </div>

      {/* Search Box */}
      <div className="d-flex justify-content-end mb-2">
        <span className="p-input-icon-left">
          <i className="pi pi-search" />
          <InputText
            value={globalFilter}
            onChange={(e) => setGlobalFilter(e.target.value)}
            placeholder="Search"
          />
        </span>
      </div>

      {/* Data Table */}
      <DataTable value={data} paginator rows={5} rowsPerPageOptions={[5, 10, 25]} globalFilter={globalFilter} tableStyle={{ minWidth: '90rem' }}>

        <Column field="maskId" header="TBT ID " sortable body={tbtIdTemplate}></Column>

        <Column field="workArea" header="Work Area " body={locationBodyTemplate}></Column>

        <Column
          field="conductedBy.firstName"
          header="Conducted By"
          filter
          filterElement={
            <Dropdown
              value={filters.conductedBy}
              options={conductedByOptions}
              onChange={(e) => setFilters(prev => ({ ...prev, conductedBy: e.value }))}
              placeholder="Select"
              showClear
            />
          }
        ></Column>

        <Column field="noOfPersonsParticipated" header="No of Participants"></Column>

        <Column
          field="commenceDate"
          header="Start Time"
          sortable
          body={(rowData) => formatDate(rowData.commenceDate)}
        />

        <Column
          field="created"
          header="End Time"
          body={(rowData) => formatDate(rowData.created)}
        />


        <Column
          field="controls.isAdditionalControlsIdentified"
          header="Deviations Noted"
          body={(rowData) => yesNoBodyTemplate(rowData.controls?.isAdditionalControlsIdentified)}
          filter
          filterElement={
            <Dropdown
              value={filters.deviationsNoted}
              options={yesNoOptions}
              onChange={(e) => setFilters(prev => ({ ...prev, deviationsNoted: e.value }))}
              placeholder="Select"
              showClear
            />
          }
        />

        <Column
          field="controls.isAdditionalControlsIdentified"
          header="Additional Controls Implemented"
          body={(rowData) => yesNoBodyTemplate(rowData.controls?.isAdditionalControlsIdentified)}
          filter
          filterElement={
            <Dropdown
              value={filters.additionalControls}
              options={yesNoOptions}
              onChange={(e) => setFilters(prev => ({ ...prev, additionalControls: e.value }))}
              placeholder="Select"
              showClear
            />
          }
        />

        <Column
          field="isCloseOutChallenges"
          header="Difficulties Encountered"
          body={(rowData) => yesNoBodyTemplate(rowData.isCloseOutChallenges)}
          filter
          filterElement={
            <Dropdown
              value={filters.difficulties}
              options={yesNoOptions}
              onChange={(e) => setFilters(prev => ({ ...prev, difficulties: e.value }))}
              placeholder="Select"
              showClear
            />
          }
        />

      </DataTable>


      {showModal &&

        <Modal size="lg" show={showModal} onHide={() => { setRiskData(null); setShowModal(false) }}>
          <Modal.Header closeButton>
            {riskData && (
              <div className="row" style={{ width: '100%' }}>
                <div className="col-9">
                  <div className="row">
                    <div className="col-3" style={{ borderRight: '1px solid #D1D5DB' }}>
                      {/* <img src={logo} className="me-3" alt="logo" style={{ maxWidth: '125px' }} /> */}
                      <ImageComponent fileName={localStorage.getItem('logo')} />
                    </div>
                    <div className="col-9">
                      <h4>ToolBoxTalk</h4>
                      <div className="d-flex align-items-center">
                        <p className="me-2">#{riskData.maskId || ''} </p>
                      
                      </div>
                    </div>
                  </div>
                </div>
                <div className="col-3 d-flex justify-content-end align-items-center">
                 
                </div>

              </div>
            )}

          </Modal.Header>
          <Modal.Body>
            <ViewToolboxTalk formData={riskData}/>
          </Modal.Body>

        </Modal>
      }
    </div>
  );
}
