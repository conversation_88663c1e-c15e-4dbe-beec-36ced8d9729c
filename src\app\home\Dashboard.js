import React, { useState } from 'react';
import { <PERSON>, Tabs, Tab, Typography, <PERSON><PERSON>ield, Grid, Button } from '@mui/material';
import { useSelector } from 'react-redux';
import AllFilterLocation from '../investigation/LocationDropDown';
// Sample services array
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import EPTW from './EPTW'
import "primereact/resources/primereact.css";
import "primereact/resources/themes/saga-blue/theme.css";
import "primeicons/primeicons.css";

function Dashboard() {
    const [activeTab, setActiveTab] = useState(0);
    const [formData, setFormData] = useState({
        "locationOneId": "",
        "locationTwoId": "",
        "locationThreeId": "",
        "locationFourId": "",
        "locationFiveId": "",
        "locationSixId": "",
    })

    const services = useSelector((state) => state.service.service)
    const [fromDate, setFromDate] = useState(null);
    const [toDate, setToDate] = useState(null);

    const [filteredServices, setFilteredServices] = useState([]);
    const handleTabChange = (event, newValue) => {
        setActiveTab(newValue);
    };
    const handleFilter = (locationOneId, locationTwoId, locationThreeId, locationFourId, locationFiveId, locationSixId) => {
        setFormData(prev => ({ ...prev, locationOneId: locationOneId, locationTwoId: locationTwoId, locationThreeId: locationThreeId, locationFourId: locationFourId, locationFiveId: locationFiveId, locationSixId: locationSixId }));

    };

    const applyDateFilter = () => {
        if (fromDate && toDate) {

            console.log(fromDate)
            console.log(toDate)
        } else {
            console.log('Please select both From Date and To Date.');
        }
    };
    return (
        <Box sx={{ width: '100%', marginTop: '40px' }}>
            {/* Tabs */}

            <AllFilterLocation handleFilter={handleFilter} getLocation={formData} />
            <h4>Date Filter</h4>
            <Box sx={{ mt: 2, mb: 4 }}>
                <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} sm={6} md={3}>
                    <div style={{ position: "relative", width: "100%" }}>
                        <DatePicker
                            selected={fromDate}
                            onChange={(date) => setFromDate(date)}
                            placeholderText="From Date"
                            dateFormat="yyyy-MM-dd"
                            className="form-control"
                            customInput={
                                <input
                                    type="text"
                                    className="form-control"
                                    style={{ paddingLeft: "2.5rem" }}
                                />
                            }
                        />
                        <i
                            className="pi pi-calendar"
                            style={{
                                position: "absolute",
                                top: "50%",
                                left: "10px",
                                transform: "translateY(-50%)",
                                color: "#757575",
                                fontSize: "1.2rem",
                            }}
                        ></i>
                    </div>
                </Grid>

                {/* To Date */}
                <Grid item xs={12} sm={6} md={3}>
                    <div style={{ position: "relative", width: "100%" }}>
                        <DatePicker
                            selected={toDate}
                            onChange={(date) => setToDate(date)}
                            placeholderText="To Date"
                            dateFormat="yyyy-MM-dd"
                            minDate={fromDate} // Prevent selecting a date earlier than the From Date
                            className="form-control"
                            customInput={
                                <input
                                    type="text"
                                    className="form-control"
                                    style={{ paddingLeft: "2.5rem" }}
                                />
                            }
                        />
                        <i
                            className="pi pi-calendar"
                            style={{
                                position: "absolute",
                                top: "50%",
                                left: "10px",
                                transform: "translateY(-50%)",
                                color: "#757575",
                                fontSize: "1.2rem",
                            }}
                        ></i>
                    </div>
                </Grid>

                </Grid>
            </Box>

          
            <Tabs
                value={activeTab}
                onChange={handleTabChange}
                indicatorColor="primary"
                textColor="primary"

            >
                {services.length !== 0 &&
                    services.map((item, i) => (
                        <Tab key={i} label={item.name} />
                    ))}
            </Tabs>

            {/* Tab Content */}
            <Box sx={{ p: 3 }} >
                {services.length !== 0 &&
                    services.map((item, i) => (
                        activeTab === i && (
                            <Box key={i}>
                                {/* <Typography>
                                    <strong>{item.name}</strong>: {item.url}
                                </Typography> */}
                                {item.maskName === "EPTW-GEN" && <EPTW  formData={formData} fromDate={fromDate} toDate={toDate} />}
                            </Box>
                        )
                    ))}
            </Box>
        </Box>
    );
}

export default Dashboard;
