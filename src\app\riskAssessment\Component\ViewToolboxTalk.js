import React from 'react';
import moment from 'moment';
import ImageComponent from '../../services/FileDownlodS3';

function ViewToolboxTalk({ formData }) {
    return (
        <div className="container ">
            <div className="card shadow-sm">
                <div className="card-body">

                    {/* Section: Toolbox Talk Info */}
                    {/* <h5 className="mb-3 border-bottom pb-2">Toolbox Talk Details</h5> */}
                    <div className="row mb-3">
                        <div className="col-md-6">
                            <strong>Commence TBT:</strong><br />
                            {formData.commenceDate
                                ? moment(formData.commenceDate).format("DD-MM-YYYY HH:mm")
                                : "Not started"}
                        </div>
                        <div className="col-md-6">
                            <strong>Conducted By:</strong><br />
                            {formData.conductedBy?.firstName || "-"}
                        </div>
                    </div>

                    <div className="row mb-3">
                        <div className="col-md-6">
                            <strong>Work Activity:</strong><br />
                            {formData.riskAssessment?.workActivity?.name || "-"}
                        </div>
                        <div className="col-md-6">
                            <strong>Location:</strong><br />
                            {[formData?.locationOne?.name, formData?.locationTwo?.name, formData?.locationThree?.name, formData?.locationFour?.name]
                                .filter(Boolean)
                                .join(' > ') || "-"}
                        </div>
                    </div>

                    <div className="mb-3">
                        <strong>Number of Persons Participated:</strong><br />
                        {formData.noOfPersonsParticipated ?? "-"}
                    </div>

                    <div className="mb-3">
                        <strong>Evidence:</strong><br />
                        <div className='row'>
                            {formData.uploads.map((item) => {

                                return (
                                    <div className='col-3'>
                                        <ImageComponent size={'100'} fileName={item} />
                                    </div>
                                )
                            })}
                        </div>
                    </div>

                    {/* Section: Sub Activities */}
                    {formData?.tasks?.map((group, groupIndex) => (
                        <div key={groupIndex} className="mb-4">
                            <h6 className="text-primary border-bottom pb-1">Sub-Activity {groupIndex + 1}</h6>

                            {group.activity?.type === "activity" && (
                                <div className="mb-2"><strong>Activity:</strong> {group.activity.name}</div>
                            )}
                            <div className='row'>
                                {/* Hazards */}
                                {group.hazards?.selected?.map((hazard, hazardIndex) => (
                                    <div key={hazard.id} className="border rounded p-2 mb-3 bg-light col-6">
                                        <div className="d-flex align-items-center">
                                            <img
                                                src={`https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/hazards/${hazard.image}`}
                                                alt={hazard.name}
                                                style={{ width: 60, height: 60, marginRight: 16, borderRadius: 10 }}
                                            />
                                            <div>
                                                <strong>{hazard.name}</strong><br />
                                                <span>{hazard.toolbox_value}</span>
                                                {(hazard.toolbox_value === "No" || hazard.toolbox_value === "Not Applicable") && (
                                                    <div><strong>Remarks:</strong> {hazard.toolbox_remarks}</div>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>

                            {/* Controls */}
                            {group.currentControl?.option?.map((control, controlIndex) => (
                                <div key={controlIndex} className="mb-3 ps-3 border-start">
                                    <div>{control.value || "No control description provided"}</div>
                                    <div><strong>Implemented:</strong> {control.toolbox_value}</div>
                                    {(control.toolbox_value === "No" || control.toolbox_value === "Not Applicable") && (
                                        <div><strong>Remarks:</strong> {control.toolbox_remarks}</div>
                                    )}
                                </div>
                            ))}
                        </div>
                    ))}

                    {/* Additional Controls */}
                    <h5 className="mt-4 border-bottom pb-2">Additional Controls</h5>
                    <div className="mb-3">
                        <strong>Identified:</strong> {formData.controls.isAdditionalControlsIdentified ? "Yes" : "No"}
                    </div>
                    {formData.controls.isAdditionalControlsIdentified && (
                        <div className="mb-3">
                            <strong>Description:</strong> {formData.controls.describeAdditionalControls}
                        </div>
                    )}
                    <div className="mb-3">
                        <strong>Team Briefed:</strong> {formData.controls.teamBrief}
                        {(formData.controls.teamBrief === "No" || formData.controls.teamBrief === "Not Applicable") && (
                            <div><strong>Remarks:</strong> {formData.controls.teamBriefRemarks}</div>
                        )}
                    </div>

                    {/* Section: Signatures */}
                    <h5 className="mt-4 border-bottom pb-2">Team Member Acknowledgement</h5>
                    {formData.toolboxSignStatuses.map((item, index) => (
                        <div key={index} className="mb-3">
                            <strong>Member:</strong> {item.signedBy?.firstName || "-"}
                            <div className="mt-2">
                                <ImageComponent size={150} fileName={item.sign} />
                            </div>
                            <div className=''>{moment(item.signedDate).format("DD-MM-YYYY HH:mm")}</div>
                        </div>
                    ))}

                    {/* Section: Close Out */}
                    <h5 className="mt-4 border-bottom pb-2">Close Out</h5>
                    <div className="mb-3">
                        <strong>Unexpected Challenges:</strong> {formData.isCloseOutChallenges ? "Yes" : "No"}
                    </div>
                    {formData.isCloseOutChallenges && formData.closeOutChallenges?.map((item, index) => (
                        <div key={index} className="mb-3 border p-2 rounded bg-light">
                            <div><strong>Challenge:</strong> {item.unexpectedChallenges}</div>
                            <div><strong>Remarks:</strong> {item.remarks}</div>
                        </div>
                    ))}

                </div>
            </div>
        </div>
    );
}

export default ViewToolboxTalk;
