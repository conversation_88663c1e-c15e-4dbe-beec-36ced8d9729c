import React, { useState, useEffect, useMemo } from 'react';
import Card<PERSON>verlay from './CardOverlay';
import { useSelector } from "react-redux";
import { AIR_URL, AIR_WITH_ID_URL, ACTION_URL, ALL_AIR_REPORTS_URL, STATIC_URL } from '../constants';
import MaterialTable from 'material-table';
import { ThemeProvider, createTheme } from '@mui/material';
import moment from 'moment';
import { Button } from 'primereact/button';
import AirViewCard from './AirViewCard';
import API from '../services/API';
import DatePicker from 'react-datepicker'; // or any other date picker component
import 'react-datepicker/dist/react-datepicker.css';
import DateRangeFilter from './DateRangeFilter';
import { Tag } from 'primereact/tag';
import { FilterMatchMode, FilterOperator } from 'primereact/api';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { InputText } from 'primereact/inputtext';
import { MultiSelect } from 'primereact/multiselect';
import { Calendar } from 'primereact/calendar';
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";
import 'primeicons/primeicons.css';

const AllIncident = () => {

    const me = useSelector((state) => state.login.user)
    const [data, setData] = useState([]);
    const [startDate, setStartDate] = useState(null)
    const [endDate, setEndDate] = useState(null)
    const [Search, setSearch] = useState([])
    const [options, setOptions] = useState({ months: [], irNos: [], displayDates: [], reportedStatuses: [], status: [], actualSeverities: [], groups: [], damagedEquipmentNumbers: [], areas: [], zones: [], dayNights: [] })
    const [filters, setFilters] = useState({
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        months: { value: null, matchMode: FilterMatchMode.IN },
        displayDates: { value: null, matchMode: FilterMatchMode.CONTAINS },
        reportedStatuses: { value: null, matchMode: FilterMatchMode.IN },
        status: { value: null, matchMode: FilterMatchMode.IN },
        actualSeverities: { value: null, matchMode: FilterMatchMode.IN },
        groups: { value: null, matchMode: FilterMatchMode.IN },
        damagedEquipmentNumbers: { value: null, matchMode: FilterMatchMode.IN },
        areas: { value: null, matchMode: FilterMatchMode.IN },
        zones: { value: null, matchMode: FilterMatchMode.IN },
        dayNights: { value: null, matchMode: FilterMatchMode.IN },
    });
    const extractColumnData = (data) => {
        const months = new Set();
        const irNos = new Set();
        const displayDates = new Set();
        const reportedStatuses = new Set();
        const status = new Set();
        const actualSeverities = new Set();
        const groups = new Set();
        const damagedEquipmentNumbers = new Set();
        const areas = new Set();
        const zones = new Set();
        const dayNights = new Set();

        data.forEach(rowData => {
            months.add({ name: moment(rowData.incidentDate, "DD-MM-YYYY HH:mm").format('MMMM'), value: moment(rowData.incidentDate, "DD-MM-YYYY HH:mm").format('MMMM') });
            irNos.add(rowData.maskId);
            displayDates.add({ name: moment(rowData.incidentDate, "DD-MM-YYYY HH:mm").format('DD-MM-YYYY HH:mm'), value: moment(rowData.incidentDate, "DD-MM-YYYY HH:mm").format('DD-MM-YYYY HH:mm') });
            reportedStatuses.add({ name: rowData.isReported === 'Yes' ? 'Reported' : rowData.isReported === 'No' ? 'Unreported' : rowData.isReported, value: rowData.isReported === 'Yes' ? 'Reported' : rowData.isReported === 'No' ? 'Unreported' : rowData.isReported });
            actualSeverities.add({ name: rowData.incidentRating?.item || '', value: rowData.incidentRating?.item || '' });
            status.add(rowData.status);
            groups.add({ name: rowData.workingGroup?.name || '', value: rowData.workingGroup?.name || '' });
            // rowData.damagedEquipmentNumber.forEach(item => damagedEquipmentNumbers.add(`${item.category} - ${item.number}`));
            areas.add({ name: rowData.locationThree?.name || '', value: rowData.locationThree?.name || '' });
            zones.add({ name: rowData.locationFour?.name || '', value: rowData.locationFour?.name || '' });
            const hour = moment(rowData.incidentDate, "DD-MM-YYYY HH:mm").hour();
            dayNights.add({ name: hour >= 6 && hour < 18 ? 'Day' : 'Night', value: hour >= 6 && hour < 18 ? 'Day' : 'Night' });
        });

        return {
            months: Array.from(months),
            irNos: Array.from(irNos),
            displayDates: Array.from(displayDates),
            reportedStatuses: Array.from(reportedStatuses),
            status: Array.from(status),
            actualSeverities: Array.from(actualSeverities),
            groups: Array.from(groups),
            damagedEquipmentNumbers: Array.from(damagedEquipmentNumbers),
            areas: Array.from(areas),
            zones: Array.from(zones),
            dayNights: Array.from(dayNights)
        };
    };

    const removeDuplicates = (data) => {
        return data.filter((ele, ind) => ind === data.findIndex(elem => elem.value === ele.value && elem.name === ele.name))
    }
    useEffect(() => {
        if (data)
            setOptions(extractColumnData(data))

    }, [data])
    const isAirGroup = useMemo(() => {
        return me?.roles?.some(item => item.name === 'AIR Group') || false;
    }, [me]);

    const defaultMaterialTheme = createTheme();
    const tableOptions = {
        actionsColumnIndex: -1,
        pageSize: 20,
        actionsCellStyle: {
            padding: '1.125rem 1.375rem',
        },
        headerStyle: {

            padding: '1.125rem 1.375rem',
            fontSize: '0.812rem'
        },
        rowStyle: {
            // padding: '1.125rem 1.375rem',
            fontSize: '0.812rem'
        }
    }
    const incidentColumns = [
        {
            title: "Month",
            field: "incidentDate",
            render: rowData => moment(rowData.incidentDate, "DD-MM-YYYY HH:mm").format('MMMM'),
            lookup: options.months.reduce((obj, item) => {
                obj[item] = item;
                return obj;
            }, {}),
            customFilterAndSearch: (term, rowData) => {
                if (term.length === 0) return true;
                const month = moment(rowData.incidentDate, "DD-MM-YYYY HH:mm").format('MMMM');
                return term.includes(month);

            }

        },
        {
            title: "IR No",
            field: "maskId",
            render: rowData => <p className='cursor-pointer' onClick={() => { viewAir(rowData.id) }}>{rowData.maskId}</p>,
            lookup: options.irNos.reduce((obj, item) => {
                obj[item] = item;
                return obj;
            }, {})
        },
        {
            title: "Display Date",
            field: "incidentDate",
            render: rowData => moment(rowData.incidentDate, "DD-MM-YYYY HH:mm").format('DD-MM-YYYY HH:mm'),
            customFilterAndSearch: (filterValue, rowData) => {

                const rowDate = moment(rowData.incidentDate, "DD-MM-YYYY HH:mm").startOf('day');

                let start = filterValue.startDate ? moment(filterValue.startDate).startOf('day') : null;
                let end = filterValue.endDate ? moment(filterValue.endDate).endOf('day') : null;

                // Adjust logic to ensure the row date is within the start and end dates, inclusive
                let isAfterStart = start ? rowDate.isSameOrAfter(start) : true; // true if start is null
                let isBeforeEnd = end ? rowDate.isSameOrBefore(end) : true; // true if end is null

                return isAfterStart && isBeforeEnd;
            },
            filterComponent: props => <DateRangeFilter {...props} />
        },
        {
            title: "Status",
            field: "status",
            render: rowData => rowData.status,
            lookup: options.status.reduce((obj, item) => {
                obj[item] = item;
                return obj;
            }, {}),
            customFilterAndSearch: (terms, rowData) => {
                if (terms.length === 0) return true;
                const status = rowData.status;
                return terms.includes(status);
            }

        },
        {
            title: "Reported Status",
            field: "isReported",
            render: rowData => rowData.isReported === 'Yes' ? 'Reported' : rowData.isReported === 'No' ? 'Unreported' : rowData.isReported,
            lookup: options.reportedStatuses.reduce((obj, item) => {
                obj[item] = item;
                return obj;
            }, {}),
            customFilterAndSearch: (terms, rowData) => {
                if (terms.length === 0) return true;
                const status = rowData.isReported === 'Yes' ? 'Reported' : rowData.isReported === 'No' ? 'Unreported' : rowData.isReported;
                return terms.includes(status);
            }

        },
        {
            title: "Actual Severity",
            field: "incidentRating",
            render: rowData => {
                const badgeStyle = {
                    backgroundColor: rowData.incidentRating?.color || '',
                    color: rowData.incidentRating?.color === 'yellow' ? 'black' : 'white',
                    padding: '5px 10px',
                    borderRadius: '8px'
                };
                return <p style={badgeStyle}>{rowData.incidentRating?.item || ''}</p>;
            },
            lookup: options.actualSeverities.reduce((obj, item) => {
                obj[item] = item;
                return obj;
            }, {}),
            customFilterAndSearch: (term, rowData) => {
                if (term.length === 0) return true;
                return Array.isArray(term) ?
                    term.includes(rowData.incidentRating?.item) :
                    rowData.incidentRating?.item === term;
            }
        },
        {
            title: "Group",
            field: "workingGroup",
            render: rowData => rowData.workingGroup?.name || '',
            lookup: options.groups.reduce((obj, item) => {
                obj[item] = item;
                return obj;
            }, {}),
            customFilterAndSearch: (terms, rowData) => { if (terms.length === 0) return true; return terms.includes(rowData.workingGroup?.name) },
        },
        {
            title: "Damaged Equipment Number",
            field: "damagedEquipmentNumber",
            render: rowData => rowData.damagedEquipmentNumber.map(item => `${item.category} - ${item.number}`).join(", "),
            lookup: options.damagedEquipmentNumbers.reduce((obj, item) => {
                obj[item] = item;
                return obj;
            }, {}),
            customFilterAndSearch: (terms, rowData) => { if (terms.length === 0) return true; return rowData.damagedEquipmentNumber.some(item => terms.includes(`${item.category} - ${item.number}`)) },
        },
        {
            title: "Area",
            field: "locationThree",
            render: rowData => rowData.locationThree?.name || '',
            lookup: options.areas.reduce((obj, item) => {
                obj[item] = item;
                return obj;
            }, {}),
            customFilterAndSearch: (terms, rowData) => { if (terms.length === 0) return true; return terms.includes(rowData.locationThree?.name) },
        },
        {
            title: "Zone",
            field: "locationFour",
            render: rowData => rowData.locationFour?.name || '',
            lookup: options.zones.reduce((obj, item) => {
                obj[item] = item;
                return obj;
            }, {}),
            customFilterAndSearch: (terms, rowData) => { if (terms.length === 0) return true; return terms.includes(rowData.locationFour?.name) },
        },
        {
            title: "Day/Night",
            field: "incidentDate",
            render: rowData => {
                const hour = moment(rowData.incidentDate, "DD-MM-YYYY HH:mm").hour();
                return hour >= 7 && hour < 19 ? 'Day' : 'Night';
            },
            lookup: options.dayNights.reduce((obj, item) => {
                obj[item] = item;
                return obj;
            }, {}),
            customFilterAndSearch: (terms, rowData) => {
                if (terms.length === 0) return true;
                const hour = moment(rowData.incidentDate, "DD-MM-YYYY HH:mm").hour();
                const dayNight = hour >= 7 && hour < 19 ? 'Day' : 'Night';
                return terms.includes(dayNight);
            }
        }
    ];


    const tableActions = [
        {
            icon: 'visibility',
            tooltip: 'View',
            onClick: (event, rowData) => {
                // Do save operation
                // console.log(rowData)
                viewAir(rowData.id)
            }
        },

    ]

    const tableStyle = {
        borderRadius: '0',
        boxShadow: 'none',
    };



    useEffect(() => {
        getAirData();
    }, [])

    const getAirData = async () => {
        const uriString = {
            include: [
                'locationOne',
                'locationTwo',
                'locationThree',
                'locationFour',
                'locationFive',
                'locationSix',
                'lighting',
                'surfaceCondition',
                'surfaceType',
                'workActivityDepartment',
                'workActivity',
                'reporter',
                'workingGroup',
                'weatherCondition',
                'reviewer',
                'drivers',
                'surveyor',
                'estimator',
                'trainee',
                'gmOps',
                'thirdParty',
                'security',
                'costReviewer',
                'financer',
                'dutyEngManager',
                'incidentTypeName'
            ]
        };

        const url = `${ALL_AIR_REPORTS_URL}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;

        const response = await API.get(url);
        if (response.status === 200) {

            const data = response.data.filter(i => !i.isArchive).map(rowData => ({
                ...rowData,
                'months': moment(rowData.incidentDate, "DD-MM-YYYY HH:mm").format('MMMM'),
                'irNos': rowData.maskId,
                'displayDates': moment(rowData.incidentDate, "DD-MM-YYYY HH:mm").format('DD-MM-YYYY HH:mm'),
                'reportedStatuses': rowData.isReported === 'Yes' ? 'Reported' : rowData.isReported === 'No' ? 'Unreported' : rowData.isReported,
                'actualSeverities': rowData.incidentRating?.item || '',
                'damagedEquipmentNumber': rowData.damagedEquipmentNumber.map(item => `${item.category} - ${item.number}`).join(", "),
                'groups': rowData.workingGroup?.name || '',
                'areas': rowData.locationThree?.name || '',
                'zones': rowData.locationFour?.name || '',
                'dayNights': moment(rowData.incidentDate, "DD-MM-YYYY HH:mm").hour() >= 6 && moment(rowData.incidentDate, "DD-MM-YYYY HH:mm").hour() < 18 ? 'Day' : 'Night'
            }))
            setData(data)
            setSearch(data);
        }
    }
    const [files, setFiles] = useState([]);

    const handleFileChange = (file) => {
        setFiles(file)

    }


    const [showModal, setShowModal] = useState(false)
    const [showInvestigationModal, setShowInvestigationModal] = useState(false)

    const [currentIncident, setCurrentIncident] = useState('')
    const viewAir = async (id) => {
        setCurrentIncident(id);
        getReportIncident(id);

    }

    const [incidentData, setIncidentData] = useState({})
    const getReportIncident = async (id) => {

        const uriString = {
            include: [
                'locationOne',
                'locationTwo',
                'locationThree',
                'locationFour',
                'locationFive',
                'locationSix',
                'lighting',
                'surfaceCondition',
                'surfaceType',
                'workActivityDepartment',
                'workActivity',
                'reporter',
                'workingGroup',
                'weatherCondition',
                'reviewer',
                'drivers',
                'surveyor',
                'estimator',
                'trainee',
                'gmOps',
                'thirdParty',
                'security',
                'costReviewer',
                'financer',
                'dutyEngManager',
                'incidentTypeName'
            ]
        };

        const url = `${AIR_WITH_ID_URL(id)}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;

        const response = await API.get(url);
        if (response.status === 200) {

            const data = response.data;
            response.data.evidence = response.data.evidence ? response.data.evidence.map(i => {
                return { src: `${STATIC_URL}/${i}`, width: 4, height: 3 }
            }) : []
            setIncidentData(data)
            setShowModal(true)


        }
    }

    const viewInvestigation = async (id) => {
        setCurrentIncident(id);
        getReportIncidentForInvestigation(id);

    }

    const getReportIncidentForInvestigation = async (id) => {

        const uriString = { include: ['locationOne', 'locationTwo', 'locationThree', 'locationFour', 'lighting', 'surfaceCondition', 'surfaceType', 'workActivityDepartment', 'workActivity', 'reporter'] }

        const url = `${AIR_WITH_ID_URL(id)}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;

        const response = await API.get(url);
        if (response.status === 200) {

            const data = response.data;
            setIncidentData(data)
            setShowInvestigationModal(true)


        }
    }
    const onDateSearch = () => {
        const [from, to] = [startDate, endDate];
        if (from === null && to === null) return true;
        if (from !== null && to === null) return true;
        if (from === null && to !== null) return true;
        const start = moment(from, 'ddd MMM DD YYYY HH:mm:ss ZZ').startOf('month');
        const end = moment(to, 'ddd MMM DD YYYY HH:mm:ss ZZ').endOf('month');

        //  console.log(start,end)
        const searchData = data.filter(item => isBetweenDateRange(item.incidentDate, start, end))

        setData(searchData)

        // return isBetweenDateRange(value, moment(from, 'ddd MMM DD YYYY HH:mm:ss ZZ').startOf('month'), moment(to, 'ddd MMM DD YYYY HH:mm:ss ZZ').endOf('month'))
    }
    const isBetweenDateRange = (dateString, date1, date2) => {
        // Parse the date strings using Moment.js
        const date = moment(dateString, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ', 'Do MMM YYYY hh:mm A', moment.ISO_8601]);

        // Check if the parsed date is between date1 and date2
        return date.isBetween(date1, date2, null, '[]'); // Use square brackets to include both ends of the range
    }
    const renderHeader = () => {
        const value = filters['global'] ? filters['global'].value : '';

        return (
            <div className='d-flex justify-content-between'>
                <div className="">
                    <span className='me-3'>Month Filter :</span>
                    <Calendar view='month' className="w-full me-2" value={startDate} placeholder='From' onChange={(e) => setStartDate(e.value)} dateFormat="mm/yy" showIcon />
                    <Calendar view='month' className="w-full  me-3" value={endDate} placeholder="To" onChange={(e) => setEndDate(e.value)} dateFormat="mm/yy" showIcon />

                    <Button icon="pi pi-search " className='me-3' rounded text raised severity="success" aria-label="Search" onClick={() => onDateSearch()} />
                    <Button icon="pi pi-times " rounded text raised severity="danger" aria-label="Cancel" onClick={() => { setData(Search); setStartDate(null); setEndDate(null) }} />

                </div>
                <span className="p-input-icon-left">
                    <i className="fa fa-search" />
                    <InputText type="search" value={value || ''} onChange={(e) => onGlobalFilterChange(e)} placeholder="Global Search" />
                </span>
            </div>
        );
    };

    const header = renderHeader();

    const onGlobalFilterChange = (event) => {
        const value = event.target.value;
        let _filters = { ...filters };

        _filters['global'].value = value;

        setFilters(_filters);
    };

    const monthsFilterTemplate = (option) => {

        return (
            <React.Fragment>
                <div className="mb-3 font-bold">Months</div>
                <MultiSelect value={option.value} options={removeDuplicates(options.months)} itemTemplate={representativesItemTemplate} onChange={(e) => option.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }
    const dayFilterTemplate = (option) => {

        return (
            <React.Fragment>
                <div className="mb-3 font-bold">Day/Night</div>
                <MultiSelect value={option.value} options={removeDuplicates(options.dayNights)} itemTemplate={representativesItemTemplate} onChange={(e) => option.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }
    const groupFilterTemplate = (option) => {

        return (
            <React.Fragment>
                <div className="mb-3 font-bold">Group</div>
                <MultiSelect value={option.value} options={removeDuplicates(options.groups)} itemTemplate={representativesItemTemplate} onChange={(e) => option.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }
    const areaFilterTemplate = (option) => {

        return (
            <React.Fragment>
                <div className="mb-3 font-bold">Area</div>
                <MultiSelect value={option.value} options={removeDuplicates(options.areas)} itemTemplate={representativesItemTemplate} onChange={(e) => option.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }
    const zoneFilterTemplate = (option) => {

        return (
            <React.Fragment>
                <div className="mb-3 font-bold">Zones</div>
                <MultiSelect value={option.value} options={removeDuplicates(options.zones)} itemTemplate={representativesItemTemplate} onChange={(e) => option.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }
    const reportFilterTemplate = (option) => {

        return (
            <React.Fragment>
                <div className="mb-3 font-bold">Status</div>
                <MultiSelect value={option.value} options={[{ name: 'Reported', value: 'Reported' }, { name: 'Unreported', value: 'Unreported' }]} itemTemplate={representativesItemTemplate} onChange={(e) => option.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }
    const severityFilterTemplate = (option) => {

        return (
            <React.Fragment>
                <div className="mb-3 font-bold">Severity</div>
                <MultiSelect value={option.value} options={removeDuplicates(options.actualSeverities)} itemTemplate={representativesItemTemplate} onChange={(e) => option.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }
    const representativesItemTemplate = (option) => {
        return (
            <div className="flex align-items-center gap-2">

                <span>{option.value}</span>
            </div>
        );
    };

    const severityBodyTemplate = (rowData) => {
        const badgeStyle = {
            backgroundColor: rowData.incidentRating?.color || '',
            color: rowData.incidentRating?.color === 'yellow' ? 'black' : 'white',
            padding: '5px 10px',
            borderRadius: '8px',
            textAlign: 'center'

        };
        return <p style={badgeStyle}>{rowData.incidentRating?.item || ''}</p>;

    }
    const idBodyTemplate = (row) => {
        return <div className='maskid' onClick={() => viewAir(row.id)}>{row.maskId}</div>;
    }
    return (<>
        <div>
            {/* <button type="button" className="btn btn-light btn-rounded mb-3 " onClick={(e) => { e.preventDefault(); setMdShow(true); }}><i className="mdi mdi-account-plus mr-2" /> Create New User</button> */}
            {/* <ThemeProvider theme={defaultMaterialTheme}>
                <MaterialTable
                    columns={incidentColumns}
                    data={data}
                    title="IR Reports"
                    style={tableStyle}
                    actions={tableActions}
                    options={{ ...tableOptions, filtering: true, exportButton: true }}


                />
            </ThemeProvider> */}

            <DataTable value={data} paginator rows={10} header={header} filters={filters} onFilter={(e) => setFilters(e.filters)}
                emptyMessage="No Data found." tableStyle={{ minWidth: '50rem' }}>

                {/* <Column field="months" header="Month"  filter sortable style={{ width: '25%' }} filterElement={monthsFilterTemplate} showFilterMatchModes={false}></Column> */}

                <Column field="maskId" header="ID" body={idBodyTemplate} sortable  ></Column>

                <Column field="incidentDate" header="Incident Date" sortable style={{ width: '25%' }} ></Column>

                <Column field='reportedStatuses' header="Reported Status" sortable filter filterElement={reportFilterTemplate} showFilterMatchModes={false}></Column>

                <Column field='actualSeverities' header="Actual Severity" body={severityBodyTemplate} sortable filter filterElement={severityFilterTemplate} showFilterMatchModes={false}></Column>

                <Column field='groups' header="Group" sortable filter filterElement={groupFilterTemplate} showFilterMatchModes={false}></Column>

                <Column field='damagedEquipmentNumber' header="Damaged Equipment Number" sortable style={{ width: '30%' }}></Column>

                <Column field='areas' header="Area" sortable filter filterElement={areaFilterTemplate} showFilterMatchModes={false}></Column>

                <Column field='status' header="Status" sortable showFilterMatchModes={false}></Column>

                <Column field='zones' header="Zone" sortable filter filterElement={zoneFilterTemplate} showFilterMatchModes={false}></Column>

                <Column field='dayNights' header="Day/Night" sortable filterElement={dayFilterTemplate} showFilterMatchModes={false} filter ></Column>



            </DataTable>

            {/* <DataTables thead={thead} options={options} /> */}
            {(incidentData && showModal) && <AirViewCard showModal={showModal} setShowModal={setShowModal} data={incidentData} setData={setIncidentData} />}

        </div>
    </>)
}

export default AllIncident;