import React, { useState, useRef, useEffect, useCallback } from 'react'
import { Modal, Button, Form, Card, Row, Col, Container } from 'react-bootstrap';
import SignatureCanvas from 'react-signature-canvas'
import { SUBMIT_PERMIT_ACTION, FILE_URL, GET_USER_ROLE_BY_MODE, OBS_ACTION_SUBMIT, SUBMIT_INSPECTION_ACTION } from '../../constants';
import Swal from 'sweetalert2';
import API from '../../services/API';
import Select from 'react-select';
// import ViewObs from './ViewObs';
import { DropzoneArea } from 'material-ui-dropzone';
import ImageComponent from '../../services/FileDownlodS3'
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import ViewInspection from './ViewInspection';
import moment from 'moment';
function ActionsModal({ show, applicationDetails, showItem, closeModal }) {

    const signRef = useRef()

    const [apiStatus, setApiStatus] = useState('')
    const [signs, setSign] = useState('')
    const [signModal, setSignModal] = useState(false)
    const [comments, setComments] = useState('')
    const [showErrors, setShowErrors] = useState(false);
    const [assessor, setAssessor] = useState([])
    const [assessorId, setAssessorId] = useState('')
    const [actionTaken, setActionTaken] = useState('')
    const [actionToBeTaken, setActionToBeTaken] = useState('')
    const [evidence, setEvidence] = useState([])
    const [dueDate, setDueDate] = useState(null)

    useEffect(() => {
        if (showItem.actionType === 'review') {
            getCrewList('obsactionowner')
        } else if (showItem.actionType === 'perform_task' || showItem.actionType === 'reperform_task') {
            getCrewList('ins_action_reviewer')
        }

    }, [showItem])

    const getCrewList = useCallback(async (type) => {
        try {
            const response = await API.post(GET_USER_ROLE_BY_MODE, {
                locationOneId: "",
                locationTwoId: "",
                locationThreeId: "",
                locationFourId: "",
                mode: type
            });

            if (response.status === 200) {
                const data = response.data.map((item) => ({
                    label: item.firstName,
                    value: item.id
                }));
                setAssessor(data);
            }
        } catch (error) {
            console.error("Error fetching crew list:", error);
        }
    }, [])



    const handleSubmit = async () => {
        setShowErrors(true);
        let hasError = false;

        if (showItem.actionType === 'perform_task' || showItem.actionType === 'reperform_task') {
            if (!actionTaken) hasError = true;
            if (!assessorId) hasError = true;
            if (evidence.length === 0) hasError = true;
        } else if (showItem.actionType === 'review') {
            if (!actionToBeTaken) hasError = true;
            if (!dueDate) hasError = true;
            if (!assessorId) hasError = true;
        } else if (showItem.actionType === 'verify_task') {
            if (!apiStatus) hasError = true;
            if (apiStatus === 'Return' && !comments) hasError = true; // Comments required only if returning
        }

        if (hasError) {
            Swal.fire({
                icon: 'error',
                title: 'Validation Error',
                text: 'Please fill all required fields before submitting!',
            });
            return;
        }

        let formData = {};

        if (showItem.actionType === 'perform_task' || showItem.actionType === 'reperform_task') {
            formData = {
                actionTaken: actionTaken,
                reviewerId: assessorId, // Reviewer for taking action
                evidence: evidence
            };
        } else if (showItem.actionType === 'review') {
            formData = {
                actionToBeTaken: actionToBeTaken,
                dueDate: dueDate,
                actionOwnerId: assessorId, // Assessor (Action Owner) for review
            };
        } else if (showItem.actionType === 'verify_task') {
            formData = {
                status: apiStatus === 'Approve' ? 'Completed' : 'Returned', // Change based on selection
                comments: comments
            };
        }


        try {

            const response = await API.patch(SUBMIT_INSPECTION_ACTION(showItem.id), formData);

            if (response.status === 204) {
                Swal.fire({
                    icon: 'success',
                    title: 'Success',
                    text: 'Action submitted successfully!',
                });
                closeModal(false);
            } else {
                throw new Error('Something went wrong. Please try again.');
            }
        } catch (error) {
            Swal.fire({
                icon: 'error',
                title: 'Submission Failed',
                text: error.message || 'Failed to submit action. Please try again.',
            });
        }
    };


    const handleApplicantChange = (selectedOption) => {
        setAssessorId(selectedOption ? selectedOption.value : "");
    };
    const handleEvidenceUpload = async (files) => {
        if (files.length > 0) {
            const latestFile = files[files.length - 1];
            const formData1 = new FormData();
            formData1.append('file', latestFile); 

            try {
                const response = await API.post(FILE_URL, formData1, {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                    },
                });

                if (response && response.status === 200) {
                    setEvidence((prevState) => [
                        ...prevState,
                        response.data.files[0].originalname // Store the entire file object instead of just the name
                    ]);
                }
            } catch (error) {
                console.error('File upload error: ', error);
            }
        }
    };

    const handleRemoveMainImage = (index) => {
        setEvidence((prevEvidence) => prevEvidence.filter((_, i) => i !== index));
    };


    return (<>

        <Modal
            show={show}
            size={'lg'}
            onHide={() => closeModal(false)}
            aria-labelledby="example-modal-sizes-title-md"
            id="pdf-content"
        >
            <Modal.Header closeButton>
                {applicationDetails && (
                    <div className="row" style={{ width: '100%' }}>
                        <div className="col-9">
                            <div className="row">

                                <div className="col-12">
                                    <h4>Inspection</h4>
                                    <div className="d-flex align-items-center">
                                        <p className="me-2">#{applicationDetails.maskId || ''} </p>
                                        {/* <p className="card-eptw">{applicationDetails.status} </p> */}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </Modal.Header>
            <Modal.Body>

                {/* <ViewObs reportData={applicationDetails} /> */}
                <ViewInspection reportData={applicationDetails} />




                {showItem.actionType === 'perform_task' || showItem.actionType === 'reperform_task' ?
                    <>
                        <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                            <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>
                                <Card.Body>
                                    <h4 className="fw-bold mb-3">{showItem.prefix === "INS-CL" && showItem.description}</h4>
                                    <h5 className="fw-bold mb-3">Action to be Taken</h5>
                                    <p>{showItem.actionToBeTaken}</p>
                                    <div className="row">

                                        {showItem.uploads?.length > 0 && (<>
                                            <h5 className="fw-bold mb-3 mt-3">Images</h5>
                                            <div className="col-3 mt-2">
                                                {showItem.uploads.map((img, i) => (
                                                    <div key={i} className="m-2 position-relative">
                                                        <ImageComponent fileName={img} size={'100'} name={false} />

                                                    </div>
                                                ))}
                                            </div>
                                        </>)}
                                    </div>
                                    {showItem.actionType === 'reperform_task' && <>
                                        <h5 className="fw-bold mb-3">Action Taken</h5>
                                        <p>{showItem.actionTaken}</p>

                                        {showItem.evidence?.length > 0 && (<>
                                            <h5 className="fw-bold mb-3 mt-3">Evidence</h5>
                                            <div className="col-3 mt-2">
                                                {showItem.evidence.map((img, i) => (
                                                    <div key={i} className="m-2 position-relative">
                                                        <ImageComponent fileName={img} size={'100'} name={false} />

                                                    </div>
                                                ))}
                                            </div>
                                        </>)}

                                        <h5 className="fw-bold mb-3">Reviewer Comments</h5>
                                        <p>{showItem.comments}</p>
                                    </>}
                                </Card.Body>
                            </Card>
                        </Col>

                        <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                            <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>
                                <Card.Body>
                                    {/* Title Section */}
                                    <h5 className=" mb-3">Action Taken</h5>

                                    <Row className="justify-content-center">
                                        <Col xs={12} sm={12} md={12} className="d-flex text-center">
                                            <textarea
                                                rows="4"
                                                cols="50"
                                                className="form-control"
                                                placeholder="Enter your action taken here..."
                                                onChange={(e) => setActionTaken(e.target.value)}
                                            />
                                        </Col>
                                    </Row>
                                    {showErrors && actionTaken === '' && (
                                        <p className="text-danger mt-2">Action Taken are required.</p>
                                    )}
                                </Card.Body>
                            </Card>
                        </Col>
                        <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                            <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>
                                <Card.Body>
                                    <h5 className="mb-3">Evidence</h5>
                                    <DropzoneArea
                                        dropzoneText="Attach supporting images / documents"
                                        filesLimit={5}
                                        maxFileSize={104857600}
                                        showPreviewsInDropzone={false}
                                        showPreviews={false}
                                        dropzoneClass="dropzoneText d-flex align-items-center justify-content-center"
                                        onChange={(files) => handleEvidenceUpload(files)}
                                    />

                                    {showErrors && evidence.length === 0 && (
                                        <p className="text-danger mt-2">At least one evidence file is required.</p>
                                    )}

                                    {evidence.length > 0 && (
                                        <div className="col-12 mt-3 mb-4">
                                            <Form.Label>Uploaded Evidence</Form.Label>
                                            <div className="image-preview">
                                                <div className="row">
                                                    {evidence.map((file, idx) => (
                                                        <div key={idx} className="col-3" style={{ position: 'relative' }}>
                                                            <div className="boxShadow d-flex align-items-center">
                                                                <ImageComponent
                                                                    fileName={file} // Ensure correct data
                                                                    size={'100'}
                                                                    name={true}
                                                                />
                                                                <i
                                                                    className="pi pi-trash"
                                                                    onClick={() => handleRemoveMainImage(idx)}
                                                                    style={{
                                                                        position: 'absolute',
                                                                        top: '5px',
                                                                        right: '5px',
                                                                        cursor: 'pointer',
                                                                    }}
                                                                />
                                                            </div>
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>
                                        </div>
                                    )}
                                </Card.Body>
                            </Card>
                        </Col>

                        <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                            <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>
                                <Card.Body>
                                    <Form.Group className="mb-3">
                                        <Form.Label>{showItem.actionType === 'In Review' ? 'Assessor' : 'Reviewer'}</Form.Label>
                                        <Select
                                            options={assessor}
                                            value={assessor.find(option => option.value === assessorId)}
                                            onChange={handleApplicantChange}
                                            placeholder={showItem.actionType === 'In Review' ? 'Select Assessor' : 'Select Reviewer'}
                                            isClearable
                                        />

                                    </Form.Group>
                                    {showErrors && !assessorId && (
                                        <p className="text-danger mt-2">{showItem.actionType === 'In Review' ? 'Assessor' : 'Reviewer'}  required.</p>
                                    )}

                                </Card.Body>
                            </Card>
                        </Col>

                    </>

                    : showItem.actionType === 'review' ?
                        <>
                            <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                                <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>
                                    <Card.Body>
                                        {/* Title Section */}
                                        <h5 className=" mb-3">Action to be Taken</h5>

                                        <Row className="justify-content-center">
                                            <Col xs={12} sm={12} md={12} className="d-flex text-center">
                                                <textarea
                                                    rows="4"
                                                    cols="50"
                                                    className="form-control"
                                                    placeholder="Enter your action taken here..."
                                                    onChange={(e) => setActionToBeTaken(e.target.value)}
                                                />
                                            </Col>
                                        </Row>
                                        {showErrors && actionToBeTaken === '' && (
                                            <p className="text-danger mt-2">Action to be Taken are required.</p>
                                        )}
                                    </Card.Body>
                                </Card>
                            </Col>

                            <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                                <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>
                                    <Card.Body>
                                        <Form.Group controlId="dueDate" className="mb-3">
                                            <Form.Label className="mb-3">Due Date</Form.Label>
                                            <DatePicker
                                                selected={dueDate ? new Date(dueDate) : null}
                                                onChange={(date) => setDueDate(date ? date.toISOString() : "")}
                                                dateFormat="yyyy-MM-dd"
                                                className="form-control"
                                                placeholderText="Select a date"
                                                minDate={new Date()} // Restrict past dates
                                            />

                                        </Form.Group>
                                        {showErrors && dueDate === null && (
                                            <p className="text-danger mt-2">dueDate is required.</p>
                                        )}

                                    </Card.Body>
                                </Card>
                            </Col>
                            <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                                <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>
                                    <Card.Body>
                                        <Form.Group className="mb-3">
                                            <Form.Label>{showItem.actionType === 'review' ? 'Action Owner' : 'Reviewer'}</Form.Label>
                                            <Select
                                                options={assessor}
                                                value={assessor.find(option => option.value === assessorId)}
                                                onChange={handleApplicantChange}
                                                placeholder={showItem.actionType === 'review' ? 'Select Action Owner' : 'Select Reviewer'}
                                                isClearable
                                            />

                                        </Form.Group>
                                        {showErrors && !assessorId && (
                                            <p className="text-danger mt-2">{showItem.actionType === 'In Review' ? 'Assessor' : 'Reviewer'}  required.</p>
                                        )}

                                    </Card.Body>
                                </Card>
                            </Col>
                        </>

                        : showItem.actionType === 'verify_task' ?

                            <>

                                <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                                    <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>
                                        <Card.Body>
                                            <h4 className="fw-bold mb-3">{showItem.prefix === "INS-CL" && showItem.description}</h4>
                                            <h5 className="fw-bold mb-3">Action to be Taken</h5>
                                            <p>{showItem.actionToBeTaken}</p>
                                            <div className="row">

                                                {showItem.uploads?.length > 0 && (<>
                                                    <h5 className="fw-bold mb-3 mt-3">Images</h5>
                                                    <div className="col-3 mt-2">
                                                        {showItem.uploads.map((img, i) => (
                                                            <div key={i} className="m-2 position-relative">
                                                                <ImageComponent fileName={img} size={'100'} name={false} />

                                                            </div>
                                                        ))}
                                                    </div>
                                                </>)}
                                            </div>

                                            <h5 className="fw-bold mb-3">Action Taken by {showItem.submittedBy?.firstName} - {moment(showItem.created).format('DD-MM-YYYY')}</h5>
                                            <p>{showItem.actionTaken}</p>

                                            <div className="row">

                                                {showItem.evidence?.length > 0 && (<>
                                                    <h5 className="fw-bold mb-3 mt-3">Evidence</h5>
                                                    <div className="col-3 mt-2">
                                                        {showItem.evidence.map((img, i) => (
                                                            <div key={i} className="m-2 position-relative">
                                                                <ImageComponent fileName={img} size={'100'} name={false} />

                                                            </div>
                                                        ))}
                                                    </div>
                                                </>)}
                                            </div>
                                        </Card.Body>
                                    </Card>
                                </Col>


                                <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                                    <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>
                                        <Card.Body >
                                            <Row className="justify-content-center">
                                                <Col xs={8} sm={8} md={8} className="d-flex  text-center">
                                                    <Container
                                                        fluid
                                                        className="col-5 p-2"
                                                        style={{ border: '1px solid #dee2e6', borderRadius: 10, color: '#000000' }}
                                                        onClick={() => setApiStatus('Approve')}
                                                    >
                                                        <Row>
                                                            <Col xs={4} sm={4} md={4}>
                                                                <div style={apiStatus === 'Approve' ? { width: 24, height: 24, borderRadius: 12, background: 'green' } : { width: 24, height: 24, borderRadius: 12, background: 'lightgray' }} >
                                                                    {apiStatus === 'Approve' && (
                                                                        <span className="material-icons" style={{ color: 'white' }}>
                                                                            done
                                                                        </span>
                                                                    )}
                                                                </div>
                                                            </Col>
                                                            <Col xs={6} sm={6} md={6} style={apiStatus === 'Approve' ? { color: 'green' } : {}}>
                                                                Approve
                                                            </Col>
                                                        </Row>
                                                    </Container>
                                                    <Container
                                                        fluid
                                                        className="col-5 p-2"
                                                        style={{ border: '1px solid #dee2e6', borderRadius: 10, color: '#000000' }}
                                                        onClick={() => setApiStatus('Return')}
                                                    >
                                                        <Row>
                                                            <Col xs={4} sm={4} md={4}>
                                                                <div style={apiStatus === 'Return' ? { width: 24, height: 24, borderRadius: 12, background: 'red' } : { width: 24, height: 24, borderRadius: 12, background: 'lightgray' }} >
                                                                    {apiStatus === 'Return' && (
                                                                        <span className="material-icons" style={{ color: 'white' }}>
                                                                            done
                                                                        </span>
                                                                    )}
                                                                </div>
                                                            </Col>
                                                            <Col xs={8} sm={8} md={8} style={apiStatus === 'Return' ? { color: 'red' } : {}}>
                                                                Return
                                                            </Col>
                                                        </Row>
                                                    </Container>
                                                </Col>
                                            </Row>
                                            {showErrors && !apiStatus && (
                                                <p className="text-danger mt-2">Please select Approve or Return.</p>
                                            )}
                                        </Card.Body>
                                    </Card>
                                </Col>

                                <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                                    <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>
                                        <Card.Body>
                                            <h5 className=" mb-3">Comments</h5>
                                            <Row className="justify-content-center">
                                                <Col xs={12} sm={12} md={12} className="d-flex text-center">
                                                    <textarea
                                                        rows="4"
                                                        cols="50"
                                                        className="form-control"
                                                        placeholder="Enter your comments here..."
                                                        onChange={(e) => setComments(e.target.value)}
                                                    />
                                                </Col>
                                            </Row>
                                            {showErrors && apiStatus === 'Return' && !comments && (
                                                <p className="text-danger mt-2">Comments are required when returning.</p>
                                            )}
                                        </Card.Body>
                                    </Card>
                                </Col>

                            </>

                            :



                            ''}



            </Modal.Body>
            <Modal.Footer>
                {showItem.actionType === 'verify_task' ?
                    <Button
                        type="button" // Explicitly set the type to button
                        severity="primary"
                        onClick={handleSubmit} // Ensure handleSubmit is correctly referenced
                    >
                        {apiStatus === 'Return' ? 'Return to Action Owner' : 'Approve'}
                    </Button>

                    :
                    <Button
                        type="button" // Explicitly set the type to button
                        severity="primary"
                        onClick={handleSubmit} // Ensure handleSubmit is correctly referenced
                    >
                        Submit
                    </Button>
                }

            </Modal.Footer>
        </Modal>


    </>)
}

export default ActionsModal