// ControlAccordionItem.js

import React from 'react';
import { Accordion, Row, Col } from 'react-bootstrap';
import { InputTextarea } from 'primereact/inputtextarea';
import { RadioButton } from 'primereact/radiobutton';
import ImmediateCauseSection from './ImmediateCauseSection';
import JobFactorsSection from './JobFactorsSection';
import OrganizationalFactorsSection from './OrganizationalFactorsSection';

const ControlAccordionItem = ({
  control,
  controlIndex,
  section,
  getControlIdPrefix,
  handleChange,
  handleImmediateCauseChange,
  incImmediateCauseOptions,
  incJobFactorOptions,
  incFatorOptions,
  incFallibilityOptions,
  handleSelectChange,
  handleJobFactorChange,
  handleAddJobFactor,
  handleDeleteJobFactor,
  handleOrganizationalFactorSelectChange,
  handleOrganizationalFactorChange,
  handleAddOrganizationalFactor,
  handleDeleteOrganizationalFactor,
  incOrganizationFactorOptions,
  unincOrganizationFactorOptions,
  unincJobFactorOptions
}) => {
  const shouldShowControlEffectiveness =
    section !== 'unidentifiedPreventiveControls' && section !== 'unidentifiedMitigativeControls';

  const shouldShowFactors =
    section === 'unidentifiedPreventiveControls' ||
    section === 'unidentifiedMitigativeControls' ||
    control.isControlImplemented === false ||
    control.isEffective === false;

  return (
    <Accordion.Item eventKey={controlIndex.toString()} key={controlIndex}>
      <Accordion.Header
        className={`custom-accordion-header ${section === 'identifiedPreventiveControls' || section === 'identifiedMitigativeControls'
          ? 'identified'
          : 'unidentified'
          }`}
      >
        <div className="fw-bold">
          {getControlIdPrefix(section)} {controlIndex + 1} - {control.controlStatement}
        </div>
      </Accordion.Header>
      <Accordion.Body>
        {/* Control Details */}
        <Row className="mb-3 mt-3">
          <div className="d-flex mb-3 align-items-center">
            <div className="col-7 px-2">
              <p className="fw-bold">Controls </p>
              <InputTextarea
                style={{ width: '100%', minHeight: 150 }}
                // rows={2}
                autoResize
                value={control.controlStatement}
                onChange={(e) => handleChange(section, controlIndex, 'controlStatement', e.target.value)}

              />
            </div>
            {shouldShowControlEffectiveness && (
              <>
                <div className="col-3 px-2 text-center">
                  <p className="fw-bold">Was the identified control implemented?</p>
                  <div className="d-flex justify-content-center">
                    <label
                      className={`d-flex align-items-center me-1 boxEff ${control.isControlImplemented ? 'boxEffective' : ''
                        }`}
                      style={{
                        backgroundColor: control.isControlImplemented ? 'lightgreen' : 'white',
                        borderColor: control.isControlImplemented ? 'green' : '#ccc',
                        cursor: 'pointer',
                      }}
                      onClick={() => handleChange(section, controlIndex, 'isControlImplemented', true)}
                    >
                      <RadioButton
                        value="Yes"
                        name={`implemented-${section}-${controlIndex}`}
                        checked={control.isControlImplemented === true}
                        onChange={() => handleChange(section, controlIndex, 'isControlImplemented', true)}
                        style={{ display: 'none' }}
                      />
                      <span style={{ color: control.isControlImplemented ? 'green' : 'black' }}>Yes</span>
                    </label>
                    <label
                      className={`d-flex align-items-center boxEff ${!control.isControlImplemented ? 'boxUnEffective' : ''}`}
                      style={{
                        backgroundColor: !control.isControlImplemented ? '#ff07073d' : 'white',
                        borderColor: !control.isControlImplemented ? 'rgb(248 0 0)' : '#ccc',
                        cursor: 'pointer',
                      }}
                      onClick={() => handleChange(section, controlIndex, 'isControlImplemented', false)}
                    >
                      <RadioButton
                        value="No"
                        name={`implemented-${section}-${controlIndex}`}
                        checked={control.isControlImplemented === false}
                        onChange={() => handleChange(section, controlIndex, 'isControlImplemented', false)}
                        style={{ display: 'none' }}
                      />
                      <span style={{ color: !control.isControlImplemented ? 'red' : 'black' }}>No</span>
                    </label>
                  </div>
                </div>
                <div className="col-2 px-2 text-center">
                  {control.isControlImplemented && (
                    <>
                      <p className="fw-bold">Was it effective?</p>
                      <div className="d-flex justify-content-evenly">
                        <label
                          className={`d-flex align-items-center me-1 boxEff ${control.isEffective ? 'boxEffective' : ''
                            }`}
                          style={{
                            backgroundColor: control.isEffective ? 'lightgreen' : 'white',
                            borderColor: control.isEffective ? 'green' : '#ccc',
                            cursor: 'pointer',
                          }}
                          onClick={() => handleChange(section, controlIndex, 'isEffective', true)}
                        >
                          <RadioButton
                            value="Effective"
                            name={`effective-${section}-${controlIndex}`}
                            checked={control.isEffective === true}
                            onChange={() => handleChange(section, controlIndex, 'isEffective', true)}
                            style={{ display: 'none' }}
                          />
                          <span style={{ color: control.isEffective ? 'green' : 'black' }}>Effective</span>
                        </label>
                        <label
                          className={`d-flex align-items-center boxEff ${!control.isEffective ? 'boxUnEffective' : ''}`}
                          style={{
                            backgroundColor: !control.isEffective ? '#ff07073d' : 'white',
                            borderColor: !control.isEffective ? 'rgb(248 0 0)' : '#ccc',
                            cursor: 'pointer',
                          }}
                          onClick={() => handleChange(section, controlIndex, 'isEffective', false)}
                        >
                          <RadioButton
                            value="NonEffective"
                            name={`effective-${section}-${controlIndex}`}
                            checked={control.isEffective === false}
                            onChange={() => handleChange(section, controlIndex, 'isEffective', false)}
                            style={{ display: 'none' }}
                          />
                          <span style={{ color: !control.isEffective ? 'red' : 'black' }}>Not Effective</span>
                        </label>
                      </div>
                    </>
                  )}
                </div>
              </>
            )}
          </div>
        </Row>
        <hr />
        {shouldShowFactors && (
          <>
            <ImmediateCauseSection
              control={control}
              controlIndex={controlIndex}
              section={section}
              handleChange={handleChange}
              handleImmediateCauseChange={handleImmediateCauseChange}
              incImmediateCauseOptions={incImmediateCauseOptions}
            />
            <JobFactorsSection
              control={control}
              controlIndex={controlIndex}
              section={section}
              incJobFactorOptions={incJobFactorOptions}
              getControlIdPrefix={getControlIdPrefix}
              incFatorOptions={incFatorOptions}
              incFallibilityOptions={incFallibilityOptions}
              handleSelectChange={handleSelectChange}
              handleJobFactorChange={handleJobFactorChange}
              handleAddJobFactor={handleAddJobFactor}
              handleDeleteJobFactor={handleDeleteJobFactor}
              unincJobFactorOptions={unincJobFactorOptions}
            />
            <OrganizationalFactorsSection
              control={control}
              controlIndex={controlIndex}
              section={section}
              getControlIdPrefix={getControlIdPrefix}
              incOrganizationFactorOptions={incOrganizationFactorOptions}
              handleOrganizationalFactorSelectChange={handleOrganizationalFactorSelectChange}
              handleOrganizationalFactorChange={handleOrganizationalFactorChange}
              handleAddOrganizationalFactor={handleAddOrganizationalFactor}
              handleDeleteOrganizationalFactor={handleDeleteOrganizationalFactor}
              unincOrganizationFactorOptions={unincOrganizationFactorOptions}
            />
          </>
        )}
      </Accordion.Body>
    </Accordion.Item>
  );
};

export default ControlAccordionItem;
