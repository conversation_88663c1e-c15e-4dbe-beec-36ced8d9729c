import React, { useEffect, useState } from 'react'
import DataTables from '../tables/DataTables';
import $ from "jquery";
import { deletePopup, secondaryPopup, singlePopup } from "../notifications/Swal";
import API from '../services/API';
import cogoToast from 'cogo-toast';
import { useHistory } from "react-router-dom";
// import {  } from '../constants';
import { Modal, Button, Form } from 'react-bootstrap';
import { DropzoneArea } from 'material-ui-dropzone';
import Switch from "react-switch";
import { BodyComponent } from "reactjs-human-body";
import Box from '@mui/material/Box';
import Stepper from '@mui/material/Stepper';
import Step from '@mui/material/Step';
import StepLabel from '@mui/material/StepLabel';
import StepContent from '@mui/material/StepContent';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import axios from 'axios';
import FilterLocation from './FilterLocation';
import IncidentStory from './IncidentStory';
import { GET_USER_BY_ROLES_URL, GET_USER_ROLE_BY_MODE, REPORT_INCIDENT_INVESTIGATION_SAVE_URL_WITH_ID, REPORT_INCIDENT_INVESTIGATION_URL_WITH_ID, STATIC_URL } from '../constants';
import FatalForm from './FatalForm';
import DatePicker from "react-datepicker";
import moment from 'moment';
import "react-datepicker/dist/react-datepicker.css";

const AirInvestigationViewCard = ({ incidentData, showModal, setShowModal }) => {

    const [showReviewModal, setShowReviewModal] = useState(false)
    const [incidentReviewer, setIncidentReviewer] = useState([])
    const [selectedReviewer, setSelectedReviewer] = useState('')
    const [files, setFiles] = useState([]);
    const [hod, setHod] = useState([]);
    const [selectedHod, setSelectedHod] = useState('')

    useEffect(() => {
        getHod();
    }, [])

    const getHod = async () => {
        const response = await API.post(GET_USER_ROLE_BY_MODE, {
            locationOneId: "",
            locationTwoId: "",
            locationThreeId: "",
            locationFourId: "",
            mode: 'ir-hod'
        });
        if (response.status === 200) {


            setHod(response.data)

        }
    }

    const [formData, setFormData] = useState({
        purposeOfInvestigation: "",
        consequenceOfInterest: "",
        timeframeOfInterest: "",
        peopleOfInterest: "",
        equipmentOfInterest: "",
        activitiesOfInterest: "",
        geographicalBoundariesOfInterest: "",
        incidentDescription: "",
        a11: "",
        a12: "",
        a13: "",
        a14: "",
        a15: "",
        a16: "",
        a2: "",
        a3: "",
        a4: "",
        a5: "",
        a6: "",
        a7: "",
        a8: "",
        a9: "",
        b1: "",
        b2: "",
        b3: "",
        b4: "",
        b5: "",
        b6: "",
        c1: "",
        c2: "",
        c3: "",
        c4: "",
        d1: "",
        d2: "",
        d3: "",
        d4: "",
        e1: [
            { id: 1, document: "Risk Assessments and Risk Registers", isRelevant: false, adequacy: "", remarks: "" },
            { id: 2, document: "JSA or Safety Method Statements", isRelevant: false, adequacy: "", remarks: "" },
            { id: 3, document: "EHS Plans", isRelevant: false, adequacy: "", remarks: "" },
            { id: 4, document: "Drawings", isRelevant: false, adequacy: "", remarks: "" },
            { id: 5, document: "Instructions", isRelevant: false, adequacy: "", remarks: "" },
            { id: 6, document: "Permits", isRelevant: false, adequacy: "", remarks: "" },
            { id: 7, document: "Certification (Test, Examination, Training)", isRelevant: false, adequacy: "", remarks: "" },
            { id: 8, document: "Licenses", isRelevant: false, adequacy: "", remarks: "" },
            { id: 9, document: "Induction & Toolbox Talk Registers", isRelevant: false, adequacy: "", remarks: "" },
            { id: 10, document: "Safety Method Statements", isRelevant: false, adequacy: "", remarks: "" }
        ],
        e2: "",
        e3: "",
        f1: "",
        f2: "",
        f3: "",
        conclusionRemarks: "",
        userRemarks: (incidentData.investigation && incidentData.investigation.investigationTeam && incidentData.investigation.investigationTeam.length > 0) ? incidentData.investigation.investigationTeam.map(user => ({
            id: user.value,
            label: user.label,
            remark: ''
        })) : []
    })

    useEffect(() => {
        if (incidentData.investigationStep) {
            if (!incidentData.investigationStep.e1) {
                incidentData.investigationStep.e1 = [
                    { id: 1, document: "Risk Assessments and Risk Registers", isRelevant: false, adequacy: "", remarks: "" },
                    { id: 2, document: "JSA or Safety Method Statements", isRelevant: false, adequacy: "", remarks: "" },
                    { id: 3, document: "EHS Plans", isRelevant: false, adequacy: "", remarks: "" },
                    { id: 4, document: "Drawings", isRelevant: false, adequacy: "", remarks: "" },
                    { id: 5, document: "Instructions", isRelevant: false, adequacy: "", remarks: "" },
                    { id: 6, document: "Permits", isRelevant: false, adequacy: "", remarks: "" },
                    { id: 7, document: "Certification (Test, Examination, Training)", isRelevant: false, adequacy: "", remarks: "" },
                    { id: 8, document: "Licenses", isRelevant: false, adequacy: "", remarks: "" },
                    { id: 9, document: "Induction & Toolbox Talk Registers", isRelevant: false, adequacy: "", remarks: "" },
                    { id: 10, document: "Safety Method Statements", isRelevant: false, adequacy: "", remarks: "" }
                ]
            }
            setFormData(incidentData.investigationStep)
            setDynamicForm(incidentData.investigationStep.dynamicForm)
        }

    }, [incidentData])

    const handleRemarkChange = (event) => {
        const index = event.target.name.split('-')[1];
        const newRemark = event.target.value;

        setFormData(prevFormData => {
            const updatedRemarks = [...prevFormData.userRemarks];
            updatedRemarks[index] = {
                ...updatedRemarks[index],
                remark: newRemark
            };
            return { ...prevFormData, userRemarks: updatedRemarks };
        });
    };


    const personInvolvedList = (persons) => {
        const filteredPersons = persons.filter(person => person.selectedEmp && person.selectedEmp.name);
        const externalPersons = persons.filter(person => person.internal === false)
        return (
            <>
                {(filteredPersons.length > 0 || externalPersons.length > 0) && <table className="table">
                    <thead className="table-header-sm">
                        <tr>
                            <th scope="col">#</th>
                            <th scope="col">Name</th>
                            <th scope="col">Employee ID</th>
                            <th scope="col">Designation</th>
                            <th scope="col">Injuries</th>
                            <th scope="col">Injury Details</th>
                            <th scope="col">PPE</th>

                        </tr>
                    </thead>
                    <tbody>
                        {filteredPersons.map((person, index) => {

                            const convertedObject = person.injuryParts.filter(i => i).reduce((acc, part) => {
                                let modifiedPart = part
                                    .replace(/^left/i, 'TEMP')
                                    .replace(/^right/i, 'left')
                                    .replace(/^TEMP/i, 'right');

                                // Convert to snake_case and lower case
                                modifiedPart = modifiedPart.replace(/([A-Z])/g, "_$1").toLowerCase();

                                // Assign to the accumulator object
                                acc[modifiedPart] = { selected: true };
                                return acc;
                            }, {});

                            return (


                                <tr>
                                    <th scope="row">{index + 1}</th>
                                    <td>{person.selectedEmp.name}</td>
                                    <td>{person.selectedEmp.uniqueId}</td>
                                    <td>{person.selectedEmp.designation}</td>
                                    <td>
                                        {person.injured ? (


                                            person.injuryParts.join(', ')


                                        ) : "None"}
                                    </td>
                                    <td>
                                        {person.injuryDetails.length > 0 ? (

                                            person.injuryDetails.filter(i => i).map(parts => parts.name).join(', ')

                                        ) : "None"}
                                    </td>

                                    <td>
                                        {person.isPPE ? (


                                            person.ppes.filter(i => i).map(parts => parts.name).join(', ')



                                        ) : "None"}
                                    </td>

                                </tr>
                            )
                        })}


                        {externalPersons.map((person, index) => {

                            const convertedObject = person.injuryParts.filter(i => i).reduce((acc, part) => {
                                let modifiedPart = part
                                    .replace(/^left/i, 'TEMP')
                                    .replace(/^right/i, 'left')
                                    .replace(/^TEMP/i, 'right');

                                // Convert to snake_case and lower case
                                modifiedPart = modifiedPart.replace(/([A-Z])/g, "_$1").toLowerCase();

                                // Assign to the accumulator object
                                acc[modifiedPart] = { selected: true };
                                return acc;
                            }, {});

                            return (


                                <tr>
                                    <th scope="row">{index + 1}</th>
                                    <td>{person.name}</td>
                                    <td>{person.empId}</td>
                                    <td>{person.designation}</td>
                                    <td>
                                        {person.injured ? (


                                            person.injuryParts.join(', ')


                                        ) : "None"}
                                    </td>
                                    <td>
                                        {person.injuryDetails.length > 0 ? (

                                            person.injuryDetails.filter(i => i).map(parts => parts.name).join(', ')

                                        ) : "None"}
                                    </td>
                                    <td>
                                        {person.isPPE ? (


                                            person.ppes.filter(i => i).map(parts => parts.name).join(', ')



                                        ) : "None"}
                                    </td>


                                </tr>
                            )
                        })}
                    </tbody>
                </table>}

                {
                    (!filteredPersons.length > 0 && !externalPersons.length > 0) && <p className="text-center">No Person Involved</p>
                }

            </>
        );
    };




    const personnelImpactedList = (persons) => {
        const filteredPersons = persons.filter(person => person.selectedEmp && person.selectedEmp.name);
        const externalPersons = persons.filter(person => person.internal === false)


        return (
            <>


                {(filteredPersons.length > 0 || externalPersons.length > 0) && <table className="table">
                    <thead className="table-header-sm">
                        <tr>
                            <th scope="col">#</th>
                            <th scope="col">Name</th>
                            <th scope="col">Employee ID</th>
                            <th scope="col">Designation</th>
                            <th scope="col">Injuries</th>
                            <th scope="col">Injury Details</th>
                            <th scope="col">PPE</th>


                        </tr>
                    </thead>
                    <tbody>
                        {filteredPersons.map((person, index) => {
                            const convertedObject = person.injuryParts.filter(i => i).reduce((acc, part) => {
                                let modifiedPart = part
                                    .replace(/^left/i, 'TEMP')
                                    .replace(/^right/i, 'left')
                                    .replace(/^TEMP/i, 'right');

                                // Convert to snake_case and lower case
                                modifiedPart = modifiedPart.replace(/([A-Z])/g, "_$1").toLowerCase();

                                // Assign to the accumulator object
                                acc[modifiedPart] = { selected: true };
                                return acc;
                            }, {});

                            return (
                                <tr>
                                    <th scope="row">{index + 1}</th>
                                    <td>{person.selectedEmp.name}</td>
                                    <td>{person.selectedEmp.uniqueId}</td>
                                    <td>{person.selectedEmp.designation}</td>
                                    <td>
                                        {person.injured ? (
                                            person.injuryParts.join(', ')
                                        ) : "None"}
                                    </td>
                                    <td>
                                        {person.injuryDetails.length > 0 ? (
                                            person.injuryDetails.filter(i => i).map(parts => parts.name).join(', ')
                                        ) : "None"}
                                    </td>
                                    <td>
                                        {person.isPPE ? (
                                            person.ppes.filter(i => i).map(parts => parts.name).join(', ')
                                        ) : "None"}
                                    </td>


                                </tr>
                            )
                        })}

                        {externalPersons.map((person, index) => {

                            const convertedObject = person.injuryParts.filter(i => i).reduce((acc, part) => {
                                let modifiedPart = part
                                    .replace(/^left/i, 'TEMP')
                                    .replace(/^right/i, 'left')
                                    .replace(/^TEMP/i, 'right');

                                // Convert to snake_case and lower case
                                modifiedPart = modifiedPart.replace(/([A-Z])/g, "_$1").toLowerCase();

                                // Assign to the accumulator object
                                acc[modifiedPart] = { selected: true };
                                return acc;
                            }, {});

                            return (


                                <tr>
                                    <th scope="row">{index + 1}</th>
                                    <td>{person.name}</td>
                                    <td>{person.empId}</td>
                                    <td>{person.designation}</td>
                                    <td>
                                        {person.injured ? (


                                            person.injuryParts.join(', ')


                                        ) : "None"}
                                    </td>
                                    <td>
                                        {person.injuryDetails.length > 0 ? (

                                            person.injuryDetails.filter(i => i).map(parts => parts.name).join(', ')

                                        ) : "None"}
                                    </td>
                                    <td>
                                        {person.isPPE ? (


                                            person.ppes.filter(i => i).map(parts => parts.name).join(', ')



                                        ) : "None"}
                                    </td>


                                </tr>
                            )
                        })}


                    </tbody>
                </table>}

                {
                    (!filteredPersons.length > 0 && !externalPersons.length > 0) && <p className="text-center">No Personnel Injured</p>
                }

            </>
        );
    };

    const witnessInvolvedList = (persons) => {





        const filteredPersons = persons.filter(person => person.selectedEmp && person.selectedEmp.name);
        const externalPersons = persons.filter(person => person.internal === false)

        return (

            <>
                {(filteredPersons.length > 0 || externalPersons.length > 0) && <table className="table">
                    <thead className="table-header-sm">
                        <tr>
                            <th scope="col">#</th>
                            <th scope="col">Name</th>
                            <th scope="col">Employee ID</th>
                            <th scope="col">Designation</th>
                            <th scope="col">Comments</th>
                        </tr>
                    </thead>
                    <tbody>
                        {filteredPersons.length > 0 && filteredPersons.map((person, index) => (
                            <tr key={person.selectedEmp.uniqueId}>
                                <th scope="row">{index + 1}</th>
                                <td>{person.selectedEmp.name}</td>
                                <td>{person.selectedEmp.uniqueId}</td>
                                <td>{person.selectedEmp.designation}</td>
                                <td></td>
                            </tr>
                        ))}

                        {externalPersons.length > 0 && externalPersons.map((person, index) => (
                            <tr key={person.empId}>
                                <th scope="row">{index + 1}</th>
                                <td>{person.name}</td>
                                <td>{person.empId}</td>
                                <td>{person.designation}</td>
                                <td>{person.comments}</td>
                            </tr>
                        ))}


                    </tbody>
                </table>
                }

                {
                    (!filteredPersons.length > 0 && !externalPersons.length > 0) && <p className="text-center">No Witness Involved</p>
                }
            </>
        );
    };

    const medicalReportCommentsList = (report) => (
        <ul>
            {report.comments.map((comment, index) => (
                <li key={index}>
                    {comment.name}: {comment.comments}
                </li>
            ))}
        </ul>
    );

    const isAnyoneInjured = (person, personnelImpacted) => person.some(person => person.injured === true) || personnelImpacted.some(person => person.injured === true);
    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prevState => ({
            ...prevState,
            [name]: value
        }));
    };
    const handleFileChange = (file) => {
        setFiles(file)

    }
    const [dynamicForm, setDynamicForm] = useState({
        teamMembers: [
            {
                name: "",
                designation: "",
                empId: "",
                company: ""
            }
        ],
        events: [
            {
                description: "",
                date: "",
                time: ""
            }
        ],
        people: [
            {
                name: "",
                company: "",
                role: ""
            }
        ],
        equipment: [
            {
                description: ""
            }
        ],
        material: [
            {
                description: ""
            }
        ],
        environment: [
            {
                description: ""
            }
        ],
        document: [
            {
                description: ""
            }
        ],
        actions: [
            {
                description: "",
                date: "",
                personResponsible: ""
            }
        ]

    })

    const addItem = (key, item) => {
        setDynamicForm(prevState => ({
            ...prevState,
            [key]: [...prevState[key], item]
        }));
    };

    const updateItem = (key, index, field, value) => {
        setDynamicForm(prevState => {
            const updatedItems = [...prevState[key]];
            updatedItems[index][field] = value;
            return { ...prevState, [key]: updatedItems };
        });
    };

    const deleteItem = (key, index) => {
        setDynamicForm(prevState => {
            const updatedItems = [...prevState[key]];
            updatedItems.splice(index, 1);
            return { ...prevState, [key]: updatedItems };
        });
    };

    const addTeamMembers = () => addItem('teamMembers', { name: "", designation: "", empId: "", company: "" });
    const handleTeamMemberChange = (index, field, value) => updateItem('teamMembers', index, field, value);
    const handleDeleteTeamMember = (index) => deleteItem('teamMembers', index);

    const addEvent = () => addItem('events', { description: "", date: "", time: "" });
    const handleEventChange = (index, field, value) => updateItem('events', index, field, value);
    const handleDeleteEvent = (index) => deleteItem('events', index);

    const addPeople = () => addItem('people', { name: "", company: "", role: "" });
    const handlePeopleChange = (index, field, value) => updateItem('people', index, field, value);
    const handleDeletePeople = (index) => deleteItem('people', index);

    const addEquipment = () => addItem('equipment', { description: "" });
    const handleEquipmentChange = (index, field, value) => updateItem('equipment', index, field, value);
    const handleDeleteEquipment = (index) => deleteItem('equipment', index);

    const addMaterial = () => addItem('material', { description: "" });
    const handleMaterialChange = (index, field, value) => updateItem('material', index, field, value);
    const handleDeleteMaterial = (index) => deleteItem('material', index);

    const addEnvironment = () => addItem('environment', { description: "" });
    const handleEnvironmentChange = (index, field, value) => updateItem('environment', index, field, value);
    const handleDeleteEnvironment = (index) => deleteItem('environment', index);

    const addDocument = () => addItem('document', { description: "" });
    const handleDocumentChange = (index, field, value) => updateItem('document', index, field, value);
    const handleDeleteDocument = (index) => deleteItem('document', index);

    const addAction = () => addItem('actions', { description: "" });
    const handleActionChange = (index, field, value) => updateItem('actions', index, field, value);
    const handleDeleteAction = (index) => deleteItem('actions', index);

    const handleSubmit = async (type) => {

        let url = '';
        let toastText = "";
        switch (type) {
            case 'submit':
                url = REPORT_INCIDENT_INVESTIGATION_URL_WITH_ID(incidentData.id);
                toastText = "Submitted!"
                break;

            case 'save':
                url = REPORT_INCIDENT_INVESTIGATION_SAVE_URL_WITH_ID(incidentData.id);
                toastText = "Saved!"
                break;
        }
        const modifiedFormData = { ...formData, dynamicForm: dynamicForm }
        const response = await API.patch(url, {


            investigationStep: modifiedFormData,
            hodId: selectedHod

        })

        if (response.status === 204) {
            cogoToast.success(toastText)
            setShowModal(false)
        }
    }

    const [locationUsers, setLocationUsers] = useState([]);

    useEffect(() => {
        const fetchUsers = async () => {
            try {

                const result = await axios.post(GET_USER_BY_ROLES_URL, { locationOneId: incidentData.locationOneId, locationTwoId: incidentData.locationTwoId, locationThreeId: incidentData.locationThreeId, locationFourId: incidentData.locationFourId, mode: 'ir-action-reviewer' });
                setLocationUsers(result.data);
            } catch (error) {
                console.error('Error fetching data', error);
            }
        };
        if (incidentData.locationOneId && incidentData.locationTwoId && incidentData.locationThreeId && incidentData.locationFourId) {
            fetchUsers();
            // setSelectedReviewer(incidentData.incidentOwnerId)
        }



    }, [incidentData])


    const steps = [

        {
            label: 'Investigation Team',
            description: (
                <>
                    <p className="h5 mb-4">Team Members: {(incidentData.investigation && incidentData.investigation.investigationTeam && incidentData.investigation.investigationTeam.length > 0) && incidentData.investigation.investigationTeam.map(i => i.label).join(', ')} </p>
                    <p>Other Participants</p>
                    {dynamicForm.teamMembers.map((action, index) => (
                        <div className="form-group align-items-center" key={index}>
                            <label className='col-4'>
                                Name
                                <input
                                    className="form-control"
                                    type="text"
                                    value={action.name}
                                    onChange={(e) =>
                                        handleTeamMemberChange(index, "name", e.target.value)
                                    }
                                    disabled
                                />
                            </label>
                            <br />
                            <label className='col-4'>
                                Designation
                                <input
                                    className="form-control"
                                    type="text"
                                    value={action.designation}
                                    onChange={(e) =>
                                        handleTeamMemberChange(index, "designation", e.target.value)
                                    }
                                    disabled
                                />
                            </label>
                            <br />
                            <br />
                            <label className='col-4'>
                                ID / Employee No
                                <input
                                    className="form-control"
                                    type="text"
                                    value={action.empId}
                                    onChange={(e) =>
                                        handleTeamMemberChange(index, "empId", e.target.value)
                                    }
                                    disabled
                                />
                            </label>
                            <br />
                            <br />
                            <label className='col-4'>
                                Company
                                <input
                                    className="form-control"
                                    type="text"
                                    value={action.company}
                                    onChange={(e) =>
                                        handleTeamMemberChange(index, "company", e.target.value)
                                    }
                                    disabled
                                />
                            </label>
                            <br />

                        </div>
                    ))}


                    {/* <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">Purpose of Investigation</label>
                                <input type='text' name="purposeOfInvestigation" value={formData.purposeOfInvestigation} onChange={handleChange} className='form-control' />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">Consequence of
                                    Interest</label>
                                <input type='text' name="consequenceOfInterest" value={formData.consequenceOfInterest} onChange={handleChange} className='form-control' />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">Timeframe of
                                    Interest</label>
                                <input type='text' name="timeframeOfInterest" value={formData.timeframeOfInterest} onChange={handleChange} className='form-control' />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">People of
                                    Interest</label>
                                <input type='text' name="peopleOfInterest" value={formData.peopleOfInterest} onChange={handleChange} className='form-control' />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">Equipment of
                                    Interest</label>
                                <input type='text' name="equipmentOfInterest" value={formData.equipmentOfInterest} onChange={handleChange} className='form-control' />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">Activities of
                                    Interest</label>
                                <input type='text' name="activitiesOfInterest" value={formData.activitiesOfInterest} onChange={handleChange} className='form-control' />
                            </div>
                        </div>
                    </div> */}

                    {/* <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">Geographical
                                    Boundaries of
                                    Interest</label>
                                <input type='text' name="geographicalBoundariesOfInterest" value={formData.geographicalBoundariesOfInterest} onChange={handleChange} className='form-control' />
                            </div>
                        </div>
                    </div> */}
                </>
            )
        },
        {
            label: 'Sequence of Events Leading to Damage / Incident',
            description: (
                <>
                    <p className='mb-2'><strong>Incident Description:</strong> {incidentData.description}</p>
                    <p className='mb-2'><strong>Incident Date:</strong> {incidentData.incidentDate}</p>
                    <p className="h5 mb-4"></p>
                    {dynamicForm.events.map((action, index) => (
                        <div className="form-group align-items-center" key={index}>
                            <label className='col-12'>
                                Description
                                <input
                                    className="form-control"
                                    type="text"
                                    value={action.description}
                                    onChange={(e) =>
                                        handleEventChange(index, "description", e.target.value)
                                    }
                                    disabled
                                />
                            </label>

                            <label className='col-6'>
                                Date
                                <DatePicker
                                    selected={new Date(moment(action.date).isValid() ? moment(action.date).toDate() : new Date())}
                                    onChange={(date) => handleEventChange(index, "date", date)}
                                    showTimeSelect
                                    timeFormat="HH:mm"
                                    timeIntervals={15}
                                    timeCaption="Time"
                                    dateFormat="dd/MM/yyyy H:mm"
                                    className="form-control w-25"
                                    disabled
                                />
                            </label>

                            <label className='col-6'>

                                <input
                                    className="form-control"
                                    type="hidden"

                                    value={action.time}
                                    onChange={(e) =>
                                        handleEventChange(index, "time", e.target.value)
                                    }
                                    disabled
                                />
                            </label>

                        </div>
                    ))}


                </>
            ),
        },
        {
            label: 'Information gathering (A. People)',
            description:
                (<>

                    <p className="h5 mb-4">A. People</p>
                    {/* {dynamicForm.people.map((action, index) => (
                        <div className="form-group align-items-center" key={index}>
                            <label className='col-12'>
                                Name
                                <input
                                    className="form-control"
                                    type="text"
                                    value={action.name}
                                    onChange={(e) =>
                                        handlePeopleChange(index, "name", e.target.value)
                                    }
                                />
                            </label>

                            <label className='col-6'>
                                Company / Contractor
                                <input
                                    className="form-control"
                                    type="text"
                                    value={action.company}
                                    onChange={(e) =>
                                        handlePeopleChange(index, "company", e.target.value)
                                    }
                                />
                            </label>

                            <label className='col-6'>
                                Role / Involvement
                                <input
                                    className="form-control"
                                    type="text"
                                    value={action.role}
                                    onChange={(e) =>
                                        handlePeopleChange(index, "role", e.target.value)
                                    }
                                />
                            </label>
                            <button
                                type="button"
                                className="btn btn-danger"
                                onClick={() => handleDeletePeople(index)}
                            >
                                Delete
                            </button>
                        </div>
                    ))}
                    <button variant="light" className='btn btn-light mb-4' type="button" onClick={addPeople}>
                        Add More
                    </button> */}

                    <div className="row">
                        <label className="col-sm-12 col-form-label">Person(s) Involved</label>
                        {incidentData.personInvolved && (


                            <div>{personInvolvedList(incidentData.personInvolved)}</div>

                        )}
                    </div>

                    <div className="row">
                        <label className="col-sm-12 col-form-label">Personnel Injured</label>
                        {incidentData.personnelImpacted && (


                            <div>{personnelImpactedList(incidentData.personnelImpacted)}</div>

                        )}
                    </div>

                    <div className="row">
                        <label className="col-sm-12 col-form-label">Witness Involved</label>
                        {incidentData.witnessInvolved && (


                            <div>{witnessInvolvedList(incidentData.witnessInvolved)}</div>

                        )}
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">A1. What did the people involved in the incident do/not do that was essential to continuing the incident
                                    sequence?</label>
                                <input type='text' disabled name="a3" value={formData.a3} onChange={handleChange} className='form-control' />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">A2. Consider - Physical capability or condition / Mental state / Behavior / Prolong Sickness / Under Medication / Culture or custom.</label>
                                <input type='text' disabled name="a4" value={formData.a4} onChange={handleChange} className='form-control' />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">A3. Consider - Skill or competence level.</label>
                                <input type='text' disabled name="a5" value={formData.a5} onChange={handleChange} className='form-control' />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">A4. Who were the first responders and what actions were taken?</label>
                                <input type='text' disabled name="a6" value={formData.a6} onChange={handleChange} className='form-control' />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">A5. Trainings</label>
                               <p> {formData.a7} </p>

                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">A6. Experience.</label>
                                <select name='a8' style={{ color: 'black' }} value={formData.a8} disabled onChange={handleChange} className='form-control'>
                                    <option value={""}>Choose one</option>

                                    <option value={"Less than 1 year"}>Less than 1 year</option>
                                    <option value={"1 - 3 Years"}>1 - 3 Years</option>
                                    <option value={"3 - 5 Years"}>3 - 5 Years</option>
                                </select>

                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">A7. Continues working hrs at the time of the incident?</label>
                                <input type='text' name="a9" disabled value={formData.a9} onChange={handleChange} className='form-control' />
                            </div>
                        </div>
                    </div>

                </>)
        },
        {
            label: 'Information Gathering (B. Equipment)',
            description: (<>
                {/* <p className="h5 mb-4">(List the 'Equipment' elements involved in the immediate circumstances of the incident -
                    plant, machinery, equipment, tools, PPE, etc.)</p> */}
                {/* {dynamicForm.equipment.map((action, index) => (
                    <div className="form-group align-items-center" key={index}>


                        <label className='col-12'>

                            <input
                                className="form-control"
                                type="text"
                                value={action.description}
                                onChange={(e) =>
                                    handleEquipmentChange(index, "description", e.target.value)
                                }
                            />
                        </label>
                        <button
                            type="button"
                            className="btn btn-danger"
                            onClick={() => handleDeleteEquipment(index)}
                        >
                            Delete
                        </button>
                    </div>
                ))}
                <button variant="light" className='btn btn-light mb-4' type="button" onClick={addEquipment}>
                    Add More
                </button> */}

                <hr />
                {(incidentData.damagedEquipmentNumber && incidentData.damagedEquipmentNumber.length > 0) &&
                    <div className="row">
                        <div className="col-sm-4">
                            <label className="col-form-label p-1">Equipment Type</label>
                            {
                                incidentData.damagedEquipmentNumber.map(i => (
                                    <p className="white-bg-border-radius-10 form-control-plaintext">

                                        {i.category}


                                    </p>
                                ))
                            }
                        </div>

                        <div className="col-sm-4">
                            <label className="col-form-label p-1">Equipment Number</label>
                            {
                                incidentData.damagedEquipmentNumber.map(i => (
                                    <p className="white-bg-border-radius-10 form-control-plaintext">

                                        {i.number}


                                    </p>
                                ))
                            }
                        </div>

                        <div className="col-sm-4">
                            <label className="col-form-label p-1">Damage Type</label>
                            {
                                incidentData.damagedEquipmentNumber.map(i => (
                                    <p className="white-bg-border-radius-10 form-control-plaintext">

                                        {i.damageType}


                                    </p>
                                ))
                            }
                        </div>

                    </div>
                }
                <hr />
                <div className="row">

                    {incidentData.vesselDetails && <div className="col-sm-6">
                        <label className="col-form-label p-1">Vessel Details:</label>
                        <p className="white-bg-border-radius-10 form-control-plaintext">

                            {incidentData.vesselDetails}


                        </p>



                    </div>}

                    {incidentData.damageType && <div className="col-sm-6 ">
                        <label className="col-form-label p-1">Any other type of damage:</label>
                        <p className="white-bg-border-radius-10 form-control-plaintext">

                            {incidentData.damageType}

                        </p>



                    </div>

                    }
                </div>
                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">B1. Were the equipment in good working condition? If No, please describe</label>
                            <input type='text' disabled name="b1" value={formData.b1} onChange={handleChange} className='form-control' />
                        </div>
                    </div>
                </div>

                {/* <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">B2. How were the equipment used?</label>
                            <input type='text' name="b2" value={formData.b2} onChange={handleChange} className='form-control' />
                        </div>
                    </div>
                </div> */}

                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">B2. Was the shape / nature of the equipment relevant to the incident? If Yes, Describe.</label>
                            <input type='text' disabled name="b2" value={formData.b2} onChange={handleChange} className='form-control' />
                        </div>
                    </div>
                </div>

                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">B3. Was difficulty / unfamiliarity in using the equipment, etc. a contributory factor? If “Yes”,
                                describe.</label>
                            <input type='text' disabled name="b3" value={formData.b3} onChange={handleChange} className='form-control' />
                        </div>
                    </div>
                </div>

                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">B4. Were the required safety controls implemented to address potential risks from the equipment?
                                If No, please Describe.</label>
                            <input type='text' disabled name="b4" value={formData.b4} onChange={handleChange} className='form-control' />
                        </div>
                    </div>
                </div>

                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">B5. Was safety equipment (e.g., PPE) adequate to operate the equipment in a safe manner?
                                If No, Describe.</label>
                            <input type='text' disabled name="b5" value={formData.b5} onChange={handleChange} className='form-control' />
                        </div>
                    </div>
                </div>





            </>),
        },
        {
            label: 'Information Gathering (C. Material)',
            description: (<>

                <p className="h5 mb-4">List the 'Material' elements involved in the immediate circumstances of the incident</p>
                {dynamicForm.material.map((action, index) => (
                    <div className="form-group align-items-center" key={index}>


                        <label className='col-12'>

                            <input
                                className="form-control"
                                type="text"
                                value={action.description}
                                onChange={(e) =>
                                    handleMaterialChange(index, "description", e.target.value)
                                }
                                disabled
                            />
                        </label>

                    </div>
                ))}



                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">C1. How were the materials used?</label>
                            <input type='text' disabled name="c1" value={formData.c1} onChange={handleChange} className='form-control' />
                        </div>
                    </div>
                </div>

                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">C2. Was the shape / nature / property of the materials relevant to the incident? Describe.</label>
                            <input type='text' disabled name="c2" value={formData.c2} onChange={handleChange} className='form-control' />
                        </div>
                    </div>
                </div>

                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">C3. Was difficulty / unfamiliarity in handling the materials, etc. a contributory factor? If “Yes”, describe.</label>
                            <input type='text' disabled name="c3" value={formData.c3} onChange={handleChange} className='form-control' />
                        </div>
                    </div>
                </div>

                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">C4. Were there any safety controls required as per manufacturer’s requirements and were they
                                implemented? Describe.</label>
                            <input type='text' disabled name="c4" value={formData.c4} onChange={handleChange} className='form-control' />
                        </div>
                    </div>
                </div>



            </>)
        }, {
            label: 'Information Gathering (D. Environment)',
            description: (<>

                <div className="row mb-3">



                    <div className="col-sm-4 ">
                        <label className="col-form-label p-1">Weather Condition</label>
                        <p className="white-bg-border-radius-10 form-control-plaintext">{incidentData.weatherCondition && incidentData.weatherCondition.name}</p>
                    </div>

                    <div className="col-sm-4">
                        <label className="col-form-label p-1">Surface Type &amp; Condition</label>
                        {(incidentData.surfaceType && incidentData.surfaceCondition) && <div className="white-bg-border-radius-10"><p className="form-control-plaintext">{incidentData.surfaceType.name} <i className="mdi mdi-menu-right"></i> {incidentData.surfaceCondition.name}</p></div>}

                    </div>

                    <div className="col-sm-4">
                        <label className="col-form-label p-1">Lighting</label>
                        <p className="white-bg-border-radius-10 form-control-plaintext">{incidentData.lighting && incidentData.lighting.name}</p>
                    </div>
                </div>



                {/* <p className="h5 mb-4">List the 'Environment' elements involved in the immediate circumstances of the
                    incident</p>
                {dynamicForm.environment.map((action, index) => (
                    <div className="form-group align-items-center" key={index}>


                        <label className='col-12'>

                            <input
                                className="form-control"
                                type="text"
                                value={action.description}
                                onChange={(e) =>
                                    handleEnvironmentChange(index, "description", e.target.value)
                                }
                            />
                        </label>
                        <button
                            type="button"
                            className="btn btn-danger"
                            onClick={() => handleDeleteEnvironment(index)}
                        >
                            Delete
                        </button>
                    </div>
                ))}
                <button variant="light" className='btn btn-light mb-4' type="button" onClick={addEnvironment}>
                    Add More
                </button> */}


                {/* <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">D1. Location of the incident on the project / operation:
                                What were the positions of all parties (injured party / witnesses), any machinery, materials, barriers,
                                signs, protections, tools &amp; equipment, etc.?
                                (Provide plan)</label>
                            <input type='text' name="d1" value={formData.d1} onChange={handleChange} className='form-control' />
                        </div>
                    </div>
                </div> */}

                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">D1. Were there anything unusual about the working conditions and work environment? If Yes, Please describe.</label>
                            <input type='text' disabled name="d1" value={formData.d1} onChange={handleChange} className='form-control' />
                        </div>
                    </div>
                </div>

                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">D2. Were maintenance, workplace layout and/or housekeeping relevant factors? If Yes, Plese describe.</label>
                            <input type='text' disabled name="d2" value={formData.d2} onChange={handleChange} className='form-control' />
                        </div>
                    </div>
                </div>

                {/* <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">D4. What other features of the environment were present/absent that was essential to contribute to the
                                incident occurring? Describe.</label>
                            <input type='text' name="d4" value={formData.d4} onChange={handleChange} className='form-control' />
                        </div>
                    </div>
                </div> */}
            </>)
        }, {
            label: 'Information Gathering (E. Method)',
            description: (
                <>
                    <p className="h5 mb-4">Documents Uploaded by Reviewer</p>
                    {
                        incidentData?.documents?.map(i => {
                            return (
                                <div>
                                    <a href={`${STATIC_URL}/${i}`} target="_blank">{i}</a>
                                </div>
                            )
                        })
                    }
                    <p className="h5 my-4">List the 'Documents' elements involved in the immediate circumstances of the
                        incident</p>
                    {dynamicForm?.document?.map((action, index) => (
                        <div className="form-group align-items-center" key={index}>


                            <label className='col-12'>

                                <input
                                    className="form-control"
                                    type="text"
                                    value={action.description}
                                    onChange={(e) =>
                                        handleDocumentChange(index, "description", e.target.value)

                                    }
                                    disabled
                                />
                            </label>

                        </div>
                    ))}



                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">E1. What documentation is relevant and are they adequate? Describe.
                                    Paper evidence includes all relevant documentation e.g., risk assessments and risk registers / JSA or
                                    safety method statements / EHS plans / drawings / instructions / permits / certification (test,
                                    examination, training), licenses / induction &amp; toolbox talk registers.</label>
                                {/* <input type='text' name="e1" value={formData.e1} onChange={handleChange} className='form-control' />
                                 */}
                                <table className='font-sm table table-striped table-responsive table-bordered'>
                                    <thead>
                                        <tr>
                                            <th>List of the documents</th>
                                            <th>Relevance</th>
                                            <th>Adequate</th>
                                            <th>Remarks Inadequate</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {console.log(formData.e1, 'E1 Data')}
                                        {formData.e1.length > 0 && formData.e1.map(doc => (
                                            <tr key={doc.id}>
                                                <td>{doc.document}</td>
                                                <td><input disabled type='checkbox' checked={doc.isRelevant} /></td>
                                                <td>{doc.adequacy}</td>
                                                <td>{doc.remarks}</td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    {/* <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">E2. Were the organisation and arrangements for the works contributory factors? Describe.</label>
                                <input type='text' name="e2" value={formData.e2} onChange={handleChange} className='form-control' />
                            </div>
                        </div>
                    </div> */}

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">E2. What other aspects of Systems and Processes were contributory to the incident occurring?
                                    Describe.</label>
                                <input type='text' name="e2" disabled value={formData.e2} onChange={handleChange} className='form-control' />
                            </div>
                        </div>
                    </div>


                </>
            )
        },
        {
            label: 'Root Cause Analysis',
            description: (
                <>
                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">F1. Immediate Cause</label>
                                <textarea name="f1" value={formData.f1} disabled onChange={handleChange} className='form-control' > </textarea>
                            </div>
                        </div>
                    </div>
                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">F2. Underlying Cause</label>
                                <textarea name="f2" value={formData.f2} disabled onChange={handleChange} className='form-control' ></textarea>
                            </div>
                        </div>
                    </div>
                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">F3. Root Cause</label>
                                <textarea type='text' name="f3" value={formData.f3} disabled onChange={handleChange} className='form-control' ></textarea>
                            </div>
                        </div>
                    </div>
                </>
            )
        },
        {
            label: 'Conclusions of Investigation Team',
            description: (
                <>

                    <div className='row'>
                        {formData.userRemarks.map((user, index) => (
                            <div key={user.id} className='row'>
                                <div className='col'>
                                    <div className='form-group'>
                                        <label htmlFor={`remark-${user.id}`}>{user.label}'s Remark:</label>
                                        <input
                                            id={`remark-${user.id}`}
                                            type="text"
                                            name={`remark-${index}`}
                                            value={user.remark}
                                            onChange={handleRemarkChange}
                                            className='form-control'
                                            disabled
                                        />
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">Conclusion Remarks
                                </label>
                                <input type='text' disabled name="conclusionRemarks" value={formData.conclusionRemarks} onChange={handleChange} className='form-control' />
                            </div>
                        </div>
                    </div>
                    {/* <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <DropzoneArea
                                    acceptedFiles={[
                                        'application/pdf',
                                        'image/jpeg',
                                        'image/png'

                                    ]}
                                    dropzoneText={"Drag and drop files that includes sketch/pictures"}
                                    filesLimit={5}

                                    onChange={handleFileChange}
                                />
                            </div>
                        </div>
                    </div> */}
                </>)
        },




    ];

    const [activeStep, setActiveStep] = useState(0);

    const handleNext = () => {
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
    };

    const handleBack = () => {
        setActiveStep((prevActiveStep) => prevActiveStep - 1);
    };

    const handleReset = () => {
        setActiveStep(0);
    };

    useEffect(() => {
        if (incidentData && incidentData.investigationStep)
            setFormData(incidentData.investigationStep)
        if (incidentData && incidentData.investigationStep && incidentData.investigationStep.dynamicForm) {
            setDynamicForm(incidentData.investigationStep.dynamicForm);
        }
    }, [incidentData])

    return (
        <>


            <div className="container h-100">

                <Stepper activeStep={activeStep} orientation="vertical">
                    {steps.map((step, index) => (
                        <Step key={step.label}>
                            <StepLabel
                                onClick={() => setActiveStep(index)}
                                style={{ cursor: 'pointer' }}>
                                {step.label}
                            </StepLabel>
                            <StepContent>
                                <Typography>{step.description}</Typography>
                                <Box sx={{ mb: 2 }}>
                                    <div>
                                        <Button
                                            disabled={index === 0}
                                            className='mt-2'
                                            onClick={handleBack}
                                            sx={{ mt: 1, mr: 1 }}
                                        >
                                            Back
                                        </Button>
                                        {index === steps.length - 1 ? (
                                            <>


                                            </>

                                        ) : (

                                            <Button
                                                variant="light"
                                                className='me-2 mt-2'
                                                onClick={handleNext}
                                                sx={{ mt: 1, mr: 1 }}
                                            >
                                                Continue
                                            </Button>
                                        )}


                                    </div>
                                </Box>
                            </StepContent>
                        </Step>
                    ))}
                </Stepper>
                {activeStep === steps.length && (
                    <Paper square elevation={0} sx={{ p: 3 }}>


                    </Paper>
                )}
            </div>




        </>
    )
}

export default AirInvestigationViewCard;