import React, { useCallback, useEffect, useRef, useState } from 'react';
import API from '../services/API';
import { ACTION_URL, AIR_WITH_ID_URL, ACTION_OTT_SUBMIT, OTT_TASKS_WITH_ID, STATIC_URL, OBSERVATION_REPORT_WITH_ID, SU<PERSON>IT_INVERSTICATION_APPROVER, RA_ACTION_WITH_ID, GET_USER_ROLE_BY_MODE, ASSIGNED_ACTION_URL, OTT_WITH_ID, INCIDENT_WITH_ID, INVERSTIGATION_WITH_ID, INCIDENT_SUBMIT, INVERTIGATION_RETURNED, INVERTIGATION_APPROVED, NTCM_ACTION, PICM_ACTION, PERMIT_REPORT_WITH_ID } from '../constants';

import moment from 'moment';
import { ThemeProvider, createTheme } from "@mui/material";
import MaterialTable from "material-table";
import { FilterMatchMode, FilterOperator } from 'primereact/api';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { InputText } from 'primereact/inputtext';
import { MultiSelect } from 'primereact/multiselect';
import { Calendar } from 'primereact/calendar';
import { Dropdown } from 'primereact/dropdown';
import { Tag } from 'primereact/tag';
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";

import { Modal, Button, Form } from 'react-bootstrap'
import SignatureCanvas from 'react-signature-canvas'
import $ from "jquery";
// import S3 from "react-aws-s3";
import { useSelector } from 'react-redux';
import Swal from 'sweetalert2';
import ActionsModal from './Component/ActionsModal';
// import Actions from './Component/Action/Actions';
// import PermitModal from './Component/PermitModal';




Buffer.from("anything", "base64");
window.Buffer = window.Buffer || require("buffer").Buffer;
window.jQuery = $;
// @ts-ignore
const customSwal = Swal.mixin({
    customClass: {
        confirmButton: 'swal2-confirm btn-secondary', // Use swal2-confirm to target confirm button
        cancelButton: 'swal2-cancel btn-light',      // Use swal2-cancel to target cancel button
    },

    buttonsStyling: true
})
const Action = ({ action, onFilterUpdate }) => {
    const user = useSelector((state) => state.login.user)
    console.log(user)
    const signRef = useRef()
    const [selectedReviewer, setSelectedReviewer] = useState('')
    const [actions, setActions] = useState([]);
    const [incidentData, setIncidentData] = useState({});
    const [modalState, setModalState] = useState({ type: null, isOpen: false, actionId: null });
    const [filters, setFilters] = useState({
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        'applicationDetails.maskId': { value: null, matchMode: FilterMatchMode.IN },
        actionType: { value: null, matchMode: FilterMatchMode.IN },
        createdDate: { operator: FilterOperator.AND, constraints: [{ value: null, matchMode: FilterMatchMode.DATE_IS }] },
        'actionSubmittedBy.firstName': { value: null, matchMode: FilterMatchMode.IN },
    });
    const [startDate, setStartDate] = useState(null)
    const [endDate, setEndDate] = useState(null)
    const [Search, setSearch] = useState([])
    const [maskId, setMaskId] = useState([])
    const [names, setNames] = useState([])
    const [dates, setDates] = useState([])

    const [data, setData] = useState([])
    const [showModal, setShowModal] = useState(false)
    const [showReview, setShowReview] = useState(false)
    const [takeModal, setTakeModal] = useState(false)
    const [verifyModal, setVerifyModal] = useState(false)
    const [showItem, setShowItem] = useState([])
    const [applicationDetails, setApplicationDetails] = useState([])
    const [reviewerComment, setReviewerComment] = useState('')


    useEffect(() => {
        if (action) {
            getActions()
        }

    }, [action])

    const handleReviewerComment = (e) => {
        setReviewerComment(e)
    }

    const getActions = async () => {

        setActions(action)

    }



    const openActionCard = async (action) => {

        try {
            const uriString = {
                include: [
                    { relation: "locationOne" },
                    { relation: "locationTwo" },
                    { relation: "locationThree" },
                    { relation: "locationFour" },
                    { relation: "locationFive" },
                    { relation: "locationSix" },
                    { relation: "reporter" },
                    { relation: "actionOwner" },
                    { relation: "reviewer" },
                ]
            };
            const url = `${OBSERVATION_REPORT_WITH_ID(action.applicationId)}?filter=${encodeURIComponent(
                JSON.stringify(uriString)
            )}`;

            const response = await API.get(url);
            if (response.status === 200) {
                setApplicationDetails(response.data);
            }

            else {
                console.error('Unexpected response status:', response.status);
            }
        } catch (error) {
            console.error('Error fetching incidents:', error);
        }


        setShowItem(action)
        if (action.actionType === 'Reapply') {
            setShowReview(true)

        } else {
            setShowModal(true)
        }

    };


    const renderHeader = () => {
        const value = filters['global'] ? filters['global'].value : '';

        return (
            <div className='d-flex justify-content-end'>

                {/* <span className="p-input-icon-left">
                    <i className="fa fa-search" />
                    <InputText type="search" value={value || ''} onChange={(e) => onGlobalFilterChange(e)} placeholder="Global Search" />
                </span> */}
            </div>
        );
    };

    const header = renderHeader();




    const representativesItemTemplate = (option) => {
        return (
            <div className="flex align-items-center gap-2">

                <span>{option.value}</span>
            </div>
        );
    };
    const nameFilterTemplate = (options) => {
        console.log(options)
        return (
            <React.Fragment>
                <div className="mb-3 font-bold">Name</div>
                <MultiSelect value={options.value} options={names} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }


    const idBodyTemplate = (row) => {
        return <div className='maskid' onClick={() => openActionCard(row)}>{row.maskId}</div>;
    }

    const actionBodyTemplate = (row) => {
        if (row.actionType === "take_action") {
            return 'Take Action'

        } else if (row.actionType === "verify_action") {
            return 'Verify Action'

        } else if (row.actionType === "review") {
            return 'Review Action'
        } else if (row.actionType === "reperform_action") {
            return 'Re-perform Action '
        } else {
            return row.actionToBeTaken
        }
    }


    const handleSelectChange = (e) => {
        setSelectedReviewer(e.value)
    }


    const dueBodyTemplate = (value) => {
        return value.dueDate && moment(value.dueDate).format('DD-MM-YYYY') || 'N/A'
    }
    const statusBodyTemplate = (value) => {
        return value.applicationDetails.status
    }




    return (

        <>


            <DataTable value={actions} paginator rows={10} header={header} filters={filters}
                emptyMessage="No Data found." tableStyle={{ minWidth: '50rem' }}>

                <Column field="maskId" header=" ID" body={idBodyTemplate} sortable ></Column>

                <Column field='actionToBeTaken' header="Description" body={actionBodyTemplate}></Column>

                <Column field="submittedBy.firstName" header="SubmittedBy" sortable ></Column>

                <Column field="applicationDetails.dueDate" header="Due Date" body={dueBodyTemplate} sortable ></Column>


            </DataTable>


            {showModal &&
                <ActionsModal show={showModal} applicationDetails={applicationDetails} showItem={showItem} closeModal={() => setShowModal(false)} />
            }

            {/* {showReview &&
                <PermitModal show={showReview} handleClose={() => setShowReview(false)} showItem={showItem} initialFormData={applicationDetails} isEditMode={true} type={'action'}/>
            } */}
        </>
    );


}

export default Action;
