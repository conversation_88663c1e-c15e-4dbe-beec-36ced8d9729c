import React, { useState, useEffect } from 'react';
import Tabs from '@mui/material/Tabs';
import MTab from '@mui/material/Tab';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import PropTypes from 'prop-types';
import CircularProgress from '@mui/material/CircularProgress';
import AppSwitch from '../pages/AppSwitch';
import Invest from './Invest';
import Investigation from './Investigation';
import Action from './Actions';
import API from '../services/API';
import { ASSIGNED_ACTION_URL, INCIDENT, INVERSTIGATION } from '../constants';
const customFontStyle = {
    fontFamily: 'Lato, sans-serif',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
};

function CustomTabPanel(props) {
    const { children, value, tabValue, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== tabValue}
            id={`incident-tabpanel-${tabValue}`}
            aria-labelledby={`incident-tab-${tabValue}`}
            {...other}
        >
            {value === tabValue && <Box bgcolor={'#fff'}>{children}</Box>}
        </div>
    );
}

CustomTabPanel.propTypes = {
    children: PropTypes.node,
    tabValue: PropTypes.string.isRequired,
    value: PropTypes.string.isRequired,
};

const Dashboard = () => {
    const [value, setValue] = useState('MY ACTIONS');

    const [actions, setActions] = useState([]);
    const [incidents, setIncidents] = useState([]);
    const [investigations, setInvestigations] = useState([]);

    const [loadingActions, setLoadingActions] = useState(true);
    const [loadingIncidents, setLoadingIncidents] = useState(true);
    const [loadingInvestigations, setLoadingInvestigations] = useState(true);

    const [actionCount, setActionCount] = useState(0);
    const [incidentCount, setIncidentCount] = useState(0);
    const [investigationCount, setInvestigationCount] = useState(0);

    const TABS = {
        ACTIONS: 'MY ACTIONS',
        DASHBOARD: 'DASHBOARD',
        ROUTINE: 'ROUTINE',
        // ... other tabs
    };

    useEffect(() => {
        fetchActions();
        fetchIncident();
        fetchInvestigation();
    }, []);

    const fetchActions = async () => {
        setLoadingActions(true); // Start loading

        const uriString = {
            include: [{ relation: "submittedBy" }]
        };
        const url = `${ASSIGNED_ACTION_URL('INCINV')}?filter=${encodeURIComponent(
            JSON.stringify(uriString)
        )}`;

        try {
            const response = await API.get(url);
            if (response.status === 200) {
                setActions(response.data);
                setActionCount(response.data.length);
            } else {
                console.error('Unexpected response status:', response.status);
            }
        } catch (error) {
            console.error('Error fetching actions:', error);
        } finally {
            setLoadingActions(false); // Stop loading regardless of success or error
        }
    };


    const fetchIncident = async () => {
        setLoadingIncidents(true);
        try {
            const uriString = {
                include: [
                    { relation: "locationOne" },
                    { relation: "locationTwo" },
                    { relation: "locationThree" },
                    { relation: "locationFour" },
                    { relation: "locationFive" },
                    { relation: "locationSix" },
                    { relation: "investigationRecords" },
                    { relation: "investigator" },
                    { relation: "reviewer" },
                    { relation: "reportedBy" },
                ]
            };
            const url = `${INCIDENT}?filter=${encodeURIComponent(
                JSON.stringify(uriString)
            )}`;

            const response = await API.get(url);
            if (response.status === 200) {
                setIncidents(response.data);
                setIncidentCount(response.data.length);
            } else {
                console.error('Unexpected response status:', response.status);
            }
        } catch (error) {
            console.error('Error fetching incidents:', error);
        } finally {
            setLoadingIncidents(false); // Stop loading regardless of success or error
        }
    };


    const fetchInvestigation = async () => {
        setLoadingInvestigations(true);
        try {
            const uriString = {
                include: [
                    { relation: "investigationApprover" },
                    { relation: "investigator" },
                    {
                        relation: "incident", scope: {
                            include: [{ relation: "locationOne" },
                            { relation: "locationTwo" },
                            { relation: "locationThree" },
                            { relation: "locationFour" },
                            { relation: "locationFive" },
                            { relation: "locationSix" },
                            { relation: "investigationRecords" },
                            { relation: "investigator" },
                            { relation: "reviewer" },
                            { relation: "reportedBy" },]
                        }
                    },
                ]
            };
            const url = `${INVERSTIGATION}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;

            const response = await API.get(url);
            if (response.status === 200) {
                // Filter data to only include items with status "Investigation Completed"
                const completedInvestigations = response.data.filter(
                    (item) => item.status === "Completed"
                );
                setInvestigations(completedInvestigations);
                setInvestigationCount(completedInvestigations.length);
            } else {
                console.error('Unexpected response status:', response.status);
            }
        } catch (error) {
            console.error('Error fetching investigations:', error);
        } finally {
            setLoadingInvestigations(false); // Stop loading regardless of success or error
        }
    };


    const handleChange = (event, newValue) => {
        setValue(newValue);
    };

    const handleFilterUpdate = (count, type) => {
        if (type === 'actions') {
            setActionCount(count);
        } else if (type === 'incidents') {
            setIncidentCount(count);
        } else if (type === 'investigations') {
            setInvestigationCount(count);
        }
    };

    return (
        <>
            <AppSwitch value={{ label: 'Incident Investigation', value: 'investigation' }} />

            <Tabs value={value} onChange={handleChange} aria-label="incident report table" className="risk">
                <MTab
                    label={
                        <Typography variant="body1" style={customFontStyle}>
                            My Actions <span className="headerCount">{actionCount}</span>
                        </Typography>
                    }
                    value="MY ACTIONS"
                />

                <MTab
                    label={
                        <Typography variant="body1" style={customFontStyle}>
                            All Incidents <span className="headerCount">{incidentCount}</span>
                        </Typography>
                    }
                    value="DASHBOARD"
                />

                <MTab
                    label={
                        <Typography variant="body1" style={customFontStyle}>
                            Investigated Incidents <span className="headerCount">{investigationCount}</span>
                        </Typography>
                    }
                    value="ROUTINE"
                />
            </Tabs>

            <CustomTabPanel value={value} tabValue="MY ACTIONS">
                {loadingActions ? (
                    <Box display="flex" justifyContent="center" alignItems="center" height="100%">
                        <CircularProgress />
                    </Box>
                ) : (
                    <Action action={actions} onFilterUpdate={(count) => handleFilterUpdate(count, 'actions')} />
                )}
            </CustomTabPanel>

            <CustomTabPanel value={value} tabValue="DASHBOARD">
                {loadingIncidents ? (
                    <Box display="flex" justifyContent="center" alignItems="center" height="100%">
                        <CircularProgress />
                    </Box>
                ) : (
                    <Invest data={incidents} onFilterUpdate={(count) => handleFilterUpdate(count, 'incidents')} />
                )}
            </CustomTabPanel>

            <CustomTabPanel value={value} tabValue="ROUTINE">
                {loadingInvestigations ? (
                    <Box display="flex" justifyContent="center" alignItems="center" height="100%">
                        <CircularProgress />
                    </Box>
                ) : (
                    <Investigation data={investigations} onFilterUpdate={(count) => handleFilterUpdate(count, 'investigations')} />
                )}
            </CustomTabPanel>
        </>
    );
};

export default Dashboard;
