import React, { useState, useEffect } from "react";
import { Nav, Tab } from "react-bootstrap";
import MaterialTable from "material-table";
import { RISKASSESSMENT_LIST, RISK_WITH_ID_URL } from "../constants";
import { ThemeProvider, createTheme } from "@mui/material";
import { useHistory, useLocation } from "react-router-dom/cjs/react-router-dom";
import CardOverlay from '../pages/CardOverlay';
import PropTypes from 'prop-types';
import { useSelector } from "react-redux";
import API from "../services/API";
import moment from "moment";
import Swal from "sweetalert2";
import Tabs from '@mui/material/Tabs';
import MTab from '@mui/material/Tab';

import Box from '@mui/material/Box';

import Dashboard from "./Dashboard";
import Curate from "./Curate";
import Assignment from "./Assignment";
import Reports from "./Reports";
import Actions from "./Actions";
import AppSwitch from "../pages/AppSwitch";
import Assign from "./Assign";

function CustomTabPanel(props) {
  const { children, value, tabValue, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== tabValue}
      id={`incident-tabpanel-${tabValue}`}
      aria-labelledby={`incident-tab-${tabValue}`}
      {...other}
    >
      {value === tabValue && (
        <Box >
          {children}
        </Box>
      )}
    </div>
  );
}

CustomTabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired,
};
const Index = () => {
  const user = useSelector((state) => state.login.user)
  console.log(user)
  const history = useHistory();
  const location = useLocation()
  const [data, setData] = useState([])
  const [risk, setRisk] = useState([])
  const [overdue, setOverdue] = useState([])
  const [additional, setAdditional] = useState([])
  const [aOverdue, setAOverdue] = useState([])
  const [access, setAccess] = useState(false)



  useEffect(() => {
    // getPermit();

  }, [])
  const customSwal = Swal.mixin({
    customClass: {
      confirmButton: 'btn btn-danger',
      cancelButton: 'btn btn-light'
    },
    buttonsStyling: false
  })
  const customSwal2 = Swal.mixin({
    customClass: {
      confirmButton: 'btn btn-primary',

    },
    buttonsStyling: false
  })


  const defaultMaterialTheme = createTheme();
  const tableStyle = {
    borderRadius: '0',
    boxShadow: 'none',
  };


  const [value, setValue] = useState('DASHBOARD');

  const TABS = {
    ACTIONS: "ACTIONS",
    DASHBOARD: "DASHBOARD",
    CURATE: "CURATE",
    ASSIGNMENT: "ASSIGNMENT",
    REPORT: "REPORT",
    ASSIGN: "ASSIGN",

    // UNDER_INVESTIGATION: "UNDER_INVESTIGATION",
    // ACTIONS: "ACTIONS"

  };
  const handleChange = (event, newValue) => {

    setValue(newValue);
  };
  return (

    <>
      <AppSwitch value={{ label: 'Knowledge', value: 'knowledge' }} />


      <Tabs value={value} onChange={handleChange} aria-label="incident report table">
        {/* <MTab label="My Actions" value={TABS.ACTIONS} /> */}
        <MTab label="Dashboard" value={TABS.DASHBOARD} />

        <MTab label={"Curate"} value={TABS.CURATE} />
        <MTab label={"Assignment"} value={TABS.ASSIGNMENT} />
        <MTab label={"Report"} value={TABS.REPORT} />
        {/* <MTab label={"Assign"} value={TABS.ASSIGN} /> */}

        {/* <MTab label={"ToolBox Talk" } value={TABS.TOOLBOX} /> */}
        {/* <Tab label="Under Investigation" value={TABS.UNDER_INVESTIGATION} />
      <Tab label="My Actions" value={TABS.ACTIONS} /> */}

      </Tabs>

      {/* <CustomTabPanel value={value} tabValue={TABS.ACTIONS}>
        <Actions />
      </CustomTabPanel> */}
      <CustomTabPanel value={value} tabValue={TABS.DASHBOARD}>
        <Dashboard />
      </CustomTabPanel>
      <CustomTabPanel value={value} tabValue={TABS.CURATE}>
        <Curate />
      </CustomTabPanel>
      <CustomTabPanel value={value} tabValue={TABS.ASSIGNMENT}>
        <Assignment />
      </CustomTabPanel>
      <CustomTabPanel value={value} tabValue={TABS.REPORT}>
        <Reports />
      </CustomTabPanel>
      {/* <CustomTabPanel value={value} tabValue={TABS.ASSIGN}>
        <Assign />
      </CustomTabPanel> */}
      {/* <CustomTabPanel value={value} tabValue={TABS.TOOLBOX}>
                <ToolBoxTalk/>
            </CustomTabPanel> */}

    </>

  );
};

export default Index;
