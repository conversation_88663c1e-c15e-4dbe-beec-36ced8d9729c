import React, { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON>, Nav, Tab } from "react-bootstrap";
import MaterialTable from "material-table";
import { RISKASSESSMENT_LIST, OTT_WITH_ID, RISK_DELETE_WITH_ID_URL, DROPDOWNS, RISK_WITH_ID_URL, OTT_ACTION_ASSIGN_CREATOR, ALL_OTT_LIST, OTT_TASKS_WITH_ID, OTT_OTTTASKS_WITH_ID, ADMINDROPDOWNS } from "../constants";
import { ThemeProvider, createTheme } from "@mui/material";
import { useHistory, useLocation } from "react-router-dom/cjs/react-router-dom";
import { useSelector } from "react-redux";
import API from "../services/API";
import moment from "moment";
import Swal from "sweetalert2";
import { FilterMatchMode, FilterOperator } from 'primereact/api';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { InputText } from 'primereact/inputtext';
import { MultiSelect } from 'primereact/multiselect';
import { Calendar } from 'primereact/calendar';
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";
import { Dropdown, Form } from 'react-bootstrap';
import { convertToLocalTime } from "../services/ConvertLocalTime";
import { sortDate } from "../services/SortDate"
import AddTask from "./Components/AddTask";
import { Modal } from 'react-bootstrap';
import DisplayTask from "./Components/ViewTask";
import { sortNumbers } from "../services/NumberSort";
import { format } from 'date-fns';
const customSwal2 = Swal.mixin({
    customClass: {
        confirmButton: "btn btn-primary",
    },
    buttonsStyling: false,
});


const Archived = ({ data, onRefresh, onFilterUpdate }) => {
    const user = useSelector((state) => state.login.user);
    const [risk, setRisk] = useState([]);
    const [assignee, setAssignee] = useState([]);
    const [assignor, setAssignor] = useState([]);
    const [reviewer, setReviewer] = useState([]);
    const [project, setProject] = useState([]);
    const [category, setCategory] = useState([]);
    const [initialData, setInitialData] = useState(null);
    const [viewModal, setViewModal] = useState(false);
    const [filteredData, setFilteredData] = useState([]);

    const [filters, setFilters] = useState({
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        'project.name': { value: null, matchMode: FilterMatchMode.IN },
        'category.name': { value: null, matchMode: FilterMatchMode.IN },
        priority: { value: null, matchMode: FilterMatchMode.IN },
        'assignee.firstName': { value: null, matchMode: FilterMatchMode.IN },
        'assignor.firstName': { value: null, matchMode: FilterMatchMode.IN },
        'reviewer.firstName': { value: null, matchMode: FilterMatchMode.IN },
        nextdate: { value: null, matchMode: FilterMatchMode.IN },
        'type.label': { value: null, matchMode: FilterMatchMode.IN },
        statusLabel: { value: null, matchMode: FilterMatchMode.IN },
        captain: { value: null, matchMode: FilterMatchMode.IN },
        department: { value: null, matchMode: FilterMatchMode.IN },
    });

    const overallStatus = [
        { value: 'Completed & Archived', name: 'Completed & Archived' },
        { value: 'Not Completed & Archived', name: 'Not Completed & Archived' },
    ];

    useEffect(() => {
        if (data) {
            const assigneeOptions = data.map(item => ({
                name: item.assignee?.firstName || '',
                value: item.assignee?.firstName || ''
            }));
            setAssignee(assigneeOptions.filter((ele, ind) =>
                ind === assigneeOptions.findIndex(elem => elem.value === ele.value && elem.name === ele.name)
            ));
            const obs1 = data.map(item => ({
                name: item.creator?.firstName || '',
                value: item.creator?.firstName || '',
            }));
            setAssignor(obs1.filter((ele, ind) => ind === obs1.findIndex(elem => elem.value === ele.value && elem.name === ele.name)));

            const obs2 = data.map(item => ({
                name: item.reviewer?.firstName || '',
                value: item.reviewer?.firstName || '',
            }));
            setReviewer(obs2.filter((ele, ind) => ind === obs2.findIndex(elem => elem.value === ele.value && elem.name === ele.name)));
            const updatedData = data.map(row => {
                if (row.status === 'Completed' && row.isArchive) {
                    return { ...row, statusLabel: 'Completed & Archived' };
                }
                return { ...row, statusLabel: 'Not Completed & Archived' };
            });

            setRisk(updatedData);
        }
        fetchDropdownData('project', setProject);
        fetchDropdownData('category', setCategory);
    }, [data]);

    const fetchDropdownData = useCallback(async (maskId, setState) => {
        try {
            const uriString = {
                where: { maskId: maskId },
                include: [{ relation: "dropdownItems" }]
            };
            const url = `${ADMINDROPDOWNS}?filter=${encodeURIComponent(
                JSON.stringify(uriString)
            )}`;
            const response = await API.get(url);
            if (response.status === 200) {
                const data = response.data[0].dropdownItems.map((item) => ({
                    label: item.name,
                    value: item.name
                }));
                setState(data);
            }
        } catch (error) {
            console.error(`Error fetching ${maskId} list:`, error);
        }
    }, []);

    const handleFilterChange = (filteredData) => {
        setFilteredData(filteredData); // Update the filtered data state

        // Update the count of filtered data in the parent component
        if (onFilterUpdate) {
            onFilterUpdate(filteredData.length);
        }
    };

    const header = (
        <div className='d-flex justify-content-end'>
            {/* Add any buttons or actions here if necessary */}
        </div>
    );

    const assigneeFilterTemplate = (options) => (
        <MultiSelect
            value={options.value}
            options={assignee}
            itemTemplate={representativesItemTemplate}
            onChange={(e) => options.filterCallback(e.value)}
            optionLabel="value"
            placeholder="Any"
            className="p-column-filter"
        />
    );

    const statusFilterTemplate = (options) => (
        <MultiSelect
            value={options.value}
            options={overallStatus}
            itemTemplate={representativesItemTemplate}
            onChange={(e) => options.filterCallback(e.value)}
            optionLabel="name"
            placeholder="Any"
            className="p-column-filter"
        />
    );

    const categoryBodyTemplate = (options) => (
        <MultiSelect
            value={options.value}
            options={category}
            itemTemplate={representativesItemTemplate}
            onChange={(e) => options.filterCallback(e.value)}
            optionLabel="value"
            placeholder="Any"
            className="p-column-filter"
        />
    );

    const priorityBodyTemplate = (options) => (
        <MultiSelect
            value={options.value}
            options={[{ 'name': 'Low', 'value': 'Low' }, { 'name': 'Medium', 'value': 'Medium' }, { 'name': 'High', 'value': 'High' }]}
            itemTemplate={representativesItemTemplate}
            onChange={(e) => options.filterCallback(e.value)}
            optionLabel="value"
            placeholder="Any"
            className="p-column-filter"
        />
    );

    const projectBodyTemplate = (options) => (
        <MultiSelect
            value={options.value}
            options={project}
            itemTemplate={representativesItemTemplate}
            onChange={(e) => options.filterCallback(e.value)}
            optionLabel="value"
            placeholder="Any"
            className="p-column-filter"
        />
    );
    const assignorFilterTemplate = (options) => {
        return (
            <MultiSelect
                value={options.value}
                options={assignor}
                itemTemplate={representativesItemTemplate}
                onChange={(e) => options.filterCallback(e.value)}
                optionLabel="value"
                placeholder="Any"
                className="p-column-filter"
            />
        );
    }

    const reviewerFilterTemplate = (options) => {
        return (
            <MultiSelect
                value={options.value}
                options={reviewer}
                itemTemplate={representativesItemTemplate}
                onChange={(e) => options.filterCallback(e.value)}
                optionLabel="value"
                placeholder="Any"
                className="p-column-filter"
            />
        );
    }
    const representativesItemTemplate = (option) => (
        <div className="flex align-items-center gap-2">
            <span>{option.value}</span>
        </div>
    );

    const createdBodyTemplate = (data) => {
        return format(new Date(data.dueDate), 'dd-MM-yyyy');
    };
    const openViewModal = (row) => {
        setInitialData(row);
        setViewModal(true);
    };

    const maskBodyTemplate = (row) => (
        <div className='maskid' onClick={() => openViewModal(row)}>{row.maskId}</div>
    );

    return (
        <div className="row">
            <div className="col-12">
                <div className="card">
                    <div className="card-body p-0">
                        <div className="row">
                            <div className="col-12">
                                <DataTable
                                    value={risk}
                                    paginator
                                    rows={10}
                                    header={header}
                                    filters={filters}
                                    onFilter={(e) => setFilters(e.filters)}
                                    onValueChange={handleFilterChange}
                                    paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                                    rowsPerPageOptions={[10, 25, 50]}
                                    emptyMessage="No Data found."
                                    tableStyle={{ minWidth: '50rem' }}
                                >
                                    <Column field="maskId" header="ID" sortable body={maskBodyTemplate}></Column>
                                    <Column field="taskName" header="Task Name"></Column>
                                    <Column field="project.name" header="Project" filterElement={projectBodyTemplate} showFilterMatchModes={false} filter></Column>
                                    <Column field="category.name" header="Category" filterElement={categoryBodyTemplate} showFilterMatchModes={false} filter></Column>
                                    <Column field="priority" header="Priority" filterElement={priorityBodyTemplate} showFilterMatchModes={false} filter></Column>
                                    <Column field="creator.firstName" header="Assignor" filterElement={assignorFilterTemplate} showFilterMatchModes={false} filter></Column>
                                    <Column field="assignee.firstName" header="Assignee" filterElement={assigneeFilterTemplate} showFilterMatchModes={false} filter></Column>
                                    <Column field="reviewer.firstName" header="Reviewer" filterElement={reviewerFilterTemplate} showFilterMatchModes={false} filter></Column>
                                    <Column field="dueDate" body={createdBodyTemplate} header="Due Date" sortFunction={sortDate} sortable></Column>
                                    <Column field="estimatedPercentage" header="Completion %" sortable sortFunction={sortNumbers}></Column>
                                    <Column field="statusLabel" header="Status" filterElement={statusFilterTemplate} showFilterMatchModes={false} filter filterPlaceholder="Search"></Column>
                                </DataTable>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {viewModal &&
                <Modal show={viewModal} onHide={() => setViewModal(false)}>
                    <Modal.Header closeButton>
                        <Modal.Title>View Task</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                        <DisplayTask initialData={initialData} />
                    </Modal.Body>

                </Modal>
            }
        </div>
    );
};


export default Archived;
