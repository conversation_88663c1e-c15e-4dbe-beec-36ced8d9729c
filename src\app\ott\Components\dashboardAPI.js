import { ASSIGNED_ACTION_URL, ALL_OTT_LIST } from "../../constants";
import API from "../../services/API";
// Fetch Action Data
export const fetchActionsAPI = () => {
    const uriString = { include: [{ relation: "submittedBy" }] };
    const url = `${ASSIGNED_ACTION_URL('OTT')}?filter=${encodeURIComponent(
        JSON.stringify(uriString)
    )}`;

    return API.get(url)
        .then((response) => {
            if (response.status === 200) {
                return response.data.reverse();  // Return reversed data
            }
            return [];
        })
        .catch((error) => {
            console.error('Error fetching actions:', error);
            return [];
        });
};

// Fetch OttList Data
export const fetchOttListAPI = () => {
    const uriString = {
        include: [
            { relation: "creator" },
            { relation: "assignee" },
            { relation: "ottTasks" },
            { relation: "reviewer" },
            { relation: "project" },
            { relation: "category" }
        ]
    };
    const url = `${ALL_OTT_LIST}?filter=${encodeURIComponent(
        JSON.stringify(uriString)
    )}`;

    return API.get(url)
        .then((response) => {
            if (response.status === 200) {
                const data = response.data.reverse();
                const activeTasks = data.filter(task => !task.isArchive);
                const archivedTasks = data.filter(task => task.isArchive);
                return { activeTasks, archivedTasks };
            }
            return { activeTasks: [], archivedTasks: [] };
        })
        .catch((error) => {
            console.error('Error fetching OTT list:', error);
            return { activeTasks: [], archivedTasks: [] };
        });
};
