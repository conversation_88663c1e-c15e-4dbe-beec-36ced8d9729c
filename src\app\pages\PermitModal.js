import React, { useEffect, useState } from "react";
import { <PERSON>dal, Button, Form } from 'react-bootstrap';
import moment from "moment";
import GalleryPage from '../apps/Gallery';
import API from "../services/API";
import { USERS_URL, API_URL } from "../constants";

const PermitModal = ({ reportData, showReportModal, setShowReportModal }) => {

    useEffect(() => {
        getAllUsers();
    }, [])

    const [users, setUsers] = useState([])
    const getAllUsers = async () => {
        const response = await API.get(USERS_URL);
        setUsers(response.data)
    }

    function getName(id) {
        const user = users.find(user => user.id === id)
        return id ? user.firstName : ''
    }
    return (
        <>
            <Modal
                show={showReportModal}
                size={'lg'}
                onHide={() => setShowReportModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
            >
                <Modal.Header>

                    <h4>Permit Report</h4>

                    <p>{reportData && moment(reportData.created, "DD-MM-YYYY hh:mm A").format('Do MMM YYYY hh:mm:ss A')}</p>
                </Modal.Header>
                <Modal.Body>
                    {
                        reportData && (<>
                            <h4><i className='mdi mdi-map-marker'></i> Location </h4>
                            {/* <p className='mb-3'>{reportData.locationOne.name} <i className='mdi mdi-chevron-right'></i>{reportData.locationTwo.name} <i className='mdi mdi-chevron-right'></i>{reportData.locationThree.name} <i className='mdi mdi-chevron-right'></i>{reportData.locationFour.name} <i className='mdi mdi-chevron-right'></i>{reportData.locationFive.name} <i className='mdi mdi-chevron-right'></i>{reportData.locationSix.name}  </p> */}



                            <div className="table-responsive mb-3">
                                <table className="table table-striped">

                                    <tbody>
                                        <tr>
                                            <td>ID</td>
                                            <td>{reportData.maskId}</td>
                                        </tr>
                                        <tr>
                                            <td>Description</td>
                                            <td>{reportData.description}</td>
                                        </tr>
                                        {/* <tr>
                                            <td>Permit Type</td>
                                            <td>{reportData.permitType}</td>
                                        </tr> */}
                                        <tr>
                                            <td>Status</td>
                                            <td>{reportData.status}</td>
                                        </tr>
                                        {reportData.dcop && reportData.dcop.ticketNo && (
                                            <tr>
                                                <td>Ticket No.</td>
                                                <td>{reportData.dcop.ticketNo}</td>
                                            </tr>
                                        )}
                                        {reportData.dcop && reportData.dcop.contactNo && (
                                            <tr>
                                                <td>Contact No.</td>
                                                <td>{reportData.dcop.contactNo}</td>
                                            </tr>
                                        )}


                                        <tr>
                                            <td>Permit Start Date</td>
                                            <td>{moment(reportData.permitStartDate, "DD-MM-YYYY hh:mm A").format('Do MMM YYYY hh:mm:ss A')}</td>
                                        </tr>
                                        <tr>
                                            <td>Permit End Date</td>
                                            <td>{moment(reportData.permitEndDate, "DD-MM-YYYY hh:mm A").format('Do MMM YYYY hh:mm:ss A')}</td>
                                        </tr>

                                        {/* <tr>
                                            <td>DCOP High Risk</td>
                                            <td>{reportData.dcop.highRisk}</td>
                                        </tr>
                                        <tr>
                                            <td>MOP Title</td>
                                            <td>{reportData.dcop.mopTitle}</td>
                                        </tr>
                                        <tr>
                                            <td>Ticket No</td>
                                            <td>{reportData.dcop.ticketNo}</td>
                                        </tr>
                                        <tr>
                                            <td>Contact Number</td>
                                            <td>{reportData.dcop.contactNo}</td>
                                        </tr>
                                        <tr>
                                            <td>Company Contact Number</td>
                                            <td>{reportData.dcop.companyContactNo}</td>
                                        </tr>
                                        <tr>
                                            <td>DC Fire</td>
                                            <td>{reportData.dcop.isDcFire}</td>
                                        </tr>
                                        <tr>
                                            <td>DC Security</td>
                                            <td>{reportData.dcop.isDcSecurity}</td>
                                        </tr> */}
                                    </tbody>
                                </table>
                                <h4 className='mt-3 mb-3'><i className='mdi mdi-fire'></i> Activities </h4>

                                {reportData.activities.map((item) => (
                                    <div >
                                        <p>{item.name}</p>

                                    </div>
                                ))}
                                <h4 className='mt-3'><i className='mdi mdi-fire'></i> HighRisk Permit </h4>
                                {reportData.additionalPermits.map((item) => (
                                    <div >
                                        <p>{item.name}</p>

                                    </div>
                                ))}
                                <h4 className='mt-3'><i className='mdi mdi-fire'></i> Workers </h4>
                                {reportData.workers.map((item) => (
                                    <div >
                                        <p>{item.name}</p>

                                    </div>
                                ))}

                                <h4 className='mt-3 mb-3'><i className='mdi mdi-fire'></i> Details </h4>

                                <h4>Applicant : <span>{reportData.high_risk.applicantName}</span></h4>
                                <img src={`${API_URL}/docs/${reportData.high_risk.applicantSign}`} style={{ width: '100px', height: '50px' }} />
                                <p>Date:<span>{reportData.high_risk.applicantSignedDate}</span></p>
                                {reportData.high_risk.assessorSign ? <>
                                    <h4>Assessor : <span>{reportData.high_risk.assessorName}</span></h4>
                                    <img src={`${API_URL}/docs/${reportData.high_risk.assessorSign}`} style={{ width: '100px', height: '50px' }} />
                                    <p>Date:<span>{reportData.high_risk.assessorSignedDate}</span></p>
                                </> : ''}
                                {reportData.high_risk.approvers ? <>
                                    <h4>Approvers</h4>


                                    <div>
                                        {Object.keys(reportData.high_risk.approvers).map(key => (
                                            <div key={key}>
                                                <p>Approver Name: {reportData.high_risk.approvers[key].approverName}</p>
                                                <p>Approver Stage: {reportData.high_risk.approvers[key].approverStage}</p>
                                                <img src={`${API_URL}/docs/${reportData.high_risk.approvers[key].approverSign}`} alt="Approver Sign" style={{ width: '100px', height: '50px' }} />
                                                <p>Approver Signed Date: {reportData.high_risk.approvers[key].approverSignedDate}</p>
                                            </div>
                                        ))}
                                    </div>
                                </> : ''}

                                {/* {reportData.high_risk.approvers.map((item) => (
                                    <div >
                                        <h4>Approver : <span>{item.approverName}</span></h4>
                                        <img src={`https://dev-sagt-user-api.acuizen.com/docs/${item.approverSign}`} style={{ width: '100px', height: '50px' }} />
                                        <p>Date:<span>{item.approverSignedDate}</span></p>

                                    </div>
                                ))} */}

                                {/* <h4 className='mt-3'><i className='mdi mdi-fire'></i> Fire Systems </h4>
                                {
                                    reportData.dcop.fireSystems && reportData.dcop.fireSystems.length > 0 ? (
                                        reportData.dcop.fireSystems.map((fireSys, index) => (
                                            <div key={index}>
                                                <p>Label: {fireSys.label}</p>
                                                <p>Isolated: {fireSys.isolated}</p>
                                            </div>
                                        ))
                                    ) : <p>No Fire Systems</p>
                                } */}

                                {/* <h4 className='mt-3'><i className='mdi mdi-security'></i> Security Systems </h4>
                                {
                                    reportData.dcop.securitySystems && reportData.dcop.securitySystems.length > 0 ? (
                                        reportData.dcop.securitySystems.map((securitySys, index) => (
                                            <div key={index}>
                                                <p>Label: {securitySys.label}</p>
                                                <p>Isolated: {securitySys.isolated}</p>
                                            </div>
                                        ))
                                    ) : <p>No Security Systems</p>
                                } */}

                                {/* <h4 className='mt-3'><i className='mdi mdi-location'></i> Fire Systems Locations </h4>
                                {
                                    reportData.dcop.fireSysLocations && reportData.dcop.fireSysLocations.length > 0 ? (
                                        reportData.dcop.fireSysLocations.map((location, index) => (
                                            <div key={index}>
                                                <p>Location 5: {location.loc5.name}</p>
                                                <p>Location 6: {location.loc6.name}</p>
                                            </div>
                                        ))
                                    ) : <p>No Fire Systems Locations</p>
                                } */}

                                {/* <h4 className='mt-3'><i className='mdi mdi-location'></i> Security Systems Locations </h4>
                                {
                                    reportData.dcop.securitySysLocations && reportData.dcop.securitySysLocations.length > 0 ? (
                                        reportData.dcop.securitySysLocations.map((location, index) => (
                                            <div key={index}>
                                                <p>Location 5: {location.loc5.name}</p>
                                                <p>Location 6: {location.loc6.name}</p>
                                            </div>
                                        ))
                                    ) : <p>No Security Systems Locations</p>
                                } */}
                                {/* <h4 className='mt-3'><i className='mdi mdi-image-album'></i> Uploads </h4>

                                {!reportData.uploads.length > 0 && <p>No Uploads</p>}
                                {
                                    reportData.uploads.length > 0 && <GalleryPage photos={reportData.uploads} />
                                }



                                <h4 className='mt-3'><i className='mdi mdi-history'></i> Action Logs </h4> */}
                                {/* {!reportData.actions || !reportData.actions.length > 0 && <p>No Actions Log</p>}
                                {
                                    reportData.actions && reportData.actions.length > 0 && (
                                        <>
                                            <div className="table-responsive mb-3">
                                                <table className="table table-striped">
                                                    <thead>
                                                        <th>Action to be Taken</th>
                                                        <th>Action Taken</th>
                                                        <th>Status</th>
                                                        <th>Timestamp</th>
                                                    </thead>
                                                    <tbody>
                                                        {
                                                            reportData.actions.map(action => {
                                                                return (
                                                                   
                                                                        <tr key={action.id}>
                                                                            <td><p>{action.actionToBeTaken}</p><p>By {getName(action.submittedById)} </p></td>
                                                                            <td><p>{action.actionTaken}</p><p>By {getName(action.assignedToId)}</p></td>
                                                                            <td>
                                                                                <p>
                                                                                    {action.actionType === 'action_owner' && <span className="badge badge-success">Assigned to Action Assignee</span>}

                                                                                    {action.actionType === 'reject' && <span className="badge badge-danger">Returned to Action Assignee</span>}

                                                                                    {action.actionType === 'approve' && <span className="badge badge-success">Approved by Reviewer</span>}

                                                                                    {action.actionType === 'reviewer' && <span className="badge badge-warning">In Review by reviewer</span>}


                                                                                </p>
                                                                            </td>
                                                                            <td>
                                                                                <p>{moment(action.createdDate).format('DD/MM/YYYY HH:mm:ss')}</p>
                                                                            </td>
                                                                        </tr>
                                                                
                                                                )
                                                            })
                                                        }
                                                    </tbody>
                                                </table>
                                            </div>
                                        </>
                                    )

                                } */}




                            </div>



                        </>)
                    }
                </Modal.Body>

                <Modal.Footer className="flex-wrap">
                    {

                        <>
                            <Button variant="light" onClick={() => setShowReportModal(false)}>Close</Button>

                        </>

                    }

                </Modal.Footer>
            </Modal>
        </>
    )
}

export default PermitModal;