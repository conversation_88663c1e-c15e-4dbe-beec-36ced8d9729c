import React from 'react';

const OrganizationalFactors = ({ organizationalFactors, startIndex = 1 }) => {
    let count = startIndex;
    return (
        <div>
            {organizationalFactors && organizationalFactors.map((factor, index) => (
                factor.extentOfContribution === 'Significant' && (
                    <div key={index} style={{ marginBottom: '10px' }}>
                        <div className='mb-2'><strong>RCO - {count++} : {factor.factor}</strong></div>
                        <div style={{ paddingLeft: '50px' }}>
                            {factor.description && <p>{factor.description}</p>}
                        </div>
                    </div>
                )
            ))}
        </div>
    );
};

export default OrganizationalFactors;
