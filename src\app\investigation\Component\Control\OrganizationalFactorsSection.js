// OrganizationalFactorsSection.js

import React from 'react';
import { Row, Col, Form, Button } from 'react-bootstrap';
import Select from 'react-select';
import { RadioButton } from 'primereact/radiobutton';

const OrganizationalFactorsSection = ({
  control,
  controlIndex,
  section,
  getControlIdPrefix,
  incOrganizationFactorOptions,
  handleOrganizationalFactorSelectChange,
  handleOrganizationalFactorChange,
  handleAddOrganizationalFactor,
  handleDeleteOrganizationalFactor,
  unincOrganizationFactorOptions
}) => {

  const controlIdPrefix = getControlIdPrefix(section);

  const factorOptions = (section === 'unidentifiedPreventiveControls' || section === 'unidentifiedMitigativeControls') 
    ? unincOrganizationFactorOptions 
    : incOrganizationFactorOptions;
  return (
    <>
      {/* Organizational Factors */}
      <Col>
        <h6 className="fw-bold">Organizational Factors</h6>
        {(section === 'unidentifiedPreventiveControls' || section === 'unidentifiedMitigativeControls') ? (
          <p className="">
            Were there any organizational factors that led to not consider these controls during planning or risk assessment?
          </p>) : (
          <p className="">
           Organizational factors are conditions related to the organization’s policies, procedures, culture, or practices that directly contributed to the immediate cause of the failed or unimplemented control in this incident.
          </p>
        )}
      </Col>
      {control.relatedOrganizationalFactors.length > 0 ? (
        control.relatedOrganizationalFactors.map((orgFactor, orgFactorIndex) => (
          <div key={orgFactorIndex} className="mb-4 p-3 boxShadow">
            <Row className="mb-3">
              <Col>
                <h6 className="fw-bold mb-2"> OF-{controlIdPrefix}-{controlIndex + 1}.{orgFactorIndex + 1}</h6>
              </Col>
              <Col className="text-end">
                <span
                  className="pi pi-trash"
                  style={{ cursor: 'pointer', color: 'red' }}
                  onClick={() => handleDeleteOrganizationalFactor(section, controlIndex, orgFactorIndex)}
                  title="Delete Organizational Factor"
                />
              </Col>
            </Row>
            <Row className="mb-3">
              <Col md="6">
                <Form.Group>
                  <Form.Label className="fw-bold">Factor</Form.Label>
                  <Select
                    value={incOrganizationFactorOptions.find((option) => option.label === orgFactor.factor)}
                    onChange={(selectedOption) =>
                      handleOrganizationalFactorSelectChange(
                        section,
                        controlIndex,
                        orgFactorIndex,
                        'factor',
                        selectedOption
                      )
                    }
                    options={factorOptions}
                  />
                </Form.Group>
                {orgFactor.factor && (
                  <p class="fst-italic">{factorOptions.find((option) => option.label === orgFactor.factor)?.desc}</p>
                )}
              </Col>
              <Col>
                <Form.Group>
                  <Form.Label className="fw-bold">
                    Is this a significant contributory factor to the incident?
                  </Form.Label>
                  <div className="d-flex justify-content-start">

                    <label
                      className={`d-flex align-items-center boxEff ${orgFactor.extentOfContribution === 'Not Significant' ? 'boxEffective' : ''
                        }`}
                      style={{
                        backgroundColor: orgFactor.extentOfContribution === 'Not Significant' ? 'lightgreen' : 'white',
                        borderColor: orgFactor.extentOfContribution === 'Not Significant' ? 'green' : '#ccc',
                        cursor: 'pointer',
                        padding: '5px 10px',
                        borderRadius: '4px',
                      }}
                      onClick={() =>
                        handleOrganizationalFactorChange(
                          section,
                          controlIndex,
                          orgFactorIndex,
                          'extentOfContribution',
                          'Not Significant'
                        )
                      }
                    >
                      <RadioButton
                        value="Not Significant"
                        name={`extentOfContribution-${section}-${controlIndex}-${orgFactorIndex}`}
                        checked={orgFactor.extentOfContribution === 'Not Significant'}
                        onChange={() =>
                          handleOrganizationalFactorChange(
                            section,
                            controlIndex,
                            orgFactorIndex,
                            'extentOfContribution',
                            'Not Significant'
                          )
                        }
                        style={{ display: 'none' }}
                      />
                      <span
                        style={{
                          color: orgFactor.extentOfContribution === 'Not Significant' ? 'green' : 'black',
                        }}
                      >
                        Not Significant
                      </span>
                    </label>
                    <label
                      className={`d-flex align-items-center ms-1 boxEff ${orgFactor.extentOfContribution === 'Significant' ? 'boxUnEffective' : ''
                        }`}
                      style={{
                        backgroundColor: orgFactor.extentOfContribution === 'Significant' ? '#ff07073d' : 'white',
                        borderColor: orgFactor.extentOfContribution === 'Significant' ? 'rgb(248 0 0)' : '#ccc',
                        cursor: 'pointer',
                        padding: '5px 10px',
                        borderRadius: '4px',
                      }}
                      onClick={() =>
                        handleOrganizationalFactorChange(
                          section,
                          controlIndex,
                          orgFactorIndex,
                          'extentOfContribution',
                          'Significant'
                        )
                      }
                    >
                      <RadioButton
                        value="Significant"
                        name={`extentOfContribution-${section}-${controlIndex}-${orgFactorIndex}`}
                        checked={orgFactor.extentOfContribution === 'Significant'}
                        onChange={() =>
                          handleOrganizationalFactorChange(
                            section,
                            controlIndex,
                            orgFactorIndex,
                            'extentOfContribution',
                            'Significant'
                          )
                        }
                        style={{ display: 'none' }}
                      />
                      <span style={{ color: orgFactor.extentOfContribution === 'Significant' ? 'red' : 'black' }}>
                        Significant
                      </span>
                    </label>
                  </div>
                </Form.Group>
              </Col>

            </Row>

            {orgFactor.extentOfContribution === 'Significant' &&
              <Row>
                <Col md="6">
                  <Form.Group>
                    <Form.Label className="fw-bold">
                      Describe why this was a significant factor in the context of this incident
                    </Form.Label>
                    <Form.Control
                      type="text"
                      value={orgFactor.description || ''}
                      onChange={(e) =>
                        handleOrganizationalFactorChange(
                          section,
                          controlIndex,
                          orgFactorIndex,
                          'description',
                          e.target.value
                        )
                      }
                    />
                  </Form.Group>
                </Col>
              </Row>
            }
          </div>
        ))
      ) : (
        <p>
         
        </p>
      )}
      <Button
        variant="outline-primary"
        onClick={() => handleAddOrganizationalFactor(section, controlIndex)}
        type="button"
        className="mb-3"
      >
        Add Organizational Factor
      </Button>
    </>
  );
};

export default OrganizationalFactorsSection;
