import React, { useEffect, useState } from 'react';
import { Checkbox } from 'primereact/checkbox';
import { FILE_DOWNLOAD_WITH_S3 } from '../../../constants';
import API from '../../../services/API';

const HazardItem = ({ hazard, selectedHazards, onClickHazards }) => {
    const [imageUrl, setImageUrl] = useState(null);
    const isSelected = selectedHazards.some(h => h.id === hazard.id);


    useEffect(() => {
        const fetchImage = async () => {
            if (hazard.image && localStorage.getItem('SELECTED_INDUSTRIES')) {
                try {
                    const presignedRes = await API.get(FILE_DOWNLOAD_WITH_S3('az-risk', hazard.image));
                    if (presignedRes.status === 200 && presignedRes.data) {
                        setImageUrl(presignedRes.data);
                    }
                } catch (error) {
                    console.error(`Failed to fetch image for ${hazard.name}`, error);
                }
            } else {
                setImageUrl(`https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/hazards/${hazard.image}`);
            }
        };

        fetchImage();
    }, [hazard]);

    return (
        <div className='col-4 mb-3'>
            <div
                className={`d-flex align-items-center hazClick ${isSelected ? 'active' : ''}`}
                onClick={() => onClickHazards(hazard)}
            >
                <div className='col-2'>
                    <Checkbox name='haz' checked={isSelected}></Checkbox>
                </div>
                <div className='col-2'>
                    <img
                        src={imageUrl}
                        style={{ height: 40 }}
                        alt="sample"
                    />
                </div>
                <div className='col-8 ms-3'>
                    <p className='m-0'>{hazard.name}</p>
                </div>
            </div>
        </div>
    );
};

export default HazardItem;
