import React, { useState, useEffect, useRef } from "react";
import JoditEditor from "jodit-react";
import { Mo<PERSON>, Button } from "react-bootstrap";

function TextWidget({ data, onChangeData }) {
  // Initialize local state with data from the parent
  const [content, setContent] = useState(data.content || "");
  const [mdShow, setMdShow] = useState(false);
  const editorRef = useRef(null);

  // Editor configuration remains the same
  const config = {
    askBeforePasteHTML: false,
    askBeforePasteFromWord: false,
    defaultActionOnPaste: "insert_clear_html",
    toolbarAdaptive: false,
    toolbarButtonSize: "large",
    toolbarSticky: false,
    buttons:
      "|,bold,underline,italic,|,font,fontsize,|,superscript,subscript,|,ul,ol,|,outdent,indent,|,align,paste,|",
    enableDragAndDropFileToEditor: true,
  };

  // Update local state if the parent data changes
  useEffect(() => {
    if (data && data.content !== content) {
      setContent(data.content);
    }
  }, [data]);

  // Update local state and notify the parent via onChangeData callback
  const changeValue = (newContent) => {
    setContent(newContent);
    if (onChangeData) {
      onChangeData({ ...data, content: newContent });
    }
  };

  return (
    <>
      <div className="paragraph mb-3">
        <div className="card">
         
          <div className="card-body">
            <div className="text-center">
              <Button variant="primary" onClick={() => setMdShow(true)}>
                Edit Text
              </Button>
            </div>
            {content && (
              <div className="mt-3 mb-3">
                <p dangerouslySetInnerHTML={{ __html: content }} />
              </div>
            )}
          </div>
        </div>
      </div>
      <Modal
        show={mdShow}
        onHide={() => setMdShow(false)}
        size="lg"
        aria-labelledby="modal-title"
      >
        <Modal.Body>
          <JoditEditor
            ref={editorRef}
            value={content}
            config={config}
            onBlur={(newContent) => changeValue(newContent)}
            onChange={(newContent) => changeValue(newContent)}
          />
        </Modal.Body>
        <Modal.Footer>
          <Button variant="light" onClick={() => setMdShow(false)}>
            Cancel
          </Button>
          <Button variant="primary" onClick={() => setMdShow(false)}>
            Save
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
}

export default TextWidget;
