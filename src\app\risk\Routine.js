// @ts-nocheck
import React, { useState, useRef } from "react";

import $ from "jquery";
import {
  <PERSON><PERSON>,
  But<PERSON>,
  Form,
  Accordion,
  OverlayTrigger,
  Tooltip,
} from "react-bootstrap";
import cogoToast from "cogo-toast";

import Swal from "sweetalert2";
import { Typeahead } from "react-bootstrap-typeahead";
import { RISKASSESSMENT_LIST, GET_TEAM_MEMBERS_RISK, GMS1_URL, GET_ALL_USER, SENT_NOTIFICATION_MAIL, DRAFT_RA, GET_USER_ROLE_BY_MODE } from "../constants";
import { useEffect } from "react";
import Select from "react-select";
import CreatableSelect from 'react-select/creatable';
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";
import { useSelector, useDispatch } from "react-redux";

import API from "../services/API";
import Switch from "react-switch";
import { useHistory } from "react-router-dom";
import SignatureCanvas from "react-signature-canvas";
import DatePicker from "react-datepicker";
import * as moment from "moment";

import "react-datepicker/dist/react-datepicker.css";
import { HAZARDS_CATEGOTY } from "../constants";
import S3 from "react-aws-s3";
import { Buffer } from "buffer";
import axios from "axios";
import TeamDeclaration from "./components/TeamDeclaration";
import Hazards from "./components/Hazards";
import RA from "./components/RA";
import Consequence from "./components/Consequence";
import CurrentControl from "./components/CurrentControl";
import { Link } from "react-router-dom/cjs/react-router-dom";
import SubActivityTitle from "./components/SubActivityTitle";
import FullLoader from "../shared/FullLoader";
Buffer.from("anything", "base64");
window.Buffer = window.Buffer || require("buffer").Buffer;
// @ts-ignore
window.jQuery = $;
// @ts-ignore
window.$ = $;
const config = {
  bucketName: "sagt",
  region: "ap-southeast-1",
  accessKeyId: "********************",
  secretAccessKey: "iuo0OyvIb5Fvwlq3t0uStZP9oUvmrLfggSj5YsOs",
};
const customSwal = Swal.mixin({
  customClass: {
    confirmButton: "btn btn-danger",
    cancelButton: "btn btn-light",
  },
  buttonsStyling: false,
});

const customSwal2 = Swal.mixin({
  customClass: {
    confirmButton: "btn btn-primary",
  },
  buttonsStyling: false,
});


const Routine = (props) => {
  const history = useHistory();
  const description = useRef();
  const department = useRef();
  const title = useRef();
  const signRef = useRef(null);
  const user = useSelector((state) => state.login.user);
  console.log(user)

  const dispatch = useDispatch();

  const [refreshToggle, setRefreshToggle] = useState(false);
  const [mdShow, setMdShow] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [crew, setCrew] = useState([]);
  const [hazards, setHazards] = useState([]);
  const [selectedAttenteesValue, setSelectedAttenteesValue] = useState([]);
  const [selectedTypeValue, setSelectedTypeValue] = useState([]);
  const [selectedTypeDepart, setSelectedDepart] = useState([]);
  const [selectedTypeActivity, setSelectedActivity] = useState([]);
  const [selectedLeaderValue, setSelectedLeaderValue] = useState([]);
  const [selectedMemberValue, setSelectedMemberValue] = useState([]);
  const [selectedAssignValue, setSelectedAssignValue] = useState({});
  const [selectedRecommendation, setselectedRecommendation] = useState({});
  const [selectedAdditionalRecommendation, setselectedAdditionalRecommendation] = useState({});
  const [eptw, setEptw] = useState(false);
  const [selectSignMember, setSignMemberList] = useState([])
  const [addition, setAddition] = useState(["Yes"]);
  const [signModal, setSignModal] = useState(false)
  const [otherActivityText, setOtherActivityText] = useState('')
  const [additionalControl, setAdditionalControl] = useState([
    [{ control: "", name: "", date: "" }],
  ]);
  const [raActivity, setRaActivity] = useState([])
  const [raDepart, setRaDepart] = useState([])
  const [currentControl, setCurrentControl] = useState([])
  const [departActivity, setDepartActivity] = useState([])
  const [depart, setDepart] = useState([])
  const [eptwHot, setEptwHot] = useState([])
  const [activity, setActivity] = useState([])
  const [responsibility, setResponsibility] = useState([])
  const [sendShow, setSendShow] = useState(true)
  const [thirdAdd, setThirdAdd] = useState('')
  const [haz, setHaz] = useState([])

  const matrix = {
    1: { 1: 1, 2: 2, 3: 3, 4: 4, 5: 5 },
    2: { 1: 2, 2: 4, 3: 6, 4: 8, 5: 10 },
    3: { 1: 3, 2: 6, 3: 9, 4: 12, 5: 15 },
    4: { 1: 4, 2: 8, 3: 12, 4: 16, 5: 20 },
    5: { 1: 5, 2: 10, 3: 15, 4: 20, 5: 25 },
  };
  const matrixColor = {
    1: { 1: "#8cc14b", 2: "#8cc14b", 3: "#8cc14b", 4: "#8cc14b", 5: "#ffef00" },
    2: { 1: "#8cc14b", 2: "#8cc14b", 3: "#ffef00", 4: "#ffef00", 5: "#ffef00" },
    3: { 1: "#8cc14b", 2: "#ffef00", 3: "#ffef00", 4: "#ffef00", 5: "#ff1900" },
    4: { 1: "#8cc14b", 2: "#ffef00", 3: "#ffef00", 4: "#ff1900", 5: "#ff1900" },
    5: { 1: "#ffef00", 2: "#ffef00", 3: "#ff1900", 4: "#ff1900", 5: "#ff1900" },
  };

  const [task, setTask] = useState([
    [
      { type: "textbox", label: "Sub-Activity Description", value: "", option: { images: [] } },
      { type: "hazards", option: [{ allhazards: [] }, { selected: [] }] },

      {
        type: "textbox1",
        variant: "consequence",
        option: [{ type: "textarea", label: "", value: "", option: { type: "file", files: [] }, current_type: '', selected: {} }],
        button: true,
      },

      {
        type: "textbox1",
        variant: "current_control",
        option: [
          { type: "textarea", label: "Current Control", value: "", option: { type: "file", files: [] }, current_type: '', selected: {} },

        ],
        button: true,
      },
      {
        type: "initial",
        severity: [
          {
            type: "select1",
            label: "People",
            value: [
              { "value": "1", "label": "Negligible (1)" },
              { "value": "2", "label": "Minor (2)" },
              { "value": "3", "label": "Moderate (3)" },
              { "value": "4", "label": "Major (4)" },
              { "value": "5", "label": "Catastrophic (5)" }
            ],
            selected: {},
            val: 0,
          },
        ],
        likelyhood: [
          {
            type: "select1",
            label: "People",
            value: [
              { label: "Rare (1)", value: "1" },
              { label: "Unlikely (2)", value: "2" },
              { label: "Possible (3)", value: "3" },
              { label: "Likely (4)", value: "4" },
              { label: "Almost Certain (5)", value: "5" },
            ],
            selected: {},
            val: 0,
          },
        ],
        risk: [{ type: "People", label: "", color: "" }],
      },
      { type: "text", label: "is this Risk Level Acceptable ?" },

      {
        type: "checkbox1",
        variant: "additional",
        values: [
          { label: "Yes ", selected: true },
          { label: "No", selected: false },
        ],
        option: [
          {
            type: "textbox1",
            variant: "additional_control",
            option: [
              {
                label: "Additional Control Proposed",
                type: "textbox1",
                value: "",
                option: [
                  { label: "Responsibility", value: "", type: "text" },
                  { label: "Date", type: "date", value: "" },
                ],
                current_type: '',
                selected: {}
              },
            ],
          },
          { label: "Add Control", type: "button", value: "control" },

          {
            type: "rpn",
            severity: [
              {
                type: "select1",
                label: "People",
                value: [
                  { "value": "1", "label": "Negligible (1)" },
                  { "value": "2", "label": "Minor (2)" },
                  { "value": "3", "label": "Moderate (3)" },
                  { "value": "4", "label": "Major (4)" },
                  { "value": "5", "label": "Catastrophic (5)" }
                ],
                selected: {},
                val: 0,
              },
            ],
            likelyhood: [
              {
                type: "select1",
                label: "People",
                value: [
                  { label: "Rare (1)", value: "1" },
                  { label: "Unlikely (2)", value: "2" },
                  { label: "Possible (3)", value: "3" },
                  { label: "Likely (4)", value: "4" },
                  { label: "Almost Certain (5)", value: "5" },
                ],
                selected: {},
                val: 0,
              },
            ],
            risk: [{ type: "People", label: "", color: "" }],
          },
        ],
      },
    ],
  ]);

  useEffect(() => {
    getCrewList();
    getHazardList();
    getWorkActivity();
    getAllRaActivity();
    getAllResponsibility()

  }, []);

  const getAllResponsibility = async () => {
    const uriString = { include: [{ "relation": "workingGroup" }, { "relation": "designation" }, { "relation": "ghsOne" }] }
    const url = `${GET_ALL_USER}?filter=${encodeURIComponent(
      JSON.stringify(uriString)
    )}`;
    const response = await API.get(url);
    if (response.status === 200) {


      const depart = response.data.map(item => {
        return { value: item.id, label: item.firstName, email: item.email }
      })
      setResponsibility(depart)
    }

  }
  const getAllRaActivity = async () => {
    const response = await API.get(RISKASSESSMENT_LIST);
    if (response.status === 200) {
      const data = response.data.map(item => {
        return item.activity
      })
      setRaActivity(data)

      const haza = response.data.filter(item =>
        item.type.label === 'Hazard-Based'
      )


      setHaz(haza)


    }
  }
  const getWorkActivity = async () => {
    const uriString = { include: ["ghsTwos"] };

    const url = `${GMS1_URL}?filter=${encodeURIComponent(
      JSON.stringify(uriString)
    )}`;
    const response = await API.get(url);
    if (response.status === 200) {

      setDepartActivity(response.data)
      const depart = response.data.map(item => {
        return { value: item.id, label: item.name }
      })
      setDepart(depart)
    }
  };
  const getHazardList = async () => {
    const uriString = { include: ["hazards"] };

    const url = `${HAZARDS_CATEGOTY}?filter=${encodeURIComponent(
      JSON.stringify(uriString)
    )}`;
    const response = await API.get(url);
    if (response.status === 200) {
      console.log(response.data)
      const data = response.data.filter((item) => item.name !== 'Hazard-Based')
      //  data = data.sort((a, b) => (a.order > b.order ? 1 : -1));
      setHazards(data);

      const t = task;

      const text = t.map((item, i) => {
        item.map((item1, j) => {
          if (item1.type === "hazards") {

            item1.option[0].allhazards = data;
          }
        });

        return item;
      });

      setTask(text);
    }
  };
  const getCrewList = async () => {
    // const uriString = { include: ["department"] };

    // const url = `${GET_TEAM_MEMBERS_RISK}?filter=${encodeURIComponent(
    //   JSON.stringify(uriString)
    // )}`;
    const response = await API.post(GET_USER_ROLE_BY_MODE, {
      locationOneId: "",
      locationTwoId: "",
      locationThreeId: "",
      locationFourId: "",
      mode: 'ra-member'
    });
    if (response.status === 200) {
      let data = [];
      response.data.map((item) => {
        let department = item.ghsOne ? item.ghsOne.name : ''
        if (item.id !== user.id) {
          data.push({ label: item.firstName, value: item.id, department: department });
        }
      });

      setCrew(data);
    }
  };

  const dataURItoFile = (dataURI, filename) => {
    var byteString = atob(dataURI.split(",")[1]);
    // separate out the mime component
    var mimeString = dataURI.split(",")[0].split(":")[1].split(";")[0];
    // write the bytes of the string to an ArrayBuffer
    var ab = new ArrayBuffer(byteString.length);
    var dw = new DataView(ab);
    for (var i = 0; i < byteString.length; i++) {
      dw.setUint8(i, byteString.charCodeAt(i));
    }

    // write the ArrayBuffer to a blob, and you're done
    return new File([ab], filename, { type: mimeString });
  };
  const handleRecommendationChange = (e) => {
    setselectedRecommendation(e)
  }
  const handleAdditionalRecommendation = (e) => {
    setselectedAdditionalRecommendation(e)
    if (e.value !== 0) {
      setEptw(true)
    } else {
      setEptw(false)
    }
  }
  const createUserHandler = async () => {
    // @ts-ignore
    let required = true;
    setIsLoading(true);
    const filename = new Date().getTime() + "captin_sign.png";

    if (!signRef.current.isEmpty()) {
      const ReactS3Client = new S3(config);

      await ReactS3Client.uploadFile(
        dataURItoFile(
          signRef.current.getTrimmedCanvas().toDataURL("image/png"),
          filename
        ),
        "uploads/risk_sign/" + filename
      )
        .then((data) => console.log(data))
        .catch((err) => console.error(err));
    }

    // if (selectedTypeValue.length === 0) {
    //   cogoToast.info("Please Choose Type of RA !", { position: "top-right" });
    //   required = false;
    // }
    if (selectedTypeActivity.length === 0) {
      cogoToast.info("Please Enter Process / Work Activity !", {
        position: "top-right",
      });
      required = false;
    } else if (selectedMemberValue.length === 0) {
      cogoToast.info("Please Choose Team Members !", { position: "top-right" });
      required = false;
    } else if (selectedTypeDepart.length === 0) {
      cogoToast.info("Please Enter Department !", { position: "top-right" });
      required = false;
    } else if (signRef.current.isEmpty()) {
      cogoToast.info("Please Sign !", { position: "top-right" });
      required = false;
    } else if (selectedRecommendation === {}) {
      cogoToast.info("Please Choose Recommendation !", { position: "top-right" });
      required = false;
    } else if (task.length !== 0) {
      task.map((item, i) => {

        item.map(async (t) => {

          if (t.type === "textbox") {
            if (t.value === "") {
              cogoToast.info("Please Enter Sub Activity Title !", {
                position: "top-right",
              });
              required = false;
            }
          }
          //  else if (t.type === "hazards") {

          //   let hazard = false;
          //   t.option[0].allhazards.map((ha) => {
          //     ha.hazards.map((zar) => {
          //       if (zar.active) {
          //         hazard = true;
          //       }
          //     });
          //   });
          //   if (hazard === false) {
          //     cogoToast.info("Please Choose Sub Activity Hazards !", {
          //       position: "top-right",
          //     });
          //     required = false;
          //   }
          // } else if (t.variant === "consequence") {
          //   t.option.map((o) => {
          //     if (o.value === "") {
          //       cogoToast.info("Sub Activity Consequence Should not empty !", {
          //         position: "top-right",
          //       });
          //       required = false;
          //     }
          //   });
          // } else if (t.variant === "current_control") {
          //   t.option.map((o) => {
          //     if (o.value === "") {
          //       cogoToast.info(
          //         "Sub Activity Current Control Should not empty !",
          //         { position: "top-right" }
          //       );
          //       required = false;
          //     }
          //   });
          // } else if (t.type === "initial") {
          //   if (t.likelyhood[0].val === 0) {
          //     cogoToast.info("Please Choose Likelihood !", {
          //       position: "top-right",
          //     });
          //     required = false;
          //   } else if (t.severity[0].val === 0) {
          //     cogoToast.info("Please Choose severity !", {
          //       position: "top-right",
          //     });
          //     required = false;
          //   }
          // } else if (t.variant === "additional") {
          //   if (t.values[1].selected) {

          //     t.option.map((k) => {
          //       if (k.variant === "additional_control") {
          //         k.option.map((oo) => {
          //           if (oo.value === "") {
          //             cogoToast.info("Please Enter Addtional control !", {
          //               position: "top-right",
          //             });
          //             required = false;
          //           }
          //           oo.option.map((yy) => {

          //             if (yy.type === "text") {
          //               if (yy.value === "") {
          //                 cogoToast.info(
          //                   "Please Enter Additional Responsibility !",
          //                   { position: "top-right" }
          //                 );
          //                 required = false;
          //               }
          //             } else if (yy.type == "date") {
          //               if (yy.value === "") {
          //                 cogoToast.info("Please Enter Additional Date !", {
          //                   position: "top-right",
          //                 });
          //                 required = false;
          //               }
          //             }
          //           });
          //         });
          //       } else if (k.type === "rpn") {
          //         if (k.likelyhood[0].val === 0) {
          //           cogoToast.info("Please Choose Residual Likelihood !", {
          //             position: "top-right",
          //           });
          //           required = false;
          //         } else if (k.severity[0].val === 0) {
          //           cogoToast.info("Please Choose Residual severity !", {
          //             position: "top-right",
          //           });
          //           required = false;
          //         }
          //       }
          //     });
          //   }
          // }
        });
      });
    }
    let control = [];

    task.map((item, i) => {

      let tempArray = [];

      item.map(item1 => {
        if (item1.variant === "current_control") {
          item1.option.map(item2 => {
            tempArray.push({ 'value': item2.value, 'files': item2.option.files, 'type': item2.current_type });
            return null;
          });
        }
        return null;
      });

      control[i] = tempArray;
      return null;
    });

    let additionControls = [];

    task.map((item, i) => {

      let tempArray = [];

      item.map(item1 => {
        if (item1.variant === "additional") {
          item1.option[0].option.map(ite => {
            tempArray.push({ 'date': ite.option[1].value, 'name': ite.option[0].value, 'type': ite.current_type, 'value': ite.value });
          })
        }
        return null;
      });

      additionControls[i] = tempArray;
      return null;
    });

    console.log(additionControls)

    if (required === true) {
      const response = await fetch(RISKASSESSMENT_LIST, {
        method: "POST",
        body: JSON.stringify({
          meetid: "",
          title: "",
          activity: selectedTypeActivity.label,
          type: { label: "Routine Work", value: "Routine Work" },
          member: selectedMemberValue,
          department: selectedTypeDepart.label,
          task: task,
          date: "",
          status: "1",
          captain: user.firstName,
          sign: filename,
          additional: addition,
          additionalDates: additionControls,
          teamMemberInvolved: selectSignMember,
          recommendation: selectedRecommendation,
          additionRecommendation: selectedAdditionalRecommendation,
          eptw: eptw,
          eptwHighRisk:eptwHot,
          currentcontrol: control,
          userId: user.id,
          thirdAdd: thirdAdd
        }),
        headers: { "Content-type": "application/json; charset=UTF-8" },
      });

      if (response.ok) {
        setIsLoading(false);
        localStorage.setItem('ra_url', 'DASHBOARD')
        customSwal2.fire("Risk Assessment Created!", "", "success").then((result)=>{
          if (result.isConfirmed) {
            history.go(0);
          }
        });
      
        
    
       
      } else {
        //show error
        customSwal2.fire("Please Try Again!", "", "error");
        setIsLoading(false);
      }
    }

    setMdShow(false);
  };

  const draftUserHandler = async () => {
    const filename = new Date().getTime() + "captin_sign.png";

    if (!signRef.current.isEmpty()) {
      const ReactS3Client = new S3(config);

      await ReactS3Client.uploadFile(
        dataURItoFile(
          signRef.current.getTrimmedCanvas().toDataURL("image/png"),
          filename
        ),
        "uploads/risk_sign/" + filename
      )
        .then((data) => console.log(data))
        .catch((err) => console.error(err));
    }


    let control = [];

    task.map((item, i) => {

      let tempArray = [];

      item.map(item1 => {
        if (item1.variant === "current_control") {
          item1.option.map(item2 => {
            tempArray.push({ 'value': item2.value, 'files': item2.option.files, 'type': item2.current_type });
            return null;
          });
        }
        return null;
      });

      control[i] = tempArray;
      return null;
    });

    let additionControls = [];

    task.map((item, i) => {

      let tempArray = [];

      item.map(item1 => {
        if (item1.variant === "additional") {
          item1.option[0].option.map(ite => {
            tempArray.push({ 'date': ite.option[1].value, 'name': ite.option[0].value, 'type': ite.current_type, 'value': ite.value });
          })
        }
        return null;
      });

      additionControls[i] = tempArray;
      return null;
    });

    const response = await fetch(DRAFT_RA, {
      method: "POST",
      body: JSON.stringify({
        meetid: "",
        title: "",
        activity: selectedTypeActivity.label,
        type: { label: "Routine Work", value: "Routine Work" },
        member: selectedMemberValue,
        department: selectedTypeDepart.label,
        task: task,
        date: "",
        status: "3",
        captain: user.firstName,
        sign: filename,
        additional: addition,
        additionalDates: additionControls,
        teamMemberInvolved: selectSignMember,
        recommendation: selectedRecommendation,
        additionRecommendation: selectedAdditionalRecommendation,
        eptw: eptw,
        eptwHighRisk:eptwHot,
        currentcontrol: control,
        userId: user.id,
        thirdAdd: thirdAdd
      }),
      headers: { "Content-type": "application/json; charset=UTF-8" },
    });

    if (response.ok) {
      // customSwal2.fire("Risk Assessment Created!", "", "success");

      localStorage.setItem('ra_url', 'DASHBOARD')
      customSwal2.fire("Risk Assessment Created!", "", "success").then((result)=>{
        if (result.isConfirmed) {
          history.go(0);
        }
      });
    } else {
      //show error
      customSwal2.fire("Please Try Again!", "", "error");
      setIsLoading(false);
    }
  }



  const handleTypeChange = (selectedOptions) => {
    setSelectedTypeValue(selectedOptions);
  };
  const handleActivityChange = (selectedOptions) => {
    setSelectedActivity(selectedOptions)
  };
  const handleDepartChange = (option) => {
    setSelectedDepart(option)
    setActivity([])
    const active = departActivity.filter(item => item.id === option.value);
    console.log(active)

    const final = active[0].ghsTwos.filter(item => !raActivity.includes(item.name))
    console.log(final)
    const activity = final.map(item => {
      return { label: item.name, value: item.id }
    })

    setActivity(activity)

  }
  const handleLeaderChange = (selectedOptions) => {
    setSelectedLeaderValue(selectedOptions);
  };
  const handleMemberChange = (selectedOptions) => {
    console.log(selectedOptions)

    if (selectedOptions !== null) {
      setSelectedMemberValue(selectedOptions);
      const sign1 = selectedOptions.map((item) => {
        return { label: item.label, sign: 'No', id: item.value, date: '', department: item.department }
      })
      setSignMemberList(sign1)
    }
    else {
      setSelectedMemberValue([])
      setSignMemberList([])
    }

    setSendShow(true)
  };
  const handleChange = (selectedOptions) => {
    setSelectedAttenteesValue(selectedOptions);
  };
  const handleSelectChange = (e, j, i, k, name, variant) => {


    //  if (name === 'Personnel') {

    if (variant === "severity") {
      var likelyhood = 0;
      const t = task;
      const text = t.map((item, ii) => {
        if (i === ii) {
          item.map((item1, jj) => {
            if (jj === j) {
              likelyhood = item1.likelyhood[k].val;
              item1.severity[k].selected = e;
              item1.severity[k].val = e.value;
              item1.risk[k].label = getMatrixValue(e.value, likelyhood)
              item1.risk[k].color = getCalculatedColor(e.value, likelyhood);
            }
          });
        }

        return item;
      });
      setTask(text);
    } else {
      var likelyhood = 0;
      const t = task;
      const text = t.map((item, ii) => {
        if (i === ii) {
          item.map((item1, jj) => {
            if (jj === j) {
              likelyhood = item1.severity[k].val;
              item1.likelyhood[k].selected = e;
              item1.likelyhood[k].val = e.value;
              item1.risk[k].label = getMatrixValue(e.value, likelyhood)
              item1.risk[k].color = getCalculatedColor(e.value, likelyhood);
            }
          });
        }

        return item;
      });
      setTask(text);
    }


  };

  const getMatrixValue = (row, col) => {
    return matrix[row][col];
  };
  const getCalculatedColor = (row, col) => {
    return matrixColor[row][col];
  };
  const handleSelectRPNChange = (e, j, i, k, l, name, variant) => {
    if (variant === "severity") {
      var likelyhood = 0;
      const t = task;
      const text = t.map((item, ii) => {
        if (i === ii) {
          item.map((item1, jj) => {
            if (jj === j) {
              item1.option.map((item2, kk) => {
                if (kk === k) {
                  item2.severity.map((item3, ll) => {
                    if (ll === l) {

                      likelyhood = item2.likelyhood[l].val;
                      item3.selected = e;
                      item3.val = e.value;
                      item2.risk[l].label = getMatrixValue(e.value, likelyhood);
                      item2.risk[l].color = getCalculatedColor(
                        e.value,
                        likelyhood
                      );
                    }
                  });
                }


              });
            }
          });
        }

        return item;
      });

      setTask(text);
    } else {
      var likelyhood = 0;
      const t = task;
      const text = t.map((item, ii) => {
        if (i === ii) {
          item.map((item1, jj) => {
            if (jj === j) {
              item1.option.map((item2, kk) => {
                if (kk === k) {
                  item2.likelyhood.map((item3, ll) => {
                    if (ll === l) {
                      likelyhood = item2.severity[l].val;
                      item3.selected = e;
                      item3.val = e.value;
                      item2.risk[l].label = getMatrixValue(e.value, likelyhood);
                      item2.risk[l].color = getCalculatedColor(
                        e.value,
                        likelyhood
                      );
                    }
                  });
                }


              });
            }
          });
        }

        return item;
      });
      setTask(text);
    }
  };
  const addTask = async () => {
    const uriString = { include: ["hazards"] };

    const url = `${HAZARDS_CATEGOTY}?filter=${encodeURIComponent(
      JSON.stringify(uriString)
    )}`;
    const response = await API.get(url);
    let addTask = [
      { type: "textbox", label: "Sub-Activity Description", value: "", option: { images: [] } },
      { type: "hazards", option: [{ allhazards: [] }, { selected: [] }] },

      {
        type: "textbox1",
        variant: "consequence",
        option: [{ type: "textarea", label: "", value: "", option: { type: "file", files: [] }, current_type: '', selected: {} }],
        button: true,
      },

      {
        type: "textbox1",
        variant: "current_control",
        option: [
          { type: "textarea", label: "Current Control", value: "", option: { type: "file", files: [] }, current_type: '', selected: {} },

        ],
        button: true,
      },
      {
        type: "initial",
        severity: [
          {
            type: "select1",
            label: "People",
            value: [
              { "value": "1", "label": "Negligible (1)" },
              { "value": "2", "label": "Minor (2)" },
              { "value": "3", "label": "Moderate (3)" },
              { "value": "4", "label": "Major (4)" },
              { "value": "5", "label": "Catastrophic (5)" }
            ],
            selected: {},
            val: 0,
          },
        ],
        likelyhood: [
          {
            type: "select1",
            label: "People",
            value: [
              { label: "Rare (1)", value: "1" },
              { label: "Unlikely (2)", value: "2" },
              { label: "Possible (3)", value: "3" },
              { label: "Likely (4)", value: "4" },
              { label: "Almost Certain (5)", value: "5" },
            ],
            selected: {},
            val: 0,
          },
        ],
        risk: [{ type: "People", label: "", color: "" }],
      },
      { type: "text", label: "is this Risk Level Acceptable ?" },

      {
        type: "checkbox1",
        variant: "additional",
        values: [
          { label: "Yes ", selected: true },
          { label: "No", selected: false },
        ],
        option: [
          {
            type: "textbox1",
            variant: "additional_control",
            option: [
              {
                label: "Addition Control Proposed",
                type: "textbox1",
                value: "",
                option: [
                  { label: "Responsibility", value: "", type: "text" },
                  { label: "Date", type: "date", value: "" },
                ],
                current_type: '',
                selected: {}
              },
            ],
          },
          { label: "Add Control", type: "button", value: "control" },

          {
            type: "rpn",
            severity: [
              {
                type: "select1",
                label: "People",
                value: [
                  { "value": "1", "label": "Negligible (1)" },
                  { "value": "2", "label": "Minor (2)" },
                  { "value": "3", "label": "Moderate (3)" },
                  { "value": "4", "label": "Major (4)" },
                  { "value": "5", "label": "Catastrophic (5)" }
                ],
                selected: {},
                val: 0,
              },
            ],
            likelyhood: [
              {
                type: "select1",
                label: "People",
                value: [
                  { label: "Rare (1)", value: "1" },
                  { label: "Unlikely (2)", value: "2" },
                  { label: "Possible (3)", value: "3" },
                  { label: "Likely (4)", value: "4" },
                  { label: "Almost Certain (5)", value: "5" },
                ],
                selected: {},
                val: 0,
              },
            ],
            risk: [{ type: "People", label: "", color: "" }],
          },
        ],
      },
    ];


    if (response.status === 200) {
      const newT = addTask.map((item1, j) => {
        if (item1.type === "hazards") {
          item1.option[0].allhazards = response.data;
        }
        return item1;
      });


      setTask((prev) => [...prev, newT]);
    }
    // setTask((prev) => [...prev, newT]);
    setAddition((prev) => [...prev, "Yes"]);
    const addcont = additionalControl;
    addcont.push([{ control: "", name: "", date: "" }]);
    setAdditionalControl(addcont);
  };
  const handleSelectInChange = (e, k, j, i) => {
    const t = task;
    const text = t.map((item, ii) => {
      if (i === ii) {
        item.map((item1, jj) => {
          if (jj === j) {
            item1.option.map((item2, kk) => {
              if (kk === k) {
                item2.value = e;
              }
            });
          }
        });
      }

      return item;
    });
    setTask(text);
  };
  const onchangeSelectOpt = (e, k, j, i) => {
    const t = task;
    const text = t.map((item, ii) => {
      if (i === ii) {
        item.map((item1, jj) => {
          if (jj === j) {
            item1.option.map((item2, kk) => {
              if (kk === k) {
                item2.selected = e;
              }
            });
          }
        });
      }

      return item;
    });
    setTask(text);
  };
  const onchangeSelect = (e, j, i) => {

    const t = task;
    const text = t.map((item, ii) => {
      if (i === ii) {
        item.map((item1, jj) => {
          if (jj === j) {
            item1.selected = e;
          }
        });
      }

      return item;
    });
    setTask(text);
  };
  const onClickButton1 = (e, j, i, k) => {

    e.preventDefault();
    const t = task;
    const text = t.map((item, ii) => {
      if (i === ii) {
        item.map((item1, jj) => {
          if (jj === j) {
            item1.option[k].option.push({
              label: "Addition Control Proposed",
              type: "textbox1",
              value: "",
              option: [
                { label: "Responsibility", value: "", type: "text" },
                { label: "Date", type: "date", value: "" },
              ],
            });

            // item[j - 1].option.push({ 'type': 'textbox1', 'option': [{ 'label': 'Addition Control Proposed', 'type': 'textbox1', 'value': '', 'option': [{ 'label': 'Responsibility', 'value': '', 'type': 'text' }, { 'label': 'Date', 'type': 'date', 'value': '' }] }] })
          }
        });
      }

      return item;
    });
    const addcont = additionalControl;
    addcont[i].push({ control: "", name: "", date: "" });
    setAdditionalControl(addcont);
    setTask(text);
  };

  const onchangeBox1Text = (e, j, i, k, l) => {
    const t = task;
    const text = t.map((item, ii) => {
      if (i === ii) {
        item.map((item1, jj) => {
          if (jj === j) {
            item1.option.map((item2, kk) => {
              if (kk === k) {
                item2.option.map((item3, ll) => {
                  if (ll === l) {
                    item3.value = e.target.value;
                  }
                });
              }
            });
          }
        });
      }

      return item;
    });
    const addcont = additionalControl;
    addcont[i][l].control = e.target.value;
    setAdditionalControl(addcont);
    setTask(text);
  };

  const changeTypeAdditionalControl = (e, j, i, k, l) => {
    const t = task;
    const text = t.map((item, ii) => {
      if (i === ii) {
        item.map((item1, jj) => {
          if (jj === j) {
            item1.option.map((item2, kk) => {
              if (kk === k) {
                item2.option.map((item3, ll) => {
                  if (ll === l) {
                    item3.selected = e;
                    item3.current_type = e.label
                  }
                });
              }
            });
          }
        });
      }

      return item;
    });

    setTask(text);
  }
  const onDeleteTextBox = (j, i, k) => {
    const t = task;
    const text = t.map((item, ii) => {
      if (i === ii) {
        item.map((item1, jj) => {
          if (jj === j) {

            if (item1.option.length !== 1) {
              item1.option.splice(k, 1);
            } else {
              cogoToast.info("You Can't Delete Consequence  !", {
                position: "top-right",
              });
            }

          }
        });
      }

      return item;
    });
    setTask(text);
  };
  const onDeleteFileUpload = (j, i, k) => {
    const t = task;
    const text = t.map((item, ii) => {
      if (i === ii) {
        item.map((item1, jj) => {
          if (jj === j) {
            if (item1.option.length !== 1) {
              item1.option.splice(k, 1);
            } else {
              cogoToast.info("You Can't Delete Current Control  !", {
                position: "top-right",
              });
            }
          }
        });
      }

      return item;
    });
    setTask(text);
  };
  const onDeleteTextBox1 = (j, i, k, l) => {
    const t = task;
    const text = t.map((item, ii) => {
      if (i === ii) {
        item.map((item1, jj) => {
          if (jj === j) {
            // delete item1.option[k]

            item1.option.map((item2, kk) => {
              if (kk === k) {
                if (item2.option.length !== 1) {
                  item2.option.splice(l, 1);
                } else {
                  cogoToast.info("You Can't Delete Additional Current Control  !", {
                    position: "top-right",
                  });
                }
              }
            });
          }
        });
      }

      return item;
    });
    const addcont = additionalControl;
    addcont[i].splice(l, 1);
    setAdditionalControl(addcont);
    setTask(text);
  };
  const arraysAreEqual = (arr1, arr2) => {
    if (arr1.length !== arr2.length) {
      return false;
    }

    for (let i = 0; i < arr1.length; i++) {
      if (arr1[i] !== arr2[i]) {
        return false;
      }
    }

    return true;
  }
  const onDeleteTask = (e, i) => {
    e.stopPropagation()
    const t = task;
    if (t.length !== 1) {
      const indexToRemove = t.findIndex(arr => arraysAreEqual(arr, t[i]));
      if (indexToRemove !== -1) {
        t.splice(indexToRemove, 1);
      }
      setTask(t);
      setRefreshToggle(!refreshToggle);
    } else {
      cogoToast.info("You Can't Delete Sub-Activity !", {
        position: "top-right",
      });
    }
  };
  const onchangeBox2Text = (e, j, i, k, l, m) => {
    const t = task;
    const text = t.map((item, ii) => {
      if (i === ii) {
        item.map((item1, jj) => {
          if (jj === j) {
            item1.option.map((item2, kk) => {
              if (kk === k) {
                item2.option.map((item3, ll) => {
                  if (ll === l) {
                    item3.option.map((item4, mm) => {
                      if (mm === m) {
                        item4.value = e;
                      }
                    });
                  }
                });
              }
            });
          }
        });
      }

      return item;
    });
    const addcont = additionalControl;
    addcont[i][l].name = e;
    setAdditionalControl(addcont);
    setTask(text);
  };
  const handleDateChange = (e, j, i, k, l, m) => {
    const t = task;
    const text = t.map((item, ii) => {
      if (i === ii) {
        item.map((item1, jj) => {
          if (jj === j) {
            item1.option.map((item2, kk) => {
              if (kk === k) {
                item2.option.map((item3, ll) => {
                  if (ll === l) {
                    item3.option.map((item4, mm) => {
                      if (mm === m) {
                        item4.value = e;
                      }
                    });
                  }
                });
              }
            });
          }
        });
      }

      return item;
    });
    const addcont = additionalControl;
    addcont[i][l].date = e;
    setAdditionalControl(addcont);
    setTask(text);
  };
  const onClickButton = (e, j, i) => {

    e.preventDefault();
    const t = task;
    const text = t.map((item, ii) => {
      if (i === ii) {

        if (item[j].variant === "consequence") {
          item[j].option.push({ type: "textarea", label: "", value: "", option: { type: "file", files: [] }, current_type: '', selected: {} });
        } else if (item[j].variant === "current_control") {
          item[j].option.push(
            {
              type: "textarea",
              label: "",
              value: "",
              option: { type: "file", files: [] },
              current_type: '',
              seleted: {}
            },

          );
        } else if (item[j].value === "control") {

          item[j - 1].option.push({
            type: "textbox1",
            option: [
              {
                label: "Addition Control Proposed",
                type: "textbox1",
                value: "",
                option: [
                  { label: "Responsibility", value: "", type: "text" },
                  { label: "Date", type: "date", value: "" },
                ],
                current_type: '',
                seleted: {}
              },
            ],
          });
        }
        // item.map((item1, jj) => {
        //     if (jj === j) {
        //         item1.selected = e
        //     }

        // })
      }

      return item;
    });
    setTask(text);
  };
  const onchangeText = (e, j, i) => {
    const t = task;

    const text = t.map((item, ii) => {

      if (i === ii) {

        item.map((item1, jj) => {
          if (jj === j) {
            item1.value = e.target.value;
          }
        });
      }

      return item;
    });
    setTask(text);
  };
  const onchangeText1 = (e, j, i, k) => {
    const t = task;
    const text = t.map((item, ii) => {
      if (i === ii) {
        item.map((item1, jj) => {
          if (jj === j) {
            item1.option.map((item2, kk) => {
              if (kk === k) {
                item2.value = e.target.value;
              }
            });
          }
        });
      }

      return item;
    });
    setTask(text);
  };
  const changeTypeControl = (e, j, i, k) => {
    const t = task;
    const text = t.map((item, ii) => {
      if (i === ii) {
        item.map((item1, jj) => {
          if (jj === j) {
            item1.option.map((item2, kk) => {
              if (kk === k) {
                item2.selected = e;
                item2.current_type = e.value;
              }
            });
          }
        });
      }

      return item;
    });
    setTask(text);
  }
  const toggleButton = (e, j, i, v) => {
    const t = task;
    const text = t.map((item, ii) => {
      if (i === ii) {
        item.map((item1, jj) => {
          if (jj === j) {

            item1.values.map((item2) => {
              item2.selected = false;
            });
            item1.values[v].selected = true;
            const adcot = addition;
            adcot[i] = item1.values[v].label;
            setAddition(adcot);
          }
        });
      }

      return item;
    });
    setTask(text);
  };

  const handleAssignChange = (e) => {
    setSelectedAssignValue(e);
  };
  const convertImageUrlToDataUrl = async (imageUrl) => {
    try {
      const response = await axios.get(imageUrl, {
        responseType: 'arraybuffer', headers: {
          'Access-Control-Allow-Origin': '*', // This header should be set on the server, not in the client
        },
      },);
      const base64 = Buffer.from(response.data, 'binary').toString('base64');
      const mimeType = response.headers['content-type'];
      const dataUrl = `data:${mimeType};base64,${base64}`;
      return dataUrl
    } catch (error) {
      console.error('Error converting image to data URL:', error);
    }
  }
  const onClickHazard = (e, i, j, c, h, ca, ha) => {


    const t = task;
    const text = t.map((item, ii) => {
      if (i === ii) {
        item.map((item1, jj) => {
          if (jj === j) {
            item1.option[0].allhazards.map((item2, k) => {
              if (k === c) {
                item2.hazards.map(async (item3, m) => {
                  if (m === h) {
                    if (item3.active === true) {
                      delete item3.active;
                      item1.option[1].selected = item1.option[1].selected.filter((item) => item.id !== item3.id);
                    } else {
                      item3.active = true;
                      // const imageDataUrl = await convertImageUrlToDataUrl('https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/hazards/'+item3.image)
                      // // console.log(imageDataUrl)
                      // item3.dataURI =imageDataUrl
                      item1.option[1].selected.push(item3)



                    }
                  }
                });
              }
            });
          }
        });
      }
      return item;
    });

    setTask(text);
  };
  const onchangeConsequencefiles = async (e, j, i, k) => {
    console.log(e, j, i, k)
    const t = task;
    let text = [];
    const ReactS3Client = new S3(config);
    const filename = new Date().getTime() + e.target.files[0].name;


    await ReactS3Client.uploadFile(
      e.target.files[0], "uploads/consequence/" + filename

    )
      .then((data) => {
        text = t.map((item, ii) => {
          if (i === ii) {
            item.map((item1, jj) => {
              if (jj === j) {
                item1.option.map(async (item2, kk) => {
                  if (kk === k) {
                    item2.option.files = []
                    item2.option.files.push(filename);
                  }
                });
              }
            });
          }

          return item;
        });

      })
      .catch((err) => console.error(err));


    setTask(text);
  };

  const onchangefiles = async (e, j, i, k) => {
    const t = task;
    let text = [];
    const ReactS3Client = new S3(config);
    const filename = new Date().getTime() + e.target.files[0].name;

    await ReactS3Client.uploadFile(
      e.target.files[0], "uploads/current_control/" + filename

    )
      .then((data) => {
        text = t.map((item, ii) => {
          if (i === ii) {
            item.map((item1, jj) => {
              if (jj === j) {
                item1.option.map(async (item2, kk) => {
                  if (kk === k) {
                    item2.option.files = []
                    item2.option.files.push(filename);
                  }
                });
              }
            });
          }

          return item;
        });

      })
      .catch((err) => console.error(err));


    setTask(text);
  };
  const onDeleteFiles = async (j, i, k, f) => {
    const t = task;
    let text = [];

    text = t.map((item, ii) => {
      if (i === ii) {
        item.map((item1, jj) => {
          if (jj === j) {
            item1.option.map(async (item2, kk) => {
              if (kk === k) {
                item2.option.files.splice(f, 1);
              }
            });
          }
        });
      }

      return item;
    });



    setTask(text);
  };
  const onDeleteActivityFiles = async (j, i, f) => {
    const t = task;
    let text = [];

    text = t.map((item, ii) => {
      if (i === ii) {
        item.map((item1, jj) => {
          if (jj === j) {

            item1.option.images.splice(f, 1);

          }
        });
      }

      return item;
    });



    setTask(text);
  };
  const setSignShow = (item) => {
    setSignModal(true)


  }
  const getSign = () => {

  }
  const onUpTask = (e, i) => {
    e.stopPropagation();
    setTask(prevArrays => {
      const newArrayOfArrays = [...prevArrays];
      [newArrayOfArrays[i], newArrayOfArrays[i - 1]] = [newArrayOfArrays[i - 1], newArrayOfArrays[i]];
      return newArrayOfArrays;
    });
  }
  const onDownTask = (e, i) => {
    e.stopPropagation();
    setTask(prevArrays => {
      const newArrayOfArrays = [...prevArrays];
      [newArrayOfArrays[i], newArrayOfArrays[i + 1]] = [newArrayOfArrays[i + 1], newArrayOfArrays[i]];
      return newArrayOfArrays;
    });
  }
  const sendNotification = async () => {
    let check = true
    if (selectedTypeActivity.length === 0) {
      check = false
    } else if (selectedTypeDepart.length === 0) {
      check = false
    } else if (selectedMemberValue.length === 0) {
      check = false
    }
    if (check) {
      const response = await API.post(SENT_NOTIFICATION_MAIL, {

        activity: selectedTypeActivity,
        depart: selectedTypeDepart,
        member: selectedMemberValue,
        leader: user.firstName,
      })
      console.log(response.status)
      if (response.status === 200) {
        customSwal2.fire("Notification Sent!", "", "success");
        setSendShow(false)
      }
    } else {
      customSwal2.fire("Please Select Activity or department or Team Member!", "", "warning");
    }
  }

  const onchangeSubActivityfiles = async (e, i, j) => {
    console.log(i)
    const t = task;
    let text = [];
    const ReactS3Client = new S3(config);
    const filename = new Date().getTime() + e.target.files[0].name;

    await ReactS3Client.uploadFile(
      e.target.files[0], "uploads/activity/" + filename

    )
      .then((data) => {
        text = t.map((item, ii) => {
          if (i === ii) {
            item.map((item1, jj) => {
              if (jj === j) {
                console.log(item1)
                item1.option.images.push({ name: filename, remarks: '' })

              }
            });
          }

          return item;
        });

        console.log(text)

      })
      .catch((err) => console.error(err));


    setTask(text);
  }
  const removeActivityImage = (i, j, f) => {
    console.log(i, j, f)

  }
  const onchangeRemarksText = (e, j, i, f) => {

    const t = task;
    let text = [];
    text = t.map((item, ii) => {
      if (i === ii) {
        item.map((item1, jj) => {
          if (jj === j) {
            item1.images.map(async (item2, kk) => {
              if (f === kk) {
                item2.remarks = e.target.value
              }


            })


            // item1.option.map(async (item2, kk) => {
            //   if (kk === k) {
            //     item2.option.files = []
            //     item2.option.files.push(filename);
            //   }
            // });
          }
        });
      }

      return item;
    });
    setTask(text);

  }

  const checkHotWork = (e,item) => {
    
    if(e.target.checked){
      setEptwHot((prev) => [...prev, {name:item.hazard_name,id:item.id}]);

    }else{
      console.log(false)
      
      setEptwHot(prevData => prevData.filter(item1 => item1.id !== item.id));
    }


  }
  return (
    <>
      {console.log(task)}
      <div>
        <div className="row">
          <div className="col-12">
            <div className="card">
              {/* <div className="col-12">
                <div className="d-flex mb-4 tabs-risk boxShadow">

                  <div className="col-4 nav-item pr-0">
                    <Link to="/risk-assessment/routine" className="nav-link active ">Routine Work</Link>
                  </div>
                  <div className="col-4 nav-item pl-0 pr-0">
                    <Link to="/risk-assessment/nonroutine" className="nav-link">Non-Routine Work</Link>
                  </div>
                  <div className="col-4 nav-item pl-0">
                    <Link to="/risk-assessment/hazardbased" className="nav-link">Hazard-Based</Link>
                  </div>

                </div>
              </div> */}
              <div className="card-body">
                <h4 className="card-title">Routine Work RiskAssessment </h4>
                <div className="row">
                  <div className="col-12">
                    <form className="forms">
                      {/* <div className="col-6">
                        {console.log(task)}
                        <div className="form-group required">
                          <label htmlFor="user_category">Type of RA</label>
                          <Select
                            labelKey="label"
                            id="user_description"
                            onChange={handleTypeChange}
                            options={[
                              {
                                label: "Routine Work",
                                value: "Routine Work"
                              },
                              {
                                label: "Non-Routine Work",
                                value: "Non-Routine Work"
                              },
                              {
                                label: "Hazard Based",
                                value: "Hazard Based"
                              },

                            ]}
                            placeholder="Type..."
                          />
                        </div>
                      </div> */}

                      <div className="row">
                        <div className="col-6">
                          <div className="form-group required">
                            <label htmlFor="user_name">
                              Department
                            </label>
                            <Select
                              labelKey="label"
                              id="user_description"
                              onChange={handleDepartChange}
                              options={depart}
                              placeholder="Type..."
                            />
                          </div>
                        </div>

                        <div className="col-6">
                          <div className="form-group required">
                            <label htmlFor="user_name">Process / Work Activity</label>
                            <Select
                              labelKey="label"
                              id="user_description"
                              onChange={handleActivityChange}
                              options={activity}
                              placeholder="Type..."
                            />
                            {/* <textarea className='form-control' ref={department} id="user_name"></textarea> */}
                          </div>
                          {/* {selectedTypeActivity.label === 'Others' &&
                            <div className="">
                              <Form.Control
                                type="text"
                                placeholder="Enter Other Activity"
                                onChange={(e) =>
                                 setOtherActivityText(e.target.value)
                                }
                                value={otherActivityText}
                              />
                            </div>
                          } */}
                        </div>
                      </div>



                      <div className="row">
                        <div className="col-12">
                          <div className="form-group required">
                            <label htmlFor="user_category">
                              RA Team Members
                            </label>
                            <Select
                              labelKey="label"
                              id="user_description"
                              onChange={handleMemberChange}
                              options={crew}
                              isMulti={true}
                              placeholder="Choose Members.."
                            />
                          </div>
                        </div>
                      </div>
                      {sendShow === true &&
                        <div className="row">
                          <div className="col-12 d-flex justify-content-center">
                            <button
                              type="button"
                              className="btn btn-primary btn-rounded mb-3 "
                              onClick={(e) => {
                                e.preventDefault();

                                sendNotification();

                              }}
                            >
                              Send Notification
                            </button>
                          </div>
                        </div>
                      }

                    </form>

                    <Accordion>
                      <div className="tasks">
                        {task.map((item1, i) => {
                          return (
                            <Accordion.Item
                              eventKey={i}
                              className="mt-3"
                              style={{
                                boxShadow: "0px 1px 15px 3px #bcbfc452",
                                position: "relative",
                              }}
                            >




                              <Accordion.Header onClick={() => {
                                if (i !== 0) {
                                  window.scrollTo({ top: 100, left: 0, behavior: 'smooth' });
                                }
                              }}>
                                <div className="row align-items-center">
                                  <div className="col-10">{"Sub Activity " +
                                    (i + 1) +
                                    " : " +
                                    item1[0].value}</div>
                                  <div className="col-2 d-flex flex-row justify-content-center">
                                    {i === 0 ?
                                      ''
                                      : <i className="mdi mdi-arrow-up uptask" onClick={(e) => onUpTask(e, i)}></i>}
                                    {task.length !== 0 ? <>

                                      {task.length === i + 1 ?
                                        ''
                                        :
                                        <i className="mdi mdi-arrow-down downtask" onClick={(e) => onDownTask(e, i)}></i>
                                      }
                                    </>
                                      : ''}
                                    <i className='mdi mdi-delete taskdelete' onClick={(e) => onDeleteTask(e, i)} ></i>
                                  </div>
                                </div>
                              </Accordion.Header>
                              <Accordion.Body>
                                <div
                                  className="col-12 add-task"
                                  style={{
                                    padding: 40,
                                    marginTop: 20,
                                    boxShadow:
                                      "rgb(0 0 0 / 20%) 0px 0px 10px 0px",
                                  }}
                                >
                                  {/* <h3 style={{ textAlign: 'center' }}></h3> */}
                                  {item1.map((item, j) => {
                                    if (item.type === "textbox") {
                                      return (<div className='row mb-4' style={{ padding: '20px', boxShadow: 'rgba(0, 0, 0, 0.12) 0px 0px 10px 4px' }}>
                                        <div className="form-group required">
                                          <label htmlFor="user_name">
                                            {item.label}
                                          </label>
                                          <Form.Control
                                            type="text"
                                            onChange={(e) =>
                                              onchangeText(e, j, i)
                                            }
                                            value={item.value}
                                          />
                                        </div>
                                        <div className="form-group">
                                          {item.option.images.length !== 0 && (
                                            <div
                                              className="row mt-3"
                                              style={{
                                                padding: 10,
                                                border: '1px dashed',
                                                borderRadius: 10

                                              }}
                                            >
                                              {item.option.images.map(
                                                (files, f) => {
                                                  return (
                                                    <div className="col-4">
                                                      <div
                                                        className=" boxShadow p-3 "
                                                        style={{
                                                          position:
                                                            "relative",
                                                          height: 100


                                                        }}
                                                      >
                                                        <img
                                                          style={{

                                                            maxHeight:
                                                              "100%",
                                                            maxWidth: "100%"
                                                          }}
                                                          src={
                                                            "https://sagt.s3-ap-southeast-1.amazonaws.com/uploads/activity/" +
                                                            files.name
                                                          }
                                                          alt="test"
                                                        />
                                                        <i
                                                          className="mdi mdi-delete taskdelete"
                                                          style={{
                                                            position: 'absolute',
                                                            top: '-8px',
                                                            right: 0,
                                                            color: 'red'
                                                          }}
                                                          onClick={() =>
                                                            onDeleteActivityFiles(
                                                              j,
                                                              i,
                                                              f
                                                            )
                                                          }
                                                        ></i>
                                                      </div>
                                                    </div>
                                                  );
                                                }
                                              )}
                                            </div>
                                          )}
                                          <label
                                            className="d-flex justify-content-center mt-4"
                                            htmlFor={"fileactiv" + j + i}
                                            style={{ position: 'relative' }}
                                          >

                                            <i
                                              style={{
                                                fontSize: 55,
                                                padding: 10,
                                                border: "1px dashed",
                                                borderRadius: 10,
                                              }}
                                              className="typcn typcn-cloud-storage-outline"
                                            ></i>



                                            <span style={{ position: 'absolute', bottom: 0, right: 0 }}>*png,gif,jpeg</span>
                                          </label>
                                          <Form.Control
                                            type="file"
                                            id={"fileactiv" + j + i}
                                            style={{
                                              display: "none",
                                            }}
                                            accept="image/png, image/gif, image/jpeg"
                                            onChange={(e) =>
                                              onchangeSubActivityfiles(
                                                e,
                                                i,
                                                j


                                              )
                                            }
                                          />
                                        </div>
                                        {/* <p onClick={() => onchangeSubActivityfiles(i, j)}>test</p> */}
                                      </div>
                                      );
                                    } else if (item.variant === "consequence") {
                                      return (
                                        <Consequence item={item} i={i} j={j} onchangeText12={onchangeText1} onDeleteTextBox1={onDeleteTextBox} onDeleteFiles1={onDeleteFiles} onchangeConsequencefiles1={onchangeConsequencefiles} onClickButton1={onClickButton} changeTypeControl1={changeTypeControl} />
                                      );
                                    } else if (
                                      item.variant === "current_control"
                                    ) {
                                      return (
                                        <CurrentControl item={item} i={i} j={j} changeTypeControl1={changeTypeControl} onchangeText11={onchangeText1} onDeleteFileUpload1={onDeleteFileUpload} onDeleteFiles1={onDeleteFiles} onchangefiles1={onchangefiles} onClickButton1={onClickButton} />
                                      );
                                    } else if (item.type === "textarea") {
                                      return (
                                        <div className="form-group">
                                          <label htmlFor="user_name">
                                            {item.label}
                                          </label>
                                          <textarea
                                            onChange={(e) =>
                                              onchangeText(e, j, i)
                                            }
                                            className="form-control"
                                          >
                                            {item.value}
                                          </textarea>
                                        </div>
                                      );
                                    } else if (item.type === "checkbox1") {
                                      return (
                                        <>
                                          <div
                                            className="col-12"
                                            style={{
                                              padding: 10,
                                              textAlign: "center",
                                            }}
                                          >
                                            <div
                                              className="form-group mt-5 mb-3"
                                              style={{ textAlign: "center" }}
                                            >
                                              <label
                                                htmlFor="user_name"
                                                style={{ fontSize: 20 }}
                                              >
                                                is this Risk Level Acceptable ?
                                              </label>
                                            </div>
                                            <div className="box-outer mb-4">
                                              {item.values.map((val, v) => {
                                                return (
                                                  <label
                                                    htmlFor="user_name mt-3"
                                                    onClick={(e) =>
                                                      toggleButton(e, j, i, v)
                                                    }
                                                    className={
                                                      val.selected
                                                        ? "box_select active"
                                                        : "box_select "
                                                    }
                                                  >
                                                    {val.label}{" "}
                                                  </label>
                                                );
                                              })}
                                            </div>
                                          </div>

                                          {item.values[1].selected === true ? (
                                            <div className="row">
                                              <div
                                                className="col-12"
                                                style={{ padding: 0 }}
                                              >
                                                <>
                                                  {item.option.map((opt, k) => {
                                                    return (
                                                      <>
                                                        {opt.type ===
                                                          "textbox1" ? (
                                                          <div
                                                            className="textbox1 mb-4"
                                                            style={{
                                                              padding: "20px",
                                                              boxShadow:
                                                                "0px 0px 10px 4px #0000001f",
                                                            }}
                                                          >
                                                            <h4 className="text-center mt-4 mb-4">Proposed Additional Controls</h4>
                                                            {opt.option.map(
                                                              (opt1, l) => {
                                                                return (<>
                                                                  <div className="row mb-4 d-flex align-items-center">
                                                                    <div className="col-1 p-0">
                                                                      <span className="span-circle number"> {l + 1}</span>
                                                                    </div>
                                                                    <div className="col-3">
                                                                      <Select
                                                                        labelKey="label"
                                                                        id="user_description"
                                                                        onChange={(e) => changeTypeAdditionalControl(e, j, i, k, l)}

                                                                        options={[{ 'label': 'Elimination', 'value': 'Elimination' },
                                                                        { 'label': 'Substitution', 'value': 'Substitution' },
                                                                        { 'label': 'Engineering', 'value': 'Engineering' },
                                                                        { 'label': 'Administrative', 'value': 'Administrative' },
                                                                        { 'label': 'PPE', 'value': 'PPE' }]}
                                                                        placeholder="Choose Type.."

                                                                      />
                                                                    </div>
                                                                    <div className="col-7 p-0">
                                                                      <Form.Control
                                                                        type="text"
                                                                        onChange={(
                                                                          e
                                                                        ) =>
                                                                          onchangeBox1Text(
                                                                            e,
                                                                            j,
                                                                            i,
                                                                            k,
                                                                            l
                                                                          )
                                                                        }
                                                                        value={
                                                                          opt1.value
                                                                        }
                                                                        className="m-0"
                                                                      />
                                                                    </div>
                                                                    <div className="col-1 p-0">
                                                                      <i
                                                                        className="mdi mdi-delete span-circle delete"
                                                                        onClick={() =>
                                                                          onDeleteTextBox1(
                                                                            j,
                                                                            i,
                                                                            k,
                                                                            l
                                                                          )
                                                                        }
                                                                      ></i>
                                                                    </div>
                                                                  </div>
                                                                  <div className="row mb-4">
                                                                    <div className="col-1 p-0"></div>
                                                                    {opt1.option.map(
                                                                      (
                                                                        opt,
                                                                        m
                                                                      ) => {
                                                                        return (
                                                                          <>
                                                                            {opt.type ===
                                                                              "date" ? (
                                                                              <div className="col-5 " style={{ paddingRight: 0 }}>
                                                                                <label htmlFor="user_name">
                                                                                  {
                                                                                    opt.label
                                                                                  }
                                                                                </label>
                                                                                <DatePicker
                                                                                  selected={
                                                                                    opt.value
                                                                                  }
                                                                                  className="form-control"
                                                                                  dateFormat={
                                                                                    "yyyy-MM-dd"
                                                                                  }
                                                                                  onChange={(
                                                                                    e
                                                                                  ) =>
                                                                                    handleDateChange(
                                                                                      e,
                                                                                      j,
                                                                                      i,
                                                                                      k,
                                                                                      l,
                                                                                      m
                                                                                    )
                                                                                  } //
                                                                                  value={
                                                                                    opt.value
                                                                                  }
                                                                                />
                                                                              </div>
                                                                            ) : (
                                                                              <div className="col-5 " style={{ paddingLeft: 0 }}>
                                                                                <label htmlFor="user_name">
                                                                                  {
                                                                                    opt.label
                                                                                  }
                                                                                </label>
                                                                                {/* <Form.Control
                                                                                  type="text"
                                                                                  onChange={(
                                                                                    e
                                                                                  ) =>
                                                                                    onchangeBox2Text(
                                                                                      e,
                                                                                      j,
                                                                                      i,
                                                                                      k,
                                                                                      l,
                                                                                      m
                                                                                    )
                                                                                  }
                                                                                  value={
                                                                                    opt.value
                                                                                  }
                                                                                /> */}

                                                                                <Select
                                                                                  labelKey="label"
                                                                                  id="user_description"
                                                                                  onChange={(
                                                                                    e
                                                                                  ) =>
                                                                                    onchangeBox2Text(
                                                                                      e,
                                                                                      j,
                                                                                      i,
                                                                                      k,
                                                                                      l,
                                                                                      m
                                                                                    )
                                                                                  }
                                                                                  options={responsibility}
                                                                                  placeholder="Type..."
                                                                                />
                                                                              </div>
                                                                            )}
                                                                          </>
                                                                        );
                                                                      }
                                                                    )}
                                                                    <div className="col-1"></div>
                                                                  </div>
                                                                </>
                                                                );
                                                              }
                                                            )}
                                                            <div
                                                              className="form-group"
                                                              style={{
                                                                textAlign:
                                                                  "center",
                                                              }}
                                                            >
                                                              <button
                                                                className="btn btn-primary"
                                                                onClick={(
                                                                  e
                                                                ) =>
                                                                  onClickButton1(
                                                                    e,
                                                                    j,
                                                                    i,
                                                                    k

                                                                  )
                                                                }
                                                              >
                                                                <span className="mdi mdi-plus"></span>
                                                              </button>
                                                            </div>
                                                          </div>
                                                        ) : opt.type ===
                                                          "rpn" ? (
                                                          <div
                                                            className="row"
                                                            style={{
                                                              display: "flex",
                                                              justifyContent:
                                                                "space-between",
                                                              margin: 0,
                                                              paddingBottom: 10,
                                                              boxShadow:
                                                                "0px 0px 10px 4px #0000001f",
                                                            }}
                                                          >
                                                            <div
                                                              className="form-group mt-3 mb-3"
                                                              style={{
                                                                textAlign:
                                                                  "center",
                                                              }}
                                                            >
                                                              <p
                                                                htmlFor="user_name"
                                                                style={{
                                                                  fontSize: 20,
                                                                }}
                                                              >
                                                                RESIDUAL RISK
                                                                ASSESSMENT
                                                              </p>
                                                              <span
                                                                htmlFor="user_name"
                                                                style={{
                                                                  fontSize: 12,
                                                                }}
                                                              >
                                                                (Expected risk
                                                                based on the
                                                                implementation
                                                                of the
                                                                identified
                                                                additional
                                                                controls)
                                                              </span>
                                                            </div>

                                                            <div className="col-4">
                                                              <div
                                                                className="form-group mt-2 mb-1"
                                                                style={{
                                                                  textAlign:
                                                                    "center",
                                                                }}
                                                              >
                                                                <label
                                                                  className="d-flex justify-content-between"
                                                                  htmlFor="user_name"
                                                                  style={{
                                                                    fontSize: 20,
                                                                  }}
                                                                >
                                                                  Severity
                                                                  <div className="likelyhood_hover severity" >
                                                                    <i className="mdi mdi-information-outline"></i>
                                                                    <img s src={require('../../assets/images/severity.png')} alt="tet" />
                                                                  </div>
                                                                </label>
                                                                <p style={{ lineHeight: '18px' }}>   Degree of harm or
                                                                  impact that could
                                                                  result from a
                                                                  hazardous event or
                                                                  situation.</p>
                                                              </div>

                                                              {opt.severity.map(
                                                                (opt2, l) => {
                                                                  if (
                                                                    opt2.type ===
                                                                    "select1"
                                                                  ) {
                                                                    return (
                                                                      <div className="col-12">
                                                                        <div className="form-group">
                                                                          <Select
                                                                            labelKey="label"
                                                                            id="user_description"
                                                                            onChange={(
                                                                              e
                                                                            ) =>
                                                                              handleSelectRPNChange(
                                                                                e,
                                                                                j,
                                                                                i,
                                                                                k,
                                                                                l,
                                                                                opt2.label,
                                                                                "severity"
                                                                              )
                                                                            }
                                                                            options={
                                                                              opt2.value
                                                                            }
                                                                            placeholder="Choose ..."
                                                                          />
                                                                        </div>
                                                                      </div>
                                                                    );
                                                                  }
                                                                }
                                                              )}
                                                            </div>
                                                            <div className="col-4">
                                                              <div
                                                                className="form-group mt-2 mb-1"
                                                                style={{
                                                                  textAlign:
                                                                    "center",
                                                                }}
                                                              >
                                                                <label
                                                                  className="d-flex justify-content-between"
                                                                  htmlFor="user_name"
                                                                  style={{
                                                                    fontSize: 20,
                                                                  }}
                                                                >
                                                                  Likelihood
                                                                  <div className="likelyhood_hover" >
                                                                    <i className="mdi mdi-information-outline"></i>
                                                                    <img s src={require('../../assets/images/likelyhood.png')} alt="tet" />
                                                                  </div>

                                                                </label>
                                                                <p style={{ lineHeight: '18px' }}>   Degree of harm or
                                                                  impact that could
                                                                  result from a
                                                                  hazardous event or
                                                                  situation.</p>
                                                              </div>

                                                              {opt.likelyhood.map(
                                                                (opt2, l) => {
                                                                  if (
                                                                    opt2.type ===
                                                                    "select1"
                                                                  ) {
                                                                    return (
                                                                      <div className="col-12">
                                                                        <div className="form-group">
                                                                          <Select
                                                                            labelKey="label"
                                                                            id="user_description"
                                                                            onChange={(
                                                                              e
                                                                            ) =>
                                                                              handleSelectRPNChange(
                                                                                e,
                                                                                j,
                                                                                i,
                                                                                k,
                                                                                l,
                                                                                opt2.label,
                                                                                "likelyhood"
                                                                              )
                                                                            }
                                                                            options={
                                                                              opt2.value
                                                                            }
                                                                            placeholder="Choose ..."
                                                                          />
                                                                        </div>
                                                                      </div>
                                                                    );
                                                                  }
                                                                }
                                                              )}
                                                            </div>
                                                            <div className="col-4 ">
                                                              <div
                                                                className="form-group  mb-1"
                                                                style={{
                                                                  textAlign:
                                                                    "center",
                                                                }}
                                                              >
                                                                <label
                                                                  className="d-flex justify-content-between"
                                                                  htmlFor="user_name"
                                                                  style={{
                                                                    fontSize: 20,
                                                                  }}
                                                                >
                                                                  Residual Risk
                                                                  <div className="likelyhood_hover " >
                                                                    <i className="mdi mdi-information-outline"></i>
                                                                    <img s src={require('../../assets/images/color.jpeg')} alt="tet" />
                                                                  </div>
                                                                </label>
                                                                <p style={{ lineHeight: '18px', visibility: 'hidden' }}>   Degree of harm or
                                                                  impact that could
                                                                  result from a
                                                                  hazardous event or
                                                                  situation.</p>
                                                              </div>

                                                              <>
                                                                {opt.risk.map(
                                                                  (opt, k) => {
                                                                    return (
                                                                      <div className="col-12">
                                                                        <label
                                                                          className={`${opt.color ===
                                                                            ""
                                                                            ? ""
                                                                            : "black"
                                                                            }`}
                                                                          style={{
                                                                            padding: 10,
                                                                            background:
                                                                              opt.color,
                                                                            textAlign:
                                                                              "center",
                                                                            boxShadow:
                                                                              "0px 0px 13px 3px #efefef",

                                                                            borderRadius: 5,
                                                                            display:
                                                                              "flex",
                                                                            height: 38,
                                                                            justifyContent:
                                                                              "center",
                                                                            marginTop: 15
                                                                          }}
                                                                        >
                                                                          {opt.label} - {opt.color === '#8cc14b' ? 'Low' : opt.color === '#ffef00' ? 'Medium' : opt.color === '#ff1900' ? 'High' : ''}
                                                                        </label>
                                                                      </div>
                                                                    );
                                                                  }
                                                                )}
                                                              </>
                                                            </div>
                                                          </div>
                                                        ) : (
                                                          ""
                                                        )}
                                                      </>
                                                    );
                                                  })}
                                                </>
                                              </div>
                                            </div>
                                          ) : (
                                            ""
                                          )}
                                        </>
                                      );
                                    } else if (item.type === "hazards") {
                                      return (
                                        <Hazards item={item} i={i} j={j} onClickHazardIn={onClickHazard} full={item1} />
                                      )

                                    } else if (item.type === "initial") {
                                      return (
                                        <RA item={item} i={i} j={j} handleSelectChange1={handleSelectChange} />
                                      );
                                    }
                                  })}
                                </div>
                              </Accordion.Body>
                            </Accordion.Item>
                          );
                        })}
                      </div>
                    </Accordion>

                    <div className="col-12 text-center" style={{ padding: 20 }}>
                      <button
                        type="button"
                        className="btn btn-primary btn-rounded mb-3 "
                        onClick={(e) => {
                          e.preventDefault();
                          addTask();
                        }}
                      >
                        {" "}
                        Add Sub Activity
                      </button>
                    </div>
                  </div>
                </div>
                <div className="col-12" style={{
                  padding: 20,
                  boxShadow:
                    "rgba(0, 0, 0, 0.12) 0px 0px 10px 4px",
                }}>
                  <h5 style={{ textAlign: 'center' }} className="mt-2 mb-3 form-group required">Overall recommendation of the RA Team</h5>
                  <div className="mb-4 d-flex">
                    <div className="col-1 p-0">
                      <span className="span-circle number"> 1</span>
                    </div>
                    <div className="col-11 ">
                      <Select
                        labelKey="label"
                        id="user_description"
                        onChange={handleRecommendationChange}
                        options={[{ label: 'The overall risk level for this work activity is LOW. No further additional controls actions are necessary. Monitoring is required to ensure that the controls are maintained.', value: '0' },
                        { label: 'The overall risk level for this work activity is MEDIUM. Work can progress with close supervision and monitoring of current controls. Additional controls identified should be implemented within the defined period of time.', value: '1' },
                        { label: 'The overall risk level for this work activity is HIGH. Work should not be started or continued until the risk level has been reduced and risk numbers enters the LOW or MEDIUM zone.', value: '2' },]}
                        placeholder="Choose ..."
                      />
                    </div>
                  </div>
                  <div className="mb-4 d-flex">
                    <div className="col-1 p-0">
                      <span className="span-circle number">2</span>
                    </div>
                    <div className="col-11 ">
                      <Select
                        labelKey="label"
                        id="user_description"
                        onChange={handleAdditionalRecommendation}
                        options={[{ label: 'Since this is routine activity with low risk, no formal permit is recommended. However, a dynamic review is advised in case of changing circumstances.', value: '0' },
                        { label: 'A formal permit to work is required before work can commence. Duration will be specified by the permit approver.', value: '1' },
                        { label: 'A formal permit to work that is required. This is valid for a specific task and limited duration. The permit shall include the names of individuals involved in the work.', value: '2' },]}
                        placeholder="Choose ..."
                      />
                    </div>
                  </div>
                  <div className="mb-4 d-flex">
                    <div className="col-1 p-0">
                      <span className="span-circle number">3</span>
                    </div>
                    <div className="col-11 p-2" style={{ border: '1px solid hsl(0, 0%, 80%)' }}>
                      <p>Considering the hazards and risks associated with this work activity, the RA team requires the following high-risk permits to be approved and active when applying for permit for this specific activity.</p>


                      {haz.map(item => {
                        return (
                          <label className="col-3">
                            <input name={item.hazard_name} type="checkbox" class="option me-2" value='hotwotk ' onChange={(e) => checkHotWork(e, item)} /> {item.hazard_name}
                          </label>
                        )
                      })}
                      {/* <label className="me-4"><input name='options[]' type="checkbox" class="option  me-2" value='hotwotk ' /> Hot Work </label>
                      <label><input name='options[]' type="checkbox" class="option  me-2" value='wotkatheight ' /> Work at Height</label> */}



                    </div>
                  </div>
                  <div className="mb-4 d-flex">
                    <div className="col-1 p-0">
                      <span className="span-circle number">4</span>
                    </div>
                    <div className="col-11 ">
                      <textarea
                        className="form-control"
                        onChange={(e) => setThirdAdd(e.target.value)}

                        id="user_name"
                        placeholder={
                          "Additional recommendation"
                        }
                      ></textarea>
                    </div>
                  </div>
                </div>

                <div className="col-12 mt-4 mb-4" style={{
                  padding: 20,
                  boxShadow:
                    "rgba(0, 0, 0, 0.12) 0px 0px 10px 4px",
                }}>
                  <h5 style={{ textAlign: 'center' }} className="mt-2 mb-3">Team Leader Declaration</h5>
                  <p style={{ textAlign: 'center' }}>I affirm my position as the Team Leader for this Risk Assessment. The eventual outcome signifies our collective professional judgment, reached through consensus and utilizing our team's fullest capabilities.</p>


                  <div className="row mt-4">
                    <div className="col-12 text-center">
                      <SignatureCanvas
                        penColor="#1F3BB3"
                        canvasProps={{
                          width: 350,
                          height: 70,
                          className: "sigCanvas",
                          style: {
                            boxShadow: "0px 0px 10px 3px rgb(189 189 189)",
                          },
                        }}
                        ref={signRef}


                      />  <i className="fa fa-undo undo" onClick={() => signRef.current.clear()}></i>
                      <p> {user.firstName}</p>
                    </div>

                  </div>
                </div>

                {selectedMemberValue.length !== 0 ?
                  <TeamDeclaration data={selectSignMember} />
                  : ''}

                <div className="col-12 text-center" style={{ padding: 20 }}>

                  <button style={{ marginRight: 10 }}
                    type="button"
                    className="btn btn-primary btn-rounded mb-3 "
                    onClick={(e) => {
                      e.preventDefault();
                      draftUserHandler();
                    }}
                  >
                    {" "}
                    Save as Draft
                  </button>

                  <button
                    type="button"
                    className="btn btn-primary btn-rounded mb-3 "
                    onClick={(e) => {
                      e.preventDefault();
                      createUserHandler();
                    }}
                  >
                    {" "}
                    Release Draft Risk Assessment
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {isLoading && <FullLoader/>}
      <Modal
        show={mdShow}
        onHide={() => setMdShow(false)}
        aria-labelledby="example-modal-sizes-title-md"
      >
        <Modal.Body>
          <form className="forms">
            <div className="form-group">
              <label htmlFor="user_description">Choose ...</label>

              <Select
                labelKey="label"
                id="user_description"
                onChange={(e) => handleAssignChange(e)}
                options={crew}
                placeholder="Choose Assign..."
              />
            </div>
          </form>
        </Modal.Body>

        <Modal.Footer className="flex-wrap">

          <Button variant="light" onClick={() => setMdShow(false)}>
            Cancel
          </Button>
          <Button variant="primary" onClick={createUserHandler}>
            Assign
          </Button>

        </Modal.Footer>
      </Modal>

    </>
  );

};
export const Column = (props) => {
  return (
    <div className="border p-4 col-sm-6" style={{ background: "#f5f5f5" }}>
      <div>
        <h6 className="card-title">{props.column.tittle}</h6>
      </div>
      <Droppable droppableId={props.column.id}>
        {(provided) => (
          <div
            className="kanbanHeight"
            ref={provided.innerRef}
            {...provided.droppableProps}
          >
            {props.tasks.map((task, index) => (
              <Task key={task.id} task={task} index={index} />
            ))}
            {provided.placeholder}
          </div>
        )}
      </Droppable>
    </div>
  );
};
export const Task = (props) => {
  return (
    <Draggable draggableId={props.task.id} index={props.index}>
      {(provided) => (
        <div
          className="mt-1 board-portlet"
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          ref={provided.innerRef}
        >
          <div className="card-body p-3 bg-white">
            <div className="media">
              <div className="media-body">
                <div className="d-flex">
                  <img
                    src={props.task.imgURL}
                    alt="profile"
                    className="img-sm me-3"
                  />
                  <div>
                    <h6 className="mb-1">{props.task.name}</h6>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </Draggable>
  );


};
export default Routine;
