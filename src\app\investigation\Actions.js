import React, { useCallback, useEffect, useRef, useState } from 'react';
import API from '../services/API';
import { ACTION_URL, AIR_WITH_ID_URL, ACTION_OTT_SUBMIT, OTT_TASKS_WITH_ID, STATIC_URL, SU<PERSON>IT_INVERSTICATION_APPROVER, RA_ACTION_WITH_ID, GET_USER_ROLE_BY_MODE, ASSIGNED_ACTION_URL, OTT_WITH_ID, INCIDENT_WITH_ID, INVERSTIGATION_WITH_ID, INCIDENT_SUBMIT, INVERTIGATION_RETURNED, INVERTIGATION_APPROVED, NTCM_ACTION, PICM_ACTION } from '../constants';

import moment from 'moment';
import { ThemeProvider, createTheme } from "@mui/material";
import MaterialTable from "material-table";
import { FilterMatchMode, FilterOperator } from 'primereact/api';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { InputText } from 'primereact/inputtext';
import { MultiSelect } from 'primereact/multiselect';
import { Calendar } from 'primereact/calendar';
import { Dropdown } from 'primereact/dropdown';
import { Tag } from 'primereact/tag';
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";

import { Modal, Button, Form } from 'react-bootstrap'
import SignatureCanvas from 'react-signature-canvas'
import $ from "jquery";
// import S3 from "react-aws-s3";
import { useSelector } from 'react-redux';
import Swal from 'sweetalert2';
import ControlsFormModal from './Component/ControlsFormModal';
import ReviewIncident from './Component/ReviewIncident';
import TakeAction from './Component/Actions/TakeAction';



Buffer.from("anything", "base64");
window.Buffer = window.Buffer || require("buffer").Buffer;
window.jQuery = $;
// @ts-ignore
const customSwal = Swal.mixin({
    customClass: {
        confirmButton: 'swal2-confirm btn-secondary', // Use swal2-confirm to target confirm button
        cancelButton: 'swal2-cancel btn-light',      // Use swal2-cancel to target cancel button
    },

    buttonsStyling: true
})
const Action = ({ action, onFilterUpdate }) => {
    const user = useSelector((state) => state.login.user)
    console.log(user)
    const signRef = useRef()
    const [selectedReviewer, setSelectedReviewer] = useState('')
    const [actions, setActions] = useState([]);
    const [incidentData, setIncidentData] = useState({});
    const [modalState, setModalState] = useState({ type: null, isOpen: false, actionId: null });
    const [filters, setFilters] = useState({
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        'applicationDetails.maskId': { value: null, matchMode: FilterMatchMode.IN },
        timelineStatus: { value: null, matchMode: FilterMatchMode.IN },
        createdDate: { value: null, matchMode: FilterMatchMode.DATE_IS },
        'submittedBy.firstName': { value: null, matchMode: FilterMatchMode.IN },
        'transformedAction': { value: null, matchMode: FilterMatchMode.IN },
        'applicationDetails.dueDate': { value: null, matchMode: FilterMatchMode.DATE_IS },

    });
    const [startDate, setStartDate] = useState(null)
    const [endDate, setEndDate] = useState(null)
    const [Search, setSearch] = useState([])
    const [maskId, setMaskId] = useState([])
    const [names, setNames] = useState([])
    const [dates, setDates] = useState([])

    const [data, setData] = useState([])
    const [showModal, setShowModal] = useState(false)
    const [showReview, setShowReview] = useState(false)
    const [takeModal, setTakeModal] = useState(false)
    const [verifyModal, setVerifyModal] = useState(false)
    const [showItem, setShowItem] = useState([])
    const [applicationDetails, setApplicationDetails] = useState([])
    const [reviewerComment, setReviewerComment] = useState('')


    useEffect(() => {
        if (action) {
            getActions()
        }

    }, [action])

    const handleReviewerComment = (e) => {
        setReviewerComment(e)
    }
    const computeTimeline = (dueDate) => {
        if (!dueDate) return 'N/A';

        const due = moment(dueDate).startOf('day');
        const now = moment().startOf('day');

        if (due.isSame(now, 'day')) {
            return 'Due Soon';
        } else if (due.isBefore(now, 'day')) {
            return 'Overdue';
        } else {
            return 'Upcoming';
        }
    };
    const getActions = async () => {
        const transformedActions = action.map(a => ({
            ...a,
            transformedAction: getActionDisplayName(a.actionType),
            timelineStatus: computeTimeline(a.dueDate)// Add a transformed field
        }));

        setActions(transformedActions);
    };
    const timelineStatusColors = {
        "Overdue": "red",
        "Due Soon": "orange",
        "Upcoming": "green",
        "N/A": "gray"
    };

    const timelineItemTemplate = (option) => {
        const color = timelineStatusColors[option.value] || "gray";
        return (
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                <span
                    style={{
                        backgroundColor: color,
                        width: '12px',
                        height: '12px',
                        borderRadius: '50%'
                    }}
                />
                <span>{option.label}</span>
            </div>
        );
    };
    const timelineFilterTemplate = (options) => {
        const uniqueStatuses = [...new Set(actions.map(a => a.timelineStatus))];
        const statusOptions = uniqueStatuses.map(status => ({
            label: status,
            value: status
        }));

        return (
            <MultiSelect
                value={options.value}
                options={statusOptions}
                onChange={(e) => options.filterCallback(e.value)}
                placeholder="Select Timeline"
                display="chip"
                showClear
                itemTemplate={timelineItemTemplate}  // <---
            />
        );
    };


    const getActionDisplayName = (actionType) => {
        const actionMap = {
            "perform_task": "Take Action",
            "verify_task": "Verify Action",
            "reperform_task": "Retake Action",
            "verify_investigation": "Review and Approve Investigation Report",
            "conduct_investigation": "Conduct Investigation",
            "review_incident": "Review Incident"
        };
        return actionMap[actionType] || actionType; // Default to original if not found
    };

    const actionBodyTemplate = (row) => {
        return row.transformedAction; // Use pre-transformed value
    };

    const openActionCard = async (action) => {

        try {
            // Define the URI string for the first API call
            const uriString = {
                include: [
                    { relation: "locationOne" },
                    { relation: "locationTwo" },
                    { relation: "locationThree" },
                    { relation: "locationFour" },
                    { relation: "locationFive" },
                    { relation: "locationSix" },
                    { relation: "investigationRecords" },
                    { relation: "operation" },
                    { relation: "property" },
                    { relation: "environment" },
                    { relation: "personnel" },
                    { relation: "reviewer" },
                    { relation: "investigator" },
                ]
            };

            const url = `${INCIDENT_WITH_ID(action.applicationId)}?filter=${encodeURIComponent(
                JSON.stringify(uriString)
            )}`;

            if (action.actionType === 'review_incident') {
                // Trigger only one API call
                const response = await API.get(url);

                if (response.status === 200) {
                    setApplicationDetails(response.data);
                    setShowReview(true)
                }
            } else if (action.actionType === 'conduct_investigation' || action.actionType === 'approve_investigation' || action.actionType === 'reconduct_investigation') {
                // Execute both API calls concurrently using Promise.all
                const [response, response1] = await Promise.all([
                    API.get(url),
                    API.get(INVERSTIGATION_WITH_ID(action.objectId))
                ]);

                if (response.status === 200) {
                    setApplicationDetails(response.data);
                }

                if (response1.status === 200) {
                    setData(response1.data);
                }
                setShowModal(true);
            } else if ((action.actionType === 'perform_task' || action.actionType === 'reperform_task' || action.actionType === 'verify_task') && action.prefix === 'NTCM-TASK') {
                const response = await API.get(url);

                if (response.status === 200) {
                    setApplicationDetails(response.data);
                    setTakeModal(true);
                }

            } else if ((action.actionType === 'perform_task' || action.actionType === 'reperform_task' || action.actionType === 'verify_task') && action.prefix === 'PICM-TASK') {
                try {
                    const response = await API.get(INVERSTIGATION_WITH_ID(action.applicationId));

                    if (response.status === 200) {
                        setData(response.data);

                        const incidentUrl = `${INCIDENT_WITH_ID(response.data.incidentId)}?filter=${encodeURIComponent(
                            JSON.stringify(uriString)
                        )}`;
                        const incidentResponse = await API.get(incidentUrl);

                        setApplicationDetails(incidentResponse.data);
                        setTakeModal(true);
                    }
                } catch (error) {
                    console.error("Error fetching task details:", error);
                    // Optional: Handle error, e.g., display a notification or set an error state
                }
            }

            // Set the action and show the modal
            setShowItem(action);


        } catch (error) {
            console.error("Error in openActionCard:", error);
            // Optionally handle errors or show an error message to the user
        }
    };


    const renderHeader = () => {
        const value = filters['global'] ? filters['global'].value : '';

        return (
            <div className='d-flex justify-content-end'>

                {/* <span className="p-input-icon-left">
                    <i className="fa fa-search" />
                    <InputText type="search" value={value || ''} onChange={(e) => onGlobalFilterChange(e)} placeholder="Global Search" />
                </span> */}
            </div>
        );
    };

    const header = renderHeader();




    const representativesItemTemplate = (option) => {
        return (
            <div className="flex align-items-center gap-2">

                <span>{option.value}</span>
            </div>
        );
    };
    const nameFilterTemplate = (options) => {
        console.log(options)
        return (
            <React.Fragment>
                <div className="mb-3 font-bold">Name</div>
                <MultiSelect value={options.value} options={names} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }


    const idBodyTemplate = (row) => {
        return <div className='maskid' onClick={() => openActionCard(row)}>{row.maskId}</div>;
    }

    // const actionBodyTemplate = (row) => {
    //     const actionMap = {
    //         "perform_task": "Take Action",
    //         "verify_task": "Verify Action",
    //         "reperform_task": "Retake Action",
    //         "verify_investigation": "Review and Approve Investigation Report",
    //     };
    //     return actionMap[row.actionType] || row.actionToBeTaken;
    // };

    const actionMultiFilterTemplate = (options) => {
        // Extract unique transformed action names
        const uniqueActions = [...new Set(actions.map(a => a.transformedAction).filter(Boolean))];

        return (
            <MultiSelect
                value={options.value}
                options={uniqueActions.map(action => ({ label: action, value: action }))}
                onChange={(e) => options.filterCallback(e.value)}
                placeholder="Select Actions"
                display="chip"
                showClear
            />
        );
    };


    const handleSelectChange = (e) => {
        setSelectedReviewer(e.value)
    }


    const dueBodyTemplate = (row) => {
        return row.dueDate
            ? moment(row.dueDate).format('DD-MM-YYYY')
            : 'N/A';
    };

    const statusBodyTemplate = (value) => {
        return value.applicationDetails.status
    }

    const submitIncidentReport = (data) => {
        customSwal.fire({
            title: "Are you sure you want to conclude the review and submit your inputs?",
            html: `<p>You won't be able to revert this!</p>
                   <div style="text-align: left;">
                       <p><strong>Note:</strong></p>
                       <p>1. This will automatically trigger all the near-term actions to respective action owners.</p>
                       <p>2. You will not be able to make further edits. However, any incident investigator (if assigned) will be able to update any of these sections based on the investigation process.</p>
                   </div>`,
            icon: "warning",
            showCancelButton: true,
            confirmButtonText: "Yes",
            cancelButtonText: "Cancel",
            confirmButtonColor: "#28a745", // This might work in most cases, but let's ensure using customClass
            cancelButtonColor: "#d33",
            customClass: {
                confirmButton: 'custom-confirm-button', // Add a custom class
            }
        }).then(async (result) => {
            if (result.isConfirmed) {
                try {
                    const response = await API.post(INCIDENT_SUBMIT(showItem.id), data);
                    if (response.status === 200) {
                        setShowReview(false);

                        Swal.fire({
                            title: "Submitted!",
                            icon: "success",
                            confirmButtonColor: "#28a745", // Ensure consistency
                            customClass: {
                                confirmButton: 'custom-confirm-button', // Apply the same custom class
                            }
                        });
                    }
                } catch (error) {
                    console.error("Submission failed:", error);
                }
            }
        });


    }

    const submitToApprover = async (leadAssign) => {
        try {
            const response = await API.patch(SUBMIT_INVERSTICATION_APPROVER(showItem.id), { investigationApproverId: leadAssign });

            if (response.status === 204) {
                Swal.fire({
                    icon: 'success',
                    title: 'Submitted!',
                    text: 'The recommendation has been submitted to the approver.',

                });

                setShowModal(false)
                getActions();
            }
        } catch (error) {
            console.error('Error submitting to approver:', error);
            Swal.fire({
                icon: 'error',
                title: 'Submission failed',
                text: 'An error occurred while submitting the recommendation.'
            });
        }
    }

    const returnToInvestigator = async (approveComments) => {
        try {
            const response = await API.patch(INVERTIGATION_RETURNED(showItem.id), {
                investigationApproverRemarks: approveComments,
                status: 'Returned'
            });

            if (response.status === 204) {
                Swal.fire({
                    icon: 'success',
                    title: 'Returned to Investigator',
                    text: 'The report has been successfully returned to the investigator.',

                });

                setShowModal(false)
                getActions();
            }
        } catch (error) {
            console.error('Error submitting to approver:', error);
            Swal.fire({
                icon: 'error',
                title: 'Submission failed',
                text: 'An error occurred while submitting the recommendation.'
            });
        }
    }

    const approveReport = async (data, comments) => {
        console.log(data)

        try {
            const response = await API.post(INVERTIGATION_APPROVED(showItem.id),
                { "recommendations": data, "approverComments": comments });

            if (response.status === 200) {
                Swal.fire({
                    icon: 'success',
                    title: 'Approved',
                    text: 'The report has been successfully Approved and post action triggered .',

                });

                setShowModal(false)
                getActions();
            }
        } catch (error) {
            console.error('Error submitting to approver:', error);
            Swal.fire({
                icon: 'error',
                title: 'Submission failed',
                text: 'An error occurred while submitting the recommendation.'
            });
        }
    }

    const reportToApprove = async ({ reviewerId, ...actionRequestData }) => {
        try {
            const endpoint = showItem.prefix === 'PICM-TASK' ? PICM_ACTION(showItem.id) : NTCM_ACTION(showItem.id);
            const payloadKey = showItem.prefix === 'PICM-TASK' ? 'investigationRecommendation' : 'nearTermControlMeasures';

            const response = await API.patch(endpoint, {
                actionRequestData,                          // All fields except reviewerId
                [payloadKey]: { reviewerId }                // Dynamically set the key based on prefix
            });

            if (response.status === 204) {
                Swal.fire({
                    icon: 'success',
                    title: 'Sent to Approver',
                    text: 'The report has been sent to the approver successfully.'
                });

                setTakeModal(false);
                getActions();
            }
        } catch (error) {
            console.error('Error submitting to approver:', error);
            Swal.fire({
                icon: 'error',
                title: 'Submission Failed',
                text: 'An error occurred while submitting the report to the approver.'
            });
        }
    };

    const submitActionApprove = async ({ actionType, actionComments }) => {
        try {
            const endpoint = showItem.prefix === 'PICM-TASK' ? PICM_ACTION(showItem.id) : NTCM_ACTION(showItem.id);
            const payloadKey = showItem.prefix === 'PICM-TASK' ? 'investigationRecommendation' : 'nearTermControlMeasures';

            const response = await API.patch(endpoint, {
                [payloadKey]: {                             // Dynamically set the key based on prefix
                    reviewerComments: actionComments,
                    status: actionType
                }
            });

            if (response.status === 204) {
                Swal.fire({
                    icon: 'success',
                    title: actionType === 'Returned' ? 'Returned to Assignee' : 'Action Verified',
                    text: actionType === 'Returned'
                        ? 'The action has been returned to the assignee.'
                        : 'The action has been verified successfully.'
                });

                setTakeModal(false);
                getActions();
            }
        } catch (error) {
            console.error(`Error on ${actionType === 'Returned' ? 'returning' : 'approving'} action:`, error);
            Swal.fire({
                icon: 'error',
                title: 'Submission Failed',
                text: `An error occurred while trying to ${actionType === 'Returned' ? 'return' : 'approve'} the action.`
            });
        }
    };

    const submittedByOptions = [
        ...new Set(actions?.map(item => item.submittedBy?.firstName).filter(Boolean))
    ].map(name => ({ label: name, value: name }));



    const submittedByFilterTemplate = (options) => {
        return (
            <MultiSelect
                value={options.value}                 // the current array of selected names
                options={submittedByOptions}          // your unique name list
                onChange={(e) => options.filterCallback(e.value)}
                placeholder="Select SubmittedBy"
                display="chip"                        // optional: shows selected items as chips
                className="p-column-filter"
                style={{ minWidth: '12rem' }}
            />
        );
    };


    const getTimelineStatus = (row) => {
        if (!row.dueDate) return 'N/A';

        const dueDate = moment(row.dueDate).startOf('day');
        const currentDate = moment().startOf('day');

        if (dueDate.isSame(currentDate, 'day')) {
            return 'Due Soon';
        } else if (dueDate.isBefore(currentDate, 'day')) {
            return 'Overdue';
        } else {
            return 'Upcoming';
        }
    };

    const timelineBodyTemplate = (row) => {
        const statusColors = {
            "Overdue": "red",
            "Due Soon": "orange",
            "Upcoming": "green",
            "N/A": "gray"
        };

        return (
            <Tag
                value={row.timelineStatus}
                style={{
                    backgroundColor: statusColors[row.timelineStatus] || 'gray',
                    color: 'white',
                    fontWeight: 'bold'
                }}
            />
        );
    };



    return (

        <>

            <DataTable value={actions} paginator rows={10} header={header} filters={filters}
                emptyMessage="No Data found." tableStyle={{ minWidth: '50rem' }}>

                <Column
                    field="timelineStatus"
                    header="Timeline"
                    body={timelineBodyTemplate}  // your Tag with color
                    filter
                    showFilterMatchModes={false}
                    filterMatchMode="in"
                    filterElement={timelineFilterTemplate}
                />

                <Column field="maskId" header=" ID" body={idBodyTemplate} sortable ></Column>

                <Column
                    field="transformedAction"  // Changed from actionToBeTaken
                    header="Required Action"
                    body={actionBodyTemplate}
                    filter
                    showFilterMatchModes={false}
                    filterMatchMode="in"  // Enables multi-value filtering
                    filterElement={actionMultiFilterTemplate}
                ></Column>



                {/* <Column field="submittedBy.firstName" header="SubmittedBy" sortable ></Column> */}

                <Column
                    field="submittedBy.firstName"
                    header="SubmittedBy"
                    filter
                    showFilterMatchModes={false}
                    filterMatchMode="in"
                    filterElement={submittedByFilterTemplate}
                />

                <Column
                    field="dueDate"  // match the new top-level field
                    header="Due Date"
                    body={dueBodyTemplate}
                    sortable
                />




            </DataTable>
            {showReview &&
                <ReviewIncident show={showReview} data={applicationDetails} submitReport={submitIncidentReport} handleClose={() => setShowReview(false)} />
            }
            {showModal &&
                <ControlsFormModal type={showItem} show={showModal} data={data} applicationDetails={applicationDetails} handleClose={() => setShowModal(false)} fetchLetest={() => openActionCard(showItem)} sendToApprover={submitToApprover} returnToInvestigator={returnToInvestigator} approveReport={approveReport} />
            }
            {takeModal &&
                <TakeAction type={showItem} show={takeModal} data={data} applicationDetails={applicationDetails} handleClose={() => setTakeModal(false)} reportToApprove={reportToApprove} submitActionType={submitActionApprove} />
            }
        </>
    );


}

export default Action;
