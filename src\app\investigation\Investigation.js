import React, { useEffect, useState, useCallback, useRef } from 'react'
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";
// import 'primereact/resources/themes/lara-light-indigo/theme.css';
import 'primeicons/primeicons.css';
import { DropzoneArea } from 'material-ui-dropzone';
import { Dialog } from 'primereact/dialog';
import { TabView, TabPanel } from 'primereact/tabview';
import { Stepper, Step, StepLabel, Typography } from '@mui/material';
import { Calendar } from 'primereact/calendar';
import Accordion from 'react-bootstrap/Accordion';
import { useAccordionButton } from 'react-bootstrap/AccordionButton';
import AccordionContext from 'react-bootstrap/AccordionContext';
import { RadioButton } from 'primereact/radiobutton';
import { Dropdown } from 'primereact/dropdown';
import { Button } from 'primereact/button';
import { MultiSelect } from 'primereact/multiselect';
import { InputText } from 'primereact/inputtext'
import { Checkbox } from 'primereact/checkbox';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { FilterMatchMode, FilterOperator } from 'primereact/api';
import { InputTextarea } from 'primereact/inputtextarea';
import { Form } from 'react-bootstrap';
import Select from 'react-select';
import { GMS1_URL, INCIDENT, GET_USER_ROLE_BY_MODE, HAZARDS_CATEGOTY, GET_ALL_USER, DROPDOWNS, WORK_ACTIVITIES_URL, FILE_URL, INCIDENT_WITH_ID, INVERSTIGATION_TRIGGER, ADMINDROPDOWNS } from '../constants';
import API from '../services/API';
import AllFilterLocation from './LocationDropDown';
import ImageComponent from '../services/FileDownlodS3';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import Swal from 'sweetalert2';
import { Modal, Nav } from 'react-bootstrap';
import moment from 'moment';
import InvestigationModal from './Component/InvestigationModal';
import ViewRecordModal from './Component/ViewRecordModal';
import JoditEditor from 'jodit-react';
import ActionTable from './Component/Actions/ActionTable';
import 'jodit/build/jodit.min.css'; // Import Jodit CSS
import ViewInvestigateModal from './Component/ViewInvesticateModal';

const customSwal2 = Swal.mixin({
    customClass: {
        confirmButton: 'btn btn-primary',

    },
    buttonsStyling: false
})

const initialFormData = {
    "maskId": "",
    "description": "",
    "images": [],
    "title": "",
    "witnessInvolved": "",
    "isCustomLocation": false,
    "customLocation": "",
    "consequences": {
        "comments": "",
        "consequenceItems": [
            {
                "impactOn": "",
                "description": "",
                "consequenceDescription": [

                ]
            }
        ],
        "status": ""
    },
    "precursors": {
        // "whatLedToTheIncident": "",
        // "unusalWorkingCondition": "",
        "workPerformed": "",
        "tasksProcedures": "",
        "rolesAwareness": "",
        "shortcutsDeviations": "",
        "recentChanges": "",
        "deadlinePressure": "",
        "distractionsInterruptions": "",
        "unusualConditions": "",
        "equipmentUsed": "",
        "equipmentMalfunction": "",
        "recentMaintenance": "",
        "similarIncidents": "",
        "status": ""
    },
    "immediateCause": {
        "initialActions": "",
        "medicalAttentionProvided": "",
        "emergencyServicesCalled": "",
        "areaSecured": "",
        "safetyControlsActivated": "",
        "evacuationInitiated": "",
        "hazardousMaterialsHandled": "",
        "communicationWithAuthorities": "",
        "status": ''
    },
    "hazards": [

    ],
    "hazardousSituationOfTheIncident": "",
    "hazardStatus": "",
    "nearTermControlMeasureStatus": "",
    "preventiveControls": {
        "identifiedPreventiveControls": [
            {
                "order": 0,
                "controlStatement": "",
                "isControlImplemented": false,
                "isEffective": false
            }
        ],
        "unIdentifiedPreventiveControls": [
            {
                "order": 0,
                "controlStatement": "",
                "isControlImplemented": false,
                "isEffective": false
            }
        ],
        "controlIdentified": false,
        "controlNotIdentified": false,
        "wasHazardControllable": false,
        "explainHazardControllable": '',
        "status": ""
    },
    "mitigativeControls": {
        "identifiedMitigativeControls": [
            {
                "order": 0,
                "controlStatement": "",
                "isControlImplemented": false,
                "isEffective": false
            }
        ],
        "unIdentifiedMitigativeControls": [
            {
                "order": 0,
                "controlStatement": "",
                "isControlImplemented": false,
                "isEffective": false
            }
        ],
        "controlIdentified": false,
        "controlNotIdentified": false,
        "wasHazardControllable": false,
        "explainHazardControllable": '',
        "status": ""
    },
    "incidentDate": "",
    "incidentStatus": 'incompleted',
    "status": "",
    "locationOneId": "",
    "locationTwoId": "",
    "locationThreeId": "",
    "locationFourId": "",
    "locationFiveId": "",
    "locationSixId": "",
    "incidentType": "",
    "incidentCircumstance": "",
    "incidentCategory": "",
    "surfaceType": "",
    "surfaceCondition": "",
    "pathways": "",
    "lighting": "",
    "weatherCondition": "",
    "reportedById": "",
    "reviewerId": "",
    "personnelId": "",
    "environmentId": "",
    "propertyId": "",
    "operationId": "",
    "personnelInvolved": "",
    "immediateActionsTaken": "",
}
function Investigation({ data, onFilterUpdate }) {
    const editor = useRef(null);
    const [activeTabIndex, setActiveTabIndex] = useState(0);
    const [lead, setLead] = useState([])
    const [leadShow, setLeadShow] = useState(false)
    const [attachShow, setAttachShow] = useState(false)
    const [show, setShow] = useState(false);
    const [viewShow, setViewShow] = useState(false)
    const [risk, setRisk] = useState([])
    const [hazards, setHazards] = useState([])
    const [reviewer, setReviewer] = useState([])
    const [formData, setFormData] = useState(initialFormData);
    const [record, setRecord] = useState([])
    const [actionModal, setActionModal] = useState(false);
    const [current, setCurrent] = useState(null);
    const [maskId, setMaskId] = useState('');
    const [totalAction, setTotalAction] = useState([]);
    const [filters, setFilters] = useState({
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        'project.name': { value: null, matchMode: FilterMatchMode.IN },
        'category.name': { value: null, matchMode: FilterMatchMode.IN },
        priority: { value: null, matchMode: FilterMatchMode.IN },
        'assignee.firstName': { value: null, matchMode: FilterMatchMode.IN },
        nextdate: { value: null, matchMode: FilterMatchMode.IN },
        'type.label': { value: null, matchMode: FilterMatchMode.IN },
        status: { value: null, matchMode: FilterMatchMode.IN },
        captain: { value: null, matchMode: FilterMatchMode.IN },
        department: { value: null, matchMode: FilterMatchMode.IN },
        'investigator.firstName': { value: null, matchMode: FilterMatchMode.IN },
        // For Investigation Approver
        'investigationApprover.firstName': { value: null, matchMode: FilterMatchMode.IN },
    });


    useEffect(() => {
        if (data) {
            getAllIncident();
        }


    }, [data])



    const getAllIncident = async () => {
        setRisk(data)
    };

    const openViewModal = (row) => {
        setRecord(row)
        setViewShow(true)
    }


    const maskBodyTemplate = (row) => {
        return <div className='maskid' onClick={() => openViewModal(row)}>{row.incident.maskId}</div>
    }


    const dateBodyTemplate = (row) => {
        return moment(row.incident.incidentDate).format('DD-MM-YYYY')
    }

    const renderHeader = () => {
        const value = filters['global'] ? filters['global'].value : '';

        return (
            <div className='d-flex justify-content-end'>

                {/* <Button className="btn btn-primary  mb-3 " onClick={() => { setFormData(initialFormData); setShow(true) }}> Add Incident</Button> */}

            </div>
        );
    };
    const header = renderHeader()

    const locationBodyTemplate = (row) => {
        if (row.incident.isCustomLocation) {
            return row.incident.customLocation;
        } else {
            return (
                <>

                    {row.incident?.locationOne?.name && (
                        <>
                            {row.incident.locationOne.name}
                            {row.incident.locationTwo?.name && ' > '}
                        </>
                    )}
                    {row.incident?.locationTwo?.name && (
                        <>
                            {row.incident.locationTwo.name}
                            {row.incident.locationThree?.name && ' > '}
                        </>
                    )}
                    {row.incident?.locationThree?.name && (
                        <>
                            {row.incident.locationThree.name}
                            {row.incident.locationFour?.name && ' > '}
                        </>
                    )}
                    {row.incident?.locationFour?.name && row.incident.locationFour.name}
                </>
            );
        }
    };
    function groupByDescription(data) {

        const filterData = data.filter(item =>
            item.actionType !== 'review_incident' &&
            item.actionType !== 'conduct_investigation' &&
            item.actionType !== 'approve_investigation'
        );


        const groupedData = [];
        const descriptionMap = {};

        filterData.forEach(item => {
            const { objectId, description, actionType, assignedToId, status, trackId } = item;
            if (!descriptionMap[trackId]) {
                descriptionMap[trackId] = {
                    objectId: objectId,
                    firstActionType: actionType,
                    lastActionType: actionType,
                    actionTypes: [actionType],
                    lastAssignedToId: assignedToId,
                    lastStatus: status,
                    data: []
                };
            } else {
                descriptionMap[trackId].lastActionType = actionType;
                descriptionMap[trackId].actionTypes.push(actionType);
                descriptionMap[trackId].lastAssignedToId = assignedToId;
                descriptionMap[trackId].lastStatus = status;

            }
            descriptionMap[trackId].data.push(item);
        });

        // Update lastActionType, lastAssignedToId, and lastStatus in each group
        for (const description in descriptionMap) {
            const group = descriptionMap[description];
            const lastDataObject = group.data[group.data.length - 1];
            group.lastActionType = lastDataObject.actionType;
            group.lastAssignedToId = lastDataObject.assignedToId;
            group.lastStatus = lastDataObject.status;
            groupedData.push(group);
        }

        return groupedData;
    }
    const nearBodyTemplate = (rowData) => {
        const totalActionData = groupByDescription(rowData.totalActions)

        console.log(totalActionData)
        console.log(rowData.maskId)

        const totalCompleted = totalActionData.filter(item => item.lastActionType === 'verify_task' && item.lastStatus === 'Completed')

        const color = totalActionData.length === totalCompleted.length ? 'greenBox' : totalCompleted.length === 0 ? 'redBox' : 'orangeBox';




        // Return the link with dynamic styles and counts
        return <a href="#" onClick={(e) => { e.preventDefault(); handleActionLog(rowData, totalActionData) }} className={color} > {totalCompleted.length} / {totalActionData.length}</a>;
    }

    const handleActionLog = (data, actions) => {

        console.log(actions)
        setActionModal(true)
        setCurrent(data)

        setMaskId(data.incident.maskId)
        setTotalAction(actions)
    }
    // Gather unique investigators
    const investigatorOptions = [
        ...new Set(risk?.map(item => item.investigator?.firstName).filter(Boolean))
    ].map(name => ({ label: name, value: name }));

    // Gather unique investigation approvers
    const approverOptions = [
        ...new Set(risk?.map(item => item.investigationApprover?.firstName).filter(Boolean))
    ].map(name => ({ label: name, value: name }));

    const investigatorFilterTemplate = (options) => {
        return (
            <MultiSelect
                value={options.value}
                options={investigatorOptions}
                onChange={(e) => options.filterCallback(e.value)}
                placeholder="Select Investigator(s)"
                display="chip"
                style={{ minWidth: '12rem' }}
                className="p-column-filter"
            />
        );
    };

    const approverFilterTemplate = (options) => {
        return (
            <MultiSelect
                value={options.value}
                options={approverOptions}
                onChange={(e) => options.filterCallback(e.value)}
                placeholder="Select Approver(s)"
                display="chip"
                style={{ minWidth: '12rem' }}
                className="p-column-filter"
            />
        );
    };
    return (
        <>
            <div>
                <div className="row  ">
                    <div className="col-12">



                        <div className="card">
                            <div className="card-body p-0">
                                <div className="row">
                                    <div className="col-12">
                                        <div>
                                            <>
                                                <DataTable value={risk} paginator rows={10} header={header} filters={filters} onFilter={(e) => setFilters(e.filters)} paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                                                    rowsPerPageOptions={[10, 25, 50]}
                                                    emptyMessage="No Data found." tableStyle={{ minWidth: '50rem' }}>
                                                    <Column field="incident.maskId" header="Incident ID" body={maskBodyTemplate} sortable ></Column>
                                                    {/* <Column field="status" header="Status" ></Column> */}
                                                    <Column field="incident.title" header="Incident Title" sortable ></Column>
                                                    <Column field="" header="Severity" sortable ></Column>
                                                    <Column field="incident.incidentDate" header="Date of Incident" body={dateBodyTemplate} sortable dataType="date"    ></Column>
                                                    <Column field="" header="Location" body={locationBodyTemplate} sortable ></Column>
                                                    <Column
                                                        field="investigator.firstName"
                                                        header="Investigator"
                                                        showFilterMatchModes={false}
                                                        filter
                                                        filterMatchMode="in"
                                                        filterElement={investigatorFilterTemplate}
                                                    />

                                                    {/* Investigation Approver filter */}
                                                    <Column
                                                        field="investigationApprover.firstName"
                                                        header="Investigation Approver"
                                                        showFilterMatchModes={false}
                                                        filter
                                                        filterMatchMode="in"
                                                        filterElement={approverFilterTemplate}
                                                    />
                                                    {/* <Column field="" header="Near-term Control Measure Status" body={nearBodyTemplate}  ></Column> */}
                                                    <Column field="" header="Post-investigation Control Measure Status " body={nearBodyTemplate}  ></Column>
                                                    {/* <Column header="Action" body={actionBodyTemplate} ></Column> */}
                                                </DataTable>
                                            </>

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>

            {viewShow &&
                <ViewInvestigateModal
                isVisible={viewShow}
                onHide={() => setViewShow(false)}
                record={record}
                />
            }

            {actionModal &&
                <Modal
                    show={actionModal}
                    size="lg"
                    onHide={() => setActionModal(false)}
                    aria-labelledby="example-modal-sizes-title-md"
                >
                    <Modal.Body>

                        <ActionTable id={maskId} actions={totalAction} current={current} />
                    </Modal.Body>
                    <Modal.Footer>
                        <Button
                            variant="light"
                            onClick={() => {
                                setActionModal(false);
                            }}
                        >
                            Close
                        </Button>
                    </Modal.Footer>
                </Modal>
            }

        </>
    )
}

export default Investigation