import React from 'react';
import { Container, Row, Col, Card } from 'react-bootstrap';
import moment from 'moment';
import { useSelector } from 'react-redux';
import pdfMake from 'pdfmake/build/pdfmake';
import pdfFonts from 'pdfmake/build/vfs_fonts';

pdfMake.vfs = pdfFonts.pdfMake.vfs;

const PDFExport = ({ applicationDetails }) => {
    const groupedControls = applicationDetails.permitRiskControl?.reduce((acc, control, index) => {
        (acc[control.permitType] = acc[control.permitType] || []).push({ ...control, controlIndex: index });
        return acc;
    }, {}) || {};

    const statusData = [
        {
            role: 'Applicant',
            name: applicationDetails.applicant?.firstName || "N/A",
            roleName: `Applicant - ${applicationDetails.applicant?.firstName || "N/A"}`,
            signature: applicationDetails.applicantStatus?.signature || "N/A",
            status: applicationDetails.applicantStatus?.status ? "Approved" : "Pending",
            signedDate: applicationDetails.applicantStatus?.signedDate || "N/A",
            comments: applicationDetails.applicantStatus?.comments || "N/A",
            declaration: "I confirm that all required fields are accurately completed, and I acknowledge responsibility for adhering to the specified safety controls for this work activity."
        },
        applicationDetails.reviewerId && {
            role: 'Reviewer',
            name: applicationDetails.reviewer?.firstName || "N/A",
            roleName: `Reviewer - ${applicationDetails.reviewer?.firstName || "N/A"}`,
            signature: applicationDetails.reviewerStatus?.signature || "N/A",
            status: applicationDetails.reviewerStatus?.status ? "Approved" : "Pending",
            signedDate: applicationDetails.reviewerStatus?.signedDate || "N/A",
            comments: applicationDetails.reviewerStatus?.comments || "N/A",
            declaration: "I have reviewed the application details and verify that the listed controls and prerequisites are suitable and sufficient for safe task execution."
        },
        applicationDetails.assessorId && {
            role: 'Assessor',
            name: applicationDetails.assessor?.firstName || "N/A",
            roleName: `Assessor - ${applicationDetails.assessor?.firstName || "N/A"}`,
            signature: applicationDetails.assessorStatus?.signature || "N/A",
            status: applicationDetails.assessorStatus?.status ? "Approved" : "Pending",
            signedDate: applicationDetails.assessorStatus?.signedDate || "N/A",
            comments: applicationDetails.assessorStatus?.comments || "N/A",
            declaration: "I affirm that I have carefully assessed the risk levels, controls, and work conditions and that all necessary precautions are documented."
        },
        applicationDetails.approverId && {
            role: 'Approver',
            name: applicationDetails.approver?.firstName || "N/A",
            roleName: `Approver - ${applicationDetails.approver?.firstName || "N/A"}`,
            signature: applicationDetails.approverStatus?.signature || "N/A",
            status: applicationDetails.approverStatus?.status ? "Approved" : "Pending",
            signedDate: applicationDetails.approverStatus?.signedDate || "N/A",
            comments: applicationDetails.approverStatus?.comments || "N/A",
            declaration: "I approve this permit with the assurance that all safety measures and controls have been verified and are in place to safely conduct this work."
        }
    ].filter(Boolean);

    const generatePdf = () => {
        const docDefinition = {
            content: [
                { text: 'AcuiZen Work Hub', style: 'title', alignment: 'center' },
                { text: `Permit To Work`, style: 'header', alignment: 'center', margin: [0, 10, 0, 20] },

                {
                    style: 'infoTable',
                    table: {
                        widths: ['25%', '25%', '25%', '25%'],
                        body: [
                            [
                                { text: 'Work Type', style: 'tableHeader' },
                                { text: applicationDetails.permitWorkType || 'N/A', style: 'tableCell' },
                                { text: 'Start Date', style: 'tableHeader' },
                                { text: applicationDetails.permitStartDate ? moment(applicationDetails.permitStartDate).format('DD-MM-YYYY hh:mm A') : 'N/A', style: 'tableCell' }
                            ],
                            [
                                { text: 'Work Description', style: 'tableHeader' },
                                { text: applicationDetails.workDescription || 'N/A', style: 'tableCell', colSpan: 3 }, '', ''
                            ],
                            [
                                { text: 'End Date', style: 'tableHeader' },
                                { text: applicationDetails.permitEndDate ? moment(applicationDetails.permitEndDate).format('DD-MM-YYYY hh:mm A') : 'N/A', style: 'tableCell' },
                                { text: 'No. of Workers', style: 'tableHeader' },
                                { text: applicationDetails.noOfWorkers || 'N/A', style: 'tableCell' }
                            ],
                            [
                                { text: 'Supervisor Contact No.', style: 'tableHeader' },
                                { text: applicationDetails.supervisorContactNo || 'N/A', style: 'tableCell' },
                                { text: 'Applicant Contact No.', style: 'tableHeader' },
                                { text: applicationDetails.applicantContactNo || 'N/A', style: 'tableCell' }
                            ],
                            [
                                { text: 'Location', style: 'tableHeader' },
                                { text: (applicationDetails.locationOne ? applicationDetails.locationOne.name : 'N/A') || 'N/A', style: 'tableCell', colSpan: 3 }, '', ''
                            ]
                        ]
                    },
                    layout: 'lightHorizontalLines',
                    margin: [0, 0, 0, 20]
                },

                { text: 'Work at Height Permit (MEWP)', style: 'sectionHeader', margin: [0, 0, 0, 10] },
                ...Object.entries(groupedControls).map(([permitType, controls]) => ({
                    style: 'infoTable',
                    table: {
                        widths: ['5%', '50%', '20%', '15%', '10%'],
                        body: [
                            [
                                { text: '#', style: 'tableHeader' },
                                { text: 'Description', style: 'tableHeader' },
                                { text: 'Type', style: 'tableHeader' },
                                { text: 'Value', style: 'tableHeader' },
                                { text: 'Remarks', style: 'tableHeader' }
                            ],
                            ...controls.map((control, index) => [
                                { text: index + 1, style: 'tableCell' },
                                { text: control.description || 'N/A', style: 'tableCell' },
                                { text: control.currentType || 'N/A', style: 'tableCell' },
                                { text: control.value || 'N/A', style: 'tableCell' },
                                { text: control.remarks || 'N/A', style: 'tableCell' }
                            ])
                        ]
                    },
                    layout: 'lightHorizontalLines',
                    margin: [0, 0, 0, 20]
                })),

                { text: 'Status Details', style: 'sectionHeader', margin: [0, 0, 0, 10] },
                {
                    style: 'infoTable',
                    table: {
                        widths: ['20%', '40%', '20%', '20%'],
                        body: [
                            [
                                { text: 'Role/Name', style: 'tableHeader' },
                                { text: 'Declaration', style: 'tableHeader' },
                                { text: 'Signature', style: 'tableHeader' },
                                { text: 'Signed Date', style: 'tableHeader' }
                            ],
                            ...statusData.map((status) => [
                                { text: status.roleName, style: 'tableCell' },
                                { text: status.declaration, style: 'tableCell' },
                                status.signature !== 'N/A' ? { image: status.signature, width: 50, height: 30 } : { text: 'N/A', style: 'tableCell' },
                                { text: status.signedDate !== 'N/A' ? moment(status.signedDate).format('DD-MM-YYYY hh:mm A') : 'N/A', style: 'tableCell' }
                            ])
                        ]
                    },
                    layout: 'lightHorizontalLines'
                }
            ],
            styles: {
                title: { fontSize: 20, bold: true },
                header: { fontSize: 16, bold: true },
                sectionHeader: { fontSize: 14, bold: true, color: '#3c8dbc' },
                tableHeader: { fontSize: 12, bold: true, fillColor: '#d9edf7', margin: [0, 5, 0, 5] },
                tableCell: { fontSize: 11, margin: [0, 5, 0, 5] },
                infoTable: { margin: [0, 0, 0, 15] },
                defaultStyle: { fontSize: 12 }
            }
        };

        pdfMake.createPdf(docDefinition).download('PermitDetails.pdf');
    };

    return (
        <Container fluid className="p-2">
            <Card className="mb-4">
                <Card.Body>
                    <Row className="mb-2">
                        <Col md={12} className="text-end">
                            <button onClick={generatePdf} className="btn btn-primary">Download Permit</button>
                        </Col>
                    </Row>
                </Card.Body>
            </Card>
        </Container>
    );
};

export default PDFExport;
