import React, { useState, useEffect } from 'react';
import { Modal, Button, Row, Col, Card } from 'react-bootstrap';
import ViewDetails from './ViewDetails';
import moment from 'moment';

const ViewRecordModal = ({ isVisible, onHide, record }) => {

    console.log(record)
    const [formData, setFormData] = useState(record);

    useEffect(() => {
        if (record) {
            setFormData(record);
        }
    }, [record]);



    return (
        <Modal show={isVisible} onHide={onHide} size="lg">
            <Modal.Header closeButton>
                <Modal.Title>Incident Review Report (View Only)</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <i className='d-flex mb-3'>This is a record of the incident based on initial submission and review and prior to any assigned investigation. No further edits are possible on this report.</i>
                {/* <div className="obs-section mb-2  pb-2">
                 

                    <div className="row mb-3">
                        <div className="col-md-6">
                            <p className="obs-title">Incident ID</p>
                            <p className="obs-content mb-3">{formData.maskId || ''}</p>
                        </div>
                        <div className="col-md-6">
                            <p className="obs-title">Incident Title</p>

                            <p className="obs-content mb-0">{formData.title}</p>

                        </div>
                        <div className="col-md-6">
                            <p className="obs-title">Incident Date & Time</p>
                            <p className="obs-content">
                                {moment(formData.incidentDate || '').format("DD-MM-YYYY - HH:MM")}
                            </p>
                        </div>

                        <div className="col-md-6">
                            <p className="obs-title">Location</p>
                            {formData.isCustomLocation ?
                                <p className="obs-content">
                                    {formData.customLocation}
                                </p>
                                :
                                <p className="obs-content">
                                    {formData.locationOne?.name && (
                                        <>
                                            {formData.locationOne.name}
                                            {formData.locationTwo?.name && ' > '}
                                        </>
                                    )}
                                    {formData.locationTwo?.name && (
                                        <>
                                            {formData.locationTwo.name}
                                            {formData.locationThree?.name && ' > '}
                                        </>
                                    )}
                                    {formData.locationThree?.name && (
                                        <>
                                            {formData.locationThree.name}
                                            {formData.locationFour?.name && ' > '}
                                        </>
                                    )}
                                    {formData.locationFour?.name && formData.locationFour.name}
                                </p>
                            }
                        </div>
                    </div>
                </div> */}
                <ViewDetails data={formData} disable={true} type={'view'} details={true}/>
            </Modal.Body>
            <Modal.Footer>
               <i>This is a view-only screen. Updates can only be made by those assigned actions on this incident, through their respective  “My Actions” tab.
               </i>
            </Modal.Footer>
        </Modal>
    );
};

export default ViewRecordModal;
