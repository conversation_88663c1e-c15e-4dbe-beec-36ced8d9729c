import React, { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON>, Button, Form } from 'react-bootstrap';
import GalleryPage from "../apps/Gallery";
import { BodyComponent } from "reactjs-human-body";
import { jsPDF } from 'jspdf';
import html2canvas from 'html2canvas';
import BodyPartTooltip from "./BodyPartToolip";
import { IRPdf } from "./IRPdf";
import pdfMake from "pdfmake/build/pdfmake";
import pdfFonts from "pdfmake/build/vfs_fonts";
import cogoToast from "cogo-toast";
import AirReviewerEditCard from "./AirReviewerEditCard";
import moment from "moment";
import LearningPack from "./LearningPack";

pdfMake.vfs = pdfFonts.pdfMake.vfs;
const IncidentStory = ({ data, editable, bodyParts }) => {
    const [editModal, setEditModal] = useState(false)

    const [Learning, setLearning] = useState(false)
    const printRef = useRef();
    const [editDescription, setEditDescription] = useState('')
    const printDocument = () => {
        try {
            IRPdf(data).then(IRData => {
                pdfMake.createPdf(IRData).download(`${data.maskId}.pdf`);
            });

        }
        catch (e) {
            console.log(e)
        }

    };

    if (!data) {
        return null;
    }
    // const personsInvolved = data.personInvolved ? JSON.parse(data.personInvolved).map(person => person.name).join(', ') : null;
    // const witnesses = data.witnessInvolved ? JSON.parse(data.witnessInvolved).map(witness => witness.name).join(', ') : null;
    // const bodyParts = data.bodyPartInjured ? data.bodyPartInjured.map(part => `${part.slug} (intensity: ${part.intensity})`).join(', ') : null;
    const renderPersonList = (people) => (
        <ol>
            {people.map((person, index) => (
                <li key={index}>
                    <p>Name: {person.name}</p>
                    <p>Emp Id: {person.empId}</p>
                    <p>Designation: {person.designation}</p>
                    <p>Comments: {person.comments}</p>
                </li>
            ))}
        </ol>
    );

    const renderDriverList = (drivers) => (
        <ol>
            {drivers.map(driver => (
                <li key={driver.id}>
                    <p>Driver Name: {driver.name}</p>
                    <p>Vehicle: {driver.vehicleNo}</p>
                    <p>License: {driver.licenseNo}</p>
                </li>
            ))}
        </ol>
    );


    const personInvolvedList = (persons) => {
        const filteredPersons = persons.filter(person => person && person.selectedEmp && person.selectedEmp.name);
        const externalPersons = persons.filter(person => person && !person.internal);
        return (
            <>
                {(filteredPersons.length > 0 || externalPersons.length > 0) && <table className="table">
                    <thead className="table-header-sm">
                        <tr>
                            <th scope="col">#</th>
                            <th scope="col">Name</th>
                            <th scope="col">Employee ID</th>
                            <th scope="col">Designation</th>
                            <th scope="col">Injuries</th>
                            <th scope="col">Injury Details</th>
                            <th scope="col">PPE</th>
                            <th scope="col"></th>
                        </tr>
                    </thead>
                    <tbody>
                        {filteredPersons.map((person, index) => {

                            const convertedObject = person.injuryParts.reduce((acc, part) => {
                                let modifiedPart = part
                                    .replace(/^left/i, 'TEMP')
                                    .replace(/^right/i, 'left')
                                    .replace(/^TEMP/i, 'right');

                                // Convert to snake_case and lower case
                                modifiedPart = modifiedPart.replace(/([A-Z])/g, "_$1").toLowerCase();

                                // Assign to the accumulator object
                                acc[modifiedPart] = { selected: true };
                                return acc;
                            }, {});

                            return (


                                <tr>
                                    <th scope="row">{index + 1}</th>
                                    <td>{person.selectedEmp.name}</td>
                                    <td>{person.selectedEmp.uniqueId}</td>
                                    <td>{person.selectedEmp.designation}</td>
                                    <td>
                                        {person.injured ? (


                                            person.injuryParts.join(', ')


                                        ) : "None"}
                                    </td>
                                    <td>
                                        {person.injuryDetails.length > 0 ? (

                                            person.injuryDetails.map(parts => parts && parts.name).join(', ')

                                        ) : "None"}
                                    </td>

                                    <td>
                                        {person.isPPE ? (


                                            person.ppes.map(parts => parts && parts.name).join(', ')



                                        ) : "None"}
                                    </td>
                                    <td>
                                        {(person.injured && bodyParts) && <BodyPartTooltip content={convertedObject} >

                                            <i className="mdi mdi-human cursor-pointer"></i>
                                        </BodyPartTooltip>
                                        }
                                    </td>
                                </tr>
                            )
                        })}


                        {externalPersons.map((person, index) => {

                            const convertedObject = person.injuryParts.reduce((acc, part) => {
                                let modifiedPart = part
                                    .replace(/^left/i, 'TEMP')
                                    .replace(/^right/i, 'left')
                                    .replace(/^TEMP/i, 'right');

                                // Convert to snake_case and lower case
                                modifiedPart = modifiedPart.replace(/([A-Z])/g, "_$1").toLowerCase();

                                // Assign to the accumulator object
                                acc[modifiedPart] = { selected: true };
                                return acc;
                            }, {});

                            return (


                                <tr>
                                    <th scope="row">{index + 1}</th>
                                    <td>{person.name}</td>
                                    <td>{person.empId}</td>
                                    <td>{person.designation}</td>
                                    <td>
                                        {person.injured ? (


                                            person.injuryParts.join(', ')


                                        ) : "None"}
                                    </td>
                                    <td>
                                        {person.injuryDetails.length > 0 ? (

                                            person.injuryDetails.map(parts => parts && parts.name).join(', ')

                                        ) : "None"}
                                    </td>
                                    <td>
                                        {person.isPPE ? (


                                            person.ppes.map(parts => parts && parts.name).join(', ')



                                        ) : "None"}
                                    </td>
                                    <td>
                                        {(person.injured && bodyParts) && <BodyPartTooltip content={convertedObject} >

                                            <i className="mdi mdi-human cursor-pointer"></i>
                                        </BodyPartTooltip>

                                        }
                                    </td>
                                </tr>
                            )
                        })}
                    </tbody>
                </table>}

                {
                    (!filteredPersons.length > 0 && !externalPersons.length > 0) && <p className="text-center">No Person Involved</p>
                }

            </>
        );
    };




    const personnelImpactedList = (persons) => {
        const filteredPersons = persons.filter(person => person && person.selectedEmp && person.selectedEmp.name);
        const externalPersons = persons.filter(person => person && !person.internal)


        return (
            <>


                {(filteredPersons.length > 0 || externalPersons.length > 0) && <table className="table">
                    <thead className="table-header-sm">
                        <tr>
                            <th scope="col">#</th>
                            <th scope="col">Name</th>
                            <th scope="col">Employee ID</th>
                            <th scope="col">Designation</th>
                            <th scope="col">Injuries</th>
                            <th scope="col">Injury Details</th>
                            <th scope="col">PPE</th>
                            <th scope="col"></th>
                        </tr>
                    </thead>
                    <tbody>
                        {filteredPersons.map((person, index) => {
                            const convertedObject = person.injuryParts.reduce((acc, part) => {
                                let modifiedPart = part
                                    .replace(/^left/i, 'TEMP')
                                    .replace(/^right/i, 'left')
                                    .replace(/^TEMP/i, 'right');

                                // Convert to snake_case and lower case
                                modifiedPart = modifiedPart.replace(/([A-Z])/g, "_$1").toLowerCase();

                                // Assign to the accumulator object
                                acc[modifiedPart] = { selected: true };
                                return acc;
                            }, {});

                            return (
                                <tr>
                                    <th scope="row">{index + 1}</th>
                                    <td>{person.selectedEmp.name}</td>
                                    <td>{person.selectedEmp.uniqueId}</td>
                                    <td>{person.selectedEmp.designation}</td>
                                    <td>
                                        {person.injured ? (
                                            person.injuryParts.join(', ')
                                        ) : "None"}
                                    </td>
                                    <td>
                                        {person.injuryDetails.length > 0 ? (
                                            person.injuryDetails.map(parts => parts && parts.name).join(', ')
                                        ) : "None"}
                                    </td>
                                    <td>
                                        {person.isPPE ? (
                                            person.ppes.map(parts => parts && parts.name).join(', ')
                                        ) : "None"}
                                    </td>
                                    <td>
                                        {(person.injured && bodyParts) && <BodyPartTooltip content={convertedObject} >

                                            <i className="mdi mdi-human cursor-pointer"></i>
                                        </BodyPartTooltip>}
                                    </td>
                                </tr>
                            )
                        })}

                        {externalPersons.map((person, index) => {

                            const convertedObject = person.injuryParts.reduce((acc, part) => {
                                let modifiedPart = part
                                    .replace(/^left/i, 'TEMP')
                                    .replace(/^right/i, 'left')
                                    .replace(/^TEMP/i, 'right');

                                // Convert to snake_case and lower case
                                modifiedPart = modifiedPart.replace(/([A-Z])/g, "_$1").toLowerCase();

                                // Assign to the accumulator object
                                acc[modifiedPart] = { selected: true };
                                return acc;
                            }, {});

                            return (


                                <tr>
                                    <th scope="row">{index + 1}</th>
                                    <td>{person.name}</td>
                                    <td>{person.empId}</td>
                                    <td>{person.designation}</td>
                                    <td>
                                        {person.injured ? (


                                            person.injuryParts.join(', ')


                                        ) : "None"}
                                    </td>
                                    <td>
                                        {person.injuryDetails.length > 0 ? (

                                            person.injuryDetails.map(parts => parts && parts.name).join(', ')

                                        ) : "None"}
                                    </td>
                                    <td>
                                        {person.isPPE ? (


                                            person.ppes.map(parts => parts && parts.name).join(', ')



                                        ) : "None"}
                                    </td>
                                    <td>
                                        {(person.injured && bodyParts) && <BodyPartTooltip content={convertedObject} >

                                            <i className="mdi mdi-human cursor-pointer"></i>
                                        </BodyPartTooltip>}
                                    </td>
                                </tr>
                            )
                        })}


                    </tbody>
                </table>}

                {
                    (!filteredPersons.length > 0 && !externalPersons.length > 0) && <p className="text-center">No Personnel Injured</p>
                }

            </>
        );
    };

    const witnessInvolvedList = (persons) => {





        const filteredPersons = persons.filter(person => person && person.selectedEmp && person.selectedEmp.name);
        const externalPersons = persons.filter(person => person && !person.internal)

        return (

            <>
                {(filteredPersons.length > 0 || externalPersons.length > 0) && <table className="table">
                    <thead className="table-header-sm">
                        <tr>
                            <th scope="col">#</th>
                            <th scope="col">Name</th>
                            <th scope="col">Employee ID</th>
                            <th scope="col">Designation</th>
                            <th scope="col">Comments</th>
                        </tr>
                    </thead>
                    <tbody>
                        {filteredPersons.length > 0 && filteredPersons.map((person, index) => (
                            <tr key={person.selectedEmp.uniqueId}>
                                <th scope="row">{index + 1}</th>
                                <td>{person.selectedEmp.name}</td>
                                <td>{person.selectedEmp.uniqueId}</td>
                                <td>{person.selectedEmp.designation}</td>
                            </tr>
                        ))}

                        {externalPersons.length > 0 && externalPersons.map((person, index) => (
                            <tr key={person.empId}>
                                <th scope="row">{index + 1}</th>
                                <td>{person.name}</td>
                                <td>{person.empId}</td>
                                <td>{person.designation}</td>
                                <td>{person.comments}</td>
                            </tr>
                        ))}


                    </tbody>
                </table>
                }

                {
                    (!filteredPersons.length > 0 && !externalPersons.length > 0) && <p className="text-center">No Witness Involved</p>
                }
            </>
        );
    };

    const medicalReportCommentsList = (report) => (
        <ul>
            {report.comments.map((comment, index) => (
                <li key={index}>
                    {comment.name}: {comment.comments}
                </li>
            ))}
        </ul>
    );

    const isAnyoneInjured = (person, personnelImpacted) => person.some(person => person && person.injured === true) || personnelImpacted.some(person => person && person.injured === true);


    const editIR = () => {

        setEditModal(true)


    }
    async function convertAllImages(imageUrls) {
        const base64Images = [];
        for (const url of imageUrls) {
            const base64Image = await convertImageToBase64(url);
            base64Images.push(base64Image);
        }
        return base64Images;
    }
    async function convertImageToBase64(url) {
        const response = await fetch(url);
        const blob = await response.blob();
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onloadend = () => resolve(reader.result);
            reader.onerror = reject;
            reader.readAsDataURL(blob);
        });
    }
    const downloadLearningPack = async () => {

        const base64Images = await convertAllImages(data.evidence.map(i => i.src));
        console.log(base64Images);

        var dd = {
            content: [
                {
                    columns: [
                        {
                            stack: [
                                {
                                    table: {
                                        body: [
                                            base64Images.map(i => {
                                                return [{ image: i, width: 100, height: 100 }];
                                            })
                                        ]
                                    }
                                }
                            ]
                        },
                        {
                            stack: [
                                {
                                    style: 'tableExample',
                                    table: {

                                        body: [
                                            ['Incident Type', data.incidentTypeName.name],
                                            [data.maskId, data.workingGroup.name],
                                            ['Location', data.locationThree.name + ' ' + data.locationFour.name],
                                            ['Date/Time', data.incidentDate]
                                        ]
                                    }
                                },
                            ]
                        }
                    ]
                },
                {
                    columns: [
                        {
                            stack: [
                                {
                                    text: 'Description (what happened):',
                                    style: 'header'
                                },
                                {
                                    ul: [
                                        'This incident took place around 8.15 hrs. in the morning on 13.03.2024. while engaging in Kerb cutting. The Patient (S. Prakash) was employed for Kerb cutting by the contractor RR Constructions who does the gate automation construction. The tool used for Kerb cutting was a concrete cutter with a diamond wheel. Once the Kerb is cut the wheel has got stuck to the Kerb and had spontaneously Popped up the cutter hitting on the left leg of Prakashs. Prakash sustains injuries in two places on his left leg'
                                    ]
                                }
                            ]
                        },
                        {
                            stack: [
                                {
                                    text: 'Contributing factors:',
                                    style: 'header'
                                },
                                {
                                    ul: [
                                        'The diamond wheel of the concrete cutter has got tucked in the Kerb and suddenly has got popped up',
                                        'Minimum supervision and routine checks on equipment and work areas',
                                        'Employees exceeded the maximum allowable working hours by working overtime the day before the incident while not adhering to risk assessment',
                                        'Despite being the most experienced worker, he failed to wear safety goggles while carrying out the kerb cutting task'
                                    ]
                                }
                            ]
                        },
                        {
                            stack: [
                                {
                                    text: 'Corrective actions:',
                                    style: 'header'
                                },
                                {
                                    ul: [
                                        'Increase supervision of work activities, including routine checks on equipment and work areas ',
                                        'Designated HSE officer responsible for conducting these checks and ensure they are adequately trained',
                                        'Ensure compliance with legal regulations regarding maximum allowable 12 working hours per day with allowed breaks',
                                        'Regularly update and provide training to ensure adherence to safety protocols',
                                        'Review existing risk assessment updating current control measures',
                                        'Establish clear protocols and communication channels for safety concerns',
                                        'Wear safety glasses to shield the eyes from debris and sparks',
                                        'Protect the upper part of legs specially when working at construction / work sites expose to hazards'
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ],
            pageOrientation: 'landscape',
            styles: {
                header: {
                    bold: true,
                    fontSize: 15
                }
            },
            defaultStyle: {
                fontSize: 12
            }
        };




        // pdfMake.createPdf(dd).download(`test.pdf`);
        console.log(dd)
        setTimeout(() => {
            pdfMake.createPdf(dd).download(`test.pdf`);
        }, 2000);

    }

    return (
        <>
            {data &&

                <>


                    <div className="container h-100">
                        {/* <button onClick={printDocument} className="btn btn-primary mb-2 me-2">Print</button> */}
                        {(data.incidentRating && !bodyParts) && <button onClick={printDocument} className="btn btn-primary mb-2 me-2">Print</button>}
                        {(data.stage === 'Investigated' || data.stage === 'closed') && <button onClick={() => setLearning(true)} className="btn btn-primary mb-2 me-2">LearningPack</button>}

                        {/* {(data.incidentRating && !bodyParts) && <button onClick={editIR} className="btn btn-warning mb-2">Withdraw and Amend</button>} */}
                        {/* {(data.stage === 'Investigated' || data.stage === 'closed') && <button onClick={downloadLearningPack} className="btn btn-warning mb-2">Download Learning Pack</button>} */}

                        <div className="p-2 not-print">


                            <div className="row">
                                <div className="container-fluid py-2 border-radius-10 m-shadow" style={{}}>
                                    <div className="row d-flex justify-content-between align-items-center">

                                        <div className="col-auto d-flex justify-content-center py-2" style={{ borderRadius: '5px', fontSize: '16px' }}>
                                            <div className="">
                                                <p className="mb-0">{data.maskId} {data.edited ? `(${data.edited})` : ``}</p>
                                                <p className="mb-0"> Last Updated : {data.edited ? `${moment(data.latestEdited).format('DD/MM/YYYY HH:mm')}` : ``}</p>
                                                <p className="mb-0">Type:  {data.incidentTypeName && data.incidentTypeName.name}</p>
                                                <p className="mb-0"> {data.isReported === 'Yes' ? 'Reported' : data.isReported === 'No' ? 'Unreported' : data.isReported}</p>
                                            </div>
                                        </div>

                                        <div className="col-auto">
                                            {data.incidentRating && <div className="px-3 text-center">
                                                <p className="mb-0">  <span
                                                    style={{
                                                        backgroundColor: data.incidentRating.color,


                                                    }}
                                                    className={`badge font-lg m-shadow border-radius-10 ${data.incidentRating.color === 'yellow' ? 'text-black' : 'text-white'}`}
                                                >
                                                    {data.incidentRating.item}
                                                </span>
                                                </p>
                                                <p className="mb-0">IR Rating</p>
                                            </div>}
                                        </div>
                                    </div>
                                </div>

                            </div>
                            <br />
                            <div className="row">

                                <div className="col-12 m-shadow p-3 border-radius-10 ">
                                    <div className=" text-center">
                                        {data.shortDescription && data.shortDescription}

                                    </div>
                                </div>
                            </div>
                            <br />
                            <div className="row">

                                <div className="col-12 m-shadow p-3 border-radius-10">
                                    <div className="card">

                                        <div className="">
                                            <form>

                                                <div className="border-radius-10">
                                                    <div className="row">
                                                        <div className="">

                                                            <div className="col-sm-12">
                                                                {data.description && <div>
                                                                    <label className="col-form-label p-1">Description of the incident</label>
                                                                    <p className="white-bg-border-radius-10 form-control-plaintext">{data.description}</p>

                                                                </div>}

                                                            </div>
                                                        </div>
                                                        <div className="col-sm-6">
                                                            <label className="col-form-label p-1">Location</label>
                                                            {(data.locationThree && data.locationFour) && <div className="white-bg-border-radius-10"><p className="form-control-plaintext">{data.locationThree.name} <i className="mdi mdi-menu-right"></i> {data.locationFour.name}</p></div>}
                                                        </div>

                                                        <div className="col-sm-6 ">
                                                            <label className="col-form-label p-1">Date / Time </label>
                                                            <p className="white-bg-border-radius-10 form-control-plaintext">{data.incidentDate && data.incidentDate}</p>
                                                        </div>
                                                    </div>
                                                    <div className="row">

                                                        <div className="col-sm-6">
                                                            <label className="col-form-label p-1">Working group</label>
                                                            <p className="white-bg-border-radius-10 form-control-plaintext">{data.workingGroup && data.workingGroup.name}</p>
                                                        </div>

                                                        <div className="col-sm-6 ">
                                                            <label className="col-form-label p-1">Weather Condition</label>
                                                            <p className="white-bg-border-radius-10 form-control-plaintext">{data.weatherCondition && data.weatherCondition.name}</p>
                                                        </div>
                                                    </div>

                                                    <div className="row">

                                                        <div className="col-sm-6">
                                                            <label className="col-form-label p-1">Surface Type &amp; Condition</label>
                                                            {(data.surfaceType && data.surfaceCondition) && <div className="white-bg-border-radius-10"><p className="form-control-plaintext">{data.surfaceType.name} <i className="mdi mdi-menu-right"></i> {data.surfaceCondition.name}</p></div>}

                                                        </div>

                                                        <div className="col-sm-6 ">
                                                            <label className="col-form-label p-1">Lighting</label>
                                                            <p className="white-bg-border-radius-10 form-control-plaintext">{data.lighting && data.lighting.name}</p>
                                                        </div>
                                                    </div>
                                                    <div className="row">
                                                        {data.workActivity &&

                                                            <div className="col-sm-6">
                                                                <label className="col-form-label p-1">Work Activity </label>
                                                                {(data.workActivity) && <div className="white-bg-border-radius-10"><p className="form-control-plaintext">{data.workActivity.name} </p></div>}

                                                            </div>



                                                        }

                                                        {data.workActivityDepartment &&

                                                            <div className="col-sm-6 ">
                                                                <label className="col-form-label p-1">Department </label>
                                                                {(data.workActivityDepartment) && <div className="white-bg-border-radius-10"><p className="form-control-plaintext">{data.workActivityDepartment.name} </p></div>}

                                                            </div>



                                                        }
                                                    </div>
                                                    <hr />
                                                    {(data.damagedEquipmentNumber && data.damagedEquipmentNumber.length > 0) &&
                                                        <div className="row">
                                                            <div className="col-sm-4">
                                                                <label className="col-form-label p-1">Equipment Type</label>
                                                                {
                                                                    data.damagedEquipmentNumber.map(i => (
                                                                        <p className="white-bg-border-radius-10 form-control-plaintext">

                                                                            {i && i.category}


                                                                        </p>
                                                                    ))
                                                                }
                                                            </div>

                                                            <div className="col-sm-4">
                                                                <label className="col-form-label p-1">Equipment Number</label>
                                                                {
                                                                    data.damagedEquipmentNumber.map(i => (
                                                                        <p className="white-bg-border-radius-10 form-control-plaintext">

                                                                            {i && i.number}


                                                                        </p>
                                                                    ))
                                                                }
                                                            </div>

                                                            <div className="col-sm-4">
                                                                <label className="col-form-label p-1">Damage Type</label>
                                                                {
                                                                    data.damagedEquipmentNumber.map(i => (
                                                                        <p className="white-bg-border-radius-10 form-control-plaintext">

                                                                            {i && i.damageType}


                                                                        </p>
                                                                    ))
                                                                }
                                                            </div>

                                                        </div>
                                                    }
                                                    <hr />
                                                    <div className="row">

                                                        {data.vesselDetails && <div className="col-sm-6">
                                                            <label className="col-form-label p-1">Vessel Details:</label>
                                                            <p className="white-bg-border-radius-10 form-control-plaintext">

                                                                {data.vesselDetails}


                                                            </p>



                                                        </div>}

                                                        {data.damageType && <div className="col-sm-6 ">
                                                            <label className="col-form-label p-1">Any other type of damage:</label>
                                                            <p className="white-bg-border-radius-10 form-control-plaintext">

                                                                {data.damageType}

                                                            </p>



                                                        </div>

                                                        }

                                                        {data.truckDetails &&
                                                            <div className="col-sm-6">
                                                                <label className="col-form-label p-1">Trucks Involved</label>
                                                                <p className="white-bg-border-radius-10 form-control-plaintext">

                                                                    {data.truckDetails}

                                                                </p>



                                                            </div>
                                                        }
                                                    </div>
                                                </div>
                                                <hr />
                                                <div className="row">
                                                    <label className="col-sm-12 col-form-label">Person(s) Involved</label>
                                                    {data.personInvolved && (


                                                        <div>{personInvolvedList(data.personInvolved)}</div>

                                                    )}
                                                </div>

                                                <div className="row">
                                                    <label className="col-sm-12 col-form-label">Personnel Injured</label>
                                                    {data.personnelImpacted && (


                                                        <div>{personnelImpactedList(data.personnelImpacted)}</div>

                                                    )}
                                                </div>

                                                <div className="row">
                                                    <label className="col-sm-12 col-form-label">Witness Involved</label>
                                                    {data.witnessInvolved && (


                                                        <div>{witnessInvolvedList(data.witnessInvolved)}</div>

                                                    )}
                                                </div>
                                                <hr />
                                                <div className=" ">




                                                    <div className="row">

                                                        <div className="col-sm-6">
                                                            <label className="col-form-label p-1">How/Why the incident Occur</label>
                                                            <p className="white-bg-border-radius-10  form-control-plaintext">{data.moreDetails && data.moreDetails}</p>
                                                        </div>
                                                        {data.actionsTaken && <>
                                                            <div className="col-sm-6 ">
                                                                <label className="col-form-label p-1">Immediate Actions taken</label>
                                                                <p className="white-bg-border-radius-10  form-control-plaintext">{data.actionsTaken && data.actionsTaken}</p>
                                                            </div> </>}
                                                    </div>

                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <br />
                            <div className="card">
                                <div className="row">

                                    <div className="col-12 m-shadow p-3 border-radius-10 ">
                                        <div className=" text-center">
                                            Evidence Photos

                                        </div>
                                    </div>
                                </div>
                                <br />
                                <div className="card-body d-flex justify-content-around m-shadow border-radius-10">

                                    {data.evidence && data.evidence.length > 0 && (
                                        <div className="border row me-2">
                                            {
                                                data.evidence.map(i => {
                                                    return (
                                                        <div className="col-md-3 p-0">

                                                            <img src={i.src} className="img-responsive w-100" />
                                                        </div>
                                                    )
                                                })
                                            }

                                        </div>
                                    )}


                                </div>
                            </div>
                            <br />
                            <div className="card">
                                <div className="row">

                                    <div className="col-12 m-shadow p-3 border-radius-10  ">
                                        <div className=" text-center">
                                            Other Details

                                        </div>
                                    </div>
                                </div>

                                <div className="">

                                    <div className="">
                                        {isAnyoneInjured(data.personInvolved, data.personnelImpacted) && (
                                            <div className="d-flex justify-content-between align-items-center mt-3">
                                                <div className="text-strong">Notification sent to Medical Officer</div>
                                                <div className="white-bg-border-radius-10 ">{data.medicalNotificationTime && data.medicalNotificationTime}</div>
                                            </div>
                                        )}

                                        {(data.damagedEquipmentNumber && data.damagedEquipmentNumber.length > 0) && (
                                            <div className="d-flex justify-content-between align-items-center mt-3">
                                                <div className="text-strong">Notification sent to Duty Engineer Manager</div>
                                                <div className="white-bg-border-radius-10 ">{data.propertyNotificationTime && data.propertyNotificationTime}</div>
                                            </div>
                                        )}

                                        {data.custodianNotificationTime && (
                                            <div className="d-flex justify-content-between align-items-center mt-3">
                                                <div className="text-strong">Notification sent to Custodian</div>
                                                <div className="white-bg-border-radius-10 ">{data.custodianNotificationTime}</div>
                                            </div>
                                        )}

                                        {data.insuranceNotificationTime && (
                                            <div className="d-flex justify-content-between align-items-center mt-3">
                                                <div className="text-strong">Notification sent to Insurance</div>
                                                <div className="white-bg-border-radius-10 ">{data.insuranceNotificationTime}</div>
                                            </div>
                                        )}

                                        {data.investigationNotificationTime && (
                                            <div className="d-flex justify-content-between align-items-center mt-3">
                                                <div className="text-strong">Notification sent to Investigation Team</div>
                                                <div className="white-bg-border-radius-10 ">{data.investigationNotificationTime}</div>
                                            </div>
                                        )}

                                        {data.thirdPartyNotificationTime && (
                                            <div className="d-flex justify-content-between align-items-center mt-3">
                                                <div className="text-strong">Notification sent to Third Party</div>
                                                <div className="white-bg-border-radius-10 ">{data.thirdPartyNotificationTime}</div>
                                            </div>
                                        )}

                                        {data.reporter && (
                                            <div className="d-flex align-items-center mt-3">
                                                <div className="text-strong me-2">Reporter:</div>
                                                <div className="white-bg-border-radius-10">{data.reporter.email} ({data.reporter.firstName})</div>
                                            </div>
                                        )}

                                        {data.reviewer && (
                                            <div className="d-flex align-items-center mt-3">
                                                <div className="text-strong me-2">Reviewer:</div>
                                                <div className="white-bg-border-radius-10 ">{data.reviewer.email} ({data.reviewer.firstName})</div>
                                            </div>
                                        )}

                                        {data.estimator && (
                                            <div className="d-flex align-items-center mt-3">
                                                <div className="text-strong me-2">Estimator:</div>
                                                <div className="white-bg-border-radius-10 ">{data.estimator.email} ({data.estimator.firstName})</div>
                                            </div>
                                        )}

                                        {data.trainee && (
                                            <div className="d-flex align-items-center mt-3">
                                                <div className="text-strong me-2">Trainer:</div>
                                                <div className="white-bg-border-radius-10 ">{data.trainee.email} ({data.trainee.firstName})</div>
                                            </div>
                                        )}

                                        {data.gmOps && (
                                            <div className="d-flex align-items-center mt-3">
                                                <div className="text-strong me-2">Punitive / Correction Action Taker:</div>
                                                <div className="white-bg-border-radius-10 ">{data.gmOps.email} ({data.gmOps.firstName})</div>
                                            </div>
                                        )}

                                        {data.thirdParty && (
                                            <div className="d-flex align-items-center mt-3">
                                                <div className="text-strong me-2">Third Party:</div>
                                                <div className="white-bg-border-radius-10 ">{data.thirdParty.email} ({data.thirdParty.firstName})</div>
                                            </div>
                                        )}

                                        {data.security && (
                                            <div className="d-flex align-items-center mt-3">
                                                <div className="text-strong me-2">Security:</div>
                                                <div className="white-bg-border-radius-10 ">{data.security.email} ({data.security.firstName})</div>
                                            </div>
                                        )}

                                        {data.costReviewer && (
                                            <div className="d-flex align-items-center mt-3">
                                                <div className="text-strong me-2">Cost Reviewer:</div>
                                                <div className="white-bg-border-radius-10 ">{data.costReviewer.email} ({data.costReviewer.firstName})</div>
                                            </div>
                                        )}

                                        {data.financer && (
                                            <div className="d-flex align-items-center mt-3">
                                                <div className="text-strong me-2">Financer:</div>
                                                <div className="white-bg-border-radius-10 ">{data.financer.email} ({data.financer.firstName})</div>
                                            </div>
                                        )}

                                        {data.dutyEngManager && (
                                            <div className="d-flex  align-items-center mt-3">
                                                <div className="text-strong me-2">Duty Eng Manager:</div>
                                                <div className="white-bg-border-radius-10 ">{data.dutyEngManager.email} ({data.dutyEngManager.firstName})</div>
                                            </div>
                                        )}

                                        {data.medicalOfficer && (
                                            <div className="d-flex  align-items-center mt-3">
                                                <div className="text-strong me-2">Medical Officer:</div>
                                                <div className="white-bg-border-radius-10 ">{data.medicalOfficer.email} ({data.medicalOfficer.firstName})</div>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                </>
            }
            {(editModal && Object.keys(data).length > 0) && (
                <AirReviewerEditCard showModal={editModal} setShowModal={setEditModal} data={data} />
            )}



            {data && <Modal
                show={Learning}
                size="md"
                onHide={() => setLearning(false)}
                aria-labelledby="example-modal-sizes-title-md"
                backdrop="static"
            >
                <Modal.Header>
                    Learning Pack
                </Modal.Header>

                <Modal.Body>
                    <div className="row">
                        <div className="col-md-12">
                            <LearningPack data={data} />
                        </div>

                    </div>



                </Modal.Body>

                <Modal.Footer className="flex-wrap">

                    <Button variant="light" onClick={() => { setLearning(false); }}>Close</Button>

                </Modal.Footer>
            </Modal>}
        </>
    )
}

IncidentStory.defaultProps = {
    editable: false,
    bodyParts: false
};

export default IncidentStory;