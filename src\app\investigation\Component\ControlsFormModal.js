import React, { useEffect, useState, useCallback } from 'react';
import { Modal, Form, Col, Row, Card, Button, Accordion, InputGroup, FormControl, Nav, Tab } from 'react-bootstrap';
import Select from 'react-select';
import ViewDetails from './ViewDetails';
import { InputTextarea } from 'primereact/inputtextarea';
import { RadioButton } from 'primereact/radiobutton';
import { fetchDropdownData } from '../../services/dropdownService';
import { CHILD_DROPDOWNS, INCIDENT_WITH_ID, INVERSTIGATION_WITH_ID, GET_USER_ROLE_BY_MODE, SUBMIT_INVERSTICATION_APPROVER } from '../../constants';
import API from '../../services/API';
import ControlAccordionItem from './Control/ControlAccordionItem';
import InvestigationModal from './InvestigationModal'
import moment from 'moment';
import 'primereact/resources/primereact.min.css'; // Ensure PrimeReact CSS is included
import 'primeicons/primeicons.css';
import Swal from 'sweetalert2';
import Recommendation from './Recommendation/Recommendation';
import Declaration from './Declaration';
import ActionAssigneeModal from './ActionAssigneeModal';

const ControlsFormModal = ({ show, type, data, applicationDetails, handleClose, fetchLetest, sendToApprover, returnToInvestigator, approveReport }) => {
    const [incImmediateCauseOptions, setIncImmediateCauseOptions] = useState([]);
    const [incOrganizationFactorOptions, setIncOrganizationFactorOptions] = useState([]);
    const [incJobFactorOptions, setIncJobFactorOptions] = useState([]);
    const [unincOrganizationFactorOptions, setUnIncOrganizationFactorOptions] = useState([]);
    const [unincJobFactorOptions, setUnIncJobFactorOptions] = useState([]);
    const [incFallibilityOptions, setIncFallibilityOptions] = useState([]);
    const [incFatorOptions, setIncFatorOptions] = useState([]);
    const [expandedSections, setExpandedSections] = useState({});
    const [isEditingTitle, setIsEditingTitle] = useState(false);
    const [editedTitle, setEditedTitle] = useState(applicationDetails.title || '');
    const [leadAssign, setLeadAssign] = useState(null);
    const [approve, setApprove] = useState([])
    const [openCardIndex, setOpenCardIndex] = useState(null);
    const [approveComments, setApproveComments] = useState(null);

    const [members, setMembers] = useState(data.investigationTeamMembers && data.investigationTeamMembers.split(',') || []); // Initial members
    const [isEditing, setIsEditing] = useState(false);
    const [newMembers, setNewMembers] = useState(members.join(', '));

    const [actionModal, setActionModal] = useState(false)

    // Function to toggle the card open/close state
    const toggleCard = (index) => {
        // If the clicked card is already open, close it. Otherwise, open it.
        setOpenCardIndex(openCardIndex === index ? null : index);
    };

    const sectionDescriptions = {
        identifiedPreventiveControls: (
            <>
                <i className='d-flex mb-2' style={{ fontSize: '14px' }}>Below is a list of Preventative Controls identified by the incident reporter and reviewer, along with their implementation status and effectiveness in the context of this incident. Use this as a foundation for your investigation, but do not treat it as final. The investigation team is encouraged to edit and update this list as necessary based on findings from site observations, interviews, document reviews, collected data, forensic analysis, maintenance and inspection records, simulations or recreations of the incident, expert consultations, environmental factors, or any other relevant sources of information.</i>
                <i className='d-flex mb-2' style={{ fontSize: '14px' }}>Identify the immediate cause(s) for the non-implementation, failure or ineffectiveness of any controls, and the underlying factors, including job factors and organizational factors. Use the drop-down selections to guide you through the process, and include all relevant factors, explaining why they were significant in the context of this incident.</i>
                <i className='d-flex mb-2' style={{ fontSize: '14px' }}>Ensure that any modifications you make are supported by evidence, and remember to upload or attach all records, documents, or files you reference or generate as part of the investigation.</i>

            </>
        ),
        unidentifiedPreventiveControls: (
            <>
                <i className='d-flex mb-2' style={{ fontSize: '14px' }}>Below is a list of Preventative Controls that were overlooked or not considered during the planning or risk assessment phase, as identified by the incident reporter and reviewer. The investigation team can use this as a starting point, but are not bound by it. The team is encouraged to review and update this list based on findings from site observations, interviews, document reviews, collected data, forensic analysis, maintenance and inspection records, simulations or recreations of the incident, expert consultations, environmental factors, or any other relevant sources of information.
                </i>
                <i className='d-flex mb-2' style={{ fontSize: '14px' }}>Identify any additional controls that could have prevented the incident and assess their potential significance. </i>
                <i className='d-flex mb-2' style={{ fontSize: '14px' }}>Ensure that any changes or additions are supported by evidence, and explain why they would have been significant in preventing the incident. Remember to upload or attach all records, documents, or files you reference or generate as part of the investigation.
                </i>

            </>
        ),
        identifiedMitigativeControls: (
            <>
                <i className='d-flex mb-2' style={{ fontSize: '14px' }}>Below is a list of Mitigative Controls identified by the incident reporter and reviewer, along with their implementation status and effectiveness in minimizing the consequences of the incident. Use this as a foundation for your investigation, but do not feel restricted by it. The investigation team is encouraged to edit and update this list based on findings from site observations, interviews, document reviews, collected data, forensic analysis, maintenance and inspection records, simulations or recreations of the incident, expert consultations, environmental factors, or any other relevant sources of information.
                </i>
                <i className='d-flex mb-2' style={{ fontSize: '14px' }}>Identify the immediate cause(s) for the non-implementation, failure or ineffectiveness of any controls, and the underlying factors, including job factors and organizational factors. Use the drop-down selections to guide you through the process, and include all relevant factors, explaining why they were significant in the context of this incident.</i>
                <i className='d-flex mb-2' style={{ fontSize: '14px' }}>Ensure that any modifications you make are supported by evidence, and remember to upload or attach all records, documents, or files you reference or generate as part of the investigation.</i>
            </>
        ),
        unidentifiedMitigativeControls: (
            <>
                <i className='d-flex mb-2' style={{ fontSize: '14px' }}>Below is a list of Mitigative Controls that were overlooked or not considered during the planning or risk assessment phase, as identified by the incident reporter and reviewer. The investigation team can use this as a starting point, but are not bound by it. The team is encouraged to review and update this list based on findings from site observations, interviews, document reviews, collected data, forensic analysis, maintenance and inspection records, simulations or recreations of the incident, expert consultations, environmental factors, or any other relevant sources of information.
                </i>
                <i className='d-flex mb-2' style={{ fontSize: '14px' }}>Identify any additional controls that could have had a mitigative effect on the consequence of the incident and assess their potential significance.</i>
                <i className='d-flex mb-2' style={{ fontSize: '14px' }}>Ensure that any changes or additions are supported by evidence, and explain why they would have been significant in mitigating the consequence of the incident. Remember to upload or attach all records, documents, or files you reference or generate as part of the investigation.</i>
            </>
        )
    };
    const InversticationApprovel = async () => {


        try {
            const response = await API.post(GET_USER_ROLE_BY_MODE, {
                locationOneId: "",
                locationTwoId: "",
                locationThreeId: "",
                locationFourId: "",
                mode: 'incident_investigation_approver'
            });

            if (response.status === 200) {
                const data = response.data.map((item) => ({
                    label: item.firstName,
                    value: item.id
                }));
                setApprove(data);

            }
        } catch (error) {
            console.error("Error fetching crew list:", error);
        }


    }

    useEffect(() => {
        InversticationApprovel();
        const additionalCondition = { type: 'General' };
        fetchDropdownData('immediate_cause', setIncImmediateCauseOptions, additionalCondition);
        fetchDropdownData('organization_factor', setIncOrganizationFactorOptions, additionalCondition);
        fetchDropdownData('job_factor', setIncJobFactorOptions, additionalCondition);
        fetchDropdownData('fallibility', setIncFallibilityOptions, additionalCondition);
        fetchDropdownData('unidentified_job_factor', setUnIncJobFactorOptions, additionalCondition);
        fetchDropdownData('unidentified_organizational_factors', setUnIncOrganizationFactorOptions, additionalCondition);
    }, []);

    const defaultJobFactors = [{
        jobFactor: '',
        fallibility: '',
        isRoutine: true,
        description: '',
        contributorFactor: '',
        extentOfContribution: 'Not Significant',
    }];

    const defaultOrganizationalFactor = {
        factor: '',
        description: '',
        extentOfContribution: 'Not Significant',
    };

    const defaultControl = {
        immediateCause: '',
        immediateCauseDescription: '',
        order: 0,
        controlStatement: '',
        isEffective: true,
        jobFactors: [],
        relatedOrganizationalFactors: [] // Changed from organizationalFactors
    };

    const defaultState = {
        identifiedPreventiveControls: [defaultControl],
        unidentifiedPreventiveControls: [defaultControl],
        identifiedMitigativeControls: [defaultControl],
        unidentifiedMitigativeControls: [defaultControl]
    };

    const [formState, setFormState] = useState(defaultState);

    useEffect(() => {
        if (data && data.controls) {
            const mergeWithDefaults = (controls, defaultControl) => {
                return controls.map(control => ({
                    ...defaultControl,
                    ...control,
                    jobFactors: (control.jobFactors || []).map(jobFactor => ({
                        ...defaultJobFactors[0],
                        ...jobFactor,
                    })),
                    relatedOrganizationalFactors: (control.relatedOrganizationalFactors || []).map(orgFactor => ({
                        ...defaultOrganizationalFactor,
                        ...orgFactor
                    }))
                }));
            };

            const newFormState = {
                identifiedPreventiveControls: mergeWithDefaults(data.controls.identifiedPreventiveControls || [], defaultControl),
                unidentifiedPreventiveControls: mergeWithDefaults(data.controls.unidentifiedPreventiveControls || [], defaultControl),
                identifiedMitigativeControls: mergeWithDefaults(data.controls.identifiedMitigativeControls || [], defaultControl),
                unidentifiedMitigativeControls: mergeWithDefaults(data.controls.unidentifiedMitigativeControls || [], defaultControl)
            };

            setFormState(newFormState);
        } else {
            console.warn("Data or controls are missing in the provided props.");
        }
    }, [data]);

    const handleChange = (section, index, name, value) => {
        const updatedControls = [...formState[section]];
        updatedControls[index][name] = value;
        setFormState({ ...formState, [section]: updatedControls });
    };

    const handleJobFactorChange = (section, controlIndex, jobFactorIndex, name, value) => {
        const updatedControls = [...formState[section]];
        const updatedJobFactors = [...updatedControls[controlIndex].jobFactors];
        updatedJobFactors[jobFactorIndex][name] = value;
        updatedControls[controlIndex].jobFactors = updatedJobFactors;
        setFormState({ ...formState, [section]: updatedControls });
    };

    const handleOrganizationalFactorChange = (section, controlIndex, orgFactorIndex, name, value) => {
        const updatedControls = [...formState[section]];
        const updatedOrgFactors = [...updatedControls[controlIndex].relatedOrganizationalFactors];
        updatedOrgFactors[orgFactorIndex][name] = value;
        updatedControls[controlIndex].relatedOrganizationalFactors = updatedOrgFactors;
        setFormState({ ...formState, [section]: updatedControls });
    };

    const handleAddJobFactor = (section, controlIndex) => {
        const updatedControls = [...formState[section]];
        updatedControls[controlIndex].jobFactors.push({
            jobFactor: '',
            fallibility: '',
            isRoutine: true,
            description: '',
            contributorFactor: '',
            extentOfContribution: 'Not Significant',
        });
        setFormState({ ...formState, [section]: updatedControls });
    };

    const handleAddOrganizationalFactor = (section, controlIndex) => {
        const updatedControls = [...formState[section]];
        updatedControls[controlIndex].relatedOrganizationalFactors.push({
            factor: '',
            description: '',
            extentOfContribution: 'Not Significant',
        });
        setFormState({ ...formState, [section]: updatedControls });
    };

    const getControlIdPrefix = (section) => {
        switch (section) {
            case 'identifiedPreventiveControls':
                return 'PC';
            case 'unidentifiedPreventiveControls':
                return 'UPC';
            case 'identifiedMitigativeControls':
                return 'MC';
            case 'unidentifiedMitigativeControls':
                return 'UMC';
            default:
                return '';
        }
    };

    const extentOfContributionOptions = [
        { value: 'major', label: 'Major' },
        { value: 'significant', label: 'Significant' },
        { value: 'insignificant', label: 'Insignificant' },
    ];

    const handleSelectChange = async (section, controlIndex, jobFactorIndex, field, selectedOption) => {
        const value = selectedOption ? selectedOption.label : '';
        handleJobFactorChange(section, controlIndex, jobFactorIndex, field, value);

        if (field === 'jobFactor') {
            await fetchChildDropdownData(selectedOption.value, setIncFatorOptions)
        }
    };

    const handleOrganizationalFactorSelectChange = (section, controlIndex, orgFactorIndex, field, selectedOption) => {
        const value = selectedOption ? selectedOption.label : '';
        handleOrganizationalFactorChange(section, controlIndex, orgFactorIndex, field, value);
    };

    const handleImmediateCauseChange = (section, controlIndex, selectedOption) => {
        const value = selectedOption ? selectedOption.label : '';
        handleChange(section, controlIndex, 'immediateCause', value);
    };

    const handleSubmit = async (event) => {
        event.preventDefault();
        console.log('Form Submitted:', formState);
        try {
            await API.patch(INVERSTIGATION_WITH_ID(data.id), { status: 'Under Investigation', controls: formState });

            fetchLetest()


            Swal.fire({
                toast: true,
                icon: 'success',
                title: 'Finalized',
                text: `Investigation Updated successfully.`,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
            });
        } catch (error) {
            console.error('Error finalizing:', error);
        }

    };

    // Delete job factor
    const handleDeleteJobFactor = (section, controlIndex, jobFactorIndex) => {
        const updatedControls = [...formState[section]];
        updatedControls[controlIndex].jobFactors.splice(jobFactorIndex, 1);
        setFormState({ ...formState, [section]: updatedControls });
    };

    // Delete organizational factor
    const handleDeleteOrganizationalFactor = (section, controlIndex, orgFactorIndex) => {
        const updatedControls = [...formState[section]];
        updatedControls[controlIndex].relatedOrganizationalFactors.splice(orgFactorIndex, 1);
        setFormState({ ...formState, [section]: updatedControls });
    };
    const fetchChildDropdownData = useCallback(async (id, setState) => {
        try {

            const url = CHILD_DROPDOWNS(id);
            const response = await API.get(url);

            if (response.status === 200) {
                const data = response.data.map((item) => ({
                    label: item.name,
                    value: item.id,
                    desc: item.description
                }));
                setState(data);


            }
        } catch (error) {
            console.error(`Error fetching list:`, error);
        }
    }, []);

    const submitLevelOne = () => {

        fetchLetest()

    }


    useEffect(() => {
        setEditedTitle(applicationDetails.title || ''); // Update editedTitle when applicationDetails changes
    }, [applicationDetails.title]);

    // Function to handle editing state
    const handleTitleEdit = () => {
        setIsEditingTitle(true);
    };

    // Save the edited title
    const handleTitleSave = async () => {
        try {
            const response = await API.patch(INCIDENT_WITH_ID(applicationDetails.id), { title: editedTitle });
            if (response.status === 204) {
                setIsEditingTitle(false);
                // Optionally, refresh data or notify parent
            }
        } catch (error) {
            console.error('Error updating title:', error);
        }
    };

    // Cancel editing
    const handleTitleCancel = () => {
        setEditedTitle(applicationDetails.title || '');
        setIsEditingTitle(false);
    };

    const toggleExpanded = (section) => {
        setExpandedSections((prev) => ({
            ...prev,
            [section]: !prev[section],
        }));
    };

    const addMemberInvestigation = async () => {
        try {
            console.log("Calling addMemberInvestigation with data:", data.id, newMembers);
            const response = await API.patch(INVERSTIGATION_WITH_ID(data.id), { investigationTeamMembers: newMembers });
            console.log("API response:", response);
            if (response.status === 204) {
                Swal.fire({
                    icon: 'success',
                    title: 'Member added successfully!',
                    toast: true,
                    position: 'top-right',
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                });
            }
        } catch (error) {
            console.error('Error updating title:', error);
        }
    };

    const accordionItems = [
        {
            key: '0',
            title: 'Incident Narrative and Impact Detailing',
            colorClass: 'accordion-header-1',
            body: (type.actionType === 'conduct_investigation' || type.actionType === 'reconduct_investigation') ? <ViewDetails data={applicationDetails} disable={false} submitReport={submitLevelOne} type={'inves'} details={false} /> : <ViewDetails data={applicationDetails} disable={true} submitReport={submitLevelOne} details={false} />
        },
        {
            key: '1',
            title: 'Investigation',
            colorClass: 'accordion-header-2',
            body: (
                <div className="p-0 m-0">
                    {console.log(formState)}
                    <Form onSubmit={handleSubmit}>
                        {Object.keys(formState).map((section, sectionIndex) => {
                            const sectionDescription = sectionDescriptions[section];
                            const isExpanded = expandedSections[section] || false; // Get expanded state for the section
                            return (
                                <Card key={sectionIndex} className="mb-4  custom-card">
                                    <b className="ms-3 section-title">
                                        {section
                                            .replace(/([A-Z])/g, ' $1')
                                            .trim()
                                            .replace(/\b\w/g, (char) => char.toUpperCase())}
                                    </b>
                                    <div className="ms-3 mb-2">
                                        {/* {isExpanded ? (
                                            <>
                                                {sectionDescription}
                                                <span
                                                    className="text-primary"
                                                    style={{ cursor: 'pointer' }}
                                                    onClick={() => toggleExpanded(section)}
                                                >
                                                    Show Less
                                                </span>
                                            </>
                                        ) : (
                                            <> */}
                                                {/* <>{`${sectionDescription.props.children[0].props.children.substring(0, 100)}...`}</> */}
                                                {/* <span
                                                    className="text-primary"
                                                    style={{ cursor: 'pointer' }}
                                                    onClick={() => toggleExpanded(section)}
                                                >
                                                    Read More
                                                </span> */}
                                            {/* </>
                                        )} */}

                                        <>{sectionDescription}</>
                                    </div>
                                    <Card.Body className=' boxShadow'>
                                        <Accordion>
                                            {formState[section].map((control, controlIndex) => (
                                                <ControlAccordionItem
                                                    key={controlIndex}
                                                    control={control}
                                                    controlIndex={controlIndex}
                                                    section={section}
                                                    getControlIdPrefix={getControlIdPrefix}
                                                    handleChange={handleChange}
                                                    handleImmediateCauseChange={handleImmediateCauseChange}
                                                    incImmediateCauseOptions={incImmediateCauseOptions}
                                                    incJobFactorOptions={incJobFactorOptions}
                                                    incFatorOptions={incFatorOptions}
                                                    incFallibilityOptions={incFallibilityOptions}
                                                    handleSelectChange={handleSelectChange}
                                                    handleJobFactorChange={handleJobFactorChange}
                                                    handleAddJobFactor={handleAddJobFactor}
                                                    handleDeleteJobFactor={handleDeleteJobFactor}
                                                    handleOrganizationalFactorSelectChange={handleOrganizationalFactorSelectChange}
                                                    handleOrganizationalFactorChange={handleOrganizationalFactorChange}
                                                    handleAddOrganizationalFactor={handleAddOrganizationalFactor}
                                                    handleDeleteOrganizationalFactor={handleDeleteOrganizationalFactor}
                                                    incOrganizationFactorOptions={incOrganizationFactorOptions}
                                                    unincOrganizationFactorOptions={unincOrganizationFactorOptions}
                                                    unincJobFactorOptions={unincJobFactorOptions}
                                                />
                                            ))}
                                        </Accordion>
                                    </Card.Body>

                                </Card>
                            )
                        })}
                    </Form>
                    {(type.actionType === 'conduct_investigation' || type.actionType === 'reconduct_investigation') && (
                        <div className="d-flex justify-content-end p-4">
                            <Button variant="success" onClick={handleSubmit}>
                                Save
                            </Button>
                        </div>
                    )}

                </div>
            )
        },

        {
            key: '2',
            title: 'Analysis, Conclusions, and Recommendations',
            colorClass: 'accordion-header-3',
            body: (
                <Recommendation data={data} type={type} />
            )
        },
        {
            key: '3',
            title: 'Declaration & Submission ', // New Accordion Item
            colorClass: 'accordion-header-5', // New CSS Class
            body: (<>
                <Declaration members={members} />
                {(type.actionType === 'conduct_investigation' || type.actionType === 'reconduct_investigation') && (
                    <div className="row mb-2 mt-3 ">
                        <div className="col-4 mb-2">
                            <label htmlFor="incidentType" className="mb-2">Investigation Approver</label>
                            <Select
                                options={approve}
                                value={approve.find(option => option.value === leadAssign)}
                                onChange={(selectedOption) => setLeadAssign(selectedOption.value)}
                                placeholder="Select"
                                isClearable
                            />
                        </div>
                    </div>
                )}
            </>

            )
        },
        {
            key: '4',
            title: 'Records', // New Accordion Item
            colorClass: 'accordion-header-4', // New CSS Class
            body: (
                <InvestigationModal
                    investigationId={applicationDetails.id}
                    type={type}
                />
            )
        },

    ];

    const submitToApprover = async () => {
        if (leadAssign) {
            const result = await Swal.fire({
                title: 'Are you sure?',
                text: 'Do you really want to submit this for approval?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, submit it!',
                cancelButtonText: 'No, cancel'
            });

            if (result.isConfirmed) {

                sendToApprover(leadAssign)

            } else {
                Swal.fire({
                    icon: 'info',
                    title: 'Submission cancelled',
                    text: 'Your recommendation was not submitted.'
                });
            }
        } else {
            Swal.fire({
                icon: 'warning',
                title: 'No approver assigned',
                text: 'Please assign an approver before submitting.'
            });
        }
    };

    const submitToInvesticator = async () => {
        if (approveComments) {
            const result = await Swal.fire({
                title: 'Are you sure?',
                text: 'Do you really want return it to the investigator?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, submit it!',
                cancelButtonText: 'No, cancel'
            });

            if (result.isConfirmed) {
                returnToInvestigator(approveComments);
            } else {
                Swal.fire({
                    icon: 'info',
                    title: 'Submission cancelled',
                    text: 'Your recommendation was not submitted.'
                });
            }
        } else {
            Swal.fire({
                icon: 'warning',
                title: 'No approver Comments',
                text: 'Please add comments before submitting.'
            });
        }
    };

    const submitReport = async () => {
        const result = await Swal.fire({
            title: 'Are you sure?',
            text: 'Do you really want to Approve the Investigation Report?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, submit it!',
            cancelButtonText: 'No, cancel'
        });

        if (result.isConfirmed) {


            setActionModal(true)


        } else {
            Swal.fire({
                icon: 'info',
                title: 'Submission cancelled',
                text: 'Your recommendation was not submitted.'

            })
        }
    }
    // Filter accordion items based on the action type
    const filteredAccordionItems = (type.actionType === 'conduct_investigation' || type.actionType === 'reconduct_investigation')
        ? accordionItems // Show only item with key '1'
        : accordionItems.filter(item => item.key !== '1'); // Show all items otherwise

    const approveRecommandReport = (data) => {

        approveReport(data, approveComments)

    }
    return (
        <>
            <Modal show={show} onHide={handleClose} size="lg">
                <Modal.Header closeButton>
                    <Modal.Title>
                        {type.actionType === 'conduct_investigation' ? 'Conduct Investigation' :
                            type.actionType === 'reconduct_investigation' ? 'Reconduct Investigation' :
                                type.actionType === 'approve_investigation' ? 'Approve Investigation' : 'Investigation'}
                    </Modal.Title>

                </Modal.Header>
                <Modal.Body>

                    <div className="obs-section mb-2 p-4 pb-2">
                        {/* <h4 className='fw-bold'>Overview</h4> */}

                        <div className="row mb-3">
                            <div className="col-md-6">
                                <p className="obs-title">Incident ID</p>
                                <p className="obs-content mb-3">{applicationDetails.maskId || ''}</p>
                            </div>
                            <div className="col-md-6">
                                <p className="obs-title">Incident Title</p>
                                {!isEditingTitle ? (
                                    <div className="d-flex align-items-center">
                                        <p className="obs-content mb-0">{editedTitle}</p>
                                        <i
                                            className="pi pi-pencil ms-2"
                                            style={{ cursor: 'pointer' }}
                                            onClick={handleTitleEdit}
                                            title="Edit Title"
                                        ></i>
                                    </div>
                                ) : (
                                    <InputGroup className="mb-3">
                                        <FormControl
                                            value={editedTitle}
                                            style={{ height: 47 }}
                                            onChange={(e) => setEditedTitle(e.target.value)}
                                            placeholder="Enter title"
                                        />
                                        <Button variant="success" onClick={handleTitleSave}>
                                            <i className="pi pi-check"></i>
                                        </Button>
                                        <Button variant="secondary" onClick={handleTitleCancel}>
                                            <i className="pi pi-times"></i>
                                        </Button>
                                    </InputGroup>
                                )}
                            </div>
                            <div className="col-md-6">
                                <p className="obs-title">Incident Date & Time</p>
                                <p className="obs-content">
                                    {moment(applicationDetails.incidentDate || '').format("DD-MM-YYYY - HH:MM")}
                                </p>
                            </div>

                            <div className="col-md-6">
                                <p className="obs-title">Location</p>
                                {applicationDetails.isCustomLocation ?
                                    <p className="obs-content">
                                        {applicationDetails.customLocation}
                                    </p>
                                    :
                                    <p className="obs-content">
                                        {applicationDetails.locationOne?.name && (
                                            <>
                                                {applicationDetails.locationOne.name}
                                                {applicationDetails.locationTwo?.name && ' > '}
                                            </>
                                        )}
                                        {applicationDetails.locationTwo?.name && (
                                            <>
                                                {applicationDetails.locationTwo.name}
                                                {applicationDetails.locationThree?.name && ' > '}
                                            </>
                                        )}
                                        {applicationDetails.locationThree?.name && (
                                            <>
                                                {applicationDetails.locationThree.name}
                                                {applicationDetails.locationFour?.name && ' > '}
                                            </>
                                        )}
                                        {applicationDetails.locationFour?.name && applicationDetails.locationFour.name}
                                    </p>
                                }
                            </div>
                            <div className="col-md-6">
                                <p className="obs-title">Investigation Team Leader</p>
                                <p className="obs-content">
                                    {applicationDetails?.investigator?.firstName}
                                </p>
                            </div>
                            <div className="col-md-6">
                                <p className="obs-title">Investigation Assignment Date</p>
                                <p className="obs-content">
                                    {moment(data.created || '').format("DD-MM-YYYY - HH:MM")}
                                </p>
                            </div>

                            <div className="col-md-6">
                                <p className="obs-title">Investigation Team Member(s)</p>
                                {!isEditing ? (
                                    <div className="d-flex align-items-center">
                                        <p className="obs-content">{members.join(', ') || 'No members added'}</p>
                                        <i
                                            className="pi pi-pencil ms-2"
                                            style={{ cursor: 'pointer' }}
                                            onClick={() => setIsEditing(true)}
                                            title="Edit Title"
                                        ></i>

                                    </div>
                                ) : (
                                    <InputGroup className="mb-3">
                                        <FormControl
                                            value={newMembers}
                                            style={{ height: 47 }}
                                            onChange={(e) => setNewMembers(e.target.value)}
                                            placeholder="Enter team members separated by commas"
                                        />
                                        <Button variant="success" onClick={() => {
                                            setMembers(newMembers.split(',').map(name => name.trim()));
                                            setIsEditing(false);
                                            addMemberInvestigation();

                                        }}>
                                            <i className="pi pi-check"></i>
                                        </Button>
                                        <Button variant="secondary" onClick={() => setIsEditing(false)}>
                                            <i className="pi pi-times"></i>
                                        </Button>
                                    </InputGroup>

                                )}
                            </div>

                            {type.actionType === 'reconduct_investigation' && (
                                <div className="col-md-6">
                                    <p className="obs-title">Approver Return Comments</p>
                                    <p className="obs-content">
                                        {type.comments}
                                    </p>
                                </div>
                            )}
                        </div>
                    </div>
                    <div className=" mb-3" style={{ color: 'grey', textAlign: 'justify' }}>As the Lead Investigator, you are responsible for conducting a thorough investigation into the incident. While the background information has already been provided by the Incident Reviewer, the investigation team is encouraged to make any necessary edits based on your findings. At all stages of the investigation, ensure to label and attach relevant evidence, including interview records, photographs, documents, or any other materials that support your analysis. A section is available for you to upload this information at any point throughout the investigation. Please ensure that all aspects of the incident are carefully reviewed and documented.</div>

                    <hr />
                    <Tab.Container >
                        <Nav variant="pills" className="mb-3">
                            {filteredAccordionItems.map((item, index) => (
                                <Nav.Item key={item.key}>
                                    <Nav.Link eventKey={item.key} className={`${item.colorClass} custom-tab mb-3`}>
                                        {(index !== 3 && index !== 4) && <span className="tab-number"> {index + 1}.</span>}{item.title}
                                    </Nav.Link>
                                </Nav.Item>
                            ))}
                        </Nav>

                        <Tab.Content className='p-0' style={{ border: '0px' }}>
                            {filteredAccordionItems.map((item) => (
                                <Tab.Pane eventKey={item.key} key={item.key}>
                                    {item.body}
                                </Tab.Pane>
                            ))}
                        </Tab.Content>
                    </Tab.Container>

                    {/* {(type.actionType === 'conduct_investigation' || type.actionType === 'reconduct_investigation') && (
                        <div className="row mb-2 mt-3 boxShadow p-3">
                            <div className="col-4 mb-2">
                                <label htmlFor="incidentType" className="mb-2">Investigation Approver</label>
                                <Select
                                    options={approve}
                                    value={approve.find(option => option.value === leadAssign)}
                                    onChange={(selectedOption) => setLeadAssign(selectedOption.value)}
                                    placeholder="Select"
                                    isClearable
                                />
                            </div>
                        </div>
                    )} */}


                    {type.actionType === 'approve_investigation' &&

                        <div className="row mb-2  mt-3 boxShadow p-3">
                            <div className="col-12 mb-2">
                                <label htmlFor="incidentType" className="mb-3 fw-bold">Approver Comments</label>
                                <InputTextarea
                                    style={{ width: '100%' }}
                                    rows={2}
                                    name="comments"
                                    value={approveComments}
                                    autoResize
                                    onChange={(e) => setApproveComments(e.target.value)}

                                />
                            </div>
                        </div>
                    }
                </Modal.Body>
                <Modal.Footer>

                    {(type.actionType === 'conduct_investigation' || type.actionType === 'reconduct_investigation') && (
                        leadAssign !==null &&
                        <Button variant="outline-secondary" onClick={submitToApprover} type="button">
                            Submit Report to Approver
                        </Button>
                    )}


                    {type.actionType === 'approve_investigation' && <>
                        <Button variant="outline-primary" onClick={submitToInvesticator} type="button">
                            Return to Investigator
                        </Button>
                        <Button variant="outline-secondary" onClick={submitReport} type="button">
                            Approve Report
                        </Button>
                    </>

                    }
                </Modal.Footer>
            </Modal>
            {actionModal &&
                <ActionAssigneeModal invest={data} show={actionModal} handleClose={() => setActionModal(false)} approveReport={approveRecommandReport} />
            }
        </>
    );
};

export default ControlsFormModal;
