import React from "react";
import Accordion from 'react-bootstrap/Accordion';
import { TabView, TabPanel } from 'primereact/tabview';
import { Checkbox } from 'primereact/checkbox';
import { InputTextarea } from 'primereact/inputtextarea';

const StageTwo = ({ formData, handleAddHazard, hazards, handleDeleteHazard, handleChange, activeTabIndex, setActiveTabIndex, errors, disable }) => {
    return (
        <div>
            <div className="col-10 mb-4">
                <p htmlFor="username" className="mb-2">Describe the hazards that were present or identified at the time of the incident?</p>
                <InputTextarea
                    style={{ width: '100%' }}
                    rows={2}
                    autoResize
                    name="hazardousSituationOfTheIncident"
                    value={formData.hazardousSituationOfTheIncident}
                    onChange={handleChange}
                    disabled={disable}  // Disable based on disable prop
                />
                {errors.hazardousSituationOfTheIncident && <small className="p-error">{errors.hazardousSituationOfTheIncident}</small>}
            </div>
            <Accordion defaultActiveKey="0">
                <Accordion.Item eventKey="0">
                    <Accordion.Header>
                        <h6>Identify the specific hazards from this list.</h6>
                    </Accordion.Header>
                    <Accordion.Body>
                        <div className="d-flex" style={{ border: '1px solid #E5E7EB' }}>
                            <TabView
                                activeIndex={activeTabIndex}
                                onTabChange={(e) => setActiveTabIndex(e.index)}
                                orientation="left"
                                className="d-flex hazTabs"
                            >
                                {hazards.map((haz, h) => (
                                    <TabPanel header={haz.name} className="tabsHead" key={h}>
                                        <div className="row">
                                            {haz.hazards.map((ha, j) => (
                                                <div
                                                    className={`col-4 mb-3 ${formData.hazards.some(hazard => hazard.id === ha.id) ? 'active' : ''}`}
                                                    key={j}
                                                    onClick={() => !disable && handleAddHazard(ha)} // Disable adding hazards if disable is true
                                                >
                                                    <div className="d-flex align-items-center hazClick">
                                                        <div className="col-2">
                                                            {formData.hazards.some(hazard => hazard.id === ha.id) ?
                                                                <Checkbox name="haz" checked disabled={disable} /> :  // Disable checkbox
                                                                <Checkbox name="haz" disabled={disable} />}
                                                        </div>
                                                        <div className="col-2">
                                                            <img
                                                                src={`https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/hazards/${ha.image}`}
                                                                style={{ height: 40 }}
                                                                alt="sample"
                                                            />
                                                        </div>
                                                        <div className="col-8 ms-3">
                                                            <p className="m-0">{ha.name}</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </TabPanel>
                                ))}
                            </TabView>
                        </div>
                    </Accordion.Body>
                </Accordion.Item>
            </Accordion>
            <h6 className="mt-4 mb-3">Hazardous conditions identified or present at the time of incident</h6>
            <div className="row">
                {formData.hazards.map((item, index) => (
                    <div className="col-3 mb-3" key={index}>
                        <div className="d-flex justify-content-between align-items-center p-2" style={{ border: '1px solid #E5E7EB', borderRadius: 8 }}>
                            <img
                                src={`https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/hazards/${item.image}`}
                                style={{ height: 40 }}
                                alt="sample"
                            />
                            <p>{item.name}</p>
                            {!disable && (
                                <i className="pi pi-times" onClick={() => handleDeleteHazard(index)}></i> // Disable delete if disable is true
                            )}
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default StageTwo;
