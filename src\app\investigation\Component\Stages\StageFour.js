import React from 'react';
import { DropzoneArea } from 'material-ui-dropzone';
import Select from 'react-select';
import ImageComponent from '../../../services/FileDownlodS3';
import { InputTextarea } from 'primereact/inputtextarea';

function StageFour({ formData, handleSelectChange, personnel, environment, propertyEquipment, operation, handleChange, handleMainImageUpload, handleRemoveMainImage, errors, disable }) {
    return (
        <div>
            <div className="col-12 mb-2">
                <label htmlFor="incidentDescription" className="mb-2">Description of the incident</label>
                <InputTextarea
                    className="d-flex"
                    id="incidentDescription"
                    name="description"
                    style={{ width: '100%' }}
                    placeholder="Provide a brief factual description of the incident. Do not include reasons or assumptions about what caused it."
                    rows={3}
                    autoResize
                    value={formData.description}
                    onChange={handleChange}
                    disabled={disable} // Disable when disable is true
                />
                {errors.description && <small className="p-error">{errors.description}</small>}
            </div>

            <div className="col-12 mt-3 mb-2">
                <label htmlFor="incidentImages" className="mb-2">Evidences</label>
                <div className="mb-3">
                    <DropzoneArea
                        dropzoneText="Attach supporting images / documents as necessary to provide more information about the incident at this point of time"
                        filesLimit={5}
                        maxFileSize={104857600}
                        showPreviewsInDropzone={false}
                        showPreviews={false}
                        dropzoneClass="dropzoneText d-flex align-items-center justify-content-center"
                        onChange={(files) => handleMainImageUpload(files)}
                        disabled={disable} // Disable file upload when disable is true
                    />
                </div>
            </div>

            <div className="col-12 mt-3 mb-4">
                <label htmlFor="username" className="mb-2">Uploaded</label>
                <div className="image-preview mt-3">
                    {formData.images && formData.images.length > 0 && (
                        <div className="row">
                            {formData.images.map((item, index) => (
                                <div key={index} className="col-3" style={{ position: 'relative' }}>
                                    <div className="boxShadow d-flex align-items-center">
                                        <ImageComponent fileName={item} size="100" name={true}/>
                                        {!disable && (
                                            <i
                                                className="pi pi-trash"
                                                onClick={() => handleRemoveMainImage(index)}
                                                style={{
                                                    position: 'absolute',
                                                    top: '5px',
                                                    right: '5px',
                                                    cursor: 'pointer',
                                                    color: 'red',
                                                }}
                                            />
                                        )}
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            </div>

            <div className="row mb-4">
                <label htmlFor="username" className="mb-2 fw-bold">Immediate apparent consequences of the incident</label>
                <div className="col-3 mb-2">
                    <label htmlFor="incidentType" className="mb-2">Personnel</label>
                    <Select
                        options={personnel}
                        value={personnel.find(option => option.value === formData.personnelId)}
                        onChange={(selectedOption) => handleSelectChange(selectedOption, 'personnelId')}
                        placeholder="Select"
                        isClearable
                        isDisabled={disable} // Disable when disable is true
                    />
                </div>
                <div className="col-3 mb-2">
                    <label htmlFor="incidentType" className="mb-2">Environment</label>
                    <Select
                        options={environment}
                        value={environment.find(option => option.value === formData.environmentId)}
                        onChange={(selectedOption) => handleSelectChange(selectedOption, 'environmentId')}
                        placeholder="Select"
                        isClearable
                        isDisabled={disable} // Disable when disable is true
                    />
                </div>
                <div className="col-3 mb-2">
                    <label htmlFor="incidentType" className="mb-2">Property / Equipment</label>
                    <Select
                        options={propertyEquipment}
                        value={propertyEquipment.find(option => option.value === formData.propertyId)}
                        onChange={(selectedOption) => handleSelectChange(selectedOption, 'propertyId')}
                        placeholder="Select"
                        isClearable
                        isDisabled={disable} // Disable when disable is true
                    />
                </div>
                <div className="col-3 mb-2">
                    <label htmlFor="incidentType" className="mb-2">Operations</label>
                    <Select
                        options={operation}
                        value={operation.find(option => option.value === formData.operationId)}
                        onChange={(selectedOption) => handleSelectChange(selectedOption, 'operationId')}
                        placeholder="Select"
                        isClearable
                        isDisabled={disable} // Disable when disable is true
                    />
                </div>
            </div>

            <div className="col-12 mb-2">
                <label htmlFor="incidentDescription" className="mb-2">Personnel Involved</label>
                <InputTextarea
                    className="d-flex"
                    id="incidentDescription"
                    name="personnelInvolved"
                    style={{ width: '100%' }}
                    placeholder="List the names, roles, and any witnesses involved or affected by the incident."
                    rows={3}
                    autoResize
                    value={formData.personnelInvolved}
                    onChange={handleChange}
                    disabled={disable} // Disable when disable is true
                />
                {errors.description && <small className="p-error">{errors.personnelInvolved}</small>}
            </div>

            <div className="col-12 mb-2">
                <label htmlFor="incidentDescription" className="mb-2">Witnesses</label>
                <InputTextarea
                    className="d-flex"
                    id="incidentDescription"
                    name="witnessInvolved"
                    style={{ width: '100%' }}
                    placeholder="List the names, roles, and any witnesses involved or affected by the incident."
                    rows={3}
                    autoResize
                    value={formData.witnessInvolved}
                    onChange={handleChange}
                    disabled={disable} // Disable when disable is true
                />
                {errors.description && <small className="p-error">{errors.personnelInvolved}</small>}
            </div>


            <div className="col-12 mb-2">
                <label htmlFor="incidentDescription" className="mb-2">Immediate Actions Taken</label>
                <InputTextarea
                    className="d-flex"
                    id="incidentDescription"
                    name="immediateActionsTaken"
                    style={{ width: '100%' }}
                    placeholder="Describe any actions taken immediately after the incident to control or manage the situation."
                    rows={3}
                    autoResize
                    value={formData.immediateActionsTaken}
                    onChange={handleChange}
                    disabled={disable} // Disable when disable is true
                />
                {errors.description && <small className="p-error">{errors.immediateActionsTaken}</small>}
            </div>
        </div>
    );
}

export default StageFour;
