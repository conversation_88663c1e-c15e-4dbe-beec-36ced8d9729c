import React, { useState, useEffect } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { But<PERSON> } from 'primereact/button';
import Swal from 'sweetalert2';
import { Modal, Form, Container } from 'react-bootstrap';
import 'primereact/resources/themes/saga-blue/theme.css';  // Theme
import 'primereact/resources/primereact.min.css';          // Core CSS
import 'primeicons/primeicons.css';                        // PrimeIcons
import API from '../../../services/API';
import { INVERTIGATION_RECOMMENDATION_WITH_ID, RECOMMENDATION_WITH_ID } from '../../../constants'
const CrudTableWithDataTable = ({ invest }) => {
  const [data, setData] = useState([]);
  const [show, setShow] = useState(false);
  const [editIndex, setEditIndex] = useState(null);
  const [formData, setFormData] = useState({
    recommendedActions: '',
    rationaleForRecommendation: '',
  });

  const apiUrl = '';

  // Fetch data from API
  const fetchData = async () => {
    try {
      const response = await API.get(INVERTIGATION_RECOMMENDATION_WITH_ID(invest.id));
      setData(response.data);
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const handleClose = () => setShow(false);
  const handleShow = () => setShow(true);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleAdd = async () => {
    try {
      const response = await API.post(INVERTIGATION_RECOMMENDATION_WITH_ID(invest.id), formData);
      fetchData(); // Refreshes data from the API
      resetForm();
      handleClose();
    } catch (error) {
      console.error('Error adding data:', error);
    }
  }

  const handleEdit = (rowData) => {
    setEditIndex(rowData.id);
    setFormData(rowData);
    handleShow();
  };

  const handleUpdate = async () => {
    try {
      const response = await API.patch(RECOMMENDATION_WITH_ID(editIndex), formData);
      fetchData(); // Refreshes data from the API
      resetForm();
      handleClose();
    } catch (error) {
      console.error('Error updating data:', error);
    }
  };

  const handleDelete = async (rowData) => {
    try {
      await API.delete(RECOMMENDATION_WITH_ID(rowData.id));
      const filteredData = data.filter((item) => item.id !== rowData.id);
      setData(filteredData);
    } catch (error) {
      console.error('Error deleting data:', error);
    }
  };

  const resetForm = () => {
    setFormData({
      recommendedActions: '',
      rationaleForRecommendation: '',
    });
    setEditIndex(null);
  };
  const confirmDelete = (rowData) => {
    Swal.fire({
        title: 'Are you sure?',
        text: 'Do you really want to delete this recommendation?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
        if (result.isConfirmed) {
            handleDelete(rowData);
            Swal.fire('Deleted!', 'Your recommendation has been deleted.', 'success');
        }
    });
};
  // PrimeReact DataTable Action Body Template
  const actionBodyTemplate = (rowData) => {
    return (
      <>
        <Button
          icon="pi pi-pencil"
          className="p-button-rounded p-button-warning p-button-text"
          onClick={() => handleEdit(rowData)}
        />
        <Button
          icon="pi pi-trash"
          className="p-button-rounded p-button-danger p-button-text"
          onClick={() => confirmDelete(rowData)}
        />
      </>
    );
  };

  return (
    <div className="mt-3">

      <div className="d-flex justify-content-end mb-3">
        <Button
          label="Add Recommendation"
          icon="pi pi-plus"
          className="p-button-primary"
          onClick={handleShow}
        />
      </div>

      <DataTable value={data} paginator rows={5} responsiveLayout="scroll">
        <Column field="id" header="S.No" body={(rowData, options) => options.rowIndex + 1} />
        <Column field="recommendedActions" header="Recommended Actions" />
        <Column field="rationaleForRecommendation" header="Rationale for Recommendation" />
        <Column header="Actions" body={actionBodyTemplate} />
      </DataTable>

      {/* Modal for Adding/Editing */}
      <Modal show={show} onHide={handleClose}
       style={{
        backgroundColor: '#f0f8ffbf', // Light yellow for Edit, Light blue for Add
        borderRadius: '8px',
      }}
      >
        <Modal.Header closeButton>
          <Modal.Title>{editIndex !== null ? 'Edit' : 'Add'} Recommendation</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Form.Group className="mb-3">
              <Form.Label>Recommended Actions</Form.Label>
              <Form.Control
                type="text"
                name="recommendedActions"
                value={formData.recommendedActions}
                onChange={handleInputChange}
              />
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Rationale for Recommendation</Form.Label>
              <Form.Control
                type="text"
                name="rationaleForRecommendation"
                value={formData.rationaleForRecommendation}
                onChange={handleInputChange}
              />
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button
            label="Close"
            icon="pi pi-times"
            className="p-button-secondary"
            onClick={handleClose}
          />
          <Button
            label={editIndex !== null ? 'Update' : 'Add'}
            icon="pi pi-check"
            className="p-button-primary"
            onClick={editIndex !== null ? handleUpdate : handleAdd}
          />
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default CrudTableWithDataTable;
