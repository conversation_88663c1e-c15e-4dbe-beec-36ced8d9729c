/* Forms */
.forms-sample {
  label {
    font-size: 0.812rem;
    line-height: 1.4rem;
    vertical-align: top;
    margin-bottom: 0.5rem;
  }
  .form-control {
    margin-bottom: 1.5rem;
  }
}
.form-group {
  margin-bottom: 1.5rem;
}

.input-group-append,
.input-group-prepend {
  color: $input-placeholder-color;
  width: auto;
  border: none;
  .input-group-text {
    border-color: $border-color;
    padding: 1rem 1rem;
    color: $input-placeholder-color;
  }
  button {
    padding-top: .3rem;
    padding-bottom: .3rem;
  }
}

.custom-control {
  .custom-control-label {
    line-height: 1.6;
    margin-bottom: 0;
  }
}

.form-control {
  border: 1px solid $border-color;
  font-size: $input-font-size;
  margin-bottom: 10px;
}

select {
  &.form-control {
    padding: .4375rem .75rem;
    border: 0;
    outline: 1px solid $border-color;
    color: $input-placeholder-color;
    &:focus {
      outline: 1px solid $border-color;
    }
    @each $color,
    $value in $theme-colors {
      &.border-#{$color} {
        outline: 1px solid $value;
        &:focus {
          outline: 1px solid $value;
        }
      }
    }
  }
}

.form-group {
  label {
    font-size: $default-font-size;
    line-height: 1;
    vertical-align: top;
    margin-bottom: .5rem;
  }
  &.has-danger {
    .form-control {
      border-color: $danger;
    }
  }
  .file-upload-default {
    visibility: hidden;
    position: absolute;
  }
  .file-upload-info {
    background: transparent;
  }
}

.form-check-input {
  position: absolute;
  margin-top: 0.2rem;
  margin-left: 0rem;
}
.custom-file {
  .visibility-hidden {
    visibility: hidden;
  }
  .custom-file-label {
    background: $input-bg;;
    border: 1px solid $border-color;
    height: calc(2.25rem + 2px);
    font-weight: normal;
    font-size: 0.875rem;
    padding: 0.625rem 0.6875rem;
    border-radius: 2px;
    &:after {
      background-color: $primary;
      height: auto;
      @extend .btn-primary;
    }
  }
}

.pencil-icon form > a::before {
  visibility: initial;
  content: "\F03EB";
 
  margin-left: 5px;
  font-family: 'Material Design Icons';
}

.pencil-icon form > a {
  visibility: hidden;
  text-decoration: none;
  margin-left: 5px;
}

.pencil-icon form > p {
 font-size: 1rem;
}
