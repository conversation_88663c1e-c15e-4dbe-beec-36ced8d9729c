import React, { useEffect, useRef, useState } from 'react';
import API from '../services/API';
import { ACTION_URL, AIR_WITH_ID_URL, STATIC_URL, RA_ACTION_WITH_ID } from '../constants';

import moment from 'moment';
import { ThemeProvider, createTheme } from "@mui/material";
import MaterialTable from "material-table";
import { FilterMatchMode, FilterOperator } from 'primereact/api';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { InputText } from 'primereact/inputtext';
import { MultiSelect } from 'primereact/multiselect';
import { Calendar } from 'primereact/calendar';
import { Dropdown } from 'primereact/dropdown';
import { Tag } from 'primereact/tag';
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";

import { Modal,Button } from 'react-bootstrap'
import SignatureCanvas from 'react-signature-canvas'
import $ from "jquery";

import { useSelector } from 'react-redux';
import Swal from 'sweetalert2';
Buffer.from("anything", "base64");
window.Buffer = window.Buffer || require("buffer").Buffer;
window.jQuery = $;
// @ts-ignore
window.$ = $;


const customSwal2 = Swal.mixin({
    customClass: {
      confirmButton: 'btn btn-primary',
  
    },
    buttonsStyling: false
  })
const Action = (props) => {
    const user = useSelector((state) => state.login.user)
    console.log(user)
    const signRef = useRef()
    const [actions, setActions] = useState([]);
    const [incidentData, setIncidentData] = useState({});
    const [modalState, setModalState] = useState({ type: null, isOpen: false, actionId: null });
    const [filters, setFilters] = useState({
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        'applicationDetails.maskId': { value: null, matchMode: FilterMatchMode.IN },
        actionType: { value: null, matchMode: FilterMatchMode.IN },
        createdDate: { operator: FilterOperator.AND, constraints: [{ value: null, matchMode: FilterMatchMode.DATE_IS }] },
        'actionSubmittedBy.firstName': { value: null, matchMode: FilterMatchMode.IN },
    });
    const [startDate, setStartDate] = useState(null)
    const [endDate, setEndDate] = useState(null)
    const [Search, setSearch] = useState([])
    const [maskId, setMaskId] = useState([])
    const [names, setNames] = useState([])
    const [dates, setDates] = useState([])
    const [showModal, setShowModal] = useState(false)
    const [showItem, setShowItem] = useState([])
    useEffect(() => {
        getActions();
    }, [modalState.isOpen])

    const getActions = async () => {
        const response = await API.get(ACTION_URL);
        if (response.status === 200) {

            if (props.id) {
                const actions = response.data

                    .filter(i => i.application === 'RA' && (i.objectId === props.id) && i.status !== 'completed')
                    .reverse()
                setActions(actions);
                setSearch(actions)

                const modifiedArray = actions.map(item => ({
                    name: item.applicationDetails.maskId ? item.applicationDetails.maskId : item.applicationDetails.docId,
                    value: item.applicationDetails.maskId ? item.applicationDetails.maskId : item.applicationDetails.docId // This adds the 
                }));
                let pp = modifiedArray.filter((ele, ind) => ind === modifiedArray.findIndex(elem => elem.value === ele.value && elem.name === ele.name))
                setMaskId(pp)
                const name = actions.map(item => ({

                    name: item.actionSubmittedBy.firstName,
                    value: item.actionSubmittedBy.firstName // This adds the 
                }));
                let pp1 = name.filter((ele, ind) => ind === name.findIndex(elem => elem.value === ele.value && elem.name === ele.name))
                setNames(pp1)
                const date = actions.map(item => ({

                    name: item.createdDate,
                    value: item.createdDate  // This adds the 
                }));
                let pp2 = date.filter((ele, ind) => ind === date.findIndex(elem => elem.value === ele.value && elem.name === ele.name))
                setDates(pp2)
            } else {
                const actions = response.data.filter(i => i.application === 'RA' && i.status !== 'completed').reverse()

                setActions(actions);
                setSearch(actions);

                const modifiedArray = actions.map(item => ({
                    name: item.applicationDetails.meetid,
                    value: item.applicationDetails.meetid // This adds the parameter with the value you specified
                }));
                let pp = modifiedArray.filter((ele, ind) => ind === modifiedArray.findIndex(elem => elem.value === ele.value && elem.name === ele.name))
                setMaskId(pp)
                const name = actions.map(item => ({
                    name: item.actionSubmittedBy.firstName,
                    value: item.actionSubmittedBy.firstName // This adds the 
                }));
                let pp1 = name.filter((ele, ind) => ind === name.findIndex(elem => elem.value === ele.value && elem.name === ele.name))
                console.log(pp1)
                setNames(pp1)
                const date = actions.map(item => ({

                    name: item.createdDate,
                    value: item.createdDate  // This adds the 
                }));
                let pp2 = date.filter((ele, ind) => ind === date.findIndex(elem => elem.value === ele.value && elem.name === ele.name))
                setDates(pp2)
            }

        }
    }

    const getSubmittedBy = (action) => {
        return action.actionSubmittedBy?.firstName || '';
    };


    const openActionCard = async (action) => {
        // await getReportIncident(action.objectId, action.id);
        // setModalState({ type: action.actionType, isOpen: true, actionId: action.id });
        setShowItem(action);
        setShowModal(true)
    };



  

    

   

    
    const renderHeader = () => {
        const value = filters['global'] ? filters['global'].value : '';

        return (
            <div className='d-flex justify-content-end'>
               
                {/* <span className="p-input-icon-left">
                    <i className="fa fa-search" />
                    <InputText type="search" value={value || ''} onChange={(e) => onGlobalFilterChange(e)} placeholder="Global Search" />
                </span> */}
            </div>
        );
    };

    const header = renderHeader();
    const onGlobalFilterChange = (event) => {
        const value = event.target.value;
        let _filters = { ...filters };

        _filters['global'].value = value;

        setFilters(_filters);
    };
    const descBodyTemplate = (row) => {
        return 'Confirm my participation in this Risk Assessment as a team member';
    }
    const nameBodyTemplate = (row) => {
        return String(row.actionSubmittedBy.firstName);
    }
    const idBodyTemplate = (row) => {
        return <div className='maskid' onClick={() => openActionCard(row)}><span className='pending'></span>{row.applicationDetails.meetid}</div>;
    }
    const submitBodyTemplate = (row) => {
        return getSubmittedBy(row);
    }
    const maskIdFilterTemplate = (options) => {

        return (
            <React.Fragment>
                <div className="mb-3 font-bold">ID Picker</div>
                <MultiSelect value={options.value} options={maskId} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }
    const descFilterTemplate = (options) => {
        return (
            <div className='d-flex justify-content-end'>
                <span className="p-input-icon-left">
                    <i className="fa fa-search" />
                    <InputText type="search" value={options.value} onChange={(e) => options.filterCallback(e.value)} placeholder="Search" />
                </span>
            </div>
        )
    }
    const representativesItemTemplate = (option) => {
        return (
            <div className="flex align-items-center gap-2">

                <span>{option.value}</span>
            </div>
        );
    };
    const nameFilterTemplate = (options) => {
        console.log(options)
        return (
            <React.Fragment>
                <div className="mb-3 font-bold">Name</div>
                <MultiSelect value={options.value} options={names} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }
    const dateFilterTemplate = (options) => {

        // return (

        //     <React.Fragment>
        //         <div className="mb-3 font-bold">Date</div>
        //         <MultiSelect value={options.value} options={dates} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
        //     </React.Fragment>
        // );
        return <Calendar value={options.value} onChange={(e) => options.filterCallback(e.value, options.index)} dateFormat="mm/dd/yy" placeholder="mm/dd/yyyy" mask="99/99/9999" />;
    }

    const dataURItoFile = (dataURI, filename) => {
        var byteString = atob(dataURI.split(",")[1]);
        // separate out the mime component
        var mimeString = dataURI.split(",")[0].split(":")[1].split(";")[0];
        // write the bytes of the string to an ArrayBuffer
        var ab = new ArrayBuffer(byteString.length);
        var dw = new DataView(ab);
        for (var i = 0; i < byteString.length; i++) {
          dw.setUint8(i, byteString.charCodeAt(i));
        }
    
        // write the ArrayBuffer to a blob, and you're done
        return new File([ab], filename, { type: mimeString });
      };

//     const onConfirm = async () => {

        
//         const filename = new Date().getTime() + "member_sign.png";

//         if (!signRef.current.isEmpty()) {
//           const ReactS3Client = new S3(config);
    
//           await ReactS3Client.uploadFile(
//             dataURItoFile(
//               signRef.current.getTrimmedCanvas().toDataURL("image/png"),
//               filename
//             ),
//             "uploads/risk_sign/" + filename
//           )
//             .then((data) => console.log(data))
//             .catch((err) => console.error(err));
//         }else {
//             customSwal2.fire(
//                 'Please Sign ..!',
//                 '',
//                 'warning'
//               )
//         }

     


//         const updatedCartItems = showItem.applicationDetails.teamMemberInvolved.map(item => {
//             if (item.id === user.id) {
//               return { ...item, sign: filename, date: moment().format('YYYY-MM-DD HH:mm') }; // Increment quantity
//             }
//             return item;
//           });
      
//     //   updatedCartItems
      
//     if (!signRef.current.isEmpty()) {

//         const response = await API.patch(RA_ACTION_WITH_ID(showItem.applicationDetails.id), {
//             actionId: showItem.id,
//             teamMemberInvolved:updatedCartItems,

//         })
//         if (response.status === 204) {
//             customSwal2.fire(
//                 'Risk Assessment Updated!',
//                 '',
//                 'success'
//               )
//               getActions();
//               setShowModal(false)
             
//     }
// }

//     }

    return (

        <>


            <DataTable value={actions} paginator rows={10} globalFilterFields={["applicationDetails.maskId", "createdDate", "actionSubmittedBy.firstName"]} header={header} filters={filters} onFilter={(e) => { if (e.filters['actionSubmittedBy.firstName'] !== undefined) { e.filters['actionSubmittedBy.firstName'].matchMode = 'in'; setFilters(e.filters) } else { setFilters(e.filters) } }}
                emptyMessage="No Data found." tableStyle={{ minWidth: '50rem' }}>

                <Column field="applicationDetails.meetid" header="ID" body={idBodyTemplate} sortable style={{ width: '25%' }} filterElement={maskIdFilterTemplate} ></Column>

                <Column field='actionType' header="Description" body={descBodyTemplate} style={{ width: '25%' }}></Column>

                <Column field="createdDate" header="Submitted On" sortable style={{ width: '25%' }}></Column>

                <Column field="actionSubmittedBy.firstName" header="Submitted By" filter showFilterMatchModes={false} filterElement={nameFilterTemplate} sortable filterPlaceholder="Search" style={{ width: '25%' }}></Column>

            </DataTable>
            <Modal
                show={showModal}
                size="md"
                onHide={() => setShowModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
                backdrop="static"
            >
                <Modal.Header>

                    <div className="w-100 d-flex justify-content-between align-items-center">
                        <h2 className="m-0"> Please Confirm</h2>
                        <div className="text-center mt-0">

                        </div>
                    </div>

                </Modal.Header>

                <Modal.Body>
                    <div className="row">
                        <div className={`animate-col-md-12 text-center`}>
                            <p>I confirm my participation in this Risk Assessment as a team member. The outcome reflects our shared professional judgment to the best of our abilities through consensus.</p>
                            <SignatureCanvas
                                penColor="#1F3BB3"
                                canvasProps={{
                                    width: 350,
                                    height: 100,
                                    className: "sigCanvas",
                                    style: {
                                        boxShadow: "0px 0px 10px 3px rgb(189 189 189)",
                                    },
                                }}
                                ref={signRef}


                            />  <i className="fa fa-undo undo" onClick={() => signRef.current.clear()}></i>
                        </div>
                    </div>
                </Modal.Body>
                <Modal.Footer className="flex-wrap">



                    {/* <Button onClick={onConfirm}>Done</Button> */}
                    <Button variant="light" onClick={() => { setShowModal(false); }}>Close</Button>

                </Modal.Footer>
            </Modal>
            {/* {renderModal()} */}
        </>
    );


}

export default Action;
