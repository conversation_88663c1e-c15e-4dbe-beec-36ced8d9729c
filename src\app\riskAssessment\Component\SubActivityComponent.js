import React, { useState } from 'react';
import { InputText } from 'primereact/inputtext';
import { DropzoneArea } from 'material-ui-dropzone';
import ImageComponent from '../../services/FileDownlodS3';
import API from '../../services/API';
import { FILE_URL } from '../../constants';

const SubActivityComponent = ({ item, onSave}) => {
    const [isEditing, setIsEditing] = useState(false); // Manage edit mode
    const [activityName, setActivityName] = useState(item[0].name); // Track activity name changes
    const [uploadedImages, setUploadedImages] = useState(item[0].images || []);

    const toggleEditMode = () => {
        setIsEditing(!isEditing);
    };

    const saveChanges = () => {
        onSave(activityName, uploadedImages); // Pass name and images to the parent
        setIsEditing(false);
    };

    const cancelChanges = () => {
        setActivityName(item[0].name); // Reset to original name
        setUploadedImages(item[0].images); // Reset to original images
        setIsEditing(false);
    };
    const handleActivityImage = async (files) => {
        if (files.length > 0) {
            const latestFile = files[files.length - 1];
            const formData1 = new FormData();
            formData1.append('file', latestFile);

            try {
                const response = await API.post(FILE_URL, formData1, {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                    }
                });

                if (response && response.status === 200) {
                    const newImageName = response.data.files[0].originalname;

                    // Update the task array and the uploaded images state
                    setUploadedImages((prevImages) => [...prevImages, newImageName]); // Update images in local state
                }
            } catch (error) {
                console.error("File upload error: ", error);
            }
        }
    };
    return (
        <div className='p-4 mb-4 col-12'>
            <div className='col-12'>
                <div className="d-flex align-items-center col-12">
                    {!isEditing ? (
                        <>
                            <h4 className='me-2'>{activityName}</h4>
                            <i
                                className="pi pi-pencil"
                                style={{ cursor: 'pointer' }}
                                onClick={toggleEditMode}
                            />
                        </>
                    ) : (
                        <div className="d-flex flex-column col-12">
                            <label htmlFor="username" className='mb-2'>Sub-Activity Name</label>
                            <InputText
                                value={activityName}
                                onChange={(e) => setActivityName(e.target.value)}
                            />
                        </div>
                    )}
                </div>
            </div>

            {isEditing ? (
                <>
                    <div className='col-12 mt-3'>
                        <label htmlFor="username" className='mb-2'>Image Uploads</label>
                        <div className="mb-3">
                            <DropzoneArea
                                acceptedFiles={['image/jpeg', 'image/png']}
                                dropzoneText={"Drag 'n' drop some files here, or click to select files"}
                                filesLimit={5}
                                maxFileSize={104857600}
                                onChange={(files) => handleActivityImage(files)}
                                showPreviewsInDropzone={false}
                                showPreviews={false}
                                dropzoneClass={'dropzoneText d-flex align-items-center justify-content-center '}
                            />
                        </div>
                    </div>

                    <div className='col-12 mt-3'>
                        <label htmlFor="username" className='mb-2'>Uploaded</label>
                    </div>

                    <div className='col-12 mt-3'>
                        <div className='row'>
                            {uploadedImages && uploadedImages.map((image, m) => (
                                <div key={m} className="col-3" style={{ position: 'relative' }}>
                                    <div className="boxShadow d-flex align-items-center justify-content-center" >
                                        <ImageComponent fileName={image} size={'100'} name={true}/>
                                        <i
                                            className="pi pi-trash"
                                            onClick={() => setUploadedImages(uploadedImages.filter((_, index) => index !== m))}
                                            style={{
                                                position: 'absolute',
                                                top: '5px',
                                                right: '5px',
                                                cursor: 'pointer',
                                                color: 'red',
                                            }}
                                        />
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>

                    <div className="col-12 mt-3">
                        <button className="btn btn-primary me-2" onClick={saveChanges}>Save</button>
                        <button className="btn btn-secondary" onClick={cancelChanges}>Cancel</button>
                    </div>
                </>
            ) : null}
        </div>
    );
};

export default SubActivityComponent;
