import React, { useState } from 'react';
import ControlOfContractorForm from './ControlOfContractorForm';
import SuspendedLoadForm from './SuspendedLoadForm';
import WorkAtHeightForm from './WorkAtHeightForm';
import TransportForm from './TransportForm';

const FatalForm = () => {
    const [selectedCategory, setSelectedCategory] = useState('');

    // Function to handle the change event of the dropdown
    const handleSelect = (event) => {
        setSelectedCategory(event.target.value);
    };
    const renderForm = () => {
        switch (selectedCategory) {
            case 'controlOfContractor':
                return <ControlOfContractorForm />;
            case 'suspendedLoad':
                return <SuspendedLoadForm />;
            case 'workAtHeight':
                return <WorkAtHeightForm />;
            case 'transport':
                return <TransportForm />;
            default:
                return null;
        }
    };

    return (<>
        <div>
            <select className="form-select" aria-label="Default select example" onChange={handleSelect}>
                <option selected>Select Category</option>
                <option value="controlOfContractor">Control of Contractor</option>
                <option value="suspendedLoad">Suspended Load</option>
                <option value="workAtHeight">Work at Height</option>
                <option value="transport">Transport</option>
            </select>
            {renderForm()}
        </div>
    </>)
}

export default FatalForm;