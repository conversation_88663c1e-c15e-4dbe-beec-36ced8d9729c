import React, { useRef } from 'react';
import JoditEditor from 'jodit-react';

const JoditEditorComponent = ({ value, onChange }) => {
  const editor = useRef(null);
  
  const config = {
    readonly: false, // Set to true if you want to disable editing
    toolbarSticky: false, // Prevents the toolbar from staying sticky
    minHeight: 300,
    buttons: [
      'bold', 'italic', 'underline', 'strikethrough', '|',
      'ul', 'ol', '|',
      'outdent', 'indent', '|',
      'link', '|',
      'align', '|',
      'undo', 'redo', '|',
      'hr', '|',
      'eraser', 'fullsize',
      'fontsize', 'font', 'brush', 'paragraph', 'table'
    ], // Ensure all necessary buttons are explicitly added
    toolbarAdaptive: false, // Set to false to disable adaptive toolbar (ensures all tools are always visible)
    toolbarStickyOffset: 0, // Adjust as needed if you want toolbar to be fully visible at the top
    showPoweredBy: false,
  };

  return (
    <div>
      <JoditEditor
        ref={editor}
        value={value}
        config={config}
        tabIndex={1}
        onBlur={(newContent) => onChange(newContent)} 
        onChange={(newContent) => {}}
      />
    </div>
  );
};

export default JoditEditorComponent;
