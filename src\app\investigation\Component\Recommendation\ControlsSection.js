import React from 'react';
import ControlItem from './ControlItem';

const ControlsSection = ({
    controlsData,
    title,
    type,
    startJobFactorIndex,
    startOrganizationalFactorIndex,

}) => {
    const titleParts = title.split("significant");

    // Initialize counters
    let jobCounter = startJobFactorIndex;
    let orgCounter = startOrganizationalFactorIndex;

    return (
        <div className='boxShadow p-4 mb-4'>
            <h5 className='mb-3 fw-bold'>
                {titleParts[0]}
                <span style={{
                    display: 'inline-block',
                    padding: '3px 8px',
                    borderRadius: '5px',
                    backgroundColor: '#ff07073d',
                    color: 'red',
                    border: `1px solid red`,
                    fontWeight: '400',

                }}>significant</span>
                {titleParts[1]}
            </h5>
            {controlsData.map((control, index) => {
                const startJobFactorIndexForControl = jobCounter;
                const startOrganizationalFactorIndexForControl = orgCounter;

                // Count significant job factors
                const significantJobFactorsCount = control.jobFactors ? control.jobFactors.filter(
                    factor => factor.extentOfContribution === 'Significant'
                ).length : 0;

                // Count significant organizational factors
                const significantOrganizationalFactorsCount = control.relatedOrganizationalFactors ? control.relatedOrganizationalFactors.filter(
                    factor => factor.extentOfContribution === 'Significant'
                ).length : 0;

                // Update counters
                jobCounter += significantJobFactorsCount;
                orgCounter += significantOrganizationalFactorsCount;

                // Only render ControlItem if there are significant factors in the current control
                const hasSignificantJobOrOrgFactors = significantJobFactorsCount > 0 || significantOrganizationalFactorsCount > 0;

                return (
                    <ul key={index}>
                        {hasSignificantJobOrOrgFactors &&
                            <ControlItem
                                control={control}
                                itemIndex={index}
                                title={title}
                                type={type}
                                startJobFactorIndex={startJobFactorIndexForControl}
                                startOrganizationalFactorIndex={startOrganizationalFactorIndexForControl}
                            />
                        }
                    </ul>
                );
            })}
        </div>
    );
};

export default ControlsSection;
