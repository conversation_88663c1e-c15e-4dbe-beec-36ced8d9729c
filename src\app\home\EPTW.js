import React, { useState, useEffect } from 'react';
import 'primereact/resources/themes/saga-blue/theme.css';  //theme
import 'primereact/resources/primereact.min.css';          //core css
import 'primeicons/primeicons.css';                        //icons
import { Row, Col, Card } from 'react-bootstrap';
import { LineChart, Line, XAxis, YAxis, Tooltip } from 'recharts';
import API from '../services/API';
import { PERMIT_REPORTS } from '../constants';
import Permit from '../eptw-gen/Permit';

function EPTW({ formData, fromDate, toDate }) {
  const [activeTab, setActiveTab] = useState('');
  const [permitData, setPermitData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [tabCounts, setTabCounts] = useState({});

  // Define your tab definitions in one place
  const tabs = [
    { key: 'Active', label: 'Active Permit' },
    { key: 'Pending Approval', label: 'Pending Approval' },
    { key: 'Pending Assessment', label: 'Pending Assessment' },
    { key: 'Pending Review', label: 'Pending Review' },
    { key: 'Archived', label: 'Archived Permit' },
  ];

  useEffect(() => {
    fetchIncident();
  }, []);

  const fetchIncident = async () => {
    try {
      const uriString = {
        include: [
          { relation: "locationOne" },
          { relation: "locationTwo" },
          { relation: "locationThree" },
          { relation: "locationFour" },
          { relation: "locationFive" },
          { relation: "locationSix" },
          { relation: "applicant" },
          { relation: "assessor" },
          { relation: "approver" },
          { relation: "reviewer" },
        ]
      };
      const url = `${PERMIT_REPORTS}?filter=${encodeURIComponent(
        JSON.stringify(uriString)
      )}`;

      const response = await API.get(url);
      if (response.status === 200) {
        const data = response.data;
        setPermitData(data);
      } else {
        console.error('Unexpected response status:', response.status);
      }
    } catch (error) {
      console.error('Error fetching incidents:', error);
    }
  };

  /**
   * Recompute filtered data and tab counts whenever:
   * - The user changes filters (location, date),
   * - The underlying data changes (permitData),
   * - The user changes activeTab.
   */
  useEffect(() => {
    // 1) Filter by location + date
    const locationDateFiltered = permitData.filter(item => {
      // Check location filters
      const matchesLocation = Object.keys(formData).every((key) =>
        formData[key] ? item[key] === formData[key] : true
      );

      // Check date filters
      const createdDate = new Date(item.created);
      const isAfterFromDate = !fromDate || createdDate >= new Date(fromDate);
      const isBeforeToDate = !toDate || createdDate <= new Date(toDate);

      return matchesLocation && isAfterFromDate && isBeforeToDate;
    });

    // 2) Recompute new counts for each tab from location+date filtered data
    const newTabCounts = tabs.reduce((acc, tab) => {
      acc[tab.key] = locationDateFiltered.filter(
        (item) => item.status === tab.key
      ).length;
      return acc;
    }, {});

    // 3) If there is an activeTab, filter further
    let finalFilteredData = locationDateFiltered;
    if (activeTab) {
      finalFilteredData = finalFilteredData.filter(
        (item) => item.status === activeTab
      );
    }

    // 4) Update state
    setTabCounts(newTabCounts);
    setFilteredData(finalFilteredData);
  }, [permitData, formData, fromDate, toDate, activeTab]);

  const getCardClass = (tabName) => {
    return activeTab === tabName ? 'active-card' : '';
  };

  return (
    <>
      <Row className="mb-4">
        {tabs.map(tab => (
          <Col key={tab.key}>
            <Card
              className={`h-100 card-border ${getCardClass(tab.key)}`}
              onClick={() => setActiveTab(tab.key)}
            >
              <Card.Body className="d-flex flex-column">
                <div
                  className="d-flex justify-content-between"
                  style={{ flexGrow: 1 }}
                >
                  <div>
                    <div
                      style={{
                        marginTop: '10px',
                        fontSize: '26px',
                        fontWeight: 'bold'
                      }}
                    >
                      {tabCounts[tab.key] || 0}
                    </div>
                    <div
                      style={{
                        color: 'black',
                        fontWeight: 'normal',
                        fontSize: '16px'
                      }}
                    >
                      {tab.label}
                    </div>
                  </div>
                </div>
              </Card.Body>
            </Card>
          </Col>
        ))}
      </Row>

      <Row className="mb-4">
        {activeTab && (
          <Permit
            data={filteredData}
            onFilterUpdate={(count) => console.log(count, 'incidents')}
            from={'Dash'}
          />
        )}
      </Row>
    </>
  );
}

export default EPTW;
