import React, { useState, useEffect, useMemo } from 'react';
// import moment from 'moment';
import { USERS_URL, STATIC_URL } from '../../../constants';
import API from '../../../services/API';
import { Accordion, AccordionTab } from 'primereact/accordion';
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";
import Typography from '@mui/material/Typography'
import moment from 'moment-timezone';
import ImageComponent from '../../../services/FileDownlodS3';
const customFontStyle = {
    fontFamily: 'Lato, sans-serif',

};
const ActionTable = ({ id, actions, current }) => {



    const [users, setUsers] = useState([]);
    const [totalAction, setTotalAction] = useState([])

    let cm = 0.0;
    let doc = 0.0;
    let picm = 0.0;
    let nc = 0.0

    let rass = 0.0;
    let cmss = 0.0;
    let poss = 0.0
    let ncs = 0.0

    useEffect(() => {
        getAllUsers();

        setTotalAction(actions); // Log totalActions for debugging or potential use
    }, []);


    const convertToLocalTime = (gmtDate) => {
        // Get the system's time zone
        const systemTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

        // Parse the GMT date string into a moment object with the given format and convert to local time
        const localDate = moment.tz(gmtDate, 'DD-MM-YYYY HH:mm', 'GMT').tz(systemTimeZone).format('Do MMM YYYY, hh:mm')
        return localDate;
    };


    const getAllUsers = async () => {
        const response = await API.get(USERS_URL);
        setUsers(response.data);
    };

    const getName = (id) => {
        const user = users.find(user => user.id === id);
        return user?.firstName || '';
    };

    const getActioNumber = (action, index) => {

        if (index === 'cm') {

            cmss = cmss + 1.0
            let status = ''
            action.data.map(item => {
                if (item.actionType === 'perform_task') {
                    if (item.prefix === 'PICM-TASK') {
                        status = 'PICM ' + cmss.toFixed(1)
                    } else {
                        status = 'CM ' + cmss.toFixed(1)
                    }

                } else if (item.actionType === 'perform_task') {
                    if (item.prefix === 'PICM-TASK') {
                        status = 'PICM ' + (cmss + 0.1)
                    }else{
                        status = 'CM ' + cmss.toFixed(1)
                    }
                }
            })
            return status
        }


    }
    const getStatusAction = (action, index) => {

        let status = '';
        let ras = false;
        let cms = false;
        let pos = false;
        let ncs = false


        if (action.firstActionType === 'perform_task') {

            switch (action.lastActionType) {
                case 'perform_task':
                    status = ' Assigned'

                    break;
                case 'reperform_task':
                    status = 'Re- Assigned'

                    break
                case 'verify_task':
                    if (action.lastStatus === 'Completed') {
                        status = 'Verified & Closed'
                    } else {
                        status = 'Implemented - Pending Verification'
                    }

                    break

                case 'approve':
                    status = 'Verified & Closed'
                    break
                default:

                    break;
            }

            cms = true
        }
        // else if (action.firstActionType === 'take_actions_ra') {
        //     switch (action.lastActionType) {
        //         case 'take_actions_ra':
        //             status = 'RA / SWP changes assigned'
        //             break;
        //         case 'retake_actions':
        //             status = 'RA / SWP Re-changes assigned'
        //             break
        //         case 'verify_actions':
        //             status = 'RA / SWP chages done - Pending Verification'
        //             break

        //         case 'approve':
        //             status = 'RA / SWP changes: Verified & Closed'
        //             break
        //     }
        //     ras = true;
        // }
        // else if (action.firstActionType === 'take_actions_control_post') {
        //     switch (action.lastActionType) {
        //         case 'take_actions_control_post':
        //             status = 'Post Investigation Control Measures Assigned'
        //             break;
        //         case 'retake_actions':
        //             status = 'Re-Post Investigation Control Measures Assigned'
        //             break
        //         case 'verify_actions':
        //             status = 'Post Investigation Control Measures - Pending Verification'
        //             break
        //         case 'approve':
        //             status = 'Post Investigation Control Measure: Verified & Closed'
        //             break
        //     }
        //     pos = true
        // }
        // else if (action.firstActionType === 'audit_take_actions') {
        //     switch (action.lastActionType) {
        //         case 'audit_take_actions':
        //             status = 'Audit Action Assigned'
        //             break;
        //         case 'aud_retake_actions':
        //             status = 'Re-Audit Action Assigned'
        //             break
        //         case 'aud_verify_actions':
        //             status = 'Audit Action - Pending Verification'
        //             break
        //         case 'approve':
        //             status = 'Audit Action: Verified & Closed'
        //             break
        //     }
        //     cms = true
        // }

        return (

            <Typography variant="body1" style={customFontStyle}>
                <div className='row d-flex mb-2'>

                    <div className='col-6'>

                        <h4>{getActioNumber(action, index)}</h4>


                    </div>
                    <div className='col-6'>
                        <span className={`badge fw-bold ${ras ? 'status-tag-orange' : cms ? 'status-tag-blue' : pos ? 'status-tag-pink' : ''}`}>
                            {status}
                        </span>
                    </div>
                </div>
                <div className='row d-flex' >

                    <div className='col-6'>


                        {showUserByStatus(action)}


                    </div>
                    <div className='col-6'>
                        <h6>DueDate : {moment(action.data[0].dueDate).format('Do MMM YYYY')}</h6>
                    </div>
                </div>

            </Typography>
        )


    }
    const showUserByStatus = (action) => {


        switch (action.lastActionType) {
            case "ins_take_actions_control":
            case "take_actions_ra":
            case "audit_take_actions":
            case "perform_task":
                return `Action Assignee : ${getName(action.lastAssignedToId[0])}`;
            case "verify_task":
                return `Action to be verified by : ${getName(action.lastAssignedToId[0])}`;
            case "aud_verify_actions":
                return `Action to be verified by : ${getName(action.lastAssignedToId[0])}`;
            case "reperform_task":
                return `Action Re-assigned to : ${getName(action.lastAssignedToId[0])}`;
            case "aud_retake_actions":
                return `Action Re-assigned to : ${getName(action.lastAssignedToId[0])}`;
            case "approve":
                return `Action Verified By : ${getName(action.lastAssignedToId[0])}`;
            default:
                return ''; // Handle default case if needed
        }
    };


    const getCMData = (data, cm, index) => {

        return data.data.map((item, index) => {
            if (item.actionType === "perform_task") {
                // cm = cm + 1.0

                return (
                    <div className="obs-section p-4">
                        <div key={index} className="row ">
                            <div className='col-8'>
                                <p className="obs-title"> Assigned Action - {item.prefix === 'PICM-TASK' ? 'PICM' : 'CM'} {cm.toFixed(1)} </p>
                                <p className="obs-content">{item.actionToBeTaken
                                }</p>
                            </div>

                            {item.status === 'Initiated' && <>
                                <p className="obs-title"> Action Assignee</p>
                                <p className="obs-content">{item.assignedToId &&
                                    getName(item.assignedToId[0]
                                    )}</p>
                            </>}
                            {item.status === 'Completed' && <>
                                <div className="row mb-3">
                                    <div className="col-md-12">
                                        <p className="obs-title">Action Taken </p>
                                        <p className="obs-content">{item.actionTaken}</p>
                                    </div>
                                </div>

                                <div className="row mb-3">
                                    <div className="col-md-6">
                                        <p className="obs-title">Action Taken By</p>
                                        <p className="obs-content">{item.assignedToId &&
                                            getName(item.assignedToId[0]
                                            )}</p>
                                    </div>
                                    <div className="col-md-6 ps-4">
                                        <p className="obs-title">Date</p>
                                        <p className="obs-content">{moment(item.created).format('DD-MM-YYYY')}</p>
                                    </div>
                                </div>

                            </>
                            }
                        </div>
                        <div className='row'>

                            {item.status === "Completed" &&
                                <div className="col-md-12">
                                    {item.uploads && item.uploads.length > 0 && (
                                        <div className='d-flex'>
                                            <div className='col-12'>
                                                <p className="obs-title">Evidence</p>
                                                <div className='row'>
                                                    {item.uploads.map((upload) => {
                                                        return (
                                                            <div className="col-3  " style={{ position: 'relative' }}>
                                                                <div className="boxShadow d-flex align-items-center justify-content-center" >
                                                                    <ImageComponent fileName={upload} size={'100'} name={true} />

                                                                </div>
                                                            </div>
                                                        )
                                                    })}
                                                </div>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            }
                        </div>
                    </div>

                );

            } else if (item.actionType === "reperform_task") {

                cm = cm + 0.1

                return (

                    <div className="obs-section p-4">
                        <div className='row'>
                            <div className='col-6'>
                                <p className="obs-title"> Action Verifier Comments & Reassigned Action  - {item.prefix === 'PICM-TASK' ? 'PICM' : 'CM'} {cm} </p>
                                <p className="obs-content">{item.comments}</p>
                                {item.status !== 'Initiated' && <>
                                    <p className="obs-title"> Action Taken </p>
                                    <p className="obs-content">{item.actionTaken}</p>
                                </>}
                                {item.status === 'Initiated' ?
                                    <p className="obs-title"> Action Assignee</p>
                                    : <p className="obs-title"> Action Taken By</p>}
                                <p className="obs-content">{item.assignedToId &&
                                    getName(item.assignedToId[0]
                                    )}</p>
                            </div>
                            <div className='col-6'>
                                <p className="obs-title">Date</p>
                                <p className="obs-content">{moment(item.created).format('DD-MM-YYYY')}</p>
                            </div>
                        </div>
                        {item.status === "Completed" &&
                            <div className="col-md-12">
                                {item.uploads && item.uploads.length > 0 && (
                                    <div className='d-flex'>
                                        <div className='col-12'>
                                            <p className="obs-title">Evidence</p>
                                            <div className='row'>
                                                {item.uploads.map((upload) => {
                                                    return (
                                                        <div className="col-3  " style={{ position: 'relative' }}>
                                                            <div className="boxShadow d-flex align-items-center justify-content-center" >
                                                                <ImageComponent fileName={upload} size={'100'} name={true} />

                                                            </div>
                                                        </div>
                                                    )
                                                })}
                                            </div>
                                        </div>
                                    </div>
                                )}
                            </div>
                        }
                    </div>
                );
            } else if (item.actionType === "verify_task") {


                return (
                    <div className="obs-section p-4">


                        <div className="row mb-3">
                            <div className="col-md-6">
                                <p className="obs-title">{item.status === 'Completed' ? 'Action Verified By' : 'Action Verifier'}</p>
                                <p className="obs-content">{item.assignedToId &&
                                    getName(item.assignedToId[0]
                                    )}</p>
                            </div>
                            {item.status === 'Completed' &&
                                <div className="col-md-6">
                                    <p className="obs-title">Date</p>
                                    <p className="obs-content">{moment(item.created).format('DD-MM-YYYY')}</p>
                                </div>
                            }
                        </div>
                        {item.status === 'Completed' &&
                            <div className="row mb-3">
                                <div className="col-md-6">
                                    <p className="obs-title">Action Verifier Comments</p>
                                    <p className="obs-content">{item.comments}</p>
                                </div>
                            </div>
                        }



                    </div>
                );
            } else if (item.actionType === "approve" && item.status === 'submitted') {


                return (
                    <div className="obs-section p-4">


                        <div className="row mb-3">
                            <div className="col-md-6">
                                <p className="obs-title">Action Verified By</p>
                                <p className="obs-content">{item.assignedToId &&
                                    getName(item.assignedToId
                                    )}</p>
                            </div>
                            <div className="col-md-6">
                                <p className="obs-title">Date</p>
                                <p className="obs-content">{convertToLocalTime(item.createdDate)}</p>
                            </div>
                        </div>
                        <div className="row mb-3">
                            <div className="col-md-6">
                                <p className="obs-title">Action Verifier Comments</p>
                                <p className="obs-content">{item.comments}</p>
                            </div>
                        </div>



                    </div>
                );
            } else if (item.actionType === 'reject' && item.status === 'submitted') {

                return (
                    <div className="obs-section p-4">
                        <div className='row'>

                            <div className="col-md-6">
                                <p className="obs-title">Action Verified By</p>
                                <p className="obs-content">{item.assignedToId &&
                                    getName(item.assignedToId
                                    )}</p>
                            </div>
                            <div className="col-md-6">
                                <p className="obs-title">Date</p>
                                <p className="obs-content">{convertToLocalTime(item.createdDate)}</p>
                            </div>
                        </div>
                    </div>
                )
            }
            return null; // Handle other cases if necessary
        });


    };



    const processedActions = useMemo(() => {
        let cmCounter = 0.0;
        return totalAction
            .map(action => {
                if (action.firstActionType === 'perform_task') {
                    cmCounter += 1.0;
                    return { ...action, cm: cmCounter };
                }
                return null;
            })
            .filter(action => action !== null);
    }, [totalAction]);
    return (<>
        <h4 className='mb-3 fw-bold'>{id} Actions</h4>


        <Accordion>

            {processedActions.map((action, index) => (

                action.firstActionType === 'perform_task' && (
                    <AccordionTab header={getStatusAction(action, 'cm')}>
                        <>{getCMData(action, action.cm, index)}</>

                    </AccordionTab>
                )

            ))
            }
        </Accordion >
    </>
    );
};

export default ActionTable;
