import React, { useState, useEffect, useCallback } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { MultiSelect } from 'primereact/multiselect';
import { But<PERSON> } from 'primereact/button';
import { Dialog } from 'primereact/dialog';
import { Modal, Form } from 'react-bootstrap';
import { ADMINDROPDOWNS } from '../constants';
import API from '../services/API';
import Select from 'react-select';
import 'primereact/resources/primereact.css';
import 'primeicons/primeicons.css';
import 'primereact/resources/themes/saga-blue/theme.css';
import Swal from 'sweetalert2';
import { useHistory } from 'react-router-dom/cjs/react-router-dom.min';
import { FilterMatchMode } from 'primereact/api';

const ChecklistLibrary = ({ checklists, fetchChecklistData }) => {
    // const [checklists, setChecklists] = useState([]);
    const history = useHistory();
    const [filters, setFilters] = useState({});
    const [selectedChecklist, setSelectedChecklist] = useState(null);
    const [viewDialogVisible, setViewDialogVisible] = useState(false);
    const [showAddModal, setShowAddModal] = useState(false);
    const [isEditMode, setIsEditMode] = useState(false);
    const [editingId, setEditingId] = useState(null);
    const [categories, setCategories] = useState([]);
    const [newChecklist, setNewChecklist] = useState({
        name: '',
        version: '',
        category: '',
        maskId: '',
        customId: '',
        curatorId: '',
    });

    useEffect(() => {
        setFilters({
            name: { value: null, matchMode: FilterMatchMode.CONTAINS },
            customId: { value: null, matchMode: FilterMatchMode.CONTAINS },
            category: { value: null, matchMode: FilterMatchMode.IN },
            status: { value: null, matchMode: FilterMatchMode.IN },
            'curator.firstName': { value: null, matchMode: FilterMatchMode.IN },
        });

        fetchDropdownData('ins_category', setCategories);
    }, []);

    const fetchDropdownData = useCallback(async (maskId, setState) => {
        try {
            const uriString = {
                where: { maskId: maskId },
                include: [{ relation: "dropdownItems" }],
            };
            const url = `${ADMINDROPDOWNS}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
            const response = await API.get(url);
            if (response.status === 200) {
                const data = response.data[0]?.dropdownItems.map(item => ({
                    label: item.name,
                    value: item.name,
                })) || [];
                setState(data);
            }
        } catch (error) {
            console.error(`Error fetching ${maskId} list:`, error);
        }
    }, []);

    // const fetchChecklistData = async () => {
    //     try {
    //         const uriString = {
    //             include: [{ relation: "curator" }]
    //         };
    //         const url = '/checklists?filter=' + encodeURIComponent(JSON.stringify(uriString));
    //         const response = await API.get(url);
    //         if (response.status === 200) {
    //             setChecklists(response.data);
    //         } else {
    //             console.error('Failed to fetch checklist data');
    //         }
    //     } catch (error) {
    //         console.error('Error fetching checklist:', error);
    //     }
    // };

    const handleView = (rowData) => {
        setSelectedChecklist(rowData);
        setViewDialogVisible(true);
    };

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setNewChecklist(prev => ({ ...prev, [name]: value }));
    };

    const handleEdit = (rowData) => {
        setIsEditMode(true);
        setEditingId(rowData.id);
        setNewChecklist({
            name: rowData.name || '',
            version: rowData.version || '',
            category: rowData.category || '',
            maskId: rowData.maskId || '',
            customId: rowData.customId || '',
            curatorId: rowData.curatorId || '',
        });
        setShowAddModal(true);
    };

    const handleAddNew = async () => {
        try {
            const confirm = await Swal.fire({
                title: isEditMode ? 'Save Changes?' : 'Save New Checklist?',
                text: isEditMode ? "Changes will be saved permanently." : "Checklist will be created.",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, Save it!'
            });

            if (!confirm.isConfirmed) {
                return; // User cancelled
            }

            const payload = {
                ...newChecklist
            };

            if (isEditMode && editingId) {
                const url = `/checklists/${editingId}`;
                const response = await API.patch(url, payload);
                if (response.status === 200 || response.status === 204) {
                    Swal.fire('Updated!', 'Checklist updated successfully.', 'success');
                } else {
                    Swal.fire('Failed!', 'Failed to update checklist.', 'error');
                }
            } else {
                const url = '/checklists';
                const response = await API.post(url, payload);
                if (response.status === 201 || response.status === 200) {
                    Swal.fire('Created!', 'Checklist added successfully.', 'success');
                } else {
                    Swal.fire('Failed!', 'Failed to create checklist.', 'error');
                }
            }

            fetchChecklistData();
            setShowAddModal(false);
            setIsEditMode(false);
            setEditingId(null);
            setNewChecklist({
                name: '',
                version: '',
                category: '',
                maskId: '',
                customId: '',
                curatorId: '',
            });
        } catch (error) {
            console.error('Error saving checklist:', error);
            Swal.fire('Error!', 'Something went wrong.', 'error');
        }
    };

    const statusList = ['Draft', 'Published', 'Archived'];
    const curators = Array.from(new Set(checklists.map(c => c.curator?.firstName || ''))).filter(Boolean).map(c => ({ label: c, value: c }));

    const handleArchive = async (rowData) => {
        const url = `/checklists/${rowData.id}`;
        const response = await API.patch(url, { isArchive: true });
        if (response.status === 200 || response.status === 204) {
            Swal.fire('Archived!', 'Checklist archived successfully.', 'success');
        } else {
            Swal.fire('Failed!', 'Failed to archive checklist.', 'error');
        }
        fetchChecklistData();
    }
    const handleCurate = (rowData) => {

        history.push('/editchecklist', rowData)

        // Swal.fire({
        //     title: `Curate Checklist`,
        //     text: `You clicked Curate for "${rowData.name}" checklist.`,
        //     icon: 'info',
        //     confirmButtonText: 'Ok'
        // });
    };

    const handlePublish = async (rowData) => {
        const confirm = await Swal.fire({
            title: 'Publish Checklist?',
            text: `This will publish the checklist "${rowData.name}".`,
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, Publish it!'
        });

        if (!confirm.isConfirmed) return;

        try {
            const response = await API.patch(`/checklists/${rowData.id}`, { status: 'Published' });
            if (response.status === 200 || response.status === 204) {
                Swal.fire('Published!', 'Checklist has been published.', 'success');
                fetchChecklistData();
            } else {
                Swal.fire('Failed!', 'Failed to publish checklist.', 'error');
            }
        } catch (error) {
            console.error('Error publishing checklist:', error);
            Swal.fire('Error!', 'Something went wrong while publishing.', 'error');
        }
    };


    return (
        <>
            <div className="d-flex justify-content-end align-items-center mb-3 p-3">
                <div>
                    <Button label="CSV Download" icon="pi pi-download" className="me-2" />
                    <Button label="Add New" icon="pi pi-plus" onClick={() => {
                        setIsEditMode(false);
                        setNewChecklist({
                            name: '',
                            version: '',
                            category: '',
                            maskId: '',
                            customId: '',
                            curatorId: '',
                        });
                        setShowAddModal(true);
                    }} />
                </div>
            </div>

            <DataTable value={checklists} paginator rows={5} filters={filters} onFilter={(e) => setFilters(e.filters)}>
                <Column field="customId" header="Checklist ID"
                    body={(row) => (
                        <div className='maskid' style={{ cursor: 'pointer' }} onClick={() => handleView(row)}>
                            {row.customId}
                        </div>
                    )}
                />
                <Column field="version" header="Version" />
                <Column field="name" header="Checklist Name" sortable  />
                <Column
                    field="category"
                    header="Category"
                    filter
                    showFilterMatchModes={false}
                    filterElement={(options) => (
                        <MultiSelect
                            value={options.value}
                            options={categories}
                            onChange={(e) => options.filterCallback(e.value)}
                            placeholder="Any"
                        />
                    )}
                />
                <Column
                    field="status"
                    header="Status"
                    filter
                    showFilterMatchModes={false}
                    filterElement={(options) => (
                        <MultiSelect
                            value={options.value}
                            options={statusList.map(s => ({ label: s, value: s }))}
                            onChange={(e) => options.filterCallback(e.value)}
                            placeholder="Any"
                        />
                    )}
                />
                <Column
                    field="curator.firstName"
                    header="Checklist Curator"
                    filter
                    showFilterMatchModes={false}
                    filterElement={(options) => (
                        <MultiSelect
                            value={options.value}
                            options={curators}
                            onChange={(e) => options.filterCallback(e.value)}
                            placeholder="Any"
                        />
                    )}
                />
                <Column header="Actions" body={(rowData) => (
                    <div className="d-flex gap-2">
                        <Button className="p-button-text p-button-success" tooltip="Publish" onClick={() => handlePublish(rowData)}>
                            <i className="pi pi-check-circle" style={{ fontSize: '1.2rem' }}></i>
                        </Button>
                        <Button className="p-button-text p-button-primary" tooltip="Curate" onClick={() => handleCurate(rowData)}>
                            <i className="mdi mdi-clipboard-edit" style={{ fontSize: '1.2rem' }}></i>
                        </Button>
                        <Button className="p-button-text p-button-warning" tooltip="Edit" onClick={() => handleEdit(rowData)}>
                            <i className="mdi mdi-pencil" style={{ fontSize: '1.2rem' }}></i>
                        </Button>
                        <Button className="p-button-text p-button-danger" tooltip="Archive" onClick={() => handleArchive(rowData)}>
                            <i className="mdi mdi-archive" style={{ fontSize: '1.2rem' }}></i>
                        </Button>
                    </div>
                )} />
            </DataTable>

            {/* View Dialog */}
            <Dialog header="Checklist Preview" visible={viewDialogVisible} style={{ width: '40vw' }} modal onHide={() => setViewDialogVisible(false)}>
                {selectedChecklist && (
                    <div>
                        <p><strong>ID:</strong> {selectedChecklist.customId}</p>
                        <p><strong>Name:</strong> {selectedChecklist.name}</p>
                        <p><strong>Version:</strong> {selectedChecklist.version}</p>
                        <p><strong>Category:</strong> {selectedChecklist.category}</p>
                        <p><strong>Status:</strong> {selectedChecklist.status}</p>
                        <p><strong>Curator:</strong> {selectedChecklist.curator?.firstName || 'N/A'}</p>
                    </div>
                )}
            </Dialog>

            {/* Add/Edit Modal */}
            <Modal show={showAddModal} onHide={() => {
                setShowAddModal(false);
                setIsEditMode(false);
                setEditingId(null);
            }}>
                <Modal.Header closeButton>
                    <Modal.Title>{isEditMode ? 'Edit Checklist' : 'Add New Checklist'}</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <Form>
                        <Form.Group className="mb-3">
                            <Form.Label>ID</Form.Label>
                            <Form.Control type="text" name="customId" value={newChecklist.customId} onChange={handleInputChange} />
                        </Form.Group>
                        <Form.Group className="mb-3">
                            <Form.Label>Name</Form.Label>
                            <Form.Control type="text" name="name" value={newChecklist.name} onChange={handleInputChange} />
                        </Form.Group>
                        {/* <Form.Group className="mb-3">
                            <Form.Label>Version</Form.Label>
                            <Form.Control type="text" name="version" value={newChecklist.version} onChange={handleInputChange} />
                        </Form.Group> */}
                        <Form.Group className="mb-3">
                            <Form.Label>Category</Form.Label>
                            <Select
                                name="category"
                                value={categories.find(opt => opt.value === newChecklist.category) || null}
                                options={categories}
                                onChange={(selected) => {
                                    setNewChecklist(prev => ({ ...prev, category: selected?.value || '' }));
                                }}
                                placeholder="Select Category"
                                isClearable
                            />
                        </Form.Group>
                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <Button label="Cancel" className="p-button-secondary" onClick={() => {
                        setShowAddModal(false);
                        setIsEditMode(false);
                        setEditingId(null);
                    }} />
                    <Button label={isEditMode ? "Save Changes" : "Save"} className="p-button-primary" onClick={handleAddNew} />
                </Modal.Footer>
            </Modal>
        </>
    );
};

export default ChecklistLibrary;
