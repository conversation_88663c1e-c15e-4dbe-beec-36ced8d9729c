import React, { useState } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';

const RiskLevel = ({ item, tableData, cellClassName, cellStyle }) => {
    console.log(tableData)

    const [riskTable, setRiskTable] = useState(false)

    const findMatrixValue = (idValue, columnValue) => {
        // Map the numeric column value to the respective field name
        const columnMap = {
            1: 'rare',
            2: 'unlikely',
            3: 'possible',
            4: 'likely',
            5: 'almostCertain'
        };
    
        // Get the actual column name based on the columnValue passed
        const columnKey = columnMap[columnValue];
    
        // Find the row that matches the given id value (e.g., "2(D)")
        const row = tableData.find(item => item.id.startsWith(`${idValue}(`));
    
        // If row and column exist, return the corresponding value
        if (row && row[columnKey]) {
            return row[columnKey];
        }
    
        return 0; // Return null if no match is found
    };
    
    return (
        <div className="row mt-4 mb-3 pb-4">
            <div className='col-8'>
                <h6 className='fw-bold'>Risk Level</h6>
                <p className='fst-italic'>Risk Level is the measure of risk determined by evaluating both the severity of potential consequences and the likelihood of the event occurring.</p>
            </div>
            <div className='col-4'>
                <div className={`boxShadow p-2 text-center fw-bold ${cellClassName(item[4].severity * item[4].likelyhood)}`}>
                    {findMatrixValue(item[4].severity ,item[4].likelyhood)}
                </div>
            </div>
            <h6 className='mt-3 pointer' onClick={() => setRiskTable(!riskTable)}>
                Understand Risk Levels <i className={`pi ${riskTable ? 'pi-angle-up' : 'pi-angle-down'} ms-1`}></i>
            </h6>
            {riskTable && (
                <div className='col-12 mt-3'>
                    <div className="card">
                        <DataTable value={tableData} className="table-bordered">
                            <Column field="id" header="" bodyClassName="text-center" headerClassName="risk-th" style={{ width: '150px' }}></Column>
                            <Column field="severity" header="" bodyClassName="text-center" headerClassName="risk-th" style={{ width: '150px' }}></Column>
                            <Column field="rare" header="1 Rare" bodyClassName={(data) => `${cellStyle(data, 'rare')} text-center`} headerClassName="risk-th" style={{ width: '150px' }}></Column>
                            <Column field="unlikely" header="2 Unlikely" bodyClassName={(data) => `${cellStyle(data, 'unlikely')} text-center`} headerClassName="risk-th" style={{ width: '150px' }}></Column>
                            <Column field="possible" header="3 Possible" bodyClassName={(data) => `${cellStyle(data, 'possible')} text-center`} headerClassName="risk-th" style={{ width: '150px' }}></Column>
                            <Column field="likely" header="4 Likely" bodyClassName={(data) => `${cellStyle(data, 'likely')} text-center`} headerClassName="risk-th" style={{ width: '150px' }}></Column>
                            <Column field="almostCertain" header="5 Almost Certain" bodyClassName={(data) => `${cellStyle(data, 'almostCertain')} text-center`} headerClassName="risk-th" style={{ width: '150px' }}></Column>
                        </DataTable>



                    </div>
                </div>
            )}
        </div>
    );
};

export default RiskLevel;
