import React, { useState, useEffect } from 'react';
import { useSelector } from "react-redux";
import API from '../services/API';
import ActionCard from './ActionCard';
import { ACTION_URL } from '../constants';

const getRandomColor = () => {
  const letters = '0123456789ABCDEF';
  let color = '#';
  for (let i = 0; i < 6; i++) {
    color += letters[Math.floor(Math.random() * 16)];
  }
  return color;
};
const Dashboard = () => {
  // All service objects
  const [actions, setActions] = useState([]);       // All action objects
  const services = useSelector((state) => state.service.service)
  // If you already have services in a Redux store, skip local state
  // and read them from your useSelector hook instead.
  // e.g. const services = useSelector((state) => state.service.service);
  const [serviceColors, setServiceColors] = useState({});
  useEffect(() => {

    getActions();
  }, []);

  useEffect(() => {
    if (services.length > 0) {
      const newColors = {};
      services.forEach((svc) => {
        // If a color already exists, retain it; otherwise generate new
        if (!serviceColors[svc.id]) {
          newColors[svc.id] = getRandomColor();
        }
      });
      setServiceColors((prevColors) => ({ ...prevColors, ...newColors }));
    }
  }, [services]);

  const getActions = async () => {
    const response = await API.get(ACTION_URL);
    if (response.status === 200) {
      setActions(response.data);
    }
  };

  return (
    <>
      <h5 className='mt-4 fw-bold actionTitle'>My Actions</h5>
      <p className='mb-5 actionDesc'>
        Click on each application to see the actions you need to take.
      </p>

      <div className='row'>
        {services.map((serviceItem) => {
          // Match action.application against serviceItem.maskName
          // (or use action.serviceId === serviceItem.id if you prefer)
          const filteredActions = actions.filter(
            (actionItem) =>
              actionItem.application === serviceItem.maskName
          );

          // The count of actions for this service
          const count = filteredActions.length;

          const color = serviceColors[serviceItem.id] || '#CCC'; // fallback

          return (
            <ActionCard
              key={serviceItem.id}
              title={serviceItem.name}
              url={serviceItem.url}
              // desc='Identify, assess, and mitigate risks to enhance decision-making.'
              color={color}
              count={count}
            />
          );
        })}
      </div>
    </>
  );
};

export default Dashboard;
