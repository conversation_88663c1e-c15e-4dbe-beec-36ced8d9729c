import React, { useState } from 'react';
import Card from 'react-bootstrap/Card';
import Container from 'react-bootstrap/Container';
import Row from 'react-bootstrap/Row';
import Col from 'react-bootstrap/Col';
import { Button } from 'react-bootstrap';
import ControlsSection from './ControlsSection';
import CrudTableWithDataTable from './CrudTable';
import JoditEditorComponent from '../../../editors/JoditEditorComponent';
import API from '../../../services/API';
import { INVERSTIGATION_WITH_ID } from '../../../constants';
import Swal from 'sweetalert2';
const Recommendation = ({ data, type }) => {

    const [conclusionRemarks, setConclusionRemarks] = useState(data.conclusionRemarks || '');

    const handleContentChange = (newContent) => {
        setConclusionRemarks(newContent);
    };
    // Array of sections
    const sections = [
        {
            controlsData: data.controls.identifiedPreventiveControls,
            type: "PC",
            title: "Summary of significant factors that led to the failure of preventative controls, resulting in the incident.",
        },
        {
            controlsData: data.controls.unidentifiedPreventiveControls,
            type: "UPC",
            title: "Summary of significant factors behind the overlooking of preventative controls, resulting in the incident.",
        },
        {
            controlsData: data.controls.identifiedMitigativeControls,
            type: "MC",
            title: "Summary of significant factors that led to the failure of mitigative controls in minimizing the consequences of the incident.",
        },
        {
            controlsData: data.controls.unidentifiedMitigativeControls,
            type: "UMC",
            title: "Summary of significant factors behind the overlooking of mitigative controls needed to minimize the consequences of the incident.",
        },
    ];

    // Initialize counters
    let jobFactorCounter = 1;
    let organizationalFactorCounter = 1;

    // Pre-calculate starting indices for each section
    const sectionsWithIndices = sections.map((section) => {
        const startJobFactorIndex = jobFactorCounter;
        const startOrganizationalFactorIndex = organizationalFactorCounter;

        // Count the number of significant job factors and organizational factors in this section
        let sectionJobFactorsCount = 0;
        let sectionOrganizationalFactorsCount = 0;


        section.controlsData.forEach((control) => {
            // Check if jobFactors exist before filtering
            const significantJobFactorsCount = control.jobFactors
                ? control.jobFactors.filter(
                    (factor) => factor.extentOfContribution === 'Significant'
                ).length
                : 0;

            // Check if relatedOrganizationalFactors exist before filtering
            const significantOrganizationalFactorsCount = control.relatedOrganizationalFactors
                ? control.relatedOrganizationalFactors.filter(
                    (factor) => factor.extentOfContribution === 'Significant'
                ).length
                : 0;

            sectionJobFactorsCount += significantJobFactorsCount;
            sectionOrganizationalFactorsCount += significantOrganizationalFactorsCount;


        });


        // Update the counters
        jobFactorCounter += sectionJobFactorsCount;
        organizationalFactorCounter += sectionOrganizationalFactorsCount;

        // Return the section data with starting indices
        return {
            ...section,
            startJobFactorIndex,
            startOrganizationalFactorIndex,

        };
    });

    const SaveConclusion = async () => {
        try {

            const response = await API.patch(INVERSTIGATION_WITH_ID(data.id), { conclusionRemarks: conclusionRemarks });
            console.log("API response:", response);
            if (response.status === 204) {
                Swal.fire({
                    icon: 'success',
                    title: 'Conclution added successfully!',
                    toast: true,
                    position: 'top-right',
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                });
            }
        } catch (error) {
            console.error('Error updating title:', error);
        }
    }

    return (
        <div className="mt-4">

            <div className='mb-2'>
                <div className='mb-2 fw-bold' style={{ fontSize: '14px' }}>
                    Analyze the investigation findings, summarize key conclusions about the causes and contributing factors,
                    and provide actionable recommendations for preventing future incidents.
                </div>
                <div className=''>
                    <i className='d-flex mb-2' style={{ fontSize: '14px' }}>
                        The sections below summarize the significant factors that led to the failure of preventative and mitigative controls, as well as any overlooked controls that could have made a difference. This information is auto-populated from the investigation above, and any changes made there will update here.
                    </i>
                    <i style={{ fontSize: '14px' }}>
                        The investigation team should review this as a full list of root causes for the incident. Use it as a basis for developing your recommendations, ensuring all root causes are addressed in your corrective actions and improvements.

                    </i>
                </div>

            </div>

            {sectionsWithIndices.map((section, index) => (<>
                {index === 0 && <div className='mb-4 fw-bold'>Analysis of Preventative Controls</div>}
                {index === 2 && <div className='mb-4 fw-bold'>Analysis of Mitigative Controls</div>}
                <Row>
                    <Col md={12}>
                        <ControlsSection
                            controlsData={section.controlsData}
                            type={section.type}
                            title={section.title}
                            startJobFactorIndex={section.startJobFactorIndex}
                            startOrganizationalFactorIndex={section.startOrganizationalFactorIndex}

                        />
                    </Col>
                </Row>
            </>))}
            {
                (type.actionType !== 'perform_task' && type.actionType !== 'reperform_task' && type.actionType !== 'verify_task') && (
                    <>
                        <div className='mb-2 fw-bold'>Recommendations</div>
                        <div className=''>
                            <i className='d-flex mb-2' style={{ fontSize: '14px' }}>
                                List the final recommendations of the investigation team for review by the approver, who will assign responsibilities for the accepted actions. The approver may request clarifications, leading to changes in the report.
                            </i>
                            <i className='d-flex mb-3' style={{ fontSize: '14px' }}>
                                Make sure your recommendations are based on the investigation findings and address all root causes. For each recommendation, include a clear reason why it’s being proposed and how it directly targets the root cause to ensure effective corrective actions.
                            </i>
                        </div>

                        <Card className="mb-4 boxShadow" style={{ borderRadius: 0 }}>
                            <Card.Body>
                                <CrudTableWithDataTable invest={data} />
                            </Card.Body>
                        </Card>
                    </>
                )
            }

            <div className='mb-4 fw-bold'>Conclusions of the Investigation Team</div>
            <Card className="mb-4 boxShadow " style={{ borderRadius: 0 }}>
                <Card.Body>
                    {
                        type.actionType && (type.actionType === 'conduct_investigation' || type.actionType === 'reconduct_investigation') ? (
                            <JoditEditorComponent value={conclusionRemarks} onChange={handleContentChange} />
                        ) : (
                            <div dangerouslySetInnerHTML={{ __html: conclusionRemarks }} />
                        )
                    }


                </Card.Body>
            </Card>


            {(type.actionType && (type.actionType === 'conduct_investigation' || type.actionType === 'reconduct_investigation')) && (
                <div className="d-flex justify-content-end p-4">
                    <Button variant="success" onClick={SaveConclusion}>
                        Save
                    </Button>
                </div>
            )}

        </div>
    );
};

export default Recommendation;
