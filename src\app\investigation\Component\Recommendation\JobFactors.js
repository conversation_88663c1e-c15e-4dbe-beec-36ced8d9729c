import React from 'react';

const JobFactors = ({ jobFactors, startIndex = 1 }) => {
    let count = startIndex;
    return (
        <div>
            {jobFactors && jobFactors.map((factor, index) => (
                factor.extentOfContribution === 'Significant' && (
                    <div key={index} style={{ marginBottom: '10px' }}>
                        <div className='mb-2'><strong>RCJ - {count++} : {factor.jobFactor}</strong></div>
                        <div style={{ paddingLeft: '50px' }}>
                            {factor.contributorFactor && <p className='m-0'><strong>Sub Factor: </strong>{factor.contributorFactor}</p>}

                            {factor.fallibility && (
                                <div>
                                    <p><strong>Fallibility:</strong> {factor.fallibility}</p>
                                    {factor.isRoutine ? (
                                        <span
                                            style={{
                                                display: 'inline-block',
                                                padding: '3px 8px',
                                                borderRadius: '5px',
                                                backgroundColor: '#ff07073d',
                                                color: 'red',
                                                border: '1px solid red',
                                            }}
                                        >
                                            Repetitive
                                        </span>
                                    ) : (
                                        <span
                                            style={{
                                                display: 'inline-block',
                                                padding: '3px 8px',
                                                borderRadius: '5px',
                                                backgroundColor: '#E0F2FE',
                                                color: '#0284C7',
                                                border: '1px solid #7DD3FC',
                                                fontWeight: 'bold',
                                            }}
                                        >
                                            Not Repetitive
                                        </span>
                                    )}
                                </div>
                            )}
                            {factor.description && <p>{factor.description}</p>}
                        </div>
                    </div>
                )
            ))}
        </div>
    );
};

export default JobFactors;
