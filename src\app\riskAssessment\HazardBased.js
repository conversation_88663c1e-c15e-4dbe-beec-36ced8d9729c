import React, { useEffect, useState, useContext, useRef } from 'react'
import { Dropdown } from 'primereact/dropdown';
import { Button } from 'primereact/button';
import { MultiSelect } from 'primereact/multiselect';
import { InputTextarea } from 'primereact/inputtextarea'
import { InputText } from 'primereact/inputtext'
import { Checkbox } from 'primereact/checkbox';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
// import 'primeflex/primeflex.css'; 
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";
// import 'primereact/resources/themes/lara-light-indigo/theme.css';
import 'primeicons/primeicons.css';
import { GMS1_URL, GET_USER_ROLE_BY_MODE, HAZARDS_CATEGOTY, RISK_WITH_ID_URL, FILE_URL, RISKASSESSMENT_LIST, RIS<PERSON>_UPDATE_WITH_ID_URL, DRAFT_RA, GET_RISK_WITH_ID_URL, RISK_UPDATE_DRAFT_WITH_ID } from '../constants';
import API from '../services/API';
import { useSelector } from 'react-redux';
import { InputSwitch } from 'primereact/inputswitch';
import { DropzoneArea } from 'material-ui-dropzone';
import { Dialog } from 'primereact/dialog';
import { TabView, TabPanel } from 'primereact/tabview';
import { Stepper, Step, StepLabel, Typography } from '@mui/material';
import { Calendar } from 'primereact/calendar';
import Accordion from 'react-bootstrap/Accordion';
import { useAccordionButton } from 'react-bootstrap/AccordionButton';
import AccordionContext from 'react-bootstrap/AccordionContext';
import { RadioButton } from 'primereact/radiobutton';
import ImageComponent from '../services/FileDownlodS3';
import Select from 'react-select'
import SignatureCanvas from "react-signature-canvas";
import Swal from 'sweetalert2';
import { useLocation, useHistory } from 'react-router-dom/cjs/react-router-dom';
import RiskUpdate from './Component/RiskUpdate';
import UpdateTable from './Component/UpdateTable';
import Consequence from './Component/Consequence/Consequence';
import CurrentControl from './Component/CurrentControl/CurrentControl';
import FullLoader from '../shared/FullLoader';


const customSwal2 = Swal.mixin({
    customClass: {
        confirmButton: "btn btn-primary",
    },
    buttonsStyling: false,
});

function Hazard({ data, type, domain }) {

    const user = useSelector((state) => state.login.user);
    const location = useLocation();
    const history = useHistory()
    console.log(location)
    const signRef = useRef();
    const [files, setFiles] = useState([])
    const [depart, setDepart] = useState([])
    const [activity, setActivity] = useState([])
    const [crew, setCrew] = useState([])
    const [selectedDepart, setSelectedDepart] = useState(null)
    const [selectedActivity, setSelectedActivity] = useState(null)
    const [selectedCrew, setSelectedCrew] = useState([])
    const [addSubActivity, setAddSubActivity] = useState(false)
    const [activityDesc, setActivityDesc] = useState('')
    const [task, setTask] = useState([[
        { type: 'consequence', option: [{ value: "", files: [], current_type: '', method: '' }] },
        { type: 'current_control', option: [{ value: "", files: [], current_type: '', method: '', required: false, validity: false }] }
    ]])
    const [subActivityName, setSubActivityName] = useState('')
    const [visible, setVisible] = useState(false)
    const [item, setItem] = useState('')
    const [index, setIndex] = useState(0)
    const [activeIndex, setActiveIndex] = useState(0);
    const [hazards, setHazards] = useState([])
    const [activeTabIndex, setActiveTabIndex] = useState(0);
    const [severityTable, setSeverityTable] = useState(false)
    const [likelyhoodTable, setLikelyhoodTable] = useState(false)
    const [riskTable, setRiskTable] = useState(false)
    const [responsibility, setResponsibility] = useState([])
    const [required, setRequired] = useState(true)
    const [recommendationOne, setRecommendationOne] = useState({})
    const [recommendationTwo, setRecommendationTwo] = useState({})
    const [nonRoutineDepartment, setNonRoutineDepartment] = useState('')
    const [nonRoutineActivity, setNonRoutineActivity] = useState('')
    const [additionalRecommendation, setAdditionalRecommendation] = useState('')
    const [isLoading, setIsLoading] = useState(false)
    const [hazardName, setHazardName] = useState('')
    const [riskUpdate, setRiskUpdate] = useState(false)
    const [Update, setUpdate] = useState([])
    const [shortName, setShortName] = useState('')

    const impactOn = [
        { 'label': 'Personnel', 'value': 'Personnel' },
        { 'label': 'Property', 'value': 'Property' },
        { 'label': 'Environment', 'value': 'Environment' },
        { 'label': 'Service Loss', 'value': 'Service Loss' },
    ]
    const control = [
        { 'label': 'Engineering', 'value': 'Engineering' },
        { 'label': 'Administrative', 'value': 'Administrative' },
        { 'label': 'PPE', 'value': 'PPE' }

    ]

    const controlType = [
        { 'label': 'Preventative', 'value': 'Preventative' },
        { 'label': 'Mitigative', 'value': 'Mitigative' }

    ]

    useEffect(() => {

        getCrewList()


    }, [])


    const getRiskUpdate = async () => {
        try {
            const response = await API.get(RISK_UPDATE_WITH_ID_URL(data.id));
            if (response.status === 200) {

                setUpdate(response.data)
            }
        } catch (error) {
            // Catch any errors during the API call
            console.error("Error fetching risk update:", error);
        }

    }

    useEffect(() => {
        // setSelectedActivity(location.state.data.workActivity.id)
        // setSelectedDepart(location.state.data.department.id)
        if (data) {
            setTask(data.tasks)
            setAdditionalRecommendation(data.additonalRemarks)
            setRecommendationOne(data.overallRecommendationOne)
            setRecommendationTwo(data.overallRecommendationTwo)
            if (data?.raTeamMembers?.length) {
                setSelectedCrew(data.raTeamMembers.map(option => ({
                    name: option.user?.firstName || 'Unknown',
                    id: option.user?.id || null
                })));
            } else {
                setSelectedCrew([]); // Set to an empty array if raTeamMembers is undefined or empty
            }

            setActivityDesc(data.description)
            setNonRoutineActivity(data.nonRoutineWorkActivity)
            setNonRoutineDepartment(data.nonRoutineDepartment)
            setHazardName(data.hazardName)
            setShortName(data.shortName)
            getRiskUpdate();
        }

    }, [data])






    const getCrewList = async () => {

        const response = await API.post(GET_USER_ROLE_BY_MODE, {
            locationOneId: "",
            locationTwoId: "",
            locationThreeId: "",
            locationFourId: "",
            mode: 'ra_member'
        });
        if (response.status === 200) {
            let data = [];
            response.data.map((item) => {

                if (item.id !== user.id) {
                    data.push({ name: item.firstName, id: item.id });
                }
            });

            setCrew(data);
        }
    };



    const onImapactOn = (value, j, type) => {
        const t = task
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === type) {

                        ite.option.map((con, c) => {
                            if (c === j) {
                                con.current_type = value
                            }


                        })

                    }
                })
            }
            return item
        }
        )
        setTask(text)
        setItem(text[index])
    }

    const onConseqText = (value, j, type) => {
        const t = task
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === type) {
                        ite.option.map((con, c) => {
                            if (c === j) {
                                con.value = value
                            }

                        })

                    }
                })
            }
            return item
        }
        )
        setTask(text)
        setItem(text[index])
    }

    const onConseqRequired = (value, j, type, type1) => {
        const t = task
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === type) {
                        ite.option.map((con, c) => {
                            if (c === j) {
                                if (type1 === 'required') {
                                    con.required = !value
                                } else {
                                    con.validity = !value
                                }

                            }

                        })

                    }
                })
            }
            return item
        }
        )

        console.log(text)
        setTask(text)
        setItem(text[index])
    }

    const handleTaskFileChange = async (value, j, type) => {

        if (value.length > 0) {
            const latestFile = value[value.length - 1];
            const formData1 = new FormData();
            formData1.append('file', latestFile);

            try {
                const response = await API.post(FILE_URL, formData1, {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                    }
                });

                if (response && response.status === 200) {
                    const t = task
                    const text = t.map((item, i) => {
                        if (i === index) {
                            item.map((ite) => {
                                if (ite.type === type) {
                                    ite.option.map((con, c) => {
                                        if (c === j) {
                                            con.files.push(response.data.files[0].originalname)
                                        }

                                    })

                                }
                            })
                        }
                        return item
                    }
                    )
                    setTask(text)
                    setItem(text[index])
                }
            } catch (error) {
                // Log the error response for debugging purposes
                console.error("File upload error: ", error);
            }

        }
    }

    const addConsequence = (type) => {
        const t = task
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === type) {
                        ite.option.push({ value: "", files: [], current_type: '', method: '' })
                    }
                })
            }
            return item
        }
        )
        setTask(text)
        setItem(text[index])
    }


    const onDeleteConseq = (j, type) => {
        const t = task
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === type) {

                        const newHazards = [...ite.option];
                        newHazards.splice(j, 1);
                        ite.option = newHazards;

                    }
                })
            }
            return item
        }
        )
        setTask(text)
        setItem(text[index])
    }


    const dataURItoFile = (dataURI, filename) => {
        var byteString = atob(dataURI.split(",")[1]);
        // separate out the mime component
        var mimeString = dataURI.split(",")[0].split(":")[1].split(";")[0];
        // write the bytes of the string to an ArrayBuffer
        var ab = new ArrayBuffer(byteString.length);
        var dw = new DataView(ab);
        for (var i = 0; i < byteString.length; i++) {
            dw.setUint8(i, byteString.charCodeAt(i));
        }

        // write the ArrayBuffer to a blob, and you're done
        return new File([ab], filename, { type: mimeString });
    };
    const createUserHandler = async () => {
        setIsLoading(true);

        // Check if the signature is empty
        if (signRef.current.isEmpty()) {
            customSwal2.fire("Please Sign!", "", "error");
            setIsLoading(false);
            return; // Stop the function if signature is missing
        }

        let uploadedSignature = '';
        const filename = new Date().getTime() + "captin_sign.png";

        // Proceed to upload the signature
        const formData1 = new FormData();
        formData1.append('file', dataURItoFile(signRef.current.getTrimmedCanvas().toDataURL("image/png"), filename));

        try {
            const response = await API.post(FILE_URL, formData1, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                }
            });

            if (response && response.status === 200) {
                uploadedSignature = response.data.files[0].originalname; // Store the uploaded signature
            } else {
                customSwal2.fire("File Upload Failed!", "", "error");
                setIsLoading(false);
                return;
            }
        } catch (error) {
            console.error("File upload error: ", error);
            setIsLoading(false);
            customSwal2.fire("Please Try Again!", "", "error");
            return;
        }

        try {
            // Proceed with the API to create the risk assessment, pass the signature
            const response1 = await API.post(RISKASSESSMENT_LIST, {
                type: 'High-Risk Hazard',
                tasks: task,
                teamLeaderDeclaration: { name: user.firstName, sign: uploadedSignature }, // Pass the uploaded signature
                workActivityId: selectedActivity?.id,
                departmentId: selectedDepart?.id,
                teamLeaderId: user.id,
                overallRecommendationOne: recommendationOne,
                overallRecommendationTwo: recommendationTwo,
                additonalRemarks: additionalRecommendation,
                highRisk: [],
                nonRoutineDepartment: nonRoutineDepartment,
                nonRoutineWorkActivity: nonRoutineActivity,
                status: 'Pending',
                hazardName: hazardName,
                raTeamMembersList: selectedCrew,
                description: activityDesc,
                shortName:shortName,
            });

            if (response1.status === 200) {
                setIsLoading(false);
                customSwal2.fire("Risk Assessment Created!", "", "success").then((result) => {
                    if (result.isConfirmed) {
                        window.location.reload();
                    }
                });
            } else {
                customSwal2.fire("Please Try Again!", "", "error");
                setIsLoading(false);
            }
        } catch (error) {
            console.error("Risk assessment creation error: ", error);
            customSwal2.fire("Please Try Again!", "", "error");
            setIsLoading(false);
        }
    };


    // Function to upload the signature
    const uploadSignature = async () => {
        const filename = new Date().getTime() + "captin_sign.png";
        const formData1 = new FormData();
        formData1.append('file', dataURItoFile(signRef.current.getTrimmedCanvas().toDataURL("image/png"), filename));

        try {
            const response = await API.post(FILE_URL, formData1, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                }
            });

            if (response && response.status === 200) {
                return response.data.files[0].originalname; // Return the uploaded file name
            } else {
                throw new Error("File upload failed.");
            }
        } catch (error) {
            console.error("File upload error: ", error);
            throw error; // Rethrow the error to be handled in the main function
        }
    };

    // Function to submit the draft
    const submitDraft = async (uploadedSignature) => {
        try {
            const response = await API.post(DRAFT_RA, {
                type: 'High-Risk Hazard',
                tasks: task,
                teamLeaderDeclaration: { name: user.firstName, sign: uploadedSignature }, // Use the uploaded signature
                workActivityId: selectedActivity?.id,
                departmentId: selectedDepart?.id,
                teamLeaderId: user.id,
                overallRecommendationOne: recommendationOne,
                overallRecommendationTwo: recommendationTwo,
                additonalRemarks: additionalRecommendation,
                highRisk: [],
                nonRoutineDepartment: nonRoutineDepartment,
                nonRoutineWorkActivity: nonRoutineActivity,
                status: 'Draft',
                hazardName: hazardName,
                raTeamMembersList: selectedCrew,
                description: activityDesc
            });

            if (response.status === 200) {
                return true;
            } else {
                throw new Error("Draft submission failed.");
            }
        } catch (error) {
            console.error("Draft submission error: ", error);
            throw error; // Rethrow the error to be handled in the main function
        }
    };

    // Main handler function
    const draftUserHandler = async () => {
        setIsLoading(true);

        try {
            let uploadedSignature = '';

            // Check if signature exists
            if (!signRef.current.isEmpty()) {
                // Step 1: Upload the signature
                uploadedSignature = await uploadSignature();
            } else {
                uploadedSignature = ""
            }

            // Step 2: Submit the draft
            const draftSubmitted = await submitDraft(uploadedSignature);

            if (draftSubmitted) {
                setIsLoading(false);
                customSwal2.fire("Risk Assessment Drafted!", "", "success").then((result) => {
                    if (result.isConfirmed) {
                        window.location.reload();
                    }
                });
            }
        } catch (error) {
            customSwal2.fire("Please Try Again!", "", "error");
            setIsLoading(false);
        }
    };


    const draftUserEditHandler = async () => {
        setIsLoading(true);

        try {
            let uploadedSignature = '';

            // Check if signature exists
            if (data.teamLeaderDeclaration.sign) {
                uploadedSignature = data.teamLeaderDeclaration.sign
            } else {
                if (!signRef.current.isEmpty()) {
                    uploadedSignature = await uploadSignature();
                }
            }

            // Step 2: Patch the draft, including the signature if uploaded
            const response1 = await API.patch(RISK_UPDATE_DRAFT_WITH_ID(data.id), {
                tasks: task,
                status: 'Draft',
                hazardName: hazardName,
                raTeamMembersList: selectedCrew,
                description: activityDesc,
                teamLeaderDeclaration: {
                    name: user.firstName,
                    sign: uploadedSignature || ''  // Include the uploaded signature, or keep it empty if not provided
                }
            });

            if (response1.status === 204) {
                setIsLoading(false);
                customSwal2.fire("Risk Assessment Drafted!", "", "success").then((result) => {
                    if (result.isConfirmed) {
                        window.location.reload();
                    }
                });
            } else {
                customSwal2.fire("Please Try Again!", "", "error");
                setIsLoading(false);
            }

        } catch (error) {
            console.error("Error: ", error);
            customSwal2.fire("Please Try Again!", "", "error");
            setIsLoading(false);
        }
    };

    const onSubmitUpdate = async () => {
        getRiskUpdate();
        editUserHandler();


    }

    const editUserHandler = async () => {
        setIsLoading(true);

        try {
            let uploadedSignature = '';

            // Check if signature exists, upload the signature if present
            if (data.teamLeaderDeclaration.sign) {
                uploadedSignature = data.teamLeaderDeclaration.sign
            } else {
                if (!signRef.current.isEmpty()) {
                    uploadedSignature = await uploadSignature();
                } else {
                    customSwal2.fire("Please Sign!", "", "error");
                    setIsLoading(false);
                    return;
                }
            }

            // Proceed to update the risk assessment
            const response1 = await API.patch(RISK_WITH_ID_URL(data.id), {
                tasks: task,
                workActivityId: selectedActivity?.id,
                shortName:shortName,
                departmentId: selectedDepart?.id,
                overallRecommendationOne: recommendationOne,
                overallRecommendationTwo: recommendationTwo,
                additonalRemarks: additionalRecommendation,
                highRisk: [],
                nonRoutineDepartment: nonRoutineDepartment,
                nonRoutineWorkActivity: nonRoutineActivity,
                hazardName: hazardName,
                raTeamMembersList: selectedCrew,
                description: activityDesc,
                status: 'Pending',
                teamLeaderDeclaration: {
                    name: user.firstName,
                    sign: uploadedSignature || '' // Include the uploaded signature or leave it empty
                }
            });

            if (response1.status === 204) {
                setIsLoading(false);
                customSwal2.fire("Risk Assessment Updated!", "", "success").then((result) => {
                    if (result.isConfirmed) {
                        window.location.reload();
                    }
                });
            } else {
                customSwal2.fire("Please Try Again!", "", "error");
                setIsLoading(false);
            }

        } catch (error) {
            console.error("Error: ", error);
            customSwal2.fire("Please Try Again!", "", "error");
            setIsLoading(false);
        }
    };
    const onMethodOn = (value, j, type) => {
        console.log(value, j, type)

        const t = task
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === type) {
                        ite.option.map((con, c) => {
                            if (c === j) {
                                con.method = value
                            }
                        })
                    }
                })
            }
            return item
        }
        )
        setTask(text)
        setItem(text[index])
    }
    const handleRemoveImage = (m, index, type) => {
        const t = task
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === type) {
                        ite.option.map((con, c) => {

                            con.files.splice(m, 1)


                        })

                    }
                })
            }
            return item
        }
        )
        setTask(text)
        setItem(text[index])
    }
    return (
        <div className="row">
            <div className="col-12">
                <div className="card">
                    <div className='card-body p-0'>

                        <div className='borderSection p-4'>

                            <h4>Systematic Hazard Identification & Mandatory Controls</h4>

                            <p>Systematically identify hazards and define mandatory controls for high-risk scenarios (e.g., confined spaces, chemical handling) that may occur during routine or non-routine work.</p>

                            <h4>Key Actions:</h4>

                            <h5>1. Evaluate Generic Hazards:</h5>
                            <ul>
                                <li>Identify risks tied to the scenario itself (e.g., fall hazards for <strong>"Working at Heights"</strong>), regardless of the specific activity.</li>
                            </ul>

                            <h5>2. Define Mandatory Controls:</h5>
                            <ul>
                                <li>Document safeguards (e.g., harness systems, gas detectors) that enforce organizational policies and comply with regulatory mandates.</li>
                            </ul>

                            <h5>3. Link to Work Permits:</h5>
                            <ul>
                                <li>These controls are automatically added as required checkpoints in permits for any work involving the scenario.</li>
                            </ul>

                            <div class="example">
                                <h4>Example:</h4>
                                <p>For <strong>"Hot Work"</strong>, controls like:</p>
                                <ul>
                                    <li>Fire extinguishers on-site</li>
                                    <li>Clearance of flammable materials</li>
                                </ul>
                                <p>become mandatory fields in permits.</p>
                            </div>
                            <hr />

                            <div className='row mb-4'>
                                <div className='col-8'>
                                    <label className='mb-3'>Critical High Risk Activity Name</label>
                                    <InputText placeholder='Critical High Risk Activity Name' value={hazardName} onChange={(e) => setHazardName(e.target.value)} style={{ width: '100%' }} />

                                </div>

                            </div>

                            {/* <div className='row mb-4'>
                                <div className='col-8'>
                                    <label htmlFor="username" className='mb-2'>Permit Short Name</label>
                                    <InputText placeholder='Short Name' onChange={(e) => setShortName(e.target.value)} style={{ width: '100%' }} />

                                </div>

                            </div> */}

                            <div className='row mb-4'>
                                <div className='col-12'>
                                    <Select
                                        labelKey="name"
                                        valueKey="id"
                                        id="user_description"
                                        onChange={(e) => setSelectedCrew(e)}
                                        options={crew}
                                        isMulti={true}
                                        value={selectedCrew}
                                        getOptionLabel={(option) => option.name}
                                        getOptionValue={(option) => option.id}
                                        placeholder="Identify Risk Assessment team members involved in this exercise"
                                    />

                                </div>
                            </div>
                            <p>Identify the qualified RA Team Members to include in this exercise using the drop-down selector; only qualified members will be listed. If a required member is not listed, please contact the Administrator to have them added. Once you’ve made your selections, click the Send Notification button to notify them via email about their inclusion in the team.</p>
                            <div className='row '>
                                <div className='col-4'>
                                    <Button label="Send Notification" outlined className='d-flex' />
                                </div>
                            </div>

                        </div>

                        <div className='borderSection p-4'>

                            <div className='row mb-4'>
                                <div className="d-flex flex-column col-8">
                                    <label htmlFor="username" className='mb-2'>Provide additional information about the high risk activity to clarify scope (Optional)
                                    </label>

                                    <InputTextarea rows={3} cols={30} autoResize value={activityDesc} onChange={(e) => setActivityDesc(e.target.value)} />

                                </div>
                            </div>

                        </div>
                        <div className='borderSection p-4'>


                            <div className='col-12 mb-4'>
                                {task.map((item, i) => {
                                    return (
                                        <>
                                            <div className='mb-3'>
                                                <h6 className='fw-bold'>Consequences</h6>
                                                <p>Identify the potential consequences of this Critical High Risk Activity on Personnel, Environment, Equipment / Property & Service due to the associated high-risk factors.</p>

                                                {item[0].option.map((con, i) => {
                                                    return (
                                                        <Consequence
                                                            key={i}
                                                            con={con}
                                                            i={i}
                                                            impactOn={impactOn}
                                                            onImapactOn={onImapactOn}
                                                            onConseqText={onConseqText}
                                                            onDeleteConseq={onDeleteConseq}
                                                            handleTaskFileChange={handleTaskFileChange}
                                                            required={required}
                                                            type={'hazard'}
                                                            handleRemoveImage={handleRemoveImage}
                                                        />


                                                    )
                                                })}


                                                <Button outlined label="Add Consequence" onClick={() => addConsequence('consequence')} />


                                            </div>
                                            <hr />
                                            <div className='mb-3'>


                                                <h6 className='fw-bold'>Necessary Controls</h6>
                                                <p>Identify the necessary controls to manage the hazards, associated risks, and potential consequences of this Critical High Risk Activity. These controls should be specific to this activity and aimed at minimizing or eliminating the identified risks to ensure safe execution.These controls will also reflect in the Permit to Work Applications when the work involves these High Risk Activities.
                                                </p>
                                                {item[1].option.map((con, i) => {

                                                    return (
                                                        <CurrentControl
                                                            key={i}
                                                            con={con}
                                                            i={i}
                                                            control={control}
                                                            controlType={controlType}
                                                            onImapactOn={onImapactOn}
                                                            onConseqText={onConseqText}
                                                            onDeleteConseq={onDeleteConseq}
                                                            onConseqRequired={onConseqRequired}
                                                            handleTaskFileChange={handleTaskFileChange}
                                                            required={required}
                                                            type={'hazard'}
                                                            handleRemoveImage={handleRemoveImage}
                                                            onMethodOn={onMethodOn}
                                                        />

                                                    )

                                                })}

                                                <Button outlined label="Add Necessary Control" onClick={() => addConsequence('current_control')} />
                                            </div>
                                        </>
                                    )
                                })}

                            </div>

                        </div>



                        <div className='borderSection p-4'>
                            <h5 className="mb-4 fw-bold ">Declaration</h5>


                            <div className='row mb-2'>
                                <p>As the Team Leader for this exercise, I confirm my role in identifying the potential consequences of this <b>Critical High Risk Activity</b> and in outlining the necessary controls. Our team has used its professional judgment to determine that these controls are essential to ensuring safety during this type of work. The results are based on the team’s collective expertise and consensus, drawing on our full capabilities.</p>

                            </div>
                            <div className='row mb-4 text-center'>
                                <div className="d-flex flex-column col-12">
                                    <div className="row mt-4">
                                        <div className="col-12 =">

                                            {domain === 'edit' ? (
                                                data && data.teamLeaderDeclaration && data.teamLeaderDeclaration?.sign ? (
                                                    <>
                                                        <ImageComponent fileName={data.teamLeaderDeclaration.sign} size={'300'} name={false} />
                                                        <p>{data.teamLeaderDeclaration.name}</p>
                                                    </>
                                                ) : (
                                                    <>
                                                        <SignatureCanvas
                                                            penColor="#1F3BB3"
                                                            canvasProps={{
                                                                width: 450,
                                                                height: 120,
                                                                className: "sigCanvas",
                                                                style: {
                                                                    boxShadow: "0px 0px 10px 3px rgb(189 189 189)",
                                                                },
                                                            }}
                                                            ref={signRef}
                                                        />
                                                        <i className="fa fa-undo undo" onClick={() => signRef.current.clear()}></i>
                                                        <p>{user.firstName}</p>
                                                    </>
                                                )
                                            ) : (
                                                <>
                                                    <SignatureCanvas
                                                        penColor="#1F3BB3"
                                                        canvasProps={{
                                                            width: 450,
                                                            height: 120,
                                                            className: "sigCanvas",
                                                            style: {
                                                                boxShadow: "0px 0px 10px 3px rgb(189 189 189)",
                                                            },
                                                        }}
                                                        ref={signRef}
                                                    />
                                                    <i className="fa fa-undo undo" onClick={() => signRef.current.clear()}></i>
                                                    <p>{user.firstName}</p>
                                                </>
                                            )}
                                        </div>

                                    </div>
                                </div>
                            </div>

                        </div>
                        {type !== 'view' &&
                            <div className="col-12 text-center" style={{ padding: 20 }}>
                                <button style={{ marginRight: 10 }}
                                    type="button"
                                    className="btn btn-primary mb-3 "
                                    onClick={(e) => {
                                        e.preventDefault();
                                        if (domain === 'edit') {
                                            draftUserEditHandler();
                                        }
                                        else {
                                            draftUserHandler();
                                        }

                                    }}
                                >
                                    Save as Draft
                                </button>
                                <button
                                    type="button"
                                    className="btn btn-secondary  mb-3 "
                                    onClick={(e) => {
                                        e.preventDefault();

                                        if (domain === 'edit') {
                                            if (data.status === 'Draft') {
                                                editUserHandler();
                                            } else {
                                                setRiskUpdate(true)
                                            }
                                        } else {
                                            createUserHandler();
                                        }

                                    }}
                                >
                                    Release Draft for Affirmation
                                </button>
                            </div>
                        }
                    </div>
                </div>
            </div>

            {console.log(task)}
            {isLoading &&
                <FullLoader />
            }
            {riskUpdate && <RiskUpdate show={riskUpdate} onChangeModel={setRiskUpdate} id={data.id} onSubmitUpdate={onSubmitUpdate} />}
            {Update.length !== 0 && <UpdateTable data={Update} />}

        </div >


    )
}

export default Hazard