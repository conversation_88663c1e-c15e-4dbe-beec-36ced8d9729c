import moment from "moment";

const parseDate = (dateString) => {
    return moment(dateString, [
        'DD-MM-YYYY HH:mm',
        'ddd MMM DD YYYY HH:mm:ss ZZ',
        'Do MMM YYYY hh:mm A',
        'Do MMM YYYY',
        moment.ISO_8601
    ]);
};

const compareDates = (dateA, dateB) => {
    if (dateA.isBefore(dateB)) {
        return -1; // dateA comes before dateB
    } else if (dateA.isAfter(dateB)) {
        return 1; // dateA comes after dateB
    } else {
        return 0; // dates are equal
    }
};

export const sortDate = (e) => {
    const sortedData = e.data.sort((a, b) => {
        const dateA = parseDate(a.created);
        const dateB = parseDate(b.created);
        return compareDates(dateA, dateB);
    });

    return e.order === 1 ? sortedData : sortedData.reverse();
};
