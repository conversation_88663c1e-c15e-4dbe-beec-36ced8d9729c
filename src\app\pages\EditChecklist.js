import React, { useState, useEffect } from "react";
import FormBuilder from "../editors/FormBuilder";
import { CHECKLIST_WITH_ID_URL } from '../constants';
import { singlePopup } from './../notifications/Swal';
import { useLocation } from "react-router-dom/cjs/react-router-dom.min";
import API from "../services/API";
const EditChecklist = () => {
    const location = useLocation();
    console.log(location)
    const [isLoading, setIsLoading] = useState(true);
    const [checklistData, setChecklistData] = useState({ name: '', value: {} });

    useEffect(() => {
        getChecklistData(location.state.id);
    }, [location.state.id]);

    const getChecklistData = async (id) => {

        const response = await API.get(CHECKLIST_WITH_ID_URL(id));
        if (response.status === 200) {
            setChecklistData(response.data)
            setIsLoading(false)
        }

    }

    const handleChecklistSave = async (data) => {
        const response = await API.patch(CHECKLIST_WITH_ID_URL(location.state.id), {
            value: data
        })
        if (response.status === 204) {
            singlePopup.fire(
                'Checklist Saved!',
                '',
                'success'
            );
        } else {
            //show error
            singlePopup.fire(
                'Please Try Again',
                '',
                'error'
            )
        }
    }


    return (
        <>
            <div>
                <div className="row">
                    <div className="col-12">
                        <div className="card">
                            <div className="card-body">

                                <h4 className="card-title">{checklistData.name}</h4>
                                <div className="row">
                                    <div className="col-12">
                                        <div>
                                            {isLoading && <h4>Please Wait! Loading...</h4>}
                                            {!isLoading && <FormBuilder onSubmit={handleChecklistSave} values={checklistData.value} id={location.state.id} />}

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    )
}

export default EditChecklist;