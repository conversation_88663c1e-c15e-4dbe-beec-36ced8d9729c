import React, { useState, useEffect } from "react";
import { Mo<PERSON>, Button, Form } from 'react-bootstrap';
import Box from '@mui/material/Box';
import Stepper from '@mui/material/Stepper';
import Step from '@mui/material/Step';
import StepLabel from '@mui/material/StepLabel';
import StepContent from '@mui/material/StepContent';
import Paper from '@mui/material/Paper';
import Switch from "react-switch";
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import Typography from '@mui/material/Typography';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import API from "../services/API";
import { DropzoneArea } from 'material-ui-dropzone';
import Select from "react-select";

import { USERS_URL, AIR_GM_OPS_URL, AIR_INVESTIGATE_WITH_ID_URL, AIR_DUTY_ENGINEER_MANAGER_URL, AIR_COST_ESTIMATION_WITH_ID_URL, STATIC_URL, API_URL } from "../constants";
import IncidentStory from "./IncidentStory";
import cogoToast from "cogo-toast";
import InvoiceGenerator from './InvoiceGenerator';
import TotalCalculator from './TotalCalculator';
import Grid from '@mui/material/Grid'; // Import Grid
import FormControl from '@mui/material/FormControl'; // Import FormControl
import FormLabel from '@mui/material/FormLabel'; // Import FormLabel
import RadioGroup from '@mui/material/RadioGroup'; // Import RadioGroup
import FormControlLabel from '@mui/material/FormControlLabel'; // Import FormControlLabel
import Radio from '@mui/material/Radio'; // Import Radio
import TextField from '@mui/material/TextField'; // Import TextField
import InvoiceComponent from "./InvoiceComponent";
import axios from "axios";

const AirCostEstimatorCard = ({ showModal, setShowModal, data, setData }) => {

    const [showNext, setShowNext] = useState(false);
    const [showLeftPane, setShowLeftPane] = useState(true);

    const [isInspection, setIsInspection] = useState(true);
    const [inspectionDate, setInspectionDate] = useState(new Date())
    const [inspectionDetails, setInspectionDetails] = useState('')

    const [inspectionStates, setInspectionStates] = useState({});
    const [damagedData, setDamagedData] = useState({})



    const [dutyEngManager, setDutyEngManager] = useState([])
    useEffect(() => {
        getDutyEngManager()
    }, [])

    const getDutyEngManager = async () => {
        const response = await API.post(AIR_DUTY_ENGINEER_MANAGER_URL, { locationOneId: '', locationTwoId: '', locationThreeId: '', locationFourId: '' });
        if (response.status === 200) {
            setDutyEngManager(response.data)
        }
    }

    const [selectedDutyEngManager, setSelectedDutyEngManager] = useState('');

    const uploadFiles = async (files) => {
        const formData = new FormData();
        files.forEach(file => {
            formData.append('file', file);
        });
        const token = localStorage.getItem('access_token');
        try {
            const response = await axios.post(`${API_URL}/files`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                    'Authorization': `Bearer ${token}`,
                },
            });
            if (response.status === 200) {
                return response.data.files.map(file => file.originalname);  // Return file names
            } else {
                throw new Error('Failed to upload files');
            }
        } catch (error) {
            console.error('Error uploading files:', error);
            throw error;
        }
    };

    const [isClicked, setIsClicked] = useState(false);
    const handleSubmit = async () => {
        setIsClicked(true);
      
        try {
            // Upload files first and get their names
            const uploadedFileNames = await uploadFiles(files);

            // Assuming each item in damagedData needs the uploaded file names
           

            // Patch Request to update cost estimation with new damaged data
            const response = await API.patch(AIR_COST_ESTIMATION_WITH_ID_URL(data.id, data.actionId), {
                costEstimationFiles: uploadedFileNames,
                costEstimation: damagedData,
                dutyEngManagerId: selectedDutyEngManager
            });

            // If the patch request fails, no need to proceed further
            if (response.status !== 204) {
                setIsClicked(false);
                console.error('Failed to patch data. Status:', response.status);
                return;  // Handle error appropriately
            }
            setIsClicked(false);

            cogoToast.success(`Action for IR ${data.maskId} Completed`);
            setShowModal(false); // Close the modal on success
        } catch (error) {
            setIsClicked(false);
            console.error('An error occurred:', error);
            cogoToast.error("Failed to complete the action.");
        }
        setIsClicked(false);
    };


    const [files, setFiles] = useState([]);

    const handleFileChange = (file) => {

        setFiles(file)

    }




    useEffect(() => {
        if (data?.engineerComments?.damagedEquipmentDetails) {
            const initialInspectionStates = {};
            data.engineerComments.damagedEquipmentDetails.forEach((item, index) => {
                initialInspectionStates[index] = false; // Initially set all to 'No'
            });
            setInspectionStates(initialInspectionStates);
            const enrichedDamagedData = data.engineerComments.damagedEquipmentDetails.map(item => ({
                ...item, // Spread the existing item properties
                isInspectionInvolved: false, // Default value
                inspectionDate: '', // Default to empty or a sensible default
                inspectionRemarks: '', // Default to empty
            }));
            setDamagedData(enrichedDamagedData)
        }
    }, [data]);

    const handleInspectionChange = (index, isInspectionInvolved) => {
        const updatedDamagedData = [...damagedData];
        // Update the specific object in the array
        updatedDamagedData[index].isInspectionInvolved = isInspectionInvolved;
        // Update the state with the new array
        setDamagedData(updatedDamagedData);
    };

    const handleInspectionDateChange = (index, date) => {
        const updatedDamagedData = [...damagedData];
        updatedDamagedData[index].inspectionDate = date;
        setDamagedData(updatedDamagedData);
    };

    const handleInspectionRemarksChange = (index, remarks) => {
        const updatedDamagedData = [...damagedData];
        updatedDamagedData[index].inspectionRemarks = remarks;
        setDamagedData(updatedDamagedData);
    };

    const handleUpdateInformation = (index, currency, exchangeRate, data) => {

        const updatedDamagedData = [...damagedData];

        // Update the specific object in the array
        const updatedItem = updatedDamagedData[index];
        updatedItem.currency = currency;
        updatedItem.exchangeRate = exchangeRate;
        updatedItem.costEstimation = data; // Assuming you want to store the third parameter under the key `additionalData`
        updatedDamagedData[index] = updatedItem;
        console.log(updatedDamagedData)
        // Update the state with the new array
        setDamagedData(updatedDamagedData);
    }


    return (
        <>
            {data && <Modal
                show={showModal}
                size="lg"
                onHide={() => setShowModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
                backdrop="static"
            >
                <Modal.Header>

                    <div className="w-100 d-flex align-items-center justify-content-between">
                        <h4 >
                            IR Information - {data.maskId}
                        </h4 >
                        <h4 >
                            Incident Date & Time: {data.incidentDate}
                        </h4 >
                    </div>

                </Modal.Header>

                <Modal.Body>
                    <div className="row">



                        <div className={`col-md-12`}>
                            <Box>
                                <div className="row">
                                    <label>Engineer Comments</label>
                                    <p>{data?.engineerComments?.comments}</p>
                                    <label>Labour Time</label>
                                    <p>{data?.engineerComments?.labourTime} hours</p>

                                    <div className="row">
                                        {damagedData?.length > 0 && (
                                            damagedData.map((item, index) => (
                                                <Accordion key={index} TransitionProps={{ unmountOnExit: true }} className="mb-3">
                                                    <AccordionSummary
                                                        expandIcon={<ExpandMoreIcon />}
                                                        aria-controls={`panel${index}a-content`}
                                                        id={`panel${index}a-header`}
                                                    >
                                                        <Typography><strong>Equipment #{index + 1}:</strong> {item.category}</Typography>
                                                        <Typography style={{ marginLeft: 'auto', opacity: 0.7 }}>Click to expand</Typography>
                                                    </AccordionSummary>
                                                    <AccordionDetails>
                                                        <Box sx={{ width: '100%' }}>
                                                            <Typography variant="body2" paragraph>
                                                                Detailed information for <strong>{item.category}</strong>:
                                                            </Typography>
                                                            <Typography variant="body2" paragraph>
                                                                Uploaded Documents:

                                                                {item?.files?.map(i => {
                                                                    return (
                                                                        <div>
                                                                            <a href={`${STATIC_URL}/${i}`} target="_blank">{i}</a>
                                                                        </div>
                                                                    )
                                                                })}

                                                            </Typography>

                                                            <Grid container spacing={2}>
                                                                <Grid item xs={6} sm={3}>
                                                                    <Typography><strong>Number:</strong> {item.number}</Typography>
                                                                </Grid>
                                                                <Grid item xs={6} sm={3}>
                                                                    <Typography><strong>Damage Type:</strong> {item.damageType}</Typography>
                                                                </Grid>
                                                                <Grid item xs={12} sm={6}>
                                                                    <Typography><strong>Cost Details:</strong> {item.costDetails}</Typography>
                                                                </Grid>
                                                                <Grid item xs={6} sm={3}>
                                                                    <Typography><strong>Operational:</strong> {item.operational ? "Yes" : "No"}</Typography>
                                                                </Grid>
                                                                <Grid item xs={12} sm={9}>
                                                                    <div>
                                                                        <FormControl component="fieldset">
                                                                            <FormLabel component="legend">Inspection Involved By Insurance</FormLabel>
                                                                            <RadioGroup
                                                                                row
                                                                                aria-label={`inspectionInvolved${index}`}
                                                                                name={`inspectionInvolved${index}`}
                                                                                onChange={(e) => handleInspectionChange(index, e.target.value === 'Yes')}
                                                                            >
                                                                                <FormControlLabel value="Yes" control={<Radio />} label="Yes" />
                                                                                <FormControlLabel value="No" control={<Radio />} label="No" />
                                                                            </RadioGroup>
                                                                        </FormControl>
                                                                    </div>
                                                                </Grid>
                                                                <Grid item xs={6} sm={3}>
                                                                    <TextField
                                                                        label="Inspection Date"
                                                                        type="date"
                                                                        style={{color: 'black'}}
                                                                        value={damagedData[index].inspectionDate}
                                                                        onChange={(e) => handleInspectionDateChange(index, e.target.value)}
                                                                        disabled={!damagedData[index].isInspectionInvolved}
                                                                        InputLabelProps={{ shrink: true }}
                                                                        fullWidth
                                                                    />
                                                                </Grid>
                                                                <Grid item xs={12} sm={9}>
                                                                    <TextField
                                                                        label="Inspection Remarks"
                                                                        type="text"
                                                                        style={{color: 'black'}}
                                                                        value={damagedData[index].inspectionRemarks}
                                                                        onChange={(e) => handleInspectionRemarksChange(index, e.target.value)}
                                                                        disabled={!damagedData[index].isInspectionInvolved}
                                                                        variant="outlined"
                                                                        fullWidth
                                                                    />
                                                                </Grid>
                                                                <Grid item xs={12}>
                                                                    <InvoiceComponent costData={damagedData[index].costEstimation ? damagedData[index].costEstimation : []} updateInformation={(currency, exchangeRate, data) => handleUpdateInformation(index, currency, exchangeRate, data)} />
                                                                </Grid>
                                                            </Grid>
                                                        </Box>
                                                    </AccordionDetails>
                                                </Accordion>
                                            ))
                                        )}
                                    </div>


                                </div>


                                <div className='row'>
                                    <div className='col'>
                                        <div className='form-group'>
                                            <DropzoneArea
                                                acceptedFiles={[
                                                    'application/pdf',
                                                    'image/jpeg',
                                                    'image/png',
                                                    

                                                ]}
                                                dropzoneText={"Drag and drop files / documents / pictures"}
                                                filesLimit={5}
                                                maxFileSize={104857600}
                                                onChange={handleFileChange}
                                            />
                                        </div>
                                    </div>
                                </div>

                                <div className='row'>
                                    <div className='col'>
                                        <div className='form-group'>
                                            <label>Submit to Duty Engineer Manager</label>
                                            <select onChange={(e) => setSelectedDutyEngManager(e.target.value)} className="form-control">
                                                <option value="">Choose Duty Engineer Manager</option>
                                                {
                                                    dutyEngManager.map(user => {
                                                        return (
                                                            <option value={user.id}> {user.firstName} </option>
                                                        )
                                                    })
                                                }
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div className="row">
                                    <div className="col-3">
                                        <Button variant="primary" onClick={handleSubmit}  disabled={isClicked}>
                                            Submit
                                        </Button>
                                    </div>

                                </div>
                            </Box>
                        </div>


                    </div>



                </Modal.Body>

                <Modal.Footer className="flex-wrap">

                    <Button variant="light" onClick={() => { setShowModal(false); }}>Close</Button>

                </Modal.Footer>
            </Modal>}
        </>
    )
}

export default AirCostEstimatorCard;