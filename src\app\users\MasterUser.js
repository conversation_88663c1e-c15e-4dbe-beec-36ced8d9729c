// @ts-nocheck
import React, { useState, useRef, useEffect } from 'react';
import DataTables from '../tables/DataTables';
import $ from "jquery";
import { Modal, Button, Form } from 'react-bootstrap';
import cogoToast from 'cogo-toast';
import Loader from '../shared/Loader';
import Swal from 'sweetalert2';
import { useHistory } from "react-router-dom";
import { SERVICE_DETAILS, EXTERNAL_USERS_URL, USERS_URL, USERS_URL_WITH_ID, GET_INDIVIDUAL_USER_LOCATION_ROLE_URL, INDIVIDUAL_USER_LOCATION_ROLE_URL, DEPARTMENT_URL, DESIGNATION_URL, GMS1_URL, WORKING_GROUP_URL, } from '../constants';
import { deletePopup, singlePopup } from "../notifications/Swal";
import API from '../services/API';
import MaterialTable from 'material-table';
import { ThemeProvider, createTheme } from '@mui/material';
// import { userColumns, tableOptions } from '../pages/TableColumns';
import CardOverlay from '../pages/CardOverlay';
import FilterLocation from '../pages/FilterLocation';

import { alpha } from '@material-ui/core/styles'

// @ts-ignore
window.jQuery = $;
// @ts-ignore
window.$ = $;



const customSwal = Swal.mixin({
    customClass: {
        confirmButton: 'btn btn-danger',
        cancelButton: 'btn btn-light'
    },
    buttonsStyling: false
})

const customSwal2 = Swal.mixin({
    customClass: {
        confirmButton: 'btn btn-primary',

    },
    buttonsStyling: false
})

const MasterUser = () => {
    const defaultMaterialTheme = createTheme();

    const [mdShow, setMdShow] = useState(false);
    const [userShow, setUserShow] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const history = useHistory();
    const uName = useRef();
    const uEmail = useRef();
    const uPassword = useRef();
    const [roles, setRoles] = useState([])
    const [allRoles, setAllRoles] = useState({ country: [], ehs: [], eptw: [], incident: [], inspection: [], plant: [], groupEhs: [], report: [], doc: [] })
    const [selectedRoles, setSelectedRoles] = useState({ country: [], ehs: [], eptw: [], incident: [], inspection: [], plant: [], groupEhs: [], report: [], doc: [] })
    const [selectedUserId, setSelectedUserId] = useState({ id: "", email: "", name: "" });
    useEffect(() => {
        // getCountry();
        // getEhsRole();
        // getEptwRole();
        // getIncidentRole();
        // getInspectionRole();
        // getPlantRole();
        // getGroupEhsRole();
        // getReportRole();
        // getDocumentRole();
        getServices()

    }, [])

    const getServices = async () => {

        const params = {
            "include": [

                { "relation": "roles" },


            ]
        };
        const response = await API.get(`${SERVICE_DETAILS}?filter=${encodeURIComponent(JSON.stringify(params))}`)

        if (response.status === 200) {

            setRoles(response.data)
        }
    }


    const uRole = useRef();

    const thead = [
        'Name',
        'Email',
        'Organization',
        'Role Assignment',

    ];



    const [data, setData] = useState([])
    useEffect(() => {
        getUsersData();
    }, [])

    const getUsersData = async () => {
        const params = {
            "include": [

                // { "relation": "workingGroup" },
                // { "relation": "designation" },
                // { "relation": "locationOne" }

            ]
        };





        const response = await API.get(`${USERS_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`);
        if (response.status === 200) {
            setData(response.data.sort((a, b) => a.firstName.toLowerCase().localeCompare(b.firstName.toLowerCase())))

        }
    }

    const userColumns = [

        {
            title: "Name",
            field: "firstName",
            cellStyle: {
                padding: '1.125rem 1.375rem',
                width: '30%',
                maxWidth: '30%'
            }
        },
        {
            title: "Email",
            field: "email",
            cellStyle: {
                padding: '1.125rem 1.375rem',
                width: '30%',
                maxWidth: '30%'
            }
        },
        {
            title: "Organization",
            field: "company",
            cellStyle: {
                padding: '1.125rem 1.375rem',
                width: '30%',
                maxWidth: '30%'
            },
            render: rowData => rowData.type === 'Internal' ? 'Internal' : (rowData.company ? rowData.company : 'External')
        }
    ];


    const tableOptions = (title = '') => {
        return {
            exportButton: true,
            exportAllData: true,
            exportCsv: (columns, data) => {
                console.log(data)
                const customTitle = title || ''; // Custom title for cell A1
                const filteredData = data.map(row => ({
                    firstName: row.firstName,
                    email: row.email,
                    type: row.type === 'Internal' ? 'Internal' : row.company ? row.company : 'External',
                }));
                // Convert the data to CSV format
                const csvContent = `${customTitle}\n${columns.map(column => column.title).join(',')}\n${filteredData.map(row => Object.values(row).join(',')).join('\n')}`;

                // Create a Blob and initiate the download
                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = 'exported_data.csv';
                link.click();
            },
            actionsColumnIndex: -1,
            actionsCellStyle: {
                padding: '1.125rem 1.375rem',
            },
            pageSize: 20,
            headerStyle: {

                padding: '1.125rem 1.375rem',
                fontSize: '0.812rem'
            },
            rowStyle: {
                // padding: '1.125rem 1.375rem',
                fontSize: '0.812rem'
            }
        }
    }
    const viewAssignPermission = async (id, email, name) => {

        const response = await API.get(USERS_URL_WITH_ID(id))
        if (response.status === 200) {

            if (response.data.customRoles)
                setSelectedRoles(response.data.customRoles)
            else
                setSelectedRoles({ country: [], ehs: [], eptw: [], incident: [], inspection: [], plant: [], groupEhs: [], report: [] })
            setSelectedUserId({ id: id, email: email, name: name })
            setMdShow(true)

        }
    }

    const viewDepartmentSelect = async (id, department, name) => {

    }



    const handleRoleChange = (e, category) => {
        const roleId = e.target.value;
        console.log(roleId)
        setIndividualSelectedRole((prevRoles) => {
            if (e.target.checked) {
                // Add the role to the selected roles
                return { ...prevRoles, roles: [...prevRoles.roles, roleId] };
            } else {
                // Remove the role from the selected roles
                return { ...prevRoles, roles: prevRoles.roles.filter((id) => id !== roleId) };
            }
        });
        setSelectedRoles((prevRoles) => {
            const categoryRoles = prevRoles[category] || [];

            console.log(category, prevRoles, categoryRoles, 'check')
            if (e.target.checked) {
                // Add the role to the selected roles
                return {
                    ...prevRoles,
                    [category]: [...categoryRoles, roleId],
                };
            } else {
                // Remove the role from the selected roles
                return {
                    ...prevRoles,
                    [category]: categoryRoles.filter((id) => id !== roleId),
                };
            }
        });
    };

    const handleAssignSubmit = async () => {
        const id = selectedUserId.id;
        let flag = false;
        const response = await API.post(INDIVIDUAL_USER_LOCATION_ROLE_URL, { userId: id, roles: individualSelectedRole.roles, locations: { locationOne: selectedLocationOne, locationTwo: selectedLocationTwo, locationThree: selectedLocationThree, locationFour: selectedLocationFour } })
        if (response.status === 200) {
            flag = true;
        }
        const response2 = await API.patch(USERS_URL_WITH_ID(id), { customRoles: selectedRoles })
        if (response2.status === 204 && flag) {
            // setSelectedRoles({ country: [], ehs: [], eptw: [], incident: [], inspection: [], plant: [] })
            // setSelectedUserId({ id: "", email: "", name: "" })
            // setMdShow(false)
            // setIndividualSelectedRole({ roles: [] })
            cogoToast.info('Assigned', { position: 'top-right' })

        }

    }

    const handleAssignClose = () => {
        setSelectedRoles({ country: [], ehs: [], eptw: [], incident: [], inspection: [], plant: [], groupEhs: [], report: [], doc: [] })
        setSelectedUserId({ id: "", email: "", name: "" })
        setMdShow(false)
        setIndividualSelectedRole({ roles: [], disabledRoles: [] })

    }
    const createUserHandler = async () => {
        // @ts-ignore
        setIsLoading(true)

        const response = await API.post(EXTERNAL_USERS_URL, {
            firstName: uName.current.value,
            email: uEmail.current.value,
            password: uPassword.current.value,


        })
        if (response.status === 200) {

            cogoToast.info('Created!', { position: 'top-right' })
            $('#dataTable').DataTable().ajax.reload();
            customSwal2.fire(
                'User Created!',
                '',
                'success'
            )
        } else {
            customSwal2.fire(
                'Please Try Again!',
                '',
                'error'
            )
            setIsLoading(false)
        }



        uName.current.value = '';
        uEmail.current.value = '';
        uPassword.current.value = '';
        setUserShow(false)
        setIsLoading(false)
    }

    const tableStyle = {
        borderRadius: '0',
        boxShadow: 'none',
    };

    const tableActions = [
        {
            icon: 'grading',
            tooltip: 'Role Assignment',
            onClick: (event, rowData) => {
                // Do save operation
                // console.log(rowData)
                viewAssignPermission(rowData.id, rowData.email, rowData.firstName)
            }
        }
    ]

    const localization = {
        header: {
            actions: 'Actions'
        }
    };


    const [selectedLocationOne, setSelectedLocationOne] = useState('');
    const [selectedLocationTwo, setSelectedLocationTwo] = useState('');
    const [selectedLocationThree, setSelectedLocationThree] = useState('');
    const [selectedLocationFour, setSelectedLocationFour] = useState('');

    const handleFilter = (locationOneId, locationTwoId, locationThreeId, locationFourId) => {

        setSelectedLocationOne(locationOneId)
        setSelectedLocationTwo(locationTwoId)
        setSelectedLocationThree(locationThreeId)
        setSelectedLocationFour(locationFourId)
    };

    const [individualSelectedRole, setIndividualSelectedRole] = useState({ roles: [], disabledRoles: [] })

    useEffect(() => {
        if (selectedLocationOne === 'tier1-all' || selectedLocationTwo === 'tier2-all' || selectedLocationThree === 'tier3-all' || selectedLocationFour)
            getIndividualRoles()
    }, [selectedLocationOne, selectedLocationTwo, selectedLocationThree, selectedLocationFour])

    const getIndividualRoles = async () => {
        const response = await API.post(GET_INDIVIDUAL_USER_LOCATION_ROLE_URL, { userId: selectedUserId.id, locations: { locationOne: selectedLocationOne, locationTwo: selectedLocationTwo, locationThree: selectedLocationThree, locationFour: selectedLocationFour } });
        if (response.status === 200) {
            if (response.data && response.data.length > 0)
                setIndividualSelectedRole(response.data[0])
            else
                setIndividualSelectedRole({ roles: [], disabledRoles: [] })
        }
    }

    const [departments, setDepartments] = useState([])
    useEffect(() => {
        getDepartments()
    }, [])

    const getDepartments = async () => {
        const response = await API.get(GMS1_URL); // Replace DEPARTMENTS_URL with your actual endpoint
        if (response.status === 200) {
            setDepartments(response.data);
        }
    }

    const [designations, setDesignations] = useState([])
    useEffect(() => {
        getDesignations()
    }, [])

    const getDesignations = async () => {
        const response = await API.get(DESIGNATION_URL); // Replace DEPARTMENTS_URL with your actual endpoint
        if (response.status === 200) {
            setDesignations(response.data);
        }
    }

    const [workingGroups, setWorkingGroups] = useState([])
    useEffect(() => {
        getWorkingGroups()
    }, [])

    const getWorkingGroups = async () => {
        const response = await API.get(WORKING_GROUP_URL); // Replace DEPARTMENTS_URL with your actual endpoint
        if (response.status === 200) {
            setWorkingGroups(response.data);
        }
    }
    return (
        <CardOverlay>
            <ThemeProvider theme={defaultMaterialTheme}>
                <MaterialTable
                    columns={userColumns}
                    data={data}
                    title={
                        <div className='mt-3 mb-3'>
                            <h4 style={{ margin: 0 }}>Master User Listing and Role Assignment</h4>
                            <p style={{ margin: 0, fontWeight: 'normal' }}>Master Users of all licensed users added through active directory or directly onto the application. Assign each user assigned roles and access rights for each module from here. </p>
                        </div>
                    }
                    style={tableStyle}
                    actions={tableActions}
                    options={tableOptions('')}
                    localization={localization}
                // editable={{
                //     onRowUpdate: (newData, oldData) =>
                //         new Promise(async (resolve, reject) => {
                //             // Check if any fields have changed




                //             // Function to perform the API update
                //             const updateUser = async () => {

                //                 console.log('updating the user');

                //                 try {
                //                     const payload = {

                //                         // ghsOneId: newData.ghsOne.id,
                //                         // designationId: newData.designation.id,
                //                         // workingGroupId: newData.workingGroup.id,
                //                         firstName: newData.firstName,
                //                         email: newData.email,
                //                         company:  newData.company,
                //                         // empId: newData.empId
                //                     };

                //                     console.log('payload', payload)
                //                     const response = await API.patch(`${USERS_URL}/${newData.id}`, payload);

                //                     if (response.status === 204) {
                //                         const dataUpdate = [...data];
                //                         const index = oldData.tableData.id;

                //                         // Update local data

                //                         dataUpdate[index].firstName = newData.firstName;
                //                         dataUpdate[index].email = newData.email;
                //                         dataUpdate[index].company =  newData.company;
                //                         // dataUpdate[index].empId = newData.empId;

                //                         // const departmentName = departments.find(item => item.id === newData.ghsOne.id);
                //                         // dataUpdate[index].ghsOne = {
                //                         //     id: newData.ghsOne.id,
                //                         //     name: departmentName.name
                //                         // };

                //                         // const designationName = designations.find(item => item.id === newData.designation.id);
                //                         // dataUpdate[index].designation = {
                //                         //     id: newData.designation.id,
                //                         //     name: designationName.name
                //                         // };

                //                         // const workingGroupName = workingGroups.find(item => item.id === newData.workingGroup.id);
                //                         // dataUpdate[index].workingGroup = {
                //                         //     id: newData.workingGroup.id,
                //                         //     name: workingGroupName.name
                //                         // };

                //                         console.log(dataUpdate, 'Updated Data')
                //                         setData([...dataUpdate]);
                //                         resolve();
                //                     } else {
                //                         reject();
                //                     }
                //                 } catch (error) {
                //                     console.error("Error updating user:", error);
                //                     reject();
                //                 }
                //             }

                //             updateUser();
                //         }),
                // }}
                />
            </ThemeProvider>


            <Modal
                show={mdShow}
                size={'xl'}
                onHide={() => setMdShow(false)}
                aria-labelledby="example-modal-sizes-title-md"
                backdrop="static" // Prevents closing on outside click
                keyboard={false} // Prevents closing on Escape key press
            >
                <Modal.Header>
                    Assign Permissions to {selectedUserId.name}
                </Modal.Header>

                <Modal.Body>
                    <form className="forms">

                      
                        <FilterLocation handleFilter={handleFilter} disableAll={true} period={false} />
                        {((selectedLocationOne === 'tier1-all' || selectedLocationTwo === 'tier2-all' || selectedLocationThree === 'tier3-all' || selectedLocationFour) && individualSelectedRole.roles) && <>

                            <>
                                {roles
                                    .filter(service => {
                                        // Helper to identify admin roles
                                        const isAdminRole = (role) =>
                                            role.name?.toLowerCase().includes('admin') ||
                                            role.maskName?.toLowerCase().includes('admin') ||
                                            role.maskId?.toLowerCase().includes('admin');

                                        // Get admin roles in this service
                                        const adminRoleIds = (service.roles || [])
                                            .filter(isAdminRole)
                                            .map(role => role.id);

                                        // Display only if user has one of the admin roles
                                        return adminRoleIds.some(id =>
                                            individualSelectedRole.roles.includes(id) ||
                                            individualSelectedRole.disabledRoles.includes(id)
                                        );
                                    })
                                    .map((service, k) => {
                                        return (
                                            <div key={k}>
                                                <h4 className='mb-3 role-h4'>{service.name}</h4>
                                                <div className='form-group mb-4'>
                                                    {service.roles &&
                                                        service.roles
                                                            .filter(role => {
                                                                // Filter out admin roles from being shown
                                                                const isAdmin = role.name?.toLowerCase().includes('admin') ||
                                                                    role.maskName?.toLowerCase().includes('admin') ||
                                                                    role.maskId?.toLowerCase().includes('admin');
                                                                return !isAdmin;
                                                            })
                                                            .map((role, l) => {
                                                                const isDisabled = individualSelectedRole.disabledRoles.includes(role.id);
                                                                const isChecked = individualSelectedRole.roles.includes(role.id) || isDisabled;
                                                                return (
                                                                    <label
                                                                        key={l}
                                                                        className={`label-role checkbox-bootstrap checkbox-lg col-4 ${isDisabled ? 'opacity-med' : ''}`}
                                                                    >
                                                                        <input
                                                                            value={role.id}
                                                                            checked={isChecked}
                                                                            disabled={isDisabled}
                                                                            onChange={(e) => handleRoleChange(e, service.id)}
                                                                            type='checkbox'
                                                                            className='me-1'
                                                                        />
                                                                        <span className="checkbox-placeholder"></span>
                                                                        {role.name}
                                                                    </label>
                                                                );
                                                            })}
                                                </div>
                                            </div>
                                        );
                                    })}
                            </>



                        </>}
                    </form>
                </Modal.Body>

                <Modal.Footer className="flex-wrap">

                    <>
                        <Button variant="light" onClick={handleAssignClose}>Close</Button>

                        {selectedUserId.id && <Button variant="primary" onClick={handleAssignSubmit}>Assign</Button>}

                    </>


                </Modal.Footer>
            </Modal>


            <Modal
                show={userShow}
                onHide={() => setUserShow(false)}
                aria-labelledby="example-modal-sizes-title-md"
            >

                <Modal.Body>
                    <form className="forms">
                        <div className="form-group">
                            <label htmlFor="user_name" >Name</label>
                            <Form.Control type="text" ref={uName} id="user_name" placeholder="Enter User Name" />
                        </div>

                        <div className="form-group">
                            <label htmlFor="user_category" >Email</label>
                            <Form.Control type="email" ref={uEmail} id="user_category" placeholder="Enter User Email" />
                        </div>

                        <div className="form-group">
                            <label htmlFor="user_description" >Temporary Password</label>
                            <Form.Control type="password" ref={uPassword} id="user_description" placeholder="Enter Password" />
                        </div>



                    </form>
                </Modal.Body>

                <Modal.Footer className="flex-wrap">
                    {
                        isLoading ? <Loader /> : (
                            <>
                                <Button variant="light" onClick={() => setUserShow(false)}>Cancel</Button>
                                <Button variant="primary" onClick={createUserHandler}>Create</Button>
                            </>
                        )
                    }

                </Modal.Footer>
            </Modal>
        </CardOverlay>
    )
}


export default MasterUser;
