import React from 'react';
import Severity from './Severity';
import Likelihood from './Likelihood';
import RiskLevel from './RiskLevel';
import AcceptableRisk from './AcceptableRisk';

const RiskAssessment = ({
    item,
    severity,
    severityData,
    required,
    onChangeSeverity,
    likelyhood,
    levelData,
    onChangeLikelyhood,
    rowClassName,
    tableData,
    cellClassName,
    cellStyle,
    onChangeReAss
}) => {
    return (
        <div>
            <h6 className='fw-bold'>Estimate the Risk of this Sub Activity</h6>
            <p>For this Sub Activity, assess the Severity & Likelihood of the <b>identified consequences</b>, considering the <b>current controls</b> in place</p>
            <ul>
                <li>User the tables below as a guide </li>
                <li>Assess how well the preventative controls reduce the chances of an event occurring and how mitigative controls limit the impact if an event does occur.
                </li>
                <li>Ensure that your assessment reflects the worst-case scenario, based on existing controls and for each impacted category (People, Environment etc.)
                </li>
            </ul>
            <p>This assessment will be used to determine the Risk Level for this sub-activity</p>

            <Severity
                severity={severity}
                severityData={severityData}
                required={required}
                onChangeSeverity={onChangeSeverity}
                item={item}
            />

            <Likelihood
                likelyhood={likelyhood}
                levelData={levelData}
                required={required}
                onChangeLikelyhood={onChangeLikelyhood}
                item={item}
                rowClassName={rowClassName}
            />

            <RiskLevel
                item={item}
                tableData={tableData}
                cellClassName={cellClassName}
                cellStyle={cellStyle}
            />

            <AcceptableRisk
                item={item}
                onChangeReAss={onChangeReAss}
            />
        </div>
    );
};

export default RiskAssessment;
