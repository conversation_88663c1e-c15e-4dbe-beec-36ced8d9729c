import React from 'react'
import Select from 'react-select'
import { InputTextarea } from 'primereact/inputtextarea'

function StageOne({ formData, handleSelectChange, surfaceType, surfaceCondition, lighting, weatherCondition, pathways, errors, handleChange, disable }) {
    return (
        <div>
            <p className='mb-3'>Events or conditions that occurred before the incident, indicating potential risks or contributing to the incident occurrence.</p>

            <h4 className='fw-bold mt-3 mb-4'>Site & Environmental Conditions at the time of the Incident</h4>
            <div className="row mb-2">

                <div className="col mb-2">
                    <label htmlFor="surfaceType" className="mb-2">Surface Type</label>
                    <Select
                        options={surfaceType}
                        value={surfaceType.find(option => option.label === formData.surfaceType)}
                        onChange={(selectedOption) => handleSelectChange(selectedOption, 'surfaceType')}
                        placeholder="Select"
                        isClearable
                        isDisabled={disable}
                    />
                </div>

                <div className="col mb-2">
                    <label htmlFor="surfaceCondition" className="mb-2">Surface Condition</label>
                    <Select
                        options={surfaceCondition}
                        value={surfaceCondition.find(option => option.label === formData.surfaceCondition)}
                        onChange={(selectedOption) => handleSelectChange(selectedOption, 'surfaceCondition')}
                        placeholder="Select"
                        isClearable
                        isDisabled={disable}
                    />
                </div>

                <div className="col mb-2">
                    <label htmlFor="lighting" className="mb-2">Lighting</label>
                    <Select
                        options={lighting}
                        value={lighting.find(option => option.label === formData.lighting)}
                        onChange={(selectedOption) => handleSelectChange(selectedOption, 'lighting')}
                        placeholder="Select"
                        isClearable
                        isDisabled={disable}
                    />
                </div>

                <div className="col mb-2">
                    <label htmlFor="weatherCondition" className="mb-2">Weather Condition</label>
                    <Select
                        options={weatherCondition}
                        value={weatherCondition.find(option => option.label === formData.weatherCondition)}
                        onChange={(selectedOption) => handleSelectChange(selectedOption, 'weatherCondition')}
                        placeholder="Select"
                        isClearable
                        isDisabled={disable}
                    />
                </div>

                <div className="col mb-2">
                    <label htmlFor="pathways" className="mb-2">Pathways</label>
                    <Select
                        options={pathways}
                        value={pathways.find(option => option.label === formData.pathways)}
                        onChange={(selectedOption) => handleSelectChange(selectedOption, 'pathways')}
                        placeholder="Select"
                        isClearable
                        isDisabled={disable}
                    />
                </div>

            </div>


            <h4 className='fw-bold mt-4 mb-4'>Other observations on the work being undertaken</h4>
            <div className="row mb-4">

                <div className="col-6 mb-4">
                    <p htmlFor="workPerformed" className="mb-2">1. What work was being performed when the incident occurred?</p>
                    <InputTextarea
                        style={{ width: '100%' }}
                        rows={2}
                        autoResize
                        name="precursors.workPerformed"
                        value={formData.precursors.workPerformed}
                        onChange={handleChange}
                        disabled={disable}
                    />
                    {errors.workPerformed && <small className="p-error">{errors.workPerformed}</small>}
                </div>

                <div className="col-6 mb-4">
                    <p htmlFor="tasksProcedures" className="mb-2">2. Were any specific tasks or procedures being followed at the time?</p>
                    <InputTextarea
                        style={{ width: '100%' }}
                        rows={2}
                        autoResize
                        name="precursors.tasksProcedures"
                        value={formData.precursors.tasksProcedures}
                        onChange={handleChange}
                        disabled={disable}
                    />
                    {errors.tasksProcedures && <small className="p-error">{errors.tasksProcedures}</small>}
                </div>

                <div className="col-6 mb-4">
                    <p htmlFor="rolesAwareness" className="mb-2">3. Were all team members aware of their roles and responsibilities at the time of the incident?</p>
                    <InputTextarea
                        style={{ width: '100%' }}
                        rows={2}
                        name="precursors.rolesAwareness"
                        autoResize
                        value={formData.precursors.rolesAwareness}
                        onChange={handleChange}
                        disabled={disable}
                    />
                    {errors.rolesAwareness && <small className="p-error">{errors.rolesAwareness}</small>}
                </div>

                <div className="col-6 mb-4">
                    <p htmlFor="shortcutsDeviations" className="mb-2">4. Were any shortcuts or deviations from standard procedures taken?</p>
                    <InputTextarea
                        style={{ width: '100%' }}
                        rows={2}
                        name="precursors.shortcutsDeviations"
                        value={formData.precursors.shortcutsDeviations}
                        autoResize
                        onChange={handleChange}
                        disabled={disable}
                    />
                    {errors.shortcutsDeviations && <small className="p-error">{errors.shortcutsDeviations}</small>}
                </div>

                <div className="col-6 mb-4">
                    <p htmlFor="recentChanges" className="mb-2">5. Were there any recent changes in workload, procedures, or staffing?</p>
                    <InputTextarea
                        style={{ width: '100%' }}
                        rows={2}
                        name="precursors.recentChanges"
                        value={formData.precursors.recentChanges}
                        autoResize
                        onChange={handleChange}
                        disabled={disable}
                    />
                    {errors.recentChanges && <small className="p-error">{errors.recentChanges}</small>}
                </div>

                <div className="col-6 mb-4">
                    <p htmlFor="deadlinePressure" className="mb-2">6. Was there a specific deadline or pressure to complete the task?</p>
                    <InputTextarea
                        style={{ width: '100%' }}
                        rows={2}
                        name="precursors.deadlinePressure"
                        value={formData.precursors.deadlinePressure}
                        autoResize
                        onChange={handleChange}
                        disabled={disable}
                    />
                    {errors.deadlinePressure && <small className="p-error">{errors.deadlinePressure}</small>}
                </div>

                <div className="col-6 mb-4">
                    <p htmlFor="distractionsInterruptions" className="mb-2">7. Were there any distractions or interruptions during the work being performed?</p>
                    <InputTextarea
                        style={{ width: '100%' }}
                        rows={2}
                        autoResize
                        name="precursors.distractionsInterruptions"
                        value={formData.precursors.distractionsInterruptions}
                        onChange={handleChange}
                        disabled={disable}
                    />
                    {errors.distractionsInterruptions && <small className="p-error">{errors.distractionsInterruptions}</small>}
                </div>

                <div className="col-6 mb-4">
                    <p htmlFor="unusualConditions" className="mb-2">8. Was there anything unusual or different about the operating conditions on the day of the incident?</p>
                    <InputTextarea
                        style={{ width: '100%' }}
                        rows={2}
                        autoResize
                        name="precursors.unusualConditions"
                        value={formData.precursors.unusualConditions}
                        onChange={handleChange}
                        disabled={disable}
                    />
                    {errors.unusualConditions && <small className="p-error">{errors.unusualConditions}</small>}
                </div>

                <div className="col-6 mb-4">
                    <p htmlFor="equipmentUsed" className="mb-2">9. What equipment or tools were in use during the incident?</p>
                    <InputTextarea
                        style={{ width: '100%' }}
                        rows={2}
                        autoResize
                        name="precursors.equipmentUsed"
                        value={formData.precursors.equipmentUsed}
                        onChange={handleChange}
                        disabled={disable}
                    />
                    {errors.equipmentUsed && <small className="p-error">{errors.equipmentUsed}</small>}
                </div>

                <div className="col-6 mb-4">
                    <p htmlFor="equipmentMalfunction" className="mb-2">10. Were there any signs of malfunction or damage to equipment before the incident?</p>
                    <InputTextarea
                        style={{ width: '100%' }}
                        rows={2}
                        autoResize
                        name="precursors.equipmentMalfunction"
                        value={formData.precursors.equipmentMalfunction}
                        onChange={handleChange}
                        disabled={disable}
                    />
                    {errors.equipmentMalfunction && <small className="p-error">{errors.equipmentMalfunction}</small>}
                </div>

                <div className="col-6 mb-4">
                    <p htmlFor="recentMaintenance" className="mb-2">11. Had any maintenance, repair, or inspections been performed recently on the equipment involved?</p>
                    <InputTextarea
                        style={{ width: '100%' }}
                        rows={2}
                        name="precursors.recentMaintenance"
                        value={formData.precursors.recentMaintenance}
                        onChange={handleChange}
                        autoResize
                        disabled={disable}
                    />
                    {errors.recentMaintenance && <small className="p-error">{errors.recentMaintenance}</small>}
                </div>

                <div className="col-6 mb-4">
                    <p htmlFor="similarIncidents" className="mb-2">12. Had there been any similar near-miss events or safety concerns reported previously?</p>
                    <InputTextarea
                        style={{ width: '100%' }}
                        rows={2}
                        autoResize
                        name="precursors.similarIncidents"
                        value={formData.precursors.similarIncidents}
                        onChange={handleChange}
                        disabled={disable}
                    />
                    {errors.similarIncidents && <small className="p-error">{errors.similarIncidents}</small>}
                </div>

            </div>
        </div>
    )
}

export default StageOne
