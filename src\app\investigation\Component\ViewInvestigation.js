import React, { useState } from 'react'
import ViewDetails from './ViewDetails';
import Recommendation from './Recommendation/Recommendation';
import Declaration from './Declaration';
import InvestigationModal from './InvestigationModal'
import { Nav, Tab } from 'react-bootstrap';
import moment from 'moment';

const ViewInvestigation = ({ type, data, applicationDetails }) => {
    const [members, setMembers] = useState(data.investigationTeamMembers && data.investigationTeamMembers.split(',') || []); // 
    const accordionItems = [
        {
            key: '0',
            title: 'Incident Narrative and Impact Detailing',
            colorClass: 'accordion-header-1',
            body: <ViewDetails data={applicationDetails} disable={true} details={false} />
        },

        {
            key: '1',
            title: 'Analysis and Conclusions',
            colorClass: 'accordion-header-3',
            body: (
                <Recommendation data={data} type={type} />
            )
        },
        {
            key: '2',
            title: 'Investigation Team Declaration ', // New Accordion Item
            colorClass: 'accordion-header-5', // New CSS Class
            body: (
                <Declaration members={members} />
            )
        },
        {
            key: '3',
            title: 'Records', // New Accordion Item
            colorClass: 'accordion-header-4', // New CSS Class
            body: (
                <InvestigationModal
                    investigationId={applicationDetails.id}
                    type={type}
                />
            )
        },

    ];

    return (<>

        <div className="obs-section mb-2 p-4 pb-2">
            {/* <h4 className='fw-bold'>Overview</h4> */}

            <div className="row mb-3">
                <div className="col-md-6">
                    <p className="obs-title">Incident ID</p>
                    <p className="obs-content mb-3">{applicationDetails.maskId || ''}</p>
                </div>
                <div className="col-md-6">
                    <p className="obs-title">Incident Title</p>
                    <p className="obs-content mb-3">{applicationDetails.title || ''}</p>

                </div>
                <div className="col-md-6">
                    <p className="obs-title">Incident Date & Time</p>
                    <p className="obs-content">
                        {moment(applicationDetails.incidentDate || '').format("DD-MM-YYYY - HH:MM")}
                    </p>
                </div>

                <div className="col-md-6">
                    <p className="obs-title">Location</p>
                    {applicationDetails.isCustomLocation ?
                        <p className="obs-content">
                            {applicationDetails.customLocation}
                        </p>
                        :
                        <p className="obs-content">
                            {applicationDetails.locationOne?.name && (
                                <>
                                    {applicationDetails.locationOne.name}
                                    {applicationDetails.locationTwo?.name && ' > '}
                                </>
                            )}
                            {applicationDetails.locationTwo?.name && (
                                <>
                                    {applicationDetails.locationTwo.name}
                                    {applicationDetails.locationThree?.name && ' > '}
                                </>
                            )}
                            {applicationDetails.locationThree?.name && (
                                <>
                                    {applicationDetails.locationThree.name}
                                    {applicationDetails.locationFour?.name && ' > '}
                                </>
                            )}
                            {applicationDetails.locationFour?.name && applicationDetails.locationFour.name}
                        </p>
                    }
                </div>
                <div className="col-md-6">
                    <p className="obs-title">Investigation Team Leader</p>
                    <p className="obs-content">
                        {applicationDetails?.investigator?.firstName}
                    </p>
                </div>
                <div className="col-md-6">
                    <p className="obs-title">Investigation Assignment Date</p>
                    <p className="obs-content">
                        {moment(data.created || '').format("DD-MM-YYYY - HH:MM")}
                    </p>
                </div>

                <div className="col-md-6">
                    <p className="obs-title">Investigation Team Member(s)</p>
                    <p className="obs-content">{members.join(', ') || 'No members added'}</p>
                </div>
                {type &&
                    <div className="col-md-6">
                        <p className="obs-title">DueDate</p>
                        <p className="obs-content">{moment(type.dueDate).format('DD-MM-YYYY')}</p>
                    </div>
                }

            </div>
        </div>

        <Tab.Container >
            <Nav variant="pills" className="mb-3">
                {accordionItems.map((item, index) => (
                    <Nav.Item key={item.key}>
                        <Nav.Link eventKey={item.key} className={`${item.colorClass} custom-tab mb-3`}>
                            {(index !== 2 && index !== 3) && <span className="tab-number"> {index + 1}.</span>}{item.title}
                        </Nav.Link>
                    </Nav.Item>
                ))}
            </Nav>

            <Tab.Content className='p-0' style={{ border: '0px' }}>
                {accordionItems.map((item) => (
                    <Tab.Pane eventKey={item.key} key={item.key}>
                        {item.body}
                    </Tab.Pane>
                ))}
            </Tab.Content>
        </Tab.Container>
    </>
    )
}

export default ViewInvestigation