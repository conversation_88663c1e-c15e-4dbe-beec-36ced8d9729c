import React from 'react';
import Select from 'react-select';
import moment from 'moment';

const Overview = ({ formData, incidentType, category, circumstance, handleSelectChange, disable }) => {
    return (
        <div className="obs-section">
            {/* <h4 className='fw-bold'>Overview</h4> */}

            {/* <div className="row mb-3">
                <div className="col-md-6">
                    <p className="obs-title">Incident ID</p>
                    <p className="obs-content mb-3">{formData.maskId || ''}</p>
                </div>
                <div className="col-md-6">
                    <p className="obs-title">Incident Title</p>
                    <p className="obs-content">{formData.title}</p>
                </div>

                <div className="col-md-6">
                    <p className="obs-title">Incident Date</p>
                    <p className="obs-content">
                        {moment(formData.incidentDate || '').format("DD-MM-YYYY")}
                    </p>
                </div>

                <div className="col-md-6">
                    <p className="obs-title">Location</p>
                    <p className="obs-content">
                        {formData.locationOne?.name && (
                            <>
                                {formData.locationOne.name}
                                {formData.locationTwo?.name && ' > '}
                            </>
                        )}
                        {formData.locationTwo?.name && (
                            <>
                                {formData.locationTwo.name}
                                {formData.locationThree?.name && ' > '}
                            </>
                        )}
                        {formData.locationThree?.name && (
                            <>
                                {formData.locationThree.name}
                                {formData.locationFour?.name && ' > '}
                            </>
                        )}
                        {formData.locationFour?.name && formData.locationFour.name}
                    </p>
                </div>
            </div> */}

            <div className='row mb-3'>
                <div className="col-4 mb-2">
                    <label htmlFor="incidentType" className="mb-2 fw-bold obs-title">Incident Type</label>
                    <p style={{ fontStyle: 'italic', height: 50 }}>A broad classification of the incident based on its primary outcome</p>
                    <Select
                        options={incidentType}
                        value={incidentType.find(option => option.label === formData.incidentType)}
                        onChange={(selectedOption) => handleSelectChange(selectedOption, 'incidentType')}
                        placeholder="Select"
                        isClearable
                        styles={{ menu: (provided) => ({ ...provided, zIndex: 999 }) }}
                        isDisabled={disable}  // Disable select if disable is true
                    />
                </div>

                <div className="col-4 mb-2">
                    <label htmlFor="incidentCategory" className="mb-2 fw-bold obs-title">Incident Category</label>
                    <p style={{ fontStyle: 'italic', height: 50 }}>Classification based on the nature of the incident</p>
                    <Select
                        options={category}
                        value={category.find(option => option.label === formData.incidentCategory)}
                        onChange={(selectedOption) => handleSelectChange(selectedOption, 'incidentCategory')}
                        placeholder="Select"
                        isClearable
                        styles={{ menu: (provided) => ({ ...provided, zIndex: 999 }) }}
                        isDisabled={disable}  // Disable select if disable is true
                    />
                </div>

                <div className="col-4 mb-2">
                    <label htmlFor="incidentCircumstance" className="mb-2 fw-bold obs-title">Circumstance</label>
                    <p style={{ fontStyle: 'italic', height: 50 }}>Specific events, conditions, or situations that directly contributed to or surrounded the occurrence of an injury or incident</p>
                    <Select
                        options={circumstance}
                        value={circumstance.find(option => option.label === formData.incidentCircumstance)}
                        onChange={(selectedOption) => handleSelectChange(selectedOption, 'incidentCircumstance')}
                        placeholder="Select"
                        isClearable
                        styles={{ menu: (provided) => ({ ...provided, zIndex: 999 }) }}
                        isDisabled={disable}  // Disable select if disable is true
                    />
                </div>
            </div>
        </div>
    );
};

export default Overview;
