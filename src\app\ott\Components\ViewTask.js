import React, { useEffect, useState, useCallback } from 'react';
import { Card, Accordion } from 'react-bootstrap';
import { Button } from 'primereact/button';
import ImageComponent from '../../services/FileDownlodS3';
import API from '../../services/API';
import { DROPDOWNS, GET_USER_ROLE_BY_MODE } from '../../constants';
import moment from 'moment';


const DisplayTask = ({ initialData }) => {


    const [assignee, setAssignee] = useState([]);
    const [project, setProject] = useState([]);
    const [formData, setFormData] = useState({
        ott: { taskName: '', taskDescription: '', dueDate: '', priority: 'Medium', assigneeId: '', projectId: '' },
        tasks: [{ name: '', img: [], status: 'Yet To Start' }]
    });

    useEffect(() => {
        getCrewList();
        getProjectList();
    }, []);

    useEffect(() => {
        if (initialData) {
            const tasks = initialData.ottTasks && initialData.ottTasks.length > 0 ? initialData.ottTasks : [];
            setFormData({
                ott: initialData,
                tasks: tasks
            });
        }
    }, [initialData]);

    const getCrewList = useCallback(async () => {
        try {
            const response = await API.post(GET_USER_ROLE_BY_MODE, {
                locationOneId: "",
                locationTwoId: "",
                locationThreeId: "",
                locationFourId: "",
                mode: 'ott_assignee'
            });

            if (response.status === 200) {
                const data = response.data.map((item) => ({
                    label: item.firstName,
                    value: item.id
                }));
                setAssignee(data);
            }
        } catch (error) {
            console.error("Error fetching crew list:", error);
        }
    }, []);

    const getProjectList = useCallback(async () => {
        try {
            const uriString = {
                where: {
                    maskId: 'project'
                },
                include: [
                    { relation: "dropdownItems" },
                ]
            };

            const url = `${DROPDOWNS}?filter=${encodeURIComponent(
                JSON.stringify(uriString)
            )}`;

            const response = await API.get(url);

            if (response.status === 200) {
                const data = response.data[0].dropdownItems.map((item) => ({
                    label: item.name,
                    value: item.id
                }));
                setProject(data);
            }
        } catch (error) {
            console.error("Error fetching project list:", error);
        }
    }, []);

    return (
        <div className="container ">
        <Card className="p-4 shadow-sm">
          <div className='row mb-3'>
            <div className='col-12'>
              <strong style={{ marginBottom: '8px', display: 'block' }}>Task Name</strong>
              <p>{formData.ott.taskName}</p>
            </div>
          </div>
          <div className='row mb-3'>
            <div className='col-6'>
              <strong style={{ marginBottom: '8px', display: 'block' }}>Task ID</strong>
              <p>{formData.ott.maskId}</p>
            </div>
            <div className='col-6'>
              <strong style={{ marginBottom: '8px', display: 'block' }}>Due Date</strong>
              <p>{moment(formData.ott.dueDate).format('DD-MM-YYYY') || 'N/A'}</p>
            </div>
          </div>
          <div className='row mb-3'>
            <div className='col-6'>
              <strong style={{ marginBottom: '8px', display: 'block' }}>Priority</strong>
              <p>
                <span className={`badge ${formData.ott.priority === 'High' ? 'bg-danger' : formData.ott.priority === 'Medium' ? 'bg-warning text-dark' : 'bg-success'}`}>
                  {formData.ott.priority}
                </span>
              </p>
            </div>
            <div className='col-3'>
              <strong style={{ marginBottom: '8px', display: 'block' }}>Status</strong>
              <p>
                <span className={`badge ${
                  formData.ott.status === 'Yet to Start' ? 'bg-secondary' :
                  formData.ott.status === 'Planning' ? 'bg-primary' :
                  formData.ott.status === 'In Progress: On Track' ? 'bg-success' :
                  formData.ott.status === 'At Risk' ? 'bg-warning text-dark' :
                  formData.ott.status === 'On Hold' ? 'bg-warning text-dark' :
                  formData.ott.status === 'Under Review' ? 'bg-info' :
                  formData.ott.status === 'Testing / QA' ? 'bg-purple' :
                  formData.ott.status === 'Ready for Deployment' ? 'bg-light text-dark' :
                  formData.ott.status === 'Completed' ? 'bg-success' :
                  formData.ott.status === 'Returned' ? 'bg-danger' : 'bg-light'
                }`}>
                  {formData.ott.status}
                </span>
              </p>
            </div>
            <div className='col-3'>
              <strong style={{ marginBottom: '8px', display: 'block' }}>Completion %</strong>
              <p>{formData.ott.estimatedPercentage || 'N/A'}</p>
            </div>
          </div>
          <div className='row mb-3'>
            <div className='col-6'>
              <strong style={{ marginBottom: '8px', display: 'block' }}>Project</strong>
              <p>{project.find(option => option.value === formData.ott.projectId)?.label || 'N/A'}</p>
            </div>
            <div className='col-6'>
              <strong style={{ marginBottom: '8px', display: 'block' }}>Category</strong>
              <p>{formData.ott.category?.name || 'N/A'}</p>
            </div>
          </div>
          <div className='row mb-3'>
            <div className='col-6'>
              <strong style={{ marginBottom: '8px', display: 'block' }}>Assignor</strong>
              <p>{formData.ott.creator?.firstName || 'N/A'}</p>
            </div>
            <div className='col-6'>
              <strong style={{ marginBottom: '8px', display: 'block' }}>Assignee</strong>
              <p>{formData.ott.assignee?.firstName || 'N/A'}</p>
            </div>
            <div className='col-6'>
              <strong style={{ marginBottom: '8px', display: 'block' }}>Reviewer</strong>
              <p>{formData.ott.reviewer?.firstName || 'N/A'}</p>
            </div>
          </div>
          <div className="row mb-3">
            <div className='col-12'>
              <strong style={{ marginBottom: '8px', display: 'block' }}>Task Description</strong>
              <p>{formData.ott.taskDescription || 'N/A'}</p>
            </div>
          </div>
          <div className="row mb-3">
            <div className='col-12'>
              <strong style={{ marginBottom: '8px', display: 'block' }}>Assignee Comments</strong>
              <p>{formData.ott.assingneeComments || 'N/A'}</p>
            </div>
          </div>
          <div className="row mb-3">
            <div className='col-12'>
              <strong style={{ marginBottom: '8px', display: 'block' }}>Reviewer Comments</strong>
              <p>{formData.ott.reviewerComments || 'N/A'}</p>
            </div>
          </div>
          <div className="row mb-3">
            <div className='col-12'>
              <strong style={{ marginBottom: '8px', display: 'block' }}>Attachments</strong>
              {formData.ott.img && formData.ott.img.length > 0 ? (
                <div className="d-flex flex-wrap">
                  {formData.ott.img.map((item, m) => (
                    <div key={m} className="mr-3 mb-3" style={{ position: 'relative', maxWidth: '120px' }}>
                      <ImageComponent fileName={item} size={'100'} name={true}/>
                    </div>
                  ))}
                </div>
              ) : <p>No images uploaded.</p>}
            </div>
          </div>
      
          <Card className=" mt-4">
            <h4 className='fw-bold'>Sub-Activities / Dependencies / Status</h4>
            <Card.Body>
              <Accordion>
                {formData.tasks.map((task, index) => (
                  <Accordion.Item eventKey={index.toString()} key={index}>
                    <Accordion.Header>Activity {index + 1}</Accordion.Header>
                    <Accordion.Body>
                      <div className="mb-3">
                        <strong style={{ marginBottom: '8px', display: 'block' }}>SubActivity Name:</strong> {task.name}
                      </div>
                      <div className="mb-3">
                        <strong style={{ marginBottom: '8px', display: 'block' }}>Status:</strong> {task.status}
                      </div>
                      <div className="mb-3">
                        <strong style={{ marginBottom: '8px', display: 'block' }}>Remarks:</strong> {task.remarks}
                      </div>
                      <div className="mb-3">
                        <strong style={{ marginBottom: '8px', display: 'block' }}>Images:</strong>
                        <div className="image-preview mt-3">
                          {task.img && task.img.length > 0 ? (
                            <div className="d-flex">
                              {task.img.map((item, m) => (
                                <div key={m} className="mr-3">
                                  <ImageComponent fileName={item} size={'100'} name={true}/>
                                </div>
                              ))}
                            </div>
                          ) : (
                            <p>No images uploaded.</p>
                          )}
                        </div>
                      </div>
                      <div className="mb-3">
                        <strong style={{ marginBottom: '8px', display: 'block' }}>Evidence:</strong>
                        <div className="image-preview mt-3">
                          {task.evidence && task.evidence.length > 0 ? (
                            <div className="d-flex">
                              {task.evidence.map((item, m) => (
                                <div key={m} className="mr-3">
                                  <ImageComponent fileName={item} size={'100'} name={true}/>
                                </div>
                              ))}
                            </div>
                          ) : (
                            <p>No evidence uploaded.</p>
                          )}
                        </div>
                      </div>
                    </Accordion.Body>
                  </Accordion.Item>
                ))}
              </Accordion>
            </Card.Body>
          </Card>
      
          {formData.ott.isArchive && (
            <div className="mt-3">
              <strong style={{ marginBottom: '8px', display: 'block' }}>Task is:</strong> {formData.ott.isTaskIsCompletedForArchive ? "Completed" : "Not Completed"}
              <div className="mt-2">
                <strong style={{ marginBottom: '8px', display: 'block' }}>Comments:</strong> {formData.ott.archiveComments}
              </div>
            </div>
          )}
        </Card>
      </div>
      

    );
};

export default DisplayTask;
