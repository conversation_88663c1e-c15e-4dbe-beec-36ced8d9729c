import React, { Component, useState, useEffect, useRef } from 'react'
import DataTables from '../tables/DataTables';
import $ from "jquery";
import { deletePopup, singlePopup } from "../notifications/Swal";
import API from '../services/API';
import cogoToast from 'cogo-toast';
import { useHistory } from "react-router-dom";
import { OBSERVATION_REPORT_URL, OBSERVATION_REPORT_WITH_ID, STATIC_URL, USERS_URL } from '../constants';
import { Button } from 'primereact/button';
import MaterialTable from 'material-table';
import { ThemeProvider, createTheme } from '@mui/material';
import ObservationModal from './ObservationModal';
import { observationColumns, tableOptions } from './TableColumns';
import CardOverlay from './CardOverlay';
import FilterLocation from './FilterLocation';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import PropTypes from 'prop-types';

import { Checkbox } from '@mui/material';
import moment from 'moment'
import { FilterMatchMode, FilterOperator, FilterService } from 'primereact/api';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { InputText } from 'primereact/inputtext';
import { MultiSelect } from 'primereact/multiselect';
import { Calendar } from 'primereact/calendar';
import { Dropdown } from 'primereact/dropdown';
import { Tag } from 'primereact/tag';
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";
import 'primeicons/primeicons.css';
import { Badge } from 'react-bootstrap';
import { Button as Button1 } from 'primereact/button';
import DatePicker from 'react-datepicker'

// @ts-ignore
window.jQuery = $;
// @ts-ignore
window.$ = $;
FilterService.register('custom_created', (value, filters) => {
  const { startMonth, startYear, endMonth, endYear } = filters;

  if (startMonth === undefined || startYear === undefined || endMonth === undefined || endYear === undefined) {
    // If any of the required filter parameters is missing, return true to include the value
    return true;
  }

  const fromDate = moment(`${startMonth}-${startYear}`, 'MM-YYYY').startOf('month');
  const toDate = moment(`${endMonth}-${endYear}`, 'MM-YYYY').endOf('month');

  const date = moment(value, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ', 'Do MMM YYYY hh:mm A', moment.ISO_8601]);

  return date.isBetween(fromDate, toDate, null, '[]');
});
function isBetweenDateRange(dateString, date1, date2) {
  // Parse the date strings using Moment.js
  const date = moment(dateString, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ', 'Do MMM YYYY hh:mm A', moment.ISO_8601]);

  // Check if the parsed date is between date1 and date2
  return date.isBetween(date1, date2, null, '[]'); // Use square brackets to include both ends of the range
}

const Ehs = ({ obsdata }) => {

  const dt = useRef()

  const [dates, setDates] = useState(null);
  const [showOverdue, setShowOverdue] = useState(false);
  const [category, setCategory] = useState([{ name: 'Health', value: 'Health' }, { name: 'Safety', value: 'Safety' }, { name: 'Environment', value: 'Environment' }])
  const [startDate, setStartDate] = useState(null)
  const [endDate, setEndDate] = useState(null)
  const [Search, setSearch] = useState([])
  const [project, setProject] = useState([])
  const [assignee, setAssignee] = useState([])
  const [users, setUsers] = useState([])
  const [filters, setFilters] = useState(null);
  const [globalFilterValue, setGlobalFilterValue] = useState('');
  const initFilters = () => {
    setFilters({
      global: { value: null, matchMode: FilterMatchMode.CONTAINS },
      'applicationDetails.maskId': { value: null, matchMode: FilterMatchMode.IN },
      category: { value: null, matchMode: FilterMatchMode.IN },
      type: { value: null, matchMode: FilterMatchMode.IN },
      description: { value: null, matchMode: FilterMatchMode.IN },
      dueDate: { value: null, matchMode: FilterMatchMode.IN },
      color: { value: null, matchMode: FilterMatchMode.IN },
      'locationFour.name': { value: null, matchMode: FilterMatchMode.IN },
      created: { value: null, matchMode: FilterMatchMode.CUSTOM },
      status: { value: null, matchMode: FilterMatchMode.IN },
      actionOwnerId: { value: null, matchMode: FilterMatchMode.IN },
    });
    setGlobalFilterValue('');
  }
  const history = useHistory();
  const thead = [
    'id',
    'Type',
    'Category',
    'Description',
    'Rectified Status',
    'Reported By',
    'Remarks',
    'Action Taken',


  ];
  const defaultMaterialTheme = createTheme();

  const statuses = [{ name: 'Overdue', value: 'Overdue' }, { name: 'Upcoming', value: 'Upcoming' }, { name: 'Due Soon', value: 'Due Soon' }, { name: 'None', value: 'None' }];
  const getSeverity = (status) => {
    switch (status) {
      case 'Overdue':
        return 'danger';

      case 'Upcoming':
        return 'info';

      case 'Due Soon':
        return 'warning';

      case 'None':
        return null;
    }
  };
  const [data, setData] = useState([]);
  const [filterData, setFilterData] = useState([]);

  useEffect(() => {
    if (obsdata) {
      getObservationData();
      getAllUsers()
    }
    setData(obsdata)
    setFilterData(obsdata)
    initFilters();

  }, [obsdata])

  const getAllUsers = async () => {
    const response = await API.get(USERS_URL);
    setUsers(response.data)
  }
  function getName(id) {
    if (id) {
      const user = users.find(user => user.id === id)
      return id ? user?.firstName || '' : ''
    }
  }
  const assigneeTemplate = (row) => {
    return getName(row.actionOwnerId)
  }
  const getObservationData = async () => {



    const obs = obsdata.map(item => {
      return { name: item.locationFour?.name || '-', value: item.locationFour?.name || '' }
    })
    setProject(obs.filter((ele, ind) => ind === obs.findIndex(elem => elem.value === ele.value && elem.name === ele.name)))

    const obs1 = obsdata.map(item => {
      return { name: getName(item.actionOwnerId), value: item.actionOwnerId || '-' }
    })
    setAssignee(obs1.filter((ele, ind) => ind === obs1.findIndex(elem => elem.value === ele.value && elem.name === ele.name)))

    // const params = {
    //   "include": [{ "relation": "submitted" }]

    // };
    // const response = await API.get(`${OBSERVATION_REPORT_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`);
    // if (response.status === 200) {
    //   const preprocessedData = response.data.map(item => ({
    //     ...item,
    //     'submitted.firstName': item.submitted ? item.submitted.firstName : '',
    //     'color': moment().isAfter(moment(item.dueDate, 'DD-MM-YYYY')) ? 'Overdue' : 'Upcoming',
    //     created: moment(item.created).format('Do MMM YYYY hh:mm A') // Ensure this is a string
    //   }));


    // setData(obsdata);
    // setFilterData(obsdata);

    // setTotalObservation(preprocessedData.length)

  }

  const [showReportModal, setShowReportModal] = useState(false);
  const [reportData, setReportData] = useState(null);

  const viewObservationReport = async (id) => {

    const params = {
      "include": [{ "relation": "actions" }, { "relation": "workActivityDepartment" }, { "relation": "ghsOne" }, { "relation": "ghsTwo" }, { "relation": "locationOne" }, { "relation": "locationTwo" }, { "relation": "locationThree" }, { "relation": "locationFour" }, { "relation": "locationFive" }, { "relation": "locationSix" }]

    };
    const response = await API.get(`${OBSERVATION_REPORT_WITH_ID(id)}?filter=${encodeURIComponent(JSON.stringify(params))}`);

    if (response.status === 200) {

      // const actionUploads = (response.data.actions && response.data.actions.length) ? response.data.actions.flatMap(obj => obj.uploads) : [];

      // response.data.uploads = [...response.data.uploads]

      // response.data.uploads = response.data.uploads ? response.data.uploads.map(i => {
      //   return { src: `${STATIC_URL}/${i}`, width: 4, height: 3 }
      // }) : []

      // response.data.evidences = response.data.evidences ? response.data.evidences.map(i => {
      //   return { src: `${STATIC_URL}/${i}`, width: 4, height: 3 }
      // }) : []

      // response.data.evidence = [...response.data.evidence, ...actionUploads]

      // response.data.evidence = response.data.evidence ? response.data.evidence.map(i => {
      //   return { src: `${STATIC_URL}/${i}`, width: 4, height: 3 }
      // }) : []
      setReportData(response.data)
      setShowReportModal(true)
    }

  }

  const tableStyle = {
    borderRadius: '0',
    boxShadow: 'none',
  };

  const tableActions = [
    {
      icon: 'visibility',
      tooltip: 'View Report',
      onClick: (event, rowData) => {
        // Do save operation

        viewObservationReport(rowData.id)
      }
    }
  ]

  const localization = {
    header: {
      actions: 'View'
    }
  };

  // useEffect(()=>{
  //   const filteredData = obsdata.filter(item => {
  //     return (
  //       (locationOneId === '' || item.locationOneId === locationOneId) &&
  //       (locationTwoId === '' || item.locationTwoId === locationTwoId) &&
  //       (locationThreeId === '' || item.locationThreeId === locationThreeId) &&
  //       (locationFourId === '' || item.locationFourId === locationFourId)
  //     );
  //   });

  //   // setFilterData(filteredData);
  //   // setTotalObservation(filterData.length)

  // },[locationOneId,locationTwoId,locationThreeId,locationFourId])

  const exportCSV = () => {
    dt.current.exportCSV();
  };


  useEffect(() => {
    let filteredData = obsdata; // Assuming response.data is your fetched data

    if (showOverdue) {
      const currentDate = moment();
      filteredData = filteredData.filter(item => {
        return moment(item.dueDate, 'DD-MM-YYYY').isBefore(currentDate);
      });
    }

    setFilterData(filteredData);
  }, [showOverdue]);
  const onDateSearch = () => {
    const [from, to] = [startDate, endDate];
    if (from === null && to === null) return true;
    if (from !== null && to === null) return true;
    if (from === null && to !== null) return true;
    const start = moment(from, 'ddd MMM DD YYYY HH:mm:ss ZZ').startOf('month');
    const end = moment(to, 'ddd MMM DD YYYY HH:mm:ss ZZ').endOf('month');

    //  console.log(start,end)
    const searchData = obsdata.filter(item => isBetweenDateRange(item.created, start, end))

    setFilterData(searchData)

    // return isBetweenDateRange(value, moment(from, 'ddd MMM DD YYYY HH:mm:ss ZZ').startOf('month'), moment(to, 'ddd MMM DD YYYY HH:mm:ss ZZ').endOf('month'))
  }
  const isBetweenDateRange = (dateString, date1, date2) => {
    // Parse the date strings using Moment.js
    const date = moment(dateString, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ', 'Do MMM YYYY hh:mm A', moment.ISO_8601]);

    // Check if the parsed date is between date1 and date2
    return date.isBetween(date1, date2, null, '[]'); // Use square brackets to include both ends of the range
  }
  const renderHeader = () => {
    // const value = filters['global'] ? filters['global'].value : '';

    return (
      <div className='d-flex justify-content-between align-items-center'>
        <div className="">
          <span className='me-3'>Month Filter :</span>
          <Calendar view='month' className="w-full me-2" value={startDate} placeholder='From' onChange={(e) => setStartDate(e.value)} dateFormat="mm/yy" showIcon />
          <Calendar view='month' className="w-full  me-3" value={endDate} placeholder="To" onChange={(e) => setEndDate(e.value)} dateFormat="mm/yy" showIcon />

          <Button1 className='me-3' rounded text raised severity="success" aria-label="Search" onClick={() => onDateSearch()} label='Apply' />
          <Button1 rounded text raised severity="danger" aria-label="Cancel" label='Clear All Filters' onClick={() => { setFilterData(data); initFilters(); setStartDate(null); setEndDate(null) }} />

        </div>
        {/* <h5 className='m-0'> A listing of all observations reported by you for the selected location(s) and time frame.</h5> */}

        <span className="p-input-icon-left">
          <i className="fa fa-search" />
          <InputText type="search" value={globalFilterValue} onChange={(e) => onGlobalFilterChange(e)} />
        </span>
        <Button type="button" icon="pi pi-file-excel" severity="success" rounded onClick={exportCSV} data-pr-tooltip="XLS" />
      </div>
    );
  };

  const header = renderHeader();
  const onGlobalFilterChange = (e) => {
    const value = e.target.value;
    let _filters = { ...filters };

    _filters['global'].value = value;

    setFilters(_filters);
    setGlobalFilterValue(value);
  };

  // const statusBodyTemplate = (row) => {

  //   return (
  //     <div className='maskid' >
  //       {row.color ==='Overdue' ? <span className='overdue mt-2' ></span> : ''}
  //     </div>
  //   )
  // }
  const statusBodyTemplate = (rowData) => {
    return <Badge pill bg={getSeverity(rowData.color)} >{rowData.color}</Badge>
  };
  const maskIdBodyTemplate = (row) => {

    return (
      <div className='maskid' onClick={() => viewObservationReport(row.id)}>
        {row.maskId}
      </div>
    );

  }
  const dateBodyTemplate = (row) => {

    return (<>{row.created}</>)

  }
  const viewBodyTemplate = (row) => {
    return (
      <div className="table-action d-flex ">
        <i className="mdi mdi-eye" onClick={() => viewObservationReport(row.id)}></i>

      </div>
    )
  }
  const categoryFilterTemplate = (options) => {

    return (
      <React.Fragment>
        <div className="mb-3 font-bold">Type</div>
        <MultiSelect value={options.value} options={category} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
      </React.Fragment>
    );
  }
  const typeFilterTemplate = (options) => {

    return (
      <React.Fragment>
        <div className="mb-3 font-bold">Type</div>
        <MultiSelect value={options.value} options={[{ name: 'At Risk', value: 'At Risk' }, { name: 'Unsafe Act', value: 'Unsafe Act' }, { name: 'Unsafe Condition', value: 'Unsafe Condition' }, { name: 'Positive OBS', value: 'Positive OBS' }, { name: 'Minor RoS', value: 'Minor RoS' }]} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
      </React.Fragment>
    );
  }
  const projectFilterTemplate = (options) => {

    return (
      <React.Fragment>
        <div className="mb-3 font-bold">Type</div>
        <MultiSelect value={options.value} options={project} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
      </React.Fragment>
    );
  }
  const representativesItemTemplate = (option) => {
    return (
      <div className="flex align-items-center gap-2">

        <span>{option.value}</span>
      </div>
    );
  };

  const dueDateTemplate = (option) => {
    return (
      <div className="flex align-items-center gap-2">

        <span>{option.dueDate
          ? moment(option.dueDate, ["DD-MM-YYYY", "DD/MM/YYYY"]).format('Do MMM YYYY')
          : '-'}</span>
      </div>
    );
  };

  const statusFilterTemplate = (options) => {
    return <React.Fragment>
      <div className="mb-3 font-bold">Type</div>
      <MultiSelect value={options.value} options={statuses} itemTemplate={statusItemTemplate} onChange={(e) => options.filterCallback(e.value, options.index)} optionLabel="name" placeholder="Any" className="p-column-filter" />
    </React.Fragment>
  };

  const assigneeFilterTemplate = (options) => {
    return <>
      <MultiSelect value={options.value} options={assignee} itemTemplate={assigneeItemTemplate} onChange={(e) => options.filterCallback(e.value, options.index)} optionLabel="name" placeholder="Any" className="p-column-filter" />
    </>
  }

  const assigneeItemTemplate = (option) => {
    return getName(option.value)
  }
  const statusItemTemplate = (option) => {
    return <Badge pill bg={getSeverity(option.value)}>{option.name}</Badge>
  };
  // const bodyFilterTemplate = (row) => {
  //   console.log(row)
  //   return <>{JSON.parse(row.remarks).location4}</>
  // }

  const monthFilterTemplate = (options) => {
    const [from, to] = options.value ?? [null, null];
    console.log(options)
    return (
      <div className="d-flex ">


        <Calendar value={from} view='month' dateFormat="mm/yy" onChange={(e) => options.filterApplyCallback([e.value, to])} className="w-full" placeholder="From" showIcon />
        <Calendar value={to} view='month' dateFormat="mm/yy" onChange={(e) => options.filterApplyCallback([from, e.value])} className="w-full" placeholder="To" showIcon />
      </div>
    )
  }

  const currentStatusFilterTemplate = (options) => {

    return (
      <React.Fragment>

        <MultiSelect value={options.value} options={[{ name: 'Under Review', value: 'Under Review' }, { name: 'Action Reassigned', value: 'Action Reassigned' }, { name: 'Action Verified - Closed', value: 'Action Verified - Closed' }, { name: 'Reported & Closed', value: 'Reported & Closed' }, { name: 'Reported & Rectified on Spot', value: 'Reported & Rectified on Spot' }, { name: 'Actions Assigned', value: 'Actions Assigned' }, { name: 'Archived without actions', value: 'Archived without actions' }, { name: 'Actions Taken - Pending Verification', value: 'Actions Taken - Pending Verification' }]} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
      </React.Fragment>
    );
  }




  return (
    <>


      <DataTable ref={dt} value={filterData} paginator rows={10} header={header} filters={filters} globalFilterFields={["maskId"]} onFilter={(e) => { setFilters(e.filters) }} paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
        rowsPerPageOptions={[10, 25, 50]}
        emptyMessage="No Data found." >

        {/* <Column body={viewBodyTemplate} header="Action" ></Column> */}
        <Column field='color' body={statusBodyTemplate} filter filterElement={statusFilterTemplate} showFilterMatchModes={false}></Column>

        <Column field='maskId' body={maskIdBodyTemplate} header="Observation ID" headerStyle={{ width: '15%' }}></Column>

        <Column field='created' body={dateBodyTemplate} header="Reported On" sortable headerStyle={{ width: '10%' }}></Column>

        {/* <Column field="submitted.firstName" header="Reported By" sortable  ></Column> */}

        <Column field="category" header="Domain" sortable filter filterElement={categoryFilterTemplate} showFilterMatchModes={false}></Column>

        <Column field="type" header="Category" sortable filter filterElement={typeFilterTemplate} showFilterMatchModes={false}></Column>

        {/* <Column field="description" header="Brief Description" sortable  ></Column> */}

        <Column field="status" header="Current Status" filter filterElement={currentStatusFilterTemplate} showFilterMatchModes={false}></Column>

        {/* <Column field="submitted.firstName" header="Reported By" sortable  ></Column>

        <Column field="status" header="Status" sortable  ></Column> */}
        <Column field="actionOwnerId" header="ActionAssignee" body={assigneeTemplate} filter filterElement={assigneeFilterTemplate} showFilterMatchModes={false}></Column>
        <Column field="workActivityDepartment.name" header="Department" sortable  ></Column>

        {/* <Column field="actionTaken" header="Action Taken / Action to be Taken" sortable  ></Column> */}
        {/* <Column field="locationFour.name" header="Project/DC name" filter filterElement={projectFilterTemplate} showFilterMatchModes={false}></Column> */}

        <Column field="dueDate" body={dueDateTemplate} header="Action Due" sortable></Column>

        <Column field="closeDate" header="Action Closed" sortable ></Column>

      </DataTable>

      <ObservationModal reportData1={reportData} showReportModal={showReportModal} setShowReportModal={(status) => setShowReportModal(status)} />



    </>
  )
}

export default Ehs;
