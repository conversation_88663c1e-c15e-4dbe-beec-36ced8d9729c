// @ts-nocheck
import React, { useState, useRef, useEffect } from 'react';
import DataTables from '../tables/DataTables';
import $ from "jquery";
import { Modal, Button, Form } from 'react-bootstrap';
import cogoToast from 'cogo-toast';
import Loader from '../shared/Loader';
import Swal from 'sweetalert2';
import { useHistory } from "react-router-dom";
import { EXTERNAL_USERS_URL, EXTERNAL_USER_PASSWORD_RESET, USERS_URL_WITH_ID } from '../constants';
import { deletePopup, singlePopup } from "../notifications/Swal";
import API from '../services/API';
import MaterialTable from 'material-table';
import { ThemeProvider, createTheme } from '@mui/material';
import { externalUserColumns, tableOptions } from '../pages/TableColumns';
import CardOverlay from '../pages/CardOverlay';
import FilterLocation from '../pages/FilterLocation';
import { DropzoneArea } from 'material-ui-dropzone'
import { LinearProgress } from '@material-ui/core';
import * as XLSX from 'xlsx';
// @ts-ignore
window.jQuery = $;
// @ts-ignore
window.$ = $;

const thumbsContainer = {
    display: 'flex',
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 16
};

const thumb = {
    display: 'inline-flex',
    borderRadius: 2,
    border: '1px solid #eaeaea',
    marginBottom: 8,
    marginRight: 8,
    width: 100,
    height: 100,
    padding: 4,
    boxSizing: 'border-box'
};

const customSwal = Swal.mixin({
    customClass: {
        confirmButton: 'btn btn-danger',
        cancelButton: 'btn btn-light'
    },
    buttonsStyling: false
})

const customSwal2 = Swal.mixin({
    customClass: {
        confirmButton: 'btn btn-primary',

    },
    buttonsStyling: false
})

const AppUser = () => {
    const defaultMaterialTheme = createTheme();

    const [files, setFiles] = useState([]);

    const handleFileChange = (file) => {
        setFiles(file)

    }

    const [mdShow, setMdShow] = useState(false);
    const [userShow, setUserShow] = useState(false);
    const [excelShow, setExcelShow] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const history = useHistory();
    const uName = useRef();
    const uEmail = useRef();
    const uCompany = useRef();
    const uPassword = useRef();
    const [allRoles, setAllRoles] = useState({ country: [], ehs: [], eptw: [], incident: [], inspection: [], plant: [], document: [] })
    const [selectedRoles, setSelectedRoles] = useState({ country: [], ehs: [], eptw: [], incident: [], inspection: [], plant: [], document: [] })
    const [selectedUserId, setSelectedUserId] = useState({ id: "", email: "", name: "" });
    useEffect(() => {
        // getCountry();
        // getEhsRole();
        // getEptwRole();
        // getIncidentRole();
        // getInspectionRole();
        // getPlantRole();
        // getDocumentRole();

    }, [])


    // const getCountry = async () => {
    //     const response = await API.get(LOCATION1_URL)

    //     if (response.status === 200) {

    //         setAllRoles(p => { return { ...p, country: response.data } })
    //     }
    // }
    // const getDocumentRole = async () => {
    //     const response = await API.get(DOCUMENT_ROLE_URL)

    //     if (response.status === 200) {

    //         setAllRoles(p => { return { ...p, document: response.data } })
    //     }
    // }
    // const getEhsRole = async () => {
    //     const response = await API.get(GROUP_EHS_ROLE_URL)

    //     if (response.status === 200) {

    //         setAllRoles(p => { return { ...p, ehs: response.data } })
    //     }
    // }

    // const getEptwRole = async () => {
    //     const response = await API.get(EPTW_ROLE_URL)

    //     if (response.status === 200) {

    //         setAllRoles(p => { return { ...p, eptw: response.data } })
    //     }
    // }

    // const getIncidentRole = async () => {
    //     const response = await API.get(INCIDENT_ROLE_URL)

    //     if (response.status === 200) {

    //         setAllRoles(p => { return { ...p, incident: response.data } })
    //     }
    // }

    // const getInspectionRole = async () => {
    //     const response = await API.get(INSPECTION_ROLE_URL)

    //     if (response.status === 200) {

    //         setAllRoles(p => { return { ...p, inspection: response.data } })
    //     }
    // }

    // const getPlantRole = async () => {
    //     const response = await API.get(PLANT_ROLE_URL)

    //     if (response.status === 200) {

    //         setAllRoles(p => { return { ...p, plant: response.data } })
    //     }
    // }


    const [data, setData] = useState([])
    useEffect(() => {
        getUsersData();
    }, [])

    const getUsersData = async () => {
        try {
            const response = await API.get(EXTERNAL_USERS_URL);
            if (response.status === 200) {
                const filteredData = response.data
                    .filter(user => user.type === "External") // Filter by type "Internal"
                    .sort((a, b) => a.firstName.toLowerCase().localeCompare(b.firstName.toLowerCase())); // Sort by firstName

                setData(filteredData); // Set the filtered and sorted data
                setIsLoading(false);
            }
        } catch (error) {
            console.error("Error fetching users data:", error);
            setIsLoading(false);
        }
    };




    const viewAssignPermission = async (id, email, name) => {
        const response = await API.get(USERS_URL_WITH_ID(id))
        if (response.status === 200) {

            if (response.data.customRoles)
                setSelectedRoles(response.data.customRoles)
            else
                setSelectedRoles({ country: [], ehs: [], eptw: [], incident: [], inspection: [], plant: [], document: [] })
            setSelectedUserId({ id: id, email: email, name: name })
            setMdShow(true)

        }
    }





    const handleRoleChange = (e, category) => {
        const roleId = e.target.value;
        console.log(roleId)

        setSelectedRoles((prevRoles) => {
            const categoryRoles = prevRoles[category];
            if (e.target.checked) {
                // Add the role to the selected roles
                return {
                    ...prevRoles,
                    [category]: [...categoryRoles, roleId],
                };
            } else {
                // Remove the role from the selected roles
                return {
                    ...prevRoles,
                    [category]: categoryRoles.filter((id) => id !== roleId),
                };
            }
        });
    };

    const handleAssignSubmit = async () => {
        const id = selectedUserId.id;
        const response = await API.patch(USERS_URL_WITH_ID(id), { email: selectedUserId.email, customRoles: selectedRoles })
        if (response.status === 204) {
            setSelectedRoles({ country: [], ehs: [], eptw: [], incident: [], inspection: [], plant: [], document: [] })
            setSelectedUserId({ id: "", email: "", name: "" })
            setMdShow(false)
            cogoToast.info('Assigned', { position: 'top-right' })

        }
    }

    const [progress, setProgress] = useState(0);

    const uploadBulkUser = async () => {
        setExcelShow(false);
        if (files && files[0]) {
            setProgress(0);
            const reader = new FileReader();
            reader.onload = (evt) => {
                // Parse data
                const bstr = evt.target.result;
                const wb = XLSX.read(bstr, { type: 'binary' });
                // Get first worksheet
                const wsname = wb.SheetNames[0];
                const ws = wb.Sheets[wsname];
                // Convert array of arrays
                const data = XLSX.utils.sheet_to_json(ws, { header: 1 });
                // Remove the first row (headers)
                data.shift();
                // Process each row, for example, you could print it
                data.forEach((row, index) => {
                    console.log(row);
                    // Calculate progress
                    createBulkUser(row[0], row[1], row[2])
                    setProgress((index + 1) / data.length * 100);
                });
            };
            reader.readAsBinaryString(files[0]);
        }
        getUsersData();
        cogoToast.success('Users Added!')
    }

    const createBulkUser = async (firstName, email, company) => {
        const response = await API.post(EXTERNAL_USERS_URL, {
            firstName: firstName,
            email: email,
            company: company,
            type: 'External'
            // password: 'INVALID',


        })
        if (response.status === 200) {
            return true
        } else {
            return false
        }
    }

    const resetPassword = async (email) => {
        const response = await API.post(EXTERNAL_USER_PASSWORD_RESET, {
            email: email
        })

        if (response.status === 200) {
            cogoToast.success('New password has been generated and mailed!')
        } else {
            cogoToast.error('Something went wrong!')
        }
    }


    const createUserHandler = async () => {
        // @ts-ignore
        setIsLoading(true)

        const response = await API.post(EXTERNAL_USERS_URL, {
            firstName: uName.current.value,
            email: uEmail.current.value,
            company: uCompany.current.value,
            type: 'External'
            // password: 'INVALID'



        })
        if (response.status === 200) {

            cogoToast.info('Created!', { position: 'top-right' })
            // $('#dataTable').DataTable().ajax.reload();
            getUsersData();
            customSwal2.fire(
                'User Created!',
                '',
                'success'
            )
        } else {
            customSwal2.fire(
                'Please Try Again!',
                '',
                'error'
            )
            setIsLoading(false)
        }



        uName.current.value = '';
        uEmail.current.value = '';
        uPassword.current.value = '';
        setUserShow(false)
        setIsLoading(false)
    }

    const tableStyle = {
        borderRadius: '0',
        boxShadow: 'none',
    };

    const tableActions = [
        // {
        //     icon: 'grading',
        //     tooltip: 'Role Assignment',
        //     onClick: (event, rowData) => {
        //         // Do save operation
        //         // console.log(rowData)
        //         viewAssignPermission(rowData.id, rowData.email, rowData.firstName)
        //     }
        // },
        {
            icon: 'password',
            tooltip: 'Reset Account',
            onClick: (event, rowData) => {
                // Do save operation
                // console.log(rowData)
                resetPassword(rowData.email)
            }
        },
    ]

    const localization = {
        header: {
            actions: 'Role Assignment'
        }
    };

    const handleFilter = (locationOneId, locationTwoId, locationThreeId, locationFourId) => {
        // const filteredData = data.filter(item => {
        //   return (
        //     (locationOneId === '' || item.locationOneId === locationOneId) &&
        //     (locationTwoId === '' || item.locationTwoId === locationTwoId) &&
        //     (locationThreeId === '' || item.locationThreeId === locationThreeId) &&
        //     (locationFourId === '' || item.locationFourId === locationFourId)
        //   );
        // });

        // setFilterData(filteredData);
        // setSelectedLocationOne(locationOneId)
        // setSelectedLocationTwo(locationTwoId)
        // setSelectedLocationThree(locationThreeId)
        // setSelectedLocationFour(locationFourId)
    };

    const updateUserData = async (column, id, value, email) => {

        const response = await API.patch(USERS_URL_WITH_ID(id), { [column]: value })



        if (response.status === 204) {
            cogoToast.success('Updated!')
        }
    }
    return (
        <CardOverlay>

            {progress > 0 && progress < 100 && <LinearProgress className="mb-3" variant="determinate" value={progress} />}

            <button className='btn btn-primary me-2' onClick={() => setUserShow(true)}>Create External User</button>
            <button className='btn btn-secondary' onClick={() => setExcelShow(true)}>Upload Bulk External Users</button>


            <ThemeProvider theme={defaultMaterialTheme}>
                <MaterialTable
                    columns={[{
                        title: "Name",
                        field: "firstName",
                        cellStyle: {
                            padding: '1.125rem 1.375rem',
                            width: '30%',
                            maxWidth: '30%'
                        }
                    },
                    {
                        title: "Email",
                        field: "email",
                        cellStyle: {
                            padding: '1.125rem 1.375rem',
                            width: '30%',
                            maxWidth: '30%'
                        }
                    }, {
                        title: "Organization",
                        field: "company",
                        cellStyle: {
                            padding: '1.125rem 1.375rem',
                            width: '30%',
                            maxWidth: '30%'
                        },
                        render: rowData => rowData.type === 'Internal' ? 'SAGT' : (rowData.company ? rowData.company : 'External')
                    }]}
                    data={data}
                    title={
                        <div className='mt-3 mb-3'>
                            <h4 style={{ margin: 0 }}>External Users</h4>
                            {/* <p style={{ margin: 0, fontWeight: 'normal' }}>List of licensed users who can be provided access to one or more of the subscribed modules and perform required actions.</p> */}
                        </div>
                    }
                    style={tableStyle}
                    actions={tableActions}
                    options={tableOptions}
                    localization={localization}
                    editable={{
                        onRowUpdate: (newData, oldData) =>
                            new Promise((resolve, reject) => {
                                // Find the index of the row in the data array
                                const dataUpdate = [...data];
                                const index = oldData.tableData.id;
                                dataUpdate[index] = newData;

                                // If type of data has changed update the company
                                if (newData.type !== oldData.type) {
                                    updateUserData('company', oldData.id, newData.type, oldData.email);
                                }

                                // Loop over each field in the newData object to update the user data
                                Object.keys(newData).forEach((field) => {
                                    if (newData[field] !== oldData[field]) {
                                        updateUserData(field, oldData.id, newData[field], oldData.email);
                                    }
                                });

                                // On successful update, change the local state
                                setData(dataUpdate);

                                resolve();
                            }),
                    }}
                />
            </ThemeProvider>

            <Modal
                show={mdShow}
                size="lg"
                onHide={() => setMdShow(false)}
                aria-labelledby="example-modal-sizes-title-md"
            >
                <Modal.Header>
                    Assign Permissions to {selectedUserId.name}
                </Modal.Header>

                <Modal.Body>
                    <form className="forms">

                        {/* <h4>Country Admin</h4>
                        <div className='form-group mb-2'>

                            {
                                allRoles.country.map((i, k) => {
                                    return (
                                        <label className='me-3' key={k}>
                                            <input value={i.id} checked={selectedRoles.country.includes(i.id)} onChange={(e) => handleRoleChange(e, 'country')} type='checkbox' /> {i.name}
                                        </label>
                                    )
                                })
                            }

                        </div> */}
                        <FilterLocation handleFilter={handleFilter} disableAll={true} period={false} />
                        <h4>Document</h4>
                        <div className='form-group mb-2'>

                            {
                                allRoles.document.map((i, k) => {
                                    return (
                                        <label className='me-3' key={k}>
                                            <input value={i.id} checked={selectedRoles.document.includes(i.id)} onChange={(e) => handleRoleChange(e, 'document')} type='checkbox' /> {i.name}
                                        </label>
                                    )
                                })
                            }

                        </div>
                        <h4>EHS Observation</h4>
                        <div className='form-group mb-2'>

                            {
                                allRoles.ehs.map((i, k) => {
                                    return (
                                        <label className='me-3' key={k}>
                                            <input value={i.id} checked={selectedRoles.ehs.includes(i.id)} onChange={(e) => handleRoleChange(e, 'ehs')} type='checkbox' /> {i.name}
                                        </label>
                                    )
                                })
                            }

                        </div>
                        <h4>ePermit to Work</h4>
                        <div className='form-group mb-2'>

                            {
                                allRoles.eptw.map((i, k) => {
                                    return (
                                        <label className='me-3' key={k}>
                                            <input value={i.id} checked={selectedRoles.eptw.includes(i.id)} onChange={(e) => handleRoleChange(e, 'eptw')} type='checkbox' /> {i.name}
                                        </label>
                                    )
                                })
                            }

                        </div>
                        <h4>IR Reporting</h4>
                        <div className='form-group mb-2'>

                            {
                                allRoles.incident.map((i, k) => {
                                    return (
                                        <label className='me-3' key={k}>
                                            <input value={i.id} checked={selectedRoles.incident.includes(i.id)} onChange={(e) => handleRoleChange(e, 'incident')} type='checkbox' /> {i.name}
                                        </label>
                                    )
                                })
                            }

                        </div>
                        <h4>Inspection and Audit</h4>
                        <div className='form-group mb-2'>

                            {
                                allRoles.inspection.map((i, k) => {
                                    return (
                                        <label className='me-3' key={k}>
                                            <input value={i.id} checked={selectedRoles.inspection.includes(i.id)} onChange={(e) => handleRoleChange(e, 'inspection')} type='checkbox' /> {i.name}
                                        </label>
                                    )
                                })
                            }

                        </div>
                        <h4>Risk Assessment</h4>
                        <div className='form-group mb-2'>

                            {
                                allRoles.plant.map((i, k) => {
                                    return (
                                        <label className='me-3' key={k}>
                                            <input value={i.id} checked={selectedRoles.plant.includes(i.id)} onChange={(e) => handleRoleChange(e, 'plant')} type='checkbox' /> {i.name}
                                        </label>
                                    )
                                })
                            }

                        </div>
                    </form>
                </Modal.Body>

                <Modal.Footer className="flex-wrap">

                    <>
                        <Button variant="light" onClick={() => setMdShow(false)}>Cancel</Button>

                        {selectedUserId.id && <Button variant="primary" onClick={handleAssignSubmit}>Assign</Button>}

                    </>


                </Modal.Footer>
            </Modal>


            <Modal
                show={userShow}
                onHide={() => setUserShow(false)}
                aria-labelledby="example-modal-sizes-title-md"
            >

                <Modal.Body>
                    <form className="forms">
                        <div className="form-group">
                            <label htmlFor="user_name" >Name</label>
                            <Form.Control type="text" ref={uName} id="user_name" placeholder="Enter User Name" />
                        </div>

                        <div className="form-group">
                            <label htmlFor="user_category" >Email</label>
                            <Form.Control type="email" ref={uEmail} id="user_category" placeholder="Enter User Email" />
                        </div>

                        <div className="form-group">
                            <label htmlFor="user_company" >Company Name</label>
                            <Form.Control type="text" ref={uCompany} id="user_company" placeholder="Enter Company Name" />
                        </div>




                    </form>
                </Modal.Body>

                <Modal.Footer className="flex-wrap">
                    {
                        isLoading ? <Loader /> : (
                            <>
                                <Button variant="light" onClick={() => setUserShow(false)}>Cancel</Button>
                                <Button variant="primary" onClick={createUserHandler}>Create</Button>
                            </>
                        )
                    }

                </Modal.Footer>
            </Modal>

            <Modal
                show={excelShow}
                onHide={() => setExcelShow(false)}
                aria-labelledby="example-modal-sizes-title-md"
            >

                <Modal.Body>
                    <DropzoneArea
                        acceptedFiles={[
                            'application/vnd.ms-excel',
                            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                        ]}
                        dropzoneText={"Drag and drop an Excel file here or click"}
                        filesLimit={1}
                        onChange={handleFileChange}
                    />
                </Modal.Body>

                <Modal.Footer className="flex-wrap">
                    {
                        isLoading ? <Loader /> : (
                            <>
                                <Button variant="light" onClick={() => setExcelShow(false)}>Cancel</Button>
                                <Button variant="primary" onClick={uploadBulkUser}>Upload</Button>
                            </>
                        )
                    }

                </Modal.Footer>
            </Modal>
        </CardOverlay>
    )
}


export default AppUser;
