import React from 'react';
import { Accordion } from 'react-bootstrap';
import HazardPanel from './HazardPanel';

const HazardAccordion = ({ hazards, activeTabIndex, setActiveTabIndex, selectedHazards, onClickHazards, required, item }) => {
    return (
        <Accordion defaultActiveKey={'0'}>
            <Accordion.Item eventKey="0">
                <Accordion.Header style={(required === false && item[1].selected.length === 0) ? { border: '1px solid red' } : {}}>
                    <h6 className='fw-bold'>Hazards Identification</h6>
                    <p>Identify potential hazards associated with sub-activity</p>
                </Accordion.Header>
                <Accordion.Body>
                    <div className="d-flex" style={{ border: '1px solid #E5E7EB' }}>
                        <HazardPanel
                            hazards={hazards}
                            activeTabIndex={activeTabIndex}
                            setActiveTabIndex={setActiveTabIndex}
                            selectedHazards={item[1].selected}
                            onClickHazards={onClickHazards}
                        />
                    </div>
                </Accordion.Body>
            </Accordion.Item>
        </Accordion>
    );
};

export default HazardAccordion;
