import React from 'react';
import { TabView, TabPanel } from 'primereact/tabview';
import HazardItem from './HazardItem';

const HazardPanel = ({ hazards, activeTabIndex, setActiveTabIndex, selectedHazards, onClickHazards }) => {
   
    console.log(hazards)

    if (localStorage.getItem('SELECTED_INDUSTRIES')) {
        return (
            <TabView
                activeIndex={activeTabIndex}
                onTabChange={(e) => setActiveTabIndex(e.index)}
                orientation="left"
                className='d-flex hazTabs'
            >
                {hazards.map((haz, h) => (
                    <TabPanel header={haz.name} className='tabsHead' key={h}>
                        <div className='row'>
                            {haz.hazardItems.map((ha, j) => (
                                <HazardItem
                                    key={j}
                                    hazard={ha}
                                    selectedHazards={selectedHazards}
                                    onClickHazards={onClickHazards}
                                />
                            ))}
                        </div>
                    </TabPanel>
                ))}
            </TabView>
        );
    }


    return (
        <TabView
            activeIndex={activeTabIndex}
            onTabChange={(e) => setActiveTabIndex(e.index)}
            orientation="left"
            className='d-flex hazTabs'
        >
            {hazards.map((haz, h) => (
                <TabPanel header={haz.name} className='tabsHead' key={h}>
                    <div className='row'>
                        {haz.hazards.map((ha, j) => (
                            <HazardItem
                                key={j}
                                hazard={ha}
                                selectedHazards={selectedHazards}
                                onClickHazards={onClickHazards}
                            />
                        ))}
                    </div>
                </TabPanel>
            ))}
        </TabView>
    );
};

export default HazardPanel;
