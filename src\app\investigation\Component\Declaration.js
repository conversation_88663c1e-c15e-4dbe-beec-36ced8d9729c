import React from 'react'

function Declaration({ members }) {
    console.log(members)
    return (
        <div>
            <div className='mb-3'>On behalf of the investigation team listed below, as Team Leader, I hereby declare that this investigation has been conducted thoroughly and impartially, with due diligence applied to gathering and analyzing all relevant facts.  The team has  examined the incident, identified root causes, contributing factors, and control failures, and provided recommendations to prevent recurrence.</div>

            <div className='mb-3'>The team affirm that the findings and conclusions presented in this report are accurate to the best of our knowledge and reflect our collective professional judgment.</div>
           
           <div className='fw-bold mb-4'>Investigation Team Members</div>
            <ol>
                {members && members.map((item) => {
                    return (
                        <li>{item}</li>
                    )
                })}

            </ol>
        </div>
    )
}

export default Declaration