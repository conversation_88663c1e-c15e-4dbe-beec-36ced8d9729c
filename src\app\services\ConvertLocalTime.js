import moment from 'moment-timezone';

export const convertToLocalTime = (gmtDate) => {
    // Get the system's time zone
    const systemTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

    // Parse the GMT date string into a moment object with the given format and convert to local time
    const localDate = moment.tz(gmtDate, 'GMT').tz(systemTimeZone).format('DD-MM-YYYY')
    return localDate;
  };