import React, { useState } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Dropdown } from 'primereact/dropdown';
import { InputText } from 'primereact/inputtext';

export default function PlannedOperational() {
  const [filters, setFilters] = useState({});
  const [globalFilter, setGlobalFilter] = useState('');

  const [data, setData] = useState([
   
    // Add more dummy data as needed
  ]);

  const responsibilityOptions = [
    { label: 'Safety Team', value: 'Safety Team' },
    { label: 'Operations', value: 'Operations' },
    { label: 'Maintenance', value: 'Maintenance' }
  ];

  const statusOptions = [
    { label: 'Planned', value: 'Planned' },
    { label: 'Overdue', value: 'Overdue' }
  ];

  const typeOptions = [
    { label: 'Preventative', value: 'Preventative' },
    { label: 'Mitigative', value: 'Mitigative' }
  ];

  const pertainsToOptions = [
    { label: 'Routine', value: 'Routine' },
    { label: 'Non-Routine', value: 'Non-Routine' },
    { label: 'High-Risk Scenario', value: 'High-Risk Scenario' }
  ];

  const raIdTemplate = (rowData) => {
    return (
      <a href="#" onClick={() => alert(`Opening RA: ${rowData.raId}`)}>
        {rowData.raId}
      </a>
    );
  };

  return (
    <div className="card p-3">
      {/* Description Section */}
      <div className="mb-4">
        <p>
          This register tracks <strong>additional controls</strong> identified during Risk Assessments (RAs) that are pending implementation. These controls are critical for addressing gaps in risk mitigation but have not yet been integrated into operational workflows due to dependencies, resource constraints, or scheduled timelines.
        </p>
        <strong>Purpose:</strong>
        <ul>
          <li>Monitor progress toward closing risk gaps and ensure accountability for implementation.</li>
          <li>Prioritize actions to align with compliance deadlines or operational needs.</li>
        </ul>
      </div>

      {/* Search Box */}
      <div className="d-flex justify-content-end mb-2">
        <span className="p-input-icon-left">
          <i className="pi pi-search" />
          <InputText 
            value={globalFilter} 
            onChange={(e) => setGlobalFilter(e.target.value)} 
            placeholder="Search" 
          />
        </span>
      </div>

      {/* Data Table */}
      <DataTable value={data} paginator rows={5} rowsPerPageOptions={[5, 10, 25]} globalFilter={globalFilter} tableStyle={{ minWidth: '90rem' }}>

        <Column field="controlId" header="Control ID" sortable></Column>

        <Column field="plannedControl" header="Planned Control"></Column>

        <Column field="dueDate" header="Due Date" sortable></Column>

        <Column 
          field="responsibility" 
          header="Responsibility" 
          filter 
          filterElement={
            <Dropdown 
              value={filters.responsibility} 
              options={responsibilityOptions} 
              onChange={(e) => setFilters(prev => ({ ...prev, responsibility: e.value }))}
              placeholder="Select Responsibility" 
              showClear
            />
          }
        ></Column>

        <Column 
          field="status" 
          header="Status (Planned / Overdue)" 
          filter 
          filterElement={
            <Dropdown 
              value={filters.status} 
              options={statusOptions} 
              onChange={(e) => setFilters(prev => ({ ...prev, status: e.value }))}
              placeholder="Select Status" 
              showClear
            />
          }
        ></Column>

        <Column field="raId" header="RA ID" sortable body={raIdTemplate}></Column>

        <Column 
          field="type" 
          header="Type (Preventative or Mitigative)" 
          filter 
          filterElement={
            <Dropdown 
              value={filters.type} 
              options={typeOptions} 
              onChange={(e) => setFilters(prev => ({ ...prev, type: e.value }))}
              placeholder="Select Type" 
              showClear
            />
          }
        ></Column>

        <Column 
          field="pertainsTo" 
          header="Pertains To (Routine / Non-Routine / High-Risk Scenario)" 
          filter 
          filterElement={
            <Dropdown 
              value={filters.pertainsTo} 
              options={pertainsToOptions} 
              onChange={(e) => setFilters(prev => ({ ...prev, pertainsTo: e.value }))}
              placeholder="Select Pertains To" 
              showClear
            />
          }
        ></Column>

      </DataTable>
    </div>
  );
}
