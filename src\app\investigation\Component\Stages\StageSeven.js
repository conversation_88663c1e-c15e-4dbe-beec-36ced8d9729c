import React from 'react';
import { InputTextarea } from 'primereact/inputtextarea';

function StageSeven({ formData, handleChange, errors, disable }) {
    return (
        <div className="row mb-4">
            <p>Steps taken immediately after the incident to manage or control the situation and ensure safety</p>

            <div className="col-6 mb-4">
                <p htmlFor="initialActions" className="mb-2">1. What were the first actions taken to control or contain the situation?</p>
                <InputTextarea
                    style={{ width: '100%' }}
                    rows={2}
                    autoResize
                    name="immediateCause.initialActions"
                    value={formData.immediateCause.initialActions}
                    onChange={handleChange}
                    disabled={disable}
                />
                {errors.initialActions && <small className="p-error">{errors.initialActions}</small>}
            </div>

            <div className="col-6 mb-4">
                <p htmlFor="medicalAttentionProvided" className="mb-2">2. Was first aid or medical attention provided to any personnel involved?</p>
                <InputTextarea
                    style={{ width: '100%' }}
                    rows={2}
                    autoResize
                    name="immediateCause.medicalAttentionProvided"
                    value={formData.immediateCause.medicalAttentionProvided}
                    onChange={handleChange}
                    disabled={disable}
                />
                {errors.medicalAttentionProvided && <small className="p-error">{errors.medicalAttentionProvided}</small>}
            </div>

            <div className="col-6 mb-4">
                <p htmlFor="emergencyServicesCalled" className="mb-2">3. Were any emergency services (firefighters, paramedics, police) called?</p>
                <InputTextarea
                    style={{ width: '100%' }}
                    rows={2}
                    autoResize
                    name="immediateCause.emergencyServicesCalled"
                    value={formData.immediateCause.emergencyServicesCalled}
                    onChange={handleChange}
                    disabled={disable}
                />
                {errors.emergencyServicesCalled && <small className="p-error">{errors.emergencyServicesCalled}</small>}
            </div>

            <div className="col-6 mb-4">
                <p htmlFor="areaSecured" className="mb-2">4. Was the affected area secured or isolated to prevent further incidents?</p>
                <InputTextarea
                    style={{ width: '100%' }}
                    rows={2}
                    autoResize
                    name="immediateCause.areaSecured"
                    value={formData.immediateCause.areaSecured}
                    onChange={handleChange}
                    disabled={disable}
                />
                {errors.areaSecured && <small className="p-error">{errors.areaSecured}</small>}
            </div>

            <div className="col-6 mb-4">
                <p htmlFor="safetyControlsActivated" className="mb-2">5. Were any safety controls (e.g., shutdown of equipment, emergency stop) activated?</p>
                <InputTextarea
                    style={{ width: '100%' }}
                    rows={2}
                    autoResize
                    name="immediateCause.safetyControlsActivated"
                    value={formData.immediateCause.safetyControlsActivated}
                    onChange={handleChange}
                    disabled={disable}
                />
                {errors.safetyControlsActivated && <small className="p-error">{errors.safetyControlsActivated}</small>}
            </div>

            <div className="col-6 mb-4">
                <p htmlFor="evacuationInitiated" className="mb-2">6. Were evacuation procedures initiated, and if so, how were they managed?</p>
                <InputTextarea
                    style={{ width: '100%' }}
                    rows={2}
                    autoResize
                    name="immediateCause.evacuationInitiated"
                    value={formData.immediateCause.evacuationInitiated}
                    onChange={handleChange}
                    disabled={disable}
                />
                {errors.evacuationInitiated && <small className="p-error">{errors.evacuationInitiated}</small>}
            </div>

            <div className="col-6 mb-4">
                <p htmlFor="hazardousMaterialsHandled" className="mb-2">7. Were any hazardous materials contained, cleaned up, or neutralized?</p>
                <InputTextarea
                    style={{ width: '100%' }}
                    rows={2}
                    autoResize
                    name="immediateCause.hazardousMaterialsHandled"
                    value={formData.immediateCause.hazardousMaterialsHandled}
                    onChange={handleChange}
                    disabled={disable}
                />
                {errors.hazardousMaterialsHandled && <small className="p-error">{errors.hazardousMaterialsHandled}</small>}
            </div>

            <div className="col-6 mb-4">
                <p htmlFor="communicationWithAuthorities" className="mb-2">8. Was there any communication with management, safety officers, or external authorities?</p>
                <InputTextarea
                    style={{ width: '100%' }}
                    rows={2}
                    autoResize
                    name="immediateCause.communicationWithAuthorities"
                    value={formData.immediateCause.communicationWithAuthorities}
                    onChange={handleChange}
                    disabled={disable}
                />
                {errors.communicationWithAuthorities && <small className="p-error">{errors.communicationWithAuthorities}</small>}
            </div>
        </div>
    );
}

export default StageSeven;
