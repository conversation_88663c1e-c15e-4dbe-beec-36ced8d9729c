import React from 'react';

const HeadStepper = ({ stages, stageStatus, activeStage, handleStageClick, getStatusClass }) => {
    console.log(stageStatus)

    const getStatusKeyByIndex = (index) => {
        switch (index) {
            case 0:
                return stageStatus.hazardsIdentification;
            case 1:
                return stageStatus.consequences;
            case 2:
                return stageStatus.currentControls;
            case 3:
                return stageStatus.riskEstimation;
            case 4:
                return stageStatus.additionalControls;
            default:
                return '';
        }
    };

    return (
        <>
            <div className="stepper-container mt-4">

                {stages.map((stage, index) => {
                    const stageKey = getStatusKeyByIndex(index);


                    const statusClass = getStatusClass(stageKey);


                    return (
                        <div key={index} className="stepper-step" onClick={() => handleStageClick(index)}>
                            <div className={`circle ${statusClass}`}>
                                {index + 1}
                            </div>
                            {index < stages.length - 1 && <div className="line"></div>}
                            <div className={`step-title ${activeStage === index ? 'active' : ''}`}>{stage}</div>
                        </div>
                    );
                })}
            </div>
            <div className='color mb-3 mt-3 d-flex justify-content-end'>
                <div className="d-flex align-items-center me-4">
                    <div className="instruction-circle" style={{ backgroundColor: '#28a745' }}></div>
                    <span className="ms-2">Finalized</span>
                </div>
                <div className="d-flex align-items-center me-4">
                    <div className="instruction-circle" style={{ backgroundColor: '#ffa500' }}></div>
                    <span className="ms-2">Drafted</span>
                </div>
                <div className="d-flex align-items-center">
                    <div className="instruction-circle" style={{ backgroundColor: '#808080' }}></div>
                    <span className="ms-2">No information entered</span>
                </div>
            </div>
        </>
    );
};

export default HeadStepper;
