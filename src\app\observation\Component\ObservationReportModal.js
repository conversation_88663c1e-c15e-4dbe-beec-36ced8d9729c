import React, { useState, useCallback, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { Form, Button, Modal } from 'react-bootstrap';
import AllFilterLocation from '../../investigation/LocationDropDown';
import ImageComponent from '../../services/FileDownlodS3';
import API from '../../services/API';
import { FILE_URL, GET_USER_ROLE_BY_MODE } from '../../constants';
import { DropzoneArea } from 'material-ui-dropzone';
import { ButtonGroup, ToggleButton } from 'react-bootstrap';
import Select from 'react-select';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { formatISO } from 'date-fns';


const ObservationReportModal = ({
    show,
    handleClose,
    onSubmit,
    formData,
    setFormData,
}) => {
    // const { register, handleSubmit } = useForm({
    //     defaultValues: formData,
    // });
    const [reviewer, setReviewer] = useState([])
    const [actionOwner, setActionOwner] = useState([])

    useEffect(() => {
        if (formData.locationOneId) {
            getActionOwner()
            getObsReviewer()
        }

    }, [formData])

    const getActionOwner = useCallback(async () => {
        try {
            const response = await API.post(GET_USER_ROLE_BY_MODE, {
                locationOneId: formData.locationOneId || "",
                locationTwoId: formData.locationTwoId || "",
                locationThreeId: formData.locationThreeId || "",
                locationFourId: formData.locationFourId || "",
                mode: 'obsactionowner'
            });

            if (response.status === 200) {
                const data = response.data.map((item) => ({
                    label: item.firstName,
                    value: item.id
                }));
                setActionOwner(data);
            }
        } catch (error) {
            console.error("Error fetching crew list:", error);
        }
    }, [formData.locationOneId, formData.locationTwoId, formData.locationThreeId, formData.locationFourId]);

    const getObsReviewer = useCallback(async () => {
        try {
            const response = await API.post(GET_USER_ROLE_BY_MODE, {
                locationOneId: formData.locationOneId || "",
                locationTwoId: formData.locationTwoId || "",
                locationThreeId: formData.locationThreeId || "",
                locationFourId: formData.locationFourId || "",
                mode: 'obsreviewer'
            });

            if (response.status === 200) {
                const data = response.data.map((item) => ({
                    label: item.firstName,
                    value: item.id
                }));
                setReviewer(data);
            }
        } catch (error) {
            console.error("Error fetching crew list:", error);
        }
    }, [formData.locationOneId, formData.locationTwoId, formData.locationThreeId, formData.locationFourId]);


    // ------------------ NEW: State for field-level errors ------------------ //
    const [fieldErrors, setFieldErrors] = useState({});

    // -------------------- FILE UPLOAD LOGIC -------------------- //
    const handleFileUpload = async (files, field) => {
        if (files.length > 0) {
            const latestFile = files[files.length - 1];
            const formData1 = new FormData();
            formData1.append('file', latestFile);
            try {
                const response = await API.post(FILE_URL, formData1, {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                    },
                });

                if (response && response.status === 200) {
                    setFormData((prevState) => ({
                        ...prevState,
                        [field]: [
                            ...(prevState[field] || []),
                            response.data.files[0].originalname,
                        ],
                    }));
                }
            } catch (error) {
                console.error('File upload error: ', error);
            }
        }
    };

    const handleSupportingUpload = (files) => handleFileUpload(files, 'uploads');
    const handleEvidenceUpload = (files) => handleFileUpload(files, 'evidence');

    // -------------------- LOCATION LOGIC -------------------- //
    const handleFilter = (
        locationOneId,
        locationTwoId,
        locationThreeId,
        locationFourId,
        locationFiveId,
        locationSixId
    ) => {
        setFormData((prev) => ({
            ...prev,
            locationOneId,
            locationTwoId,
            locationThreeId,
            locationFourId,
            locationFiveId,
            locationSixId,
        }));
    };

    // -------------------- REMOVE FILES -------------------- //
    const handleRemoveMainImage = (index) => {
        const updatedImages = formData.uploads.filter((_, i) => i !== index);
        setFormData({
            ...formData,
            uploads: updatedImages,
        });
    };

    const handleRemoveEvidenceImage = (index) => {
        const updatedImages = formData.evidence.filter((_, i) => i !== index);
        setFormData({
            ...formData,
            evidence: updatedImages,
        });
    };

    // -------------------- SELECT DROPDOWNS -------------------- //
    const handleApplicantChange = (selectedOption) => {
        setFormData({
            ...formData,
            reviewerId: selectedOption ? selectedOption.value : '',
        });
    };

    const handleActionOwnerChange = (selectedOption) => {
        setFormData({
            ...formData,
            actionOwnerId: selectedOption ? selectedOption.value : '',
        });
    };

    // -------------------- CUSTOM VALIDATION -------------------- //
    const handleFormSubmit = () => {
        // We'll collect errors in an object
        const newFieldErrors = {};

        // 1. LOCATION: at least locationOneId is required
        if (!formData.locationOneId) {
            newFieldErrors.locationOneId = 'Location is required.';
        }

        // 2. observationCategory is required
        if (!formData.observationCategory) {
            newFieldErrors.observationCategory = 'Observation Category is required.';
        }

        // 3. observationType is required
        if (!formData.observationType) {
            newFieldErrors.observationType = 'Observation Type is required.';
        }

        // 4. observationActOrCondition is required
        if (!formData.observationActOrCondition) {
            newFieldErrors.observationActOrCondition =
                'Observation Act or Condition is required.';
        }

        // 5. description is required
        if (!formData.description?.trim()) {
            newFieldErrors.description = 'Description is required.';
        }

        // 6. supporting files: min 2 attachments
        if (!formData.uploads || formData.uploads.length < 2) {
            newFieldErrors.uploads =
                'Please attach at least 2 supporting documents/images.';
        }

        // 7. If it's UNSAFE...
        if (formData.observationType === 'Unsafe') {
            // rectifiedOnSpot is required (true/false)
            if (typeof formData.rectifiedOnSpot !== 'boolean') {
                newFieldErrors.rectifiedOnSpot = 'Rectified on Spot is required.';
            } else {
                // If rectifiedOnSpot = true, actionTaken & at least 2 evidence attachments
                if (formData.rectifiedOnSpot === true) {
                    if (!formData.actionTaken?.trim()) {
                        newFieldErrors.actionTaken = 'Action Taken is required.';
                    }
                    if (!formData.evidence || formData.evidence.length < 2) {
                        newFieldErrors.evidence = 'Please attach at least 2 evidence files.';
                    }
                } else {
                    // rectifiedOnSpot = false
                    if (typeof formData.isReviewerRequired !== 'boolean') {
                        newFieldErrors.isReviewerRequired =
                            'Please specify if Reviewer is required.';
                    } else {
                        if (formData.isReviewerRequired === true && !formData.reviewerId) {
                            newFieldErrors.reviewerId = 'Please select a Reviewer.';
                        }
                        if (formData.isReviewerRequired === false) {
                            if (!formData.actionToBeTaken?.trim()) {
                                newFieldErrors.actionToBeTaken =
                                    'Action to be Taken is required.';
                            }
                            if (!formData.dueDate) {
                                newFieldErrors.dueDate = 'Due Date is required.';
                            }
                            if (!formData.actionOwnerId) {
                                newFieldErrors.actionOwnerId = 'Action Owner is required.';
                            }
                        }
                    }
                }
            }
        }

        // Now set the errors in state
        setFieldErrors(newFieldErrors);

        // If we have ANY errors, do not proceed
        if (Object.keys(newFieldErrors).length > 0) {
            return; // Stop here
        }

        // Otherwise, clear errors and proceed
        setFieldErrors({});
        onSubmit(formData);
    };

    return (
        <Modal show={show} onHide={handleClose} size="lg" className="observation-report-modal">
            <Modal.Header closeButton>
                <Modal.Title>Observation Report</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                {/* Instead of onSubmit in <Form>, we do onClick on the button to handle custom validation */}
                <Form onSubmit={(e) => e.preventDefault()}>
                    {/* --- LOCATION --- */}
                    <Form.Group className="mb-3">
                        <Form.Label>
                            Location <span className="text-danger">*</span>
                        </Form.Label>
                        <AllFilterLocation handleFilter={handleFilter} getLocation={formData} />
                        {fieldErrors.locationOneId && (
                            <div className="text-danger mt-1">{fieldErrors.locationOneId}</div>
                        )}
                    </Form.Group>

                    {/* --- OBSERVATION CATEGORY --- */}
                    <Form.Group controlId="observationCategory">
                        <Form.Label>
                            Observation Category <span className="text-danger">*</span>
                        </Form.Label>
                        <ButtonGroup className="d-flex toggle-button-group">
                            {['Environment', 'HSE', 'Social'].map((category, index) => (
                                <ToggleButton
                                    key={category}
                                    id={`observationCategory-${index}`}
                                    type="radio"
                                    variant={
                                        formData.observationCategory === category
                                            ? 'primary'
                                            : 'outline-primary'
                                    }
                                    name="observationCategory"
                                    value={category}
                                    checked={formData.observationCategory === category}
                                    onChange={(e) =>
                                        setFormData({
                                            ...formData,
                                            observationCategory: e.currentTarget.value,
                                            observationType: '',  // Reset dependent fields
                                            observationActOrCondition: '',
                                        })
                                    }
                                    className="w-100 btn-toggle"
                                >
                                    {category}
                                </ToggleButton>
                            ))}
                        </ButtonGroup>
                        {fieldErrors.observationCategory && (
                            <div className="text-danger mt-1">{fieldErrors.observationCategory}</div>
                        )}
                    </Form.Group>

                    {/* --- OBSERVATION TYPE --- */}
                    <Form.Group controlId="observationType">
                        <Form.Label>
                            Observation Type <span className="text-danger">*</span>
                        </Form.Label>
                        <ButtonGroup className="d-flex toggle-button-group">
                            {[
                                { label: 'Safe', value: 'Safe', variant: 'success' },
                                { label: 'Unsafe', value: 'Unsafe', variant: 'danger' },
                            ].map(({ label, value, variant }, index) => (
                                <ToggleButton
                                    key={value}
                                    id={`observationType-${index}`}
                                    type="radio"
                                    variant={
                                        formData.observationType === value
                                            ? variant
                                            : `outline-${variant}`
                                    }
                                    name="observationType"
                                    value={value}
                                    checked={formData.observationType === value}
                                    onChange={(e) =>
                                        setFormData({
                                            ...formData,
                                            observationType: e.currentTarget.value,
                                            observationActOrCondition: '', // Reset dependent fields
                                            rectifiedOnSpot: false,
                                            actionTaken: '',
                                            evidence: [],
                                            isReviewerRequired: false,
                                            reviewerId: '',
                                            actionToBeTaken: '',
                                            dueDate: new Date().toISOString(),
                                            actionOwnerId: '',
                                        })
                                    }
                                    className="w-100 btn-toggle"
                                >
                                    {label}
                                </ToggleButton>
                            ))}
                        </ButtonGroup>
                        {fieldErrors.observationType && (
                            <div className="text-danger mt-1">{fieldErrors.observationType}</div>
                        )}
                    </Form.Group>

                    {/* --- OBSERVATION ACT OR CONDITION --- */}
                    <Form.Group controlId="observationActOrCondition">
                        <Form.Label>
                            Observation Act or Condition{' '}
                            <span className="text-danger">*</span>
                        </Form.Label>
                        <ButtonGroup className="d-flex toggle-button-group">
                            {[
                                { label: 'Act', value: 'Act' },
                                { label: 'Condition', value: 'Condition' },
                            ].map(({ label, value }, index) => {
                                const isSelected = formData.observationActOrCondition === value;
                                const baseVariant =
                                    formData.observationType === 'Safe' ? 'success' : 'danger';

                                return (
                                    <ToggleButton
                                        key={value}
                                        id={`observationActOrCondition-${index}`}
                                        type="radio"
                                        variant={
                                            isSelected ? baseVariant : `outline-${baseVariant}`
                                        }
                                        name="observationActOrCondition"
                                        value={value}
                                        checked={isSelected}
                                        onChange={(e) =>
                                            setFormData({
                                                ...formData,
                                                observationActOrCondition: e.currentTarget.value,
                                            })
                                        }
                                        className="w-100 btn-toggle"
                                    >
                                        {label}
                                    </ToggleButton>
                                );
                            })}
                        </ButtonGroup>
                        {fieldErrors.observationActOrCondition && (
                            <div className="text-danger mt-1">
                                {fieldErrors.observationActOrCondition}
                            </div>
                        )}
                    </Form.Group>

                    {/* --- DESCRIPTION --- */}
                    <Form.Group controlId="description">
                        <Form.Label>
                            Description <span className="text-danger">*</span>
                        </Form.Label>
                        <Form.Control
                            as="textarea"
                            rows={3}
                            value={formData.description || ''}
                            onChange={(e) =>
                                setFormData((prev) => ({
                                    ...prev,
                                    description: e.target.value,
                                }))
                            }
                        />
                        {fieldErrors.description && (
                            <div className="text-danger mt-1">{fieldErrors.description}</div>
                        )}
                    </Form.Group>

                    {/* --- SUPPORTING FILES --- */}
                    <div className="col-12 mt-3 dropzone-container">
                        <Form.Label>
                            Add/Upload images of the work location
                            (Min 2 attachments are mandatory){' '}
                            <span className="text-danger">*</span>
                        </Form.Label>
                        <DropzoneArea
                            dropzoneText="Attach supporting images / documents"
                            filesLimit={5}
                            maxFileSize={104857600} // ~100 MB
                            showPreviewsInDropzone={false}
                            showPreviews={false}
                            dropzoneClass="dropzoneText d-flex align-items-center justify-content-center"
                            onChange={(files) => handleSupportingUpload(files)}
                        />
                        {fieldErrors.uploads && (
                            <div className="text-danger mt-1">{fieldErrors.uploads}</div>
                        )}
                    </div>

                    {/* --- DISPLAY UPLOADED SUPPORTING FILES --- */}
                    {formData.uploads && formData.uploads.length > 0 && (
                        <div className="col-12 mt-3 mb-4">
                            <Form.Label>Uploaded Documents</Form.Label>
                            <div className="image-preview">
                                <div className="row">
                                    {formData.uploads.map((file, idx) => (
                                        <div
                                            key={idx}
                                            className="col-3"
                                            style={{ position: 'relative' }}
                                        >
                                            <div className="boxShadow d-flex align-items-center">
                                                <ImageComponent
                                                    fileName={file}
                                                    size={'100'}
                                                    name={true}
                                                />
                                                <i
                                                    className="pi pi-trash"
                                                    onClick={() => handleRemoveMainImage(idx)}
                                                    style={{
                                                        position: 'absolute',
                                                        top: '5px',
                                                        right: '5px',
                                                        cursor: 'pointer',
                                                    }}
                                                />
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    )}

                    {/* --- SHOW THESE FIELDS ONLY IF OBSERVATION TYPE IS UNSAFE --- */}
                    {formData.observationType === 'Unsafe' && (
                        <>
                            {/* Rectified On Spot */}
                            <Form.Group controlId="rectifiedOnSpot">
                                <Form.Label>Rectified on Spot</Form.Label>
                                <ButtonGroup className="d-flex toggle-button-group">
                                    {[
                                        { label: 'Yes', value: true, variant: 'success' },
                                        { label: 'No', value: false, variant: 'danger' },
                                    ].map(({ label, value, variant }, index) => (
                                        <ToggleButton
                                            key={label}
                                            id={`rectifiedOnSpot-${index}`}
                                            type="radio"
                                            variant={
                                                formData.rectifiedOnSpot === value
                                                    ? variant
                                                    : `outline-${variant}`
                                            }
                                            name="rectifiedOnSpot"
                                            value={value}
                                            checked={formData.rectifiedOnSpot === value}
                                            onChange={() =>
                                                setFormData({
                                                    ...formData,
                                                    rectifiedOnSpot: value,
                                                    actionTaken: '',
                                                    evidence: [],
                                                    isReviewerRequired: false,
                                                    reviewerId: '',
                                                    actionToBeTaken: '',
                                                    dueDate: new Date().toISOString(),
                                                    actionOwnerId: '',
                                                })
                                            }
                                            className="w-100 btn-toggle"
                                        >
                                            {label}
                                        </ToggleButton>
                                    ))}
                                </ButtonGroup>
                                {fieldErrors.rectifiedOnSpot && (
                                    <div className="text-danger mt-1">
                                        {fieldErrors.rectifiedOnSpot}
                                    </div>
                                )}
                            </Form.Group>

                            {formData.rectifiedOnSpot ? (
                                <>
                                    {/* Action Taken */}
                                    <Form.Group controlId="actionTaken">
                                        <Form.Label>Action Taken</Form.Label>
                                        <Form.Control
                                            type="text"
                                            value={formData.actionTaken || ''}
                                            onChange={(e) =>
                                                setFormData((prev) => ({
                                                    ...prev,
                                                    actionTaken: e.target.value,
                                                }))
                                            }
                                        />
                                        {fieldErrors.actionTaken && (
                                            <div className="text-danger mt-1">
                                                {fieldErrors.actionTaken}
                                            </div>
                                        )}
                                    </Form.Group>

                                    {/* Evidence Upload */}
                                    <div className="col-12 mt-3 dropzone-container">
                                        <Form.Label>
                                            Add/Upload Evidence (Min 2 attachments are mandatory){' '}
                                            <span className="text-danger">*</span>
                                        </Form.Label>
                                        <DropzoneArea
                                            dropzoneText="Attach supporting images / documents"
                                            filesLimit={5}
                                            maxFileSize={104857600}
                                            showPreviewsInDropzone={false}
                                            showPreviews={false}
                                            dropzoneClass="dropzoneText d-flex align-items-center justify-content-center"
                                            onChange={(files) => handleEvidenceUpload(files)}
                                        />
                                        {fieldErrors.evidence && (
                                            <div className="text-danger mt-1">
                                                {fieldErrors.evidence}
                                            </div>
                                        )}
                                    </div>

                                    {formData.evidence && formData.evidence.length > 0 && (
                                        <div className="col-12 mt-3 mb-4">
                                            <Form.Label>Uploaded Evidence</Form.Label>
                                            <div className="image-preview">
                                                <div className="row">
                                                    {formData.evidence.map((file, idx) => (
                                                        <div
                                                            key={idx}
                                                            className="col-3"
                                                            style={{ position: 'relative' }}
                                                        >
                                                            <div className="boxShadow d-flex align-items-center">
                                                                <ImageComponent
                                                                    fileName={file}
                                                                    size={'100'}
                                                                    name={true}
                                                                />
                                                                <i
                                                                    className="pi pi-trash"
                                                                    onClick={() => handleRemoveEvidenceImage(idx)}
                                                                    style={{
                                                                        position: 'absolute',
                                                                        top: '5px',
                                                                        right: '5px',
                                                                        cursor: 'pointer',
                                                                    }}
                                                                />
                                                            </div>
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>
                                        </div>
                                    )}
                                </>
                            ) : (
                                <>
                                    {/* Need reviewer? */}
                                    <Form.Group controlId="isReviewerRequired">
                                        <Form.Label>
                                            Do you Need to send this Observation to Reviewer?
                                        </Form.Label>
                                        <ButtonGroup className="d-flex toggle-button-group">
                                            {[
                                                { label: 'Yes', value: true, variant: 'success' },
                                                { label: 'No', value: false, variant: 'danger' },
                                            ].map(({ label, value, variant }, index) => (
                                                <ToggleButton
                                                    key={label}
                                                    id={`isReviewerRequired-${index}`}
                                                    type="radio"
                                                    variant={
                                                        formData.isReviewerRequired === value
                                                            ? variant
                                                            : `outline-${variant}`
                                                    }
                                                    name="isReviewerRequired"
                                                    value={value}
                                                    checked={formData.isReviewerRequired === value}
                                                    onChange={() =>
                                                        setFormData({
                                                            ...formData,
                                                            isReviewerRequired: value,
                                                            reviewerId: value ? '' : formData.reviewerId,
                                                            actionToBeTaken: value ? '' : formData.actionToBeTaken,
                                                            dueDate: value ? '' : formData.dueDate,
                                                            actionOwnerId: value ? '' : formData.actionOwnerId,
                                                        })
                                                    }
                                                    className="w-100 btn-toggle"
                                                >
                                                    {label}
                                                </ToggleButton>
                                            ))}
                                        </ButtonGroup>
                                        {fieldErrors.isReviewerRequired && (
                                            <div className="text-danger mt-1">
                                                {fieldErrors.isReviewerRequired}
                                            </div>
                                        )}
                                    </Form.Group>

                                    {formData.isReviewerRequired ? (
                                        <>
                                            {/* Reviewer is required */}
                                            <Form.Group className="mb-3">
                                                <Form.Label>Reviewer</Form.Label>
                                                <Select
                                                    options={reviewer}
                                                    value={reviewer.find(
                                                        (option) => option.value === formData.reviewerId
                                                    )}
                                                    onChange={handleApplicantChange}
                                                    placeholder="Select Reviewer"
                                                    isClearable
                                                />
                                                {fieldErrors.reviewerId && (
                                                    <div className="text-danger mt-1">
                                                        {fieldErrors.reviewerId}
                                                    </div>
                                                )}
                                            </Form.Group>
                                        </>
                                    ) : (
                                        <>
                                            {/* Action fields */}
                                            <Form.Group controlId="actionToBeTaken" className="mb-3">
                                                <Form.Label className="mb-3">Action to be Taken</Form.Label>
                                                <Form.Control
                                                    type="text"
                                                    value={formData.actionToBeTaken || ''}
                                                    onChange={(e) =>
                                                        setFormData((prev) => ({
                                                            ...prev,
                                                            actionToBeTaken: e.target.value,
                                                        }))
                                                    }
                                                />
                                                {fieldErrors.actionToBeTaken && (
                                                    <div className="text-danger mt-1">
                                                        {fieldErrors.actionToBeTaken}
                                                    </div>
                                                )}
                                            </Form.Group>

                                            <Form.Group controlId="dueDate" className="mb-3">
                                                <Form.Label className="mb-3">Due Date</Form.Label>
                                                <DatePicker
                                                    selected={formData.dueDate ? new Date(formData.dueDate) : null}
                                                    onChange={(date) =>
                                                        setFormData((prev) => ({
                                                            ...prev,
                                                            dueDate: date ? date.toISOString() : "", // Convert date to ISO format
                                                        }))
                                                    }
                                                    dateFormat="yyyy-MM-dd"
                                                    className="form-control"
                                                    placeholderText="Select a date"
                                                    minDate={new Date()} // Restrict past dates
                                                />
                                                {fieldErrors.dueDate && (
                                                    <div className="text-danger mt-1">{fieldErrors.dueDate}</div>
                                                )}
                                            </Form.Group>

                                            <Form.Group className="mb-3">
                                                <Form.Label className="mb-3">Action Owner</Form.Label>
                                                <Select
                                                    options={actionOwner}
                                                    value={actionOwner.find(
                                                        (option) => option.value === formData.actionOwnerId
                                                    )}
                                                    onChange={handleActionOwnerChange}
                                                    placeholder="Select Action Owner"
                                                    isClearable
                                                />
                                                {fieldErrors.actionOwnerId && (
                                                    <div className="text-danger mt-1">
                                                        {fieldErrors.actionOwnerId}
                                                    </div>
                                                )}
                                            </Form.Group>
                                        </>
                                    )}
                                </>
                            )}
                        </>
                    )}

                    <Modal.Footer>
                        <Button variant="secondary" onClick={handleClose}>
                            Close
                        </Button>
                        {/* Trigger the custom validation & final onSubmit */}
                        <Button variant="primary" onClick={handleFormSubmit}>
                            Submit
                        </Button>
                    </Modal.Footer>
                </Form>
            </Modal.Body>
        </Modal>
    );
};

export default ObservationReportModal;
