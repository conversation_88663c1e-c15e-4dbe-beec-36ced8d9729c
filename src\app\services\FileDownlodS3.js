import React, { useState, useEffect } from 'react';
import { fetchFullImageUrl, fetchDataUrlForImage } from './imageUtils';
import GalleryPage from '../apps/Gallery';

const ImageComponent = ({ fileName, size, name }) => {
    const [fileUrl, setFileUrl] = useState(null);
    const [dataUrl, setDataUrl] = useState(null);

    useEffect(() => {
        const getFileUrl = async () => {
            try {
                const url = await fetchFullImageUrl(fileName);
                setFileUrl(url);

                // Check if the URL is an image and fetch the data URL
                const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
                const extension = fileName.split('.').pop().toLowerCase();
                console.log(fileName)
                if (imageExtensions.includes(extension)) {
                    const dataUrl = await fetchDataUrlForImage(url);
                 
                    setDataUrl(dataUrl);
                }
            } catch (error) {
                console.error('Error fetching file or data URL:', error);
            }
        };

        getFileUrl();
    }, [fileName]);

    const cleanFileName = (fileName) => {
        return fileName.replace(/^\d+[\s-_]*/, '');
    };

    const checkFileType = (fileName) => {
        const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
        const pdfExtensions = ['pdf'];
        const xlsExtensions = ['xls', 'xlsx'];
        const extension = fileName.split('.').pop().toLowerCase();

        if (imageExtensions.includes(extension)) {
            return 'image';
        } else if (pdfExtensions.includes(extension)) {
            return 'pdf';
        } else if (xlsExtensions.includes(extension)) {
            return 'xls';
        } else {
            return 'other';
        }
    };

    if (!fileUrl) {
        return <p>Loading...</p>;
    }

    const fileType = checkFileType(fileName);
    const cleanedFileName = cleanFileName(fileName);

    switch (fileType) {
        case 'image':
            return (
                <div className="d-flex flex-column align-items-center p-2">
                    {dataUrl ? (
                        <GalleryPage
                            photos={[{ src: dataUrl }]}
                            alt="Image Data URL"
                            width={size}
                        />
                    ) : (
                        <p>Loading image...</p>
                    )}
                    {name && name === true ? (
                        <p style={{ wordBreak: 'break-word', textAlign: 'center' }}>{cleanedFileName}</p>
                    ) : null}
                </div>
            );
        case 'pdf':
            return (
                <div className="d-flex flex-column align-items-center p-2">
                    <a href={fileUrl} target="_blank" rel="noopener noreferrer">
                        <i className="fa fa-file-pdf-o fa-3x" aria-hidden="true"></i>
                    </a>
                    {name && name === true ? (
                        <p style={{ wordBreak: 'break-word', textAlign: 'center' }}>{cleanedFileName}</p>
                    ) : null}
                </div>
            );
        case 'xls':
            return (
                <div className="d-flex flex-column align-items-center p-2">
                    <a href={fileUrl} target="_blank" rel="noopener noreferrer">
                        <i className="fa fa-file-excel-o fa-3x" aria-hidden="true"></i>
                    </a>
                    {name && name === true ? (
                        <p style={{ wordBreak: 'break-word', textAlign: 'center' }}>{cleanedFileName}</p>
                    ) : null}
                </div>
            );
        case 'other':
            return (
                <div className="d-flex flex-column align-items-center p-2">
                    <a href={fileUrl} target="_blank" rel="noopener noreferrer">
                        <i className="fa fa-file-o fa-3x" aria-hidden="true"></i>
                    </a>
                    {name && name === true ? (
                        <p style={{ wordBreak: 'break-word', textAlign: 'center' }}>{cleanedFileName}</p>
                    ) : null}
                </div>
            );
        default:
            return <p>Unknown file type</p>;
    }
};

export default ImageComponent;
