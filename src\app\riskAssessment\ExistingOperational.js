import React, { useState } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Dropdown } from 'primereact/dropdown';

export default function ExistingOperational() {
  const [filters, setFilters] = useState({});
  const [data, setData] = useState([
    
    // Add more sample data as needed
  ]);

  const typeOptions = [
    { label: 'Preventative', value: 'Preventative' },
    { label: 'Mitigative', value: 'Mitigative' }
  ];

  const pertainsToOptions = [
    { label: 'Routine', value: 'Routine' },
    { label: 'Non-Routine', value: 'Non-Routine' },
    { label: 'High-Risk Scenario', value: 'High-Risk Scenario' }
  ];

  const statusOptions = [
    { label: 'Active', value: 'Active' },
    { label: 'Under Review', value: 'Under Review' },
    { label: 'Archived', value: 'Archived' }
  ];

  const raIdTemplate = (rowData) => {
    return (
      <div className='maskId' onClick={() => alert(`Open RA: ${rowData.raId}`)}>
        {rowData.raId}
      </div>
    );
  };

  return (
    <div className="card p-3">
      <div className="mb-4">
        <p>
        This register serves as a centralized repository of all operational controls identified during Risk Assessments (RAs) conducted on the platform. It dynamically updates as controls are approved in RAs, providing visibility into safeguards currently in place to mitigate risks across routine activities, non-routine tasks, and high-risk scenarios.

        </p>
        <strong>Purpose:</strong>
        <ul>
          <li>Track and manage controls to ensure compliance with organizational policies and regulatory standards.</li>
          <li>Enable teams to reference, review, and audit existing safeguards during audits or workflow updates.</li>
        </ul>
      </div>

      <DataTable value={data} paginator rows={5} rowsPerPageOptions={[5, 10, 25]} tableStyle={{ minWidth: '70rem' }}>

        <Column field="controlId" header="Control ID" sortable></Column>

        <Column field="identifiedControl" header="Identified Control"></Column>

        <Column
          field="type"
          header="Type (Preventative / Mitigative)"
          filter
          filterElement={
            <Dropdown
              value={filters.type}
              options={typeOptions}
              onChange={(e) => setFilters((prev) => ({ ...prev, type: e.value }))}
              placeholder="Select Type"
              className="p-column-filter"
              showClear
            />
          }
          body={(rowData) => rowData.type}
        ></Column>

        <Column
          field="pertainsTo"
          header="Pertains To (Routine / Non-Routine / High-Risk Scenario)"
          filter
          filterElement={
            <Dropdown
              value={filters.pertainsTo}
              options={pertainsToOptions}
              onChange={(e) => setFilters((prev) => ({ ...prev, pertainsTo: e.value }))}
              placeholder="Select Pertains To"
              className="p-column-filter"
              showClear
            />
          }
          body={(rowData) => rowData.pertainsTo}
        ></Column>

        <Column
          field="raId"
          header="RA ID"
          sortable
          body={raIdTemplate}
        ></Column>

        <Column
          field="status"
          header="Status (Active / Under Review / Archived)"
          filter
          filterElement={
            <Dropdown
              value={filters.status}
              options={statusOptions}
              onChange={(e) => setFilters((prev) => ({ ...prev, status: e.value }))}
              placeholder="Select Status"
              className="p-column-filter"
              showClear
            />
          }
          body={(rowData) => rowData.status}
        ></Column>

        <Column field="lastReview" header="Last Review / Update Date" sortable></Column>

      </DataTable>
    </div>
  );
}
